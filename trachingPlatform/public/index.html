<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <!-- element-ui 依赖vue-->
    <link rel="stylesheet" href="/static/css/<EMAIL>">
    <link rel="stylesheet" href="/static/css/icon/iconfont.css">



    <!-- // api公共配置 -->
    <script>
        // 注入环境变量到全局对象
        // window.__ENV__ = {
        //     NODE_ENV: '<%= process.env.NODE_ENV %>',
        //     API_URL: '<%= process.env.VUE_APP_API_URL %>'
        // }
    </script>
    <script src="./config/api_config.js"></script>
    <script src="/static/cdn/clipboard.min.js" async></script>
    <script src="/static/jq/jquery.min.js"></script>
    <script src="/static/jq/jquery.fullscreen.js"></script>

    <script src="/static/vue.min.js"></script>
    <script src="/static/<EMAIL>"></script>
    <!-- <link rel="stylesheet" href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.6/theme-chalk/index.min.css">
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.6/index.min.js"></script> -->
    <!-- 压缩，提升了版本，并且不确定有没有使用到压缩功能 -->
    <script src="/static/cdn/FileSaver.min.js" defer></script>
    <script src="/static/moment.js"></script>
    <script src="/static/tinymce/tinymce.min.js" defer></script>
    <!-- antv g6相关 -->

    <!-- //echarts相关文件 -->
    <script src="/static/echarts/echarts.min.js"></script>
    <script src="/static/echarts/echarts-gl.min.js"></script>
    <script src="/static/echarts/ecStat.min.js"></script>
    <script src="/static/echarts/dataTool.min.js"></script>
    <script src="/static/echarts/china.js"></script>
    <script src="/static/echarts/world.js"></script>
    <script src="/static/echarts/bmap.min.js"></script>

    <script src="/static/cdn/crypto/crypto-js.min.js" defer></script>
    <title>
        <%= webpackConfig.name %>
    </title>

</head>

<body>
    <noscript>
        <div style="display:flex;align-items: center;justify-content: center;width: 100vw;height:100vh;">
            <div style="background-color: aliceblue; border-radius: 28px;padding:50px;">
                <strong>抱歉，由于您使用的浏览器JavaScript已被禁用，造成“<%= webpackConfig.name %>”不能正常访问。请启用JavaScript。</strong>
            </div>
        </div>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
</body>

</html>