{"name": "teaching", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "start": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve": "vue-cli-service serve", "build": "node --max-old-space-size=4096 node_modules/@vue/cli-service/bin/vue-cli-service.js build", "lint": "vue-cli-service lint"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@packy-tang/vue-tinymce": "^1.1.2", "@svgdotjs/svg.js": "^3.2.4", "@tinymce/tinymce-vue": "^6.1.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@ztree/ztree_v3": "3.5.48", "animate.css": "^4.1.1", "async_hooks": "^1.0.0", "axios": "^0.24.0", "bignumber.js": "^9.0.2", "biguint-format": "^1.0.2", "bluebird": "^3.5.5", "china-area-data": "^5.0.1", "codemirror": "^5.65.16", "core-js": "^3.6.5", "countup.js": "^2.8.0", "eventemitter3": "^5.0.1", "fabric": "^5.3.0", "file-saver": "^2.0.5", "flake-idgen": "^1.4.0", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "intro.js": "^7.2.0", "js-cookie": "^3.0.5", "js-pinyin": "^0.2.4", "jspdf": "^2.5.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "nprogress": "0.2.0", "pinyin-pro": "^3.18.5", "qrcodejs2": "^0.0.2", "quill": "^1.3.7", "raw-loader": "^4.0.2", "script-ext-html-webpack-plugin": "^2.1.5", "search-bar-vue2": "^0.2.1", "simple-mind-map": "^0.10.6", "snabbdom": "^3.6.2", "sortablejs": "^1.15.2", "splitpanes": "^2.4.1", "tinymce": "^7.9.0", "uuid": "^10.0.0", "vue-barcode": "^1.3.0", "vue-clipboard2": "^0.3.3", "vue-codemirror": "4.0.6", "vue-json-viewer": "^2.2.22", "vue-layer": "^1.2.5", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.3", "vue-wordcloud": "^1.1.1", "vue2-verify": "^1.1.5", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.17.12", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.15", "@vue/cli-plugin-vuex": "^4.5.15", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "copy-webpack-plugin": "^6.4.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "html-webpack-plugin": "^4.0.0", "jsonwebtoken": "^8.5.1", "mockjs": "^1.1.0", "monaco-editor": "^0.30.1", "monaco-editor-webpack-plugin": "^6.0.0", "sass": "^1.26.2", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "vue-template-compiler": "^2.7.10", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.8.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}