
<template>
  <div ref="page" class="page" :class="model">
    <div
      id="knowledge-graph"
      class="knowledge-graph"></div>
    <full-screen v-if="visibleFull" :visible.sync="visibleFull" :graphData="graphData"/>
    <div class="tools">
      <div class="legend">
        <span class="legend-item">
          <i class="knowledge"></i>知识点
        </span>
        <span class="legend-item">
          <i class="scene"></i>场景
        </span>
        <span class="legend-item">
          <i class="material"></i>素材
        </span>
      </div>
      <el-button @click="refreshGraph" class="refresh-btn">更新图谱</el-button>
    </div>
    <div class="tips">可拖动画布或者节点</div>
  </div>
</template>

<script>
import {getGraphData, getNodesByAttrs, setActiveByNode, NodeStyle} from "./util.js"
// import G6 from "@/plugins/g6.min.js"
export default {
  name:"KnowledgeGraph",
  components: {
    FullScreen: () => import('./full-screen.vue')
  },
  props: {
    // 模式， mini 小图， normal 正常
    model: {
      type: String,
      default: 'normal'
    },
    graphData: {
      type: Object,
      default: () => {}
    },
    // 节点数据是否已经加载，在知识仓，素材等场景下都是true； 在场景的对话框中，因为取不到关联的节点，需要传false
    nodeLoaded:{
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      graph: null,
      visibleFull: false,
    };
  },
  computed: {
    height() {
      return this.model === 'mini' ? 200 : this.$el.clientHeight;
    },
    width() {
      return this.model === 'mini' ? 368 : this.$el.clientWidth;
    }
  },
  watch:{
    graphData(){
      this.graph?.destroy();
      this.initPage();
    }
  },
  mounted() {
    this.initPage();
  },
  methods: {
    initPage() {
      const {knowledge,knowledge0,form,form0,material,material0} = NodeStyle;
      const self = this;
      const baseCfg = {
        container: this.$el.querySelector("#knowledge-graph"),
        height: this.height,
        width: this.width,
        layout: {
          type: 'force',
          linkDistance: 150,// 边的长
          nodeStrength: -900, // 节点作用力
          edgeStrength: 1, // 边作用力
          preventOverlap: true, // 防止节点之间以及 combo 之间的重叠
          nodeSpacing: 20, //  防止重叠时节点边缘间距的最小值
          collideStrength: 1 // 统一设置防止节点之间以及 combo 之间重叠的力强
        },
        defaultEdge: {
          type: 'line',
          style: {
            stroke: '#bcbcbc70', // 线条未激活状态颜色
            lineWidth: 1,
            shadowBlur: 0
          },
        },
      }
      const normalCfg = {
        modes: {default:['drag-node', 'drag-canvas','zoom-canvas']},
        defaultNode:{
          style: {
            lineWidth:1,
            shadowBlur: 1
          },
        },
        // 状态
        nodeStateStyles: { knowledge, form, material },
        edgeStateStyles: {
          active: {
            stroke: 'red', // 线条激活状态颜色
            lineWidth: 1,
            shadowBlur: 1
          }
        }
      }
      const graph = new G6.Graph({
        ...baseCfg, 
        ...(this.model === 'mini' ? {} : normalCfg)
      });
      const data = getGraphData(this.graphData, this.nodeLoaded);
      if(!data?.nodes?.length){
        return;
      }
      data.nodes.map(node=>{
        const {mType} = node
        if(mType===1){
          node.size = 80;
          if(this.model === 'mini'){
            node.style = { ...knowledge }
          }else{
            node.style = { ...knowledge0 }
          }
        }else if(mType===2){
          node.size = 120;
          if(this.model === 'mini'){
            node.style = { ...form }
          }else{
            node.style = { ...form0 }
          }
        }else if(mType===3){
          node.size = 60;
          if(this.model === 'mini'){
            node.style = { ...material }
          }else{
            node.style = { ...material0 }
          }
        }
        return node;
      })
      graph.read(data);
      // 画布事件
      if(this.model === 'mini'){
        // 画布点击打开详情
        graph.on('click', () => {
          this.visibleFull = true;
        });
      }else{
        // 节点点击，更新图谱
        graph.on('node:click', (ev) => {
          const node = ev.item; // 被点击的节点元素
          const mAttrs = node.getModel().mAttrs; // 被点击的节点数据
          const { isLoaded } = mAttrs
          if(!isLoaded){
            // 加载更多节点
            getNodesByAttrs(mAttrs,node,graph, ()=>{
              self.$emit('showDetail', node.getModel().mAttrs)
              setActiveByNode(graph,node)
            })
          }else{
            this.$emit('showDetail', mAttrs)
            setActiveByNode(graph,node)
          }
        });
        // 设置原点为激活状态
        const initNode = graph.find('node', (node) => {
          return node.getModel()?.mAttrs?.id === data.nodes[0]?.mAttrs?.id;
        });
        // 手动触发点击
        graph.emit('node:click', {
          item: initNode, // 被点击的节点
        })
        // this.$emit('showDetail', initNode.getModel().mAttrs)
      }
      this.graph = graph;
    },
    // 更新图谱
    refreshGraph() {
      const res = this.graph.save()
      res.nodes.forEach(node => {
        delete node.x
        delete node.y
      });
      this.graph.changeData(res)
    }
  },
  destroyed() {
    this.graph?.destroy();
  },
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  flex-grow: 1;
  .knowledge-graph {
    position: relative;
  }
}
.mini{
  width:368px;
  height: 200px;
  border: 1px solid #ccc;
  .tips,.tools{
    display: none;
  }
}
.normal{
  .knowledge-graph{
    width: 100%;
    min-width: 700px;
    border: none;
  }
  .tools{
    position: fixed;
    width: 50%;
    top: 52px;
    .refresh-btn{
      float: right;
      margin: 0 4px;
    }
    .legend{
      float: left;
      display: block;
      .legend-item{
        margin-right: 4px;
        background-color:#fff;
        i{
          display: inline-block;
          width: 14px;
          height: 10px;
          margin: 0px 2px;
          border-radius: 4px;
          vertical-align: middle;
        }
        .knowledge{
          background-color:#469cff;
        }
        .scene{
          background-color:#8198fc;
        }
        .material{
          background-color:#ebd580;
        }
      }
    }
  }
  .tips{
    position: fixed;
    padding: 6px;
    width: 50%;
    bottom: 0;
    right: 0;
    text-align: right;
    color: #999;
  }
}
</style>
