function nodeIsExsit(node, nodes) {
    return nodes.some(n => n.mAttrs.id === node.mAttrs.id);
}

// 未激活的节点
const knowledge0 = { fill: "#d0e4fb50", lineWidth: 0 };
const form0 = { fill: "#d2d3f150", lineWidth: 0 };
const material0 = { fill: "#fdedaf50", lineWidth: 0 };
// 激活的节点
const knowledge = { fill: "#469cff", lineWidth: 0 };
const form = { fill: "#8198fc", lineWidth: 0 };
const material = { fill: "#ebd580", lineWidth: 0 };
export const NodeStyle = {
    knowledge0,
    form0,
    material0,
    knowledge,
    form,
    material
}
let index = 0;
// 根据node0 获取图谱数据
export function getGraphData({ node0, points, scenes, resources }, load = true) {
    const nodes = [{...node0, mAttrs: {...node0.mAttrs, isLoaded: load } }];
    const edges = [];
    // 知识点 1
    points ? .forEach(point => {
        const node = {
            id: `node${++index}`,
            label: point.name,
            mType: 1,
            style: knowledge0,
            mAttrs: {...point, mType: 1, mTypeKey: "knowledge" }
        }
        if (nodeIsExsit(node, nodes)) return;
        nodes.push(node);
        edges.push({
            id: `edge0-${index}`,
            source: node0.id,
            target: `node${index}`
        })
    });
    // 场景 2
    scenes ? .forEach(scene => {
        const node = {
            id: `node${++index}`,
            label: scene.name,
            mType: 2,
            style: form0,
            mAttrs: {...scene, mType: 2, mTypeKey: "form" }
        };
        if (nodeIsExsit(node, nodes)) return;
        nodes.push(node);
        edges.push({
            id: `edge0-${index}`,
            source: node0.id,
            target: `node${index}`
        })
    });
    // 素材 3
    resources ? .forEach(resource => {
        const node = {
            id: `node${++index}`,
            label: resource.fileName,
            mType: 3,
            style: material0,
            mAttrs: {...resource, mType: 3, mTypeKey: "material" }
        };
        if (nodeIsExsit(node, nodes)) return;
        nodes.push(node);
        edges.push({
            id: `edge0-${index}`,
            source: node0.id,
            target: `node${index}`
        })
    });
    return {
        nodes,
        edges
    }
}
// 格式化节点
function formatNodeData(node) {
    const { mType } = node
    if (mType === 1) {
        node.size = 80;
    } else if (mType === 2) {
        node.size = 120;
    } else if (mType === 3) {
        node.size = 60;
    }
    return node
}

// 批量增加节点
function addNodes(nodes, graph, callback) {
    const source = nodes[0].id
    nodes.forEach((n) => {
        // 排除已经实例化的节点
        const curNode = graph.find('node', fn => fn.getModel().mAttrs ? .id === n.mAttrs.id)
        if (!curNode) {
            graph.addItem('node', (formatNodeData(n)))
            graph.addItem('edge', {
                id: `edge${source}-${n.id}`,
                target: `${n.id}`,
                source: `${source}`,
            })
        } else {
            const target = nodes[0].target;
            let existEdge = false
            graph.getEdges().forEach(edge => {
                const model = edge.getModel();
                if (model.source === source && model.target === target) {
                    // 已经存在同向的线条
                    existEdge = true
                } else if (model.source === target && model.target === source) {
                    // 已经存在反向的线条
                    existEdge = true
                } else {}
            })
            const curNodeId = curNode.getModel().id
            if (!existEdge && source !== curNodeId) {
                graph.addItem('edge', {
                    id: `edge${source}-${curNodeId}`,
                    target: `${curNodeId}`,
                    source: `${source}`,
                })
            }
        }
    })
    const res = graph.save()
    graph.changeData(res)
    callback && callback()
}

// 根据节点，查询关联的节点
import K from "@/api/module/knowledge.js"
import M from "@/api/module/material.js"
import S from "@/api/module/scene.js"
export function getNodesByAttrs(attr, node, graph, callback) {
    const mType = attr.mType;
    const node0 = node.getModel();
    // 取到数据之后合并
    if (mType === 1) {
        // 知识点
        K.KnowledgeFindByID({ id: attr.id }).then(res => {
            const { data } = res;
            const { pointList, sceneList, resourceList } = data;
            node0.mAttrs = {...node0.mAttrs, ...data, isLoaded: true }
            const { nodes } = getGraphData({ node0, points: pointList, scenes: sceneList, resources: resourceList })
            addNodes(nodes, graph, callback)
        })
    } else if (mType === 2) {
        // 场景
        S.FindKnowledgeBySceneId({ sceneId: attr.id }).then(res => {
            let dynamicFormKnowledge = res ? .data
            if (dynamicFormKnowledge ? .length !== 0) {
                node0.mAttrs = {...node0.mAttrs, isLoaded: true }
                const { nodes } = getGraphData({ node0, points: dynamicFormKnowledge, scenes: [], resources: [] })
                addNodes(nodes, graph, callback)
            } else {
                callback && callback()
            }
        })
    } else if (mType === 3) {
        // 素材
        M.FilesResourcesFindByID({ id: attr.id }).then(res => {
            const { data } = res;
            const { pointList, sceneList, relatedList } = data;
            node0.mAttrs = {...node0.mAttrs, ...data, isLoaded: true }
            const { nodes } = getGraphData({ node0, points: pointList, scenes: sceneList, resources: relatedList })
            addNodes(nodes, graph, callback)
        })
    }
}

// 设置节点为激活状态 目前只有一个状态
export function setActiveByNode(graph, node) {
    if (!node) return;
    if (!graph) return;
    // 去除状态
    graph.getEdges().forEach(ed => {
        ed.clearStates();
    })
    graph.getNodes().forEach(nd => {
            nd.clearStates();
        })
        // 添加状态
    const edges = node.getEdges();
    edges.forEach(ed => {
            // 获取所有关联的边 设置激活状态， 边的来源和目标也设置激活状态
            ed.setState('active', true)
                // 来源
            const gs = ed.getSource()
            const stateS = gs.getModel().mAttrs ? .mTypeKey
            if (!gs.hasState(stateS)) {
                gs.setState(stateS, true)
            }
            // 目标
            const gt = ed.getTarget()
            const stateT = gt.getModel().mAttrs ? .mTypeKey
            if (!gt.hasState(stateT)) {
                gt.setState(stateT, true)
            }
        })
        // graph.focusItem(node, true);
}