
<template>
  <base-dialog :fullscreen="true" :noFooter="true" :visible.sync="visibleFull" :class="{'hidden':hidden}">
    <div class="full-screen-graph">
      <div class="node-detail">
        <!-- 知识点 -->
        <DetailKnowledge v-if="detailType == 1" :knowledgInfo="attr" status="with-click">
        </DetailKnowledge>
        <DetailMaterial v-else-if="detailType === 3" :fileObj="attr" />
        <template v-else-if="detailType === 2">
          <div class="tips" v-if="isDemo">
            这是一条演示数据
          </div>
          <!-- 动态表单 -->
          <FormCustom v-if="formType === 1" :dataId="dataId" type="detail" :moduleId="attr.id" :caseId="attr.caseId" />
          <!-- 葡萄城 -->
          <FormSpread v-else-if="formType === 2" :dataId="dataId" :erId="attr.erId" type="detail" :moduleId="attr.id" :caseId="attr.caseId" />
          <!-- 通用票据 -->
          <FormBill v-else-if="formType === 3" :dataId="dataId" :erId="attr.erId" type="detail" :sceneId="attr.id" :isView="true" />
          <i v-else></i>
        </template>
      </div>
      <knowledge-graph class="graph" :graphData="graphData" :nodeLoaded="nodeLoaded" model="normal" @showDetail="showDetail" />
    </div>
    <!-- <el-button @click="close">关闭</el-button> -->
  </base-dialog>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import { mapGetters } from 'vuex';
export default {
  name:"FullScreenGraph",
  components:{
    KnowledgeGraph: ()=>import("./index.vue"),
    // 详情展示
    FormCustom: ()=>import("@/components/designer/form-designer/outer-usage.vue"),
    FormSpread: ()=>import("@/components/designer/sheets-designer/outer-usage.vue"),
    FormBill: () => import("@/components/base/bill/bill-with-dispatch.vue"),
    DetailMaterial: ()=>import("@/components/materialModule/courseMaterial.vue"),
    DetailKnowledge: ()=>import("@/components/knowledgeDetail/index.vue"),
  },
  props: {
    graphData: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    },
    // 节点数据是否已经加载，在知识仓，素材等场景下都是true； 在场景的对话框中，因为取不到关联的节点，需要传false
    nodeLoaded:{
      type: Boolean,
      default: true
    }
  },
  computed: {
    visibleFull: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    },
    ...mapGetters({
      knowledgeGraphVm: ["knowledgeGraphVm"]
      
    }),
  },
  data() {
    return {
      detailType: 0,
      formType: 0,
      attr:{},
      hidden: false,// 隐藏这个视图
      dataId: 0, // 表单显示的数据ID
      isDemo: false, // 是否是演示数据
      firstNodeClick: true, // 是否是第一次点击第一个节点【从案例进入知识图谱，第一个节点需要从案例获取填充的数据，第一次以后使用演示数据】
    };
  },
  watch: {
    visible(val){
      if(val){
        this.hidden = false;
      }else{
        this.hidden = true;
      }
    },
    graphData(val){
      if(val){
        this.hidden = false;
      }else{
        this.hidden = true;
      }
    }
  },
  beforeCreate(){
    const vms = this.$store.state.knowledgeGraphVm||[];
    vms[vms.length-1]?.hideView();
  },
  mounted(){
    this.$store.commit('knowledgeGraphVm',this);
  },
  methods: {
    hideView(){
      this.hidden = true;
    },
    close() {
      this.$emit("update:visible", false);
    },
    async showDetail(attr){
      this.detailType = 0;
      this.dataId = 0;
      this.isDemo = false
      if(attr.mType === 2){
        if(this.$attrs.dataId && this.firstNodeClick ){
          this.firstNodeClick = false
          this.dataId = this.$attrs.dataId;
        }else{
          const {data, code} = await this.$api.SearchMainTableDataByERId({
            erId: attr.erId,
            isPaging: 1,
            pageIndex : 1,
            pageSize: 1
          })
          if(code === 200){
            this.dataId = data.length > 0 ? data[0].id : 0;
            this.isDemo = true
          }
        }
      }
      this.$nextTick(()=>{
        this.detailType = attr.mType;
        this.formType = attr.formType
        this.attr = cloneDeep(attr);
      })
    }
  },
  destroyed(){
    const vms = this.$store.state.knowledgeGraphVm||[]
    vms.forEach((vm)=>{
      vm&&vm.close();
    })
  }
};
</script>

<style lang="scss" scoped>
.hidden{
  display: none;
}
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 0;
  height: calc(100% - 50px) !important;
  min-height: auto !important;
  overflow: hidden !important;
}
.full-screen-graph{
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  .node-detail{
    position: relative;
    height: 100%;
    width: 50%;
    max-height: 100%;
    overflow: auto;
    border-right: 1px solid #ccc;
    padding: 20px;
    ::v-deep .knowledgeDetail-tabs{
      flex: 1 1 auto;
    }
    .tips{
      position: absolute;
      bottom: 8px;
      right: 8px;
      text-align: right;
      z-index: 2;
      color: #e6a23c;
      background-color: #fdf6ec;
      border-color: #faecd8;
      padding: 6px;
      border-radius: 4px;
    }
  }
  .graph{
    position: relative;
    width: 50%;
    height: 100%;
  }
}
</style>
