<template>
  <div :style="style">
    <Toolbar style="border-bottom: 1px solid #ccc" :style="toolbarStyle" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
    <Editor v-model="content" :style="editorStyle" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated"
      @onBlur="onBlur"
      @onChange="onChange" />
    <span v-if="showWordNumber" class="wordNumber">{{ currentLength }}/{{ maxLength }}</span>
  </div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { Message } from 'element-ui'
function example_image_upload_handler(blobInfo, success, failure) {
  let xhr, formData;
  xhr = new XMLHttpRequest();
  xhr.withCredentials = false;
  xhr.open("POST", window.FILEIP);
  xhr.onload = function () {
    let json;
    if (xhr.status === 403) {
      failure("HTTP Error: " + xhr.status, { remove: true });
      return;
    }
    if (xhr.status < 200 || xhr.status >= 300) {
      failure("HTTP Error: " + xhr.status);
      return;
    }
    json = JSON.parse(xhr.responseText);
    console.log("图片上传", json);
    if (!json || typeof json.data != "string") {
      failure("Invalid JSON: " + xhr.responseText);
      return;
    }
    success(json.data);
  };
  xhr.onerror = function () {
    failure("Image upload failed due to a XHR Transport error. Code: " + xhr.status);
  };
  formData = new FormData();
  formData.append("file", blobInfo);
  formData.append('iscompressed', false); //是否压缩
  xhr.send(formData);
}

export default {
  components: {
    Editor,
    Toolbar
  },
  props: {
    showWordNumber: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: 0
    },
    validateMaxLen: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 100
    },
    height: {
      type: [Number,String],
      default: 500
    },
    excludeKeys:{ // 要排除的工具栏
      type: Array,
      default: ()=>[]
    }
  },
  data() {
    return {
      mode: 'default', // or 'simple'
      editor: null,
      toolbarConfig: {
        excludeKeys: ['uploadVideo', 'emotion'] //"insertTable",
      },
      editorConfig: {
        readOnly: this.readOnly,
        placeholder:" ",
        MENU_CONF: {                // 配置上传服务器地址
          uploadImage: {
            // 小于该值就插入 base64 格式（而不上传），默认为 0
            // base64LimitSize: 5 * 1024 * 1024, // 5kb
            // 单个文件的最大体积限制，默认为 2M
            // maxFileSize: 1 * 1024 * 1024, // 1M
            // // 最多可上传几个文件，默认为 100
            maxNumberOfFiles: 10,
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: ['image/*'],
            // 自定义上传
            async customUpload(file, insertFn) { // 文件上传
              try {
                example_image_upload_handler(file, (data) => {
                  insertFn(data)
                })
              } catch (e) {
                Message.error('上传附件失败！');
              }
            }
          },
          uploadVideo: {
            async customUpload(file, insertFn) {
              try {
              } catch (e) {
                Message.error('上传附件失败！');
              }
            }
          }
        }

      },
      content: '',
      currentLength: 0,
      defaultMaxLen: 20,
      validateStatus: true
    }
  },
  computed: {
    maxLength() {
      let len = this.defaultMaxLen
      if (this.max >= 1) {
        len = this.max
      }
      return len
    },
    style() {
      let res = ''
      // if (!this.validateStatus) {
      //   res = 'border: 2px solid #F56C6C'
      // } else {
      res = 'border: 1px solid #CCC'
      // }
      if (this.width < 100) {
        res = res + 'width:' + this.width + '%;'
      }
      return res
    },
    toolbarStyle(){
      return {display:this.readOnly?'none':'block'}
    },
    editorStyle() {
      return {
        'overflow-y': 'hidden',
        height: this.height==='auto'?'auto': (this.height+'px')
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.content = newVal
      },
      immediate: true,
    },
    excludeKeys:{
      handler(newVal) {
        this.toolbarConfig.excludeKeys =  this.toolbarConfig.excludeKeys.concat(newVal); // 合并默认值
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    onChange(editor) {
      this.currentLength = editor.getText().length
      this.$emit('input', this.content)
      if (editor.getText().length > this.editorConfig.maxLength) {
        this.validateStatus = false
      } else {
        this.validateStatus = true
      }
    },
    // 编辑器 失去焦点事件
    onBlur(editor) {
      if (editor.getText()) {
        this.$emit('handleSave', this.content)
      }
    }
  },
  mounted() {
    // 模拟 ajax 请求，异步渲染编辑器
    this.$nextTick(() => {
      //this.content = '<p>模拟 Ajax 异步设置内容 HTML</p>'
      this.content = this.value
      this.editorConfig.maxLength = this.maxLength

      console.log('this.editorConfig', this.editorConfig);


    
      // 图片上传配置
      // this.editorConfig.MENU_CONF['uploadImage'] = {
      //   server: window.FILEIP,
      // }
      // // 视频上传配置
      // this.editorConfig.MENU_CONF['uploadVideo'] = {
      //   server: window.FILEIP,
      // }
    })
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  }
}
</script>
<style scoped>
.wordNumber {
  color: #909399;
  background: #fff;
  text-align: right;
  z-index: 100;
  right: 10px;
  bottom: 4px;
  font-size: 12px;
  position: absolute;
}
</style>
