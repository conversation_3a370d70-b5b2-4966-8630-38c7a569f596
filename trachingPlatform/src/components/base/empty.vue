<template>
  <div :class="`${size} empty-info`">
    <div v-if="loading" class="loading-wrapper iconfont icon-jiazai loading-icon">
    </div>
    <template v-else>
      <img v-if="type === 'noResult'" class="img" src="@/assets/images/no-result.png" :alt="msg" />
      <img v-else-if="type === 'empty'" class="img" src="@/assets/images/case-empty.png" :alt="msg" />
      <img v-else-if="type === 'noContent'" class="img" src="@/assets/images/no-content.png" :alt="msg" />
      <img v-else-if="type === 'noNetwork'" class="img" src="@/assets/images/no-network.png" :alt="msg" />
      <img v-else-if="type === 'developing'" class="img" src="@/assets/images/developing.png" :alt="msg" />
      <img v-else-if="type === 'noData'" class="img" src="@/assets/images/no-data.png" :alt="msg" />
    </template>

    <div class="msg">
      <div v-if="loading">
        数据加载中，请稍后...
      </div>

      <div v-else-if="msg">
        {{ msg }}
      </div>

      <slot v-else></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FusionFrontEmpty',
  props: {
    loading: {
      type: Boolean,
    },
    msg: {
      type: String,
    },
    type: {
      type: String,
      default: 'noResult',
    },
    size: {
      type: String,
      default: 'small',
    },
  },
  data() {
    return {}
  },

  mounted() { },

  methods: {},
}
</script>

<style lang="scss" scoped>
// @import '@/styles/element-variables.scss';

.empty-info {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;

  .loading-wrapper {
    font-size: 64px;
    color: #768bff;
    animation: refresh 1s linear 0.5s infinite;
  }

  .msg {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #606a78;
    margin-top: 10px;
  }

  &.small img {
    width: 64px;
    height: auto;
  }

  &.middle img {
    width: 128px;
    height: auto;
  }

  &.large img {
    width: 168px;
    height: auto;
  }
}
</style>
