<!-- iframe 综合页面的基础页面 -->
<template>
  <div class="single-collect-base-page">
    <div
      class="page-content"
      ref="contentRef">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "FusionFrontSingleCollectPage",
  props: {
   
  },
  data() {
    return {
      
    };
  },
  methods: {
    
  }
};
</script>
<style lang="scss" scoped>
.single-collect-base-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 20px;
  line-height: 22px;
  overflow: auto;
  .page-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .content-item {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .content-item-title {
      text-indent: 20px;
      font-weight: bold;
    }
    .text-center {
      text-align: center;
    }
    .m-center-content {
      margin: 0 auto;
    }
    .content-item-body {
      display: flex;
      flex-direction: column;
      gap: 10px;
      .content-item-body-header {
        font-weight: bold;
      }
      .row {
        text-indent: 20px;
      }
      .content-auto {
        width: 100%;
        overflow: auto;
      }
      .content-item-tip,
      .content-item-body-tip {
        text-align: center;
      }
      ::v-deep .attachment-wrapper {
        min-height: 400px;
        height: 400px;
      }
      ::v-deep .dynamic-spread {
        min-height: 400px;
        height: 400px;
      }
      ::v-deep .single-flow-preview {
        min-height: 400px;
        height: 400px;
      }
    }
  }
}
</style>
