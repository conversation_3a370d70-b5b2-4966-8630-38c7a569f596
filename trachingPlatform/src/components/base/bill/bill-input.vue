<template>
    <!-- v-if="dispatchInfo.isKnowledge" 在调度的基础上，增加关联知识点的显示 -->
    <div :class="['bill-input', className, (dispatchInfo.isView||dispatchInfo.isKnowledge) ? 'view-mode' : 'edit-mode']" v-on="$listeners">
      <template>
        <slot name="label"></slot>
      </template>
      <slot v-if="$slots.content" name="content" :isView="dispatchInfo.isView"></slot>
      <el-input v-else :readonly="dispatchInfo.isView" placeholder="" size="mini" v-model="modelValue" readonly
        v-bind="$attrs" class="input">
      </el-input>
      <div v-if="dispatchInfo.isDispatch && !dispatchInfo.isView" class="dispatch-img">
        <img src="@/assets/dispatch/dispatch.png" alt="调度" />
      </div>
      <dispatchIcon v-else v-bind="{ ...dispatchInfo }" />
      <div v-if="dispatchInfo.isKnowledge" class="show-knowledge">
        <i class="el-icon-chat-line-square" @click="showKnowledgeList"></i>
      </div>
    </div>
</template>

<script>
// 'isDispatch',
// 'fieldId', // 当前字段ID
// 'typeId', // 当前调度类型
// 'refId', // 当前调度类型ID,
// 'dispatchInfo',
// 'baseRefId',
// 'baseTypeId',
// 'originalTableId',
// 'originalTableName'
import { BILL_SCENE_TYPE } from '@/enums'
import eventBus from '@/utils/eventBus'
export default {
  name: 'FusionFrontBillInput',
  props: ['fieldName', 'isView', 'getDispatchInfo', 'value', 'className'],
  components: {
    designerDispatchIcon: () =>
      import('@/components/dispatch/setting/dispatch-icon.vue'),
    dispatchIcon: () =>
      import('@/components/dispatch/trigger/dispatch-icon.vue')
  },
  data() {
    return {
      val: '',
      dispatchInfo: {}
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('changeInput', {
          fieldName: this.fieldName,
          value: val
        })
      }
    }
  },
  mounted() {
    this.dispatchInfo = this.prepareDispatchInfo()
    eventBus.$on('configDispatch', item => {
      if (item.billFieldName === this.fieldName) {
        this.dispatchInfo = {
          ...this.dispatchInfo,
          isDispatch: item.value
        }
      }
    })
    eventBus.$on('configKnowledge', item => {
      if (item.billFieldName === this.fieldName) {
        this.dispatchInfo = {
          ...this.dispatchInfo,
          isKnowledge: item.value
        }
      }
    })
  },

  methods: {
    prepareDispatchInfo() {
      const { item, refId, isView, triggerData,isKnowledge, knowledgeList} = this.getDispatchInfo(
        this.fieldName
      )
      const dispatchInfo = {
        isView,
        refId,
        typeId: BILL_SCENE_TYPE,
        fieldId: item.fieldId,
        triggerData,
        isDispatch: item.isDispatch,
        isKnowledge,
        knowledgeList
      }
      return dispatchInfo
    },
    showKnowledgeList() {
      this.dispatchInfo.isView&& this.$eventBus.$emit('show-knowledge', this.dispatchInfo.knowledgeList)
    }
  }
}
</script>

<style lang="scss">
.bill-input {
  display: flex;
  flex: 1;
  min-width: 0;
  justify-content: space-between;
  align-items: center;
  display: flex;
  font-size: 24px;
  font-family: STSongti-SC-Regular, STSongti-SC;
  font-weight: 400;
  color: #333;
  line-height: 37px;
  height: 100%;

  .dispatch-img {
    display: flex;
    align-items: center;

    img {
      cursor: pointer;
      width: 16px;
      height: 16px;
      margin-left: 6px;
    }
  }



  >.el-input,
  >.el-input input,
  >.el-input .el-input__inner {
    height: 25px !important;
    line-height: 25px !important;
    padding: 0 !important;
    background: inherit !important;
    font-size: 14px;
    color: #666cf2 !important;

    &:focus-visible {
      outline: auto;
    }
  }

  &.full {

    >.el-input,
    >.el-input input,
    >.el-input .el-input__inner {
      height: 100% !important;
      line-height: 100% !important;
      padding: 0 !important;
      background: inherit !important;
      font-size: 14px;
      color: #666cf2 !important;

      &:focus-visible {
        outline: auto;
      }
    }
  }

  &.view-mode>.el-input .el-input__inner {
    border: none !important;
  }

  .dispatch-btn {
    padding-left: 10px;
  }

  // 知识点
  .el-icon-chat-line-square{
    margin-left:4px;
    font-size: 20px;
    cursor: pointer;
    color: var(--theme_primary_color);
  }
    
}
</style>
