<template>
  <div class="circle-wrapper">
    <span
      class="circle"
      v-for="item in 11"
      :key="item"></span>
  </div>
</template>

<script>
export default {
  name: 'FusionFrontCircleBar',

  data() {
    return {}
  },

  mounted() {},

  methods: {}
}
</script>

<style lang="scss" scoped>
.circle-wrapper {
  width: 34px;
  height: 100%;
  .circle {
    width: 19px;
    height: 19px;
    background: #f9f9f9;
    border-radius: 50%;
    display: block;
    margin: 12px auto 29px;
    box-shadow: inset 1px 0px 1px 0px rgba(105, 105, 105, 0.5),
      inset 1px 0px 4px 0px #d6d8fd;

    &:nth-child(4n) {
      position: relative;

      &::after {
        content: '....';
        position: absolute;
        color: #d5d6ff;
        top: 10px;
        left: -2px;
        font-size: 21px;
        letter-spacing: 1px;
        opacity: 0.8;
      }
    }
  }
}
</style>
