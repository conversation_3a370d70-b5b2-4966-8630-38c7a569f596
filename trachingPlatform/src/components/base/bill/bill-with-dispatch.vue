<template>
  <div class="bill-page">
    <div style="display: flex;justify-content: end;margin-right: 20px">
      <el-button @click="handleEdit" v-if="checkExportPermission()&&showEditBtn">编辑模型</el-button>
    </div>
    <component v-if="invoice.typeId == 3 && !loading" :is="currentBill.component"
      style="display: flex; justify-content: center; align-items: center" class="component" :list="list"
      :isView="isView" :invoice="invoice" :currentBill="currentBill" :style="getStyle(currentBill.style)"
      :otherInfo="otherInfo" :answerValue="answerValue" ref="models" />
    <div class="task-tool" v-if="openGlobalTask && this.$route.path !== '/homework/create'">
      <ChooseCertificate  v-if="this.invoice.isBillPool==1" :currentData="{}" type="primary" @handlecallback="handlecallback"></ChooseCertificate>
    </div>
    <base-dialog
        class="bill-designer-base-dialog"
        :destroy-on-close="true"
        :fullscreen="true"
        :show-close="false"
        :noFooter="true"
        :visible.sync="billDlgVisible">
      <bill-designer-dialog
          :sceneId="curScene.id"
          @handleSave="handleSave"
          @handleClose="closeBillDlg" />
    </base-dialog>
  </div>
</template>

<script>
import { checkExportPermission } from "@/utils/check-role";
import { getStyle } from '@/utils/style'
import { loadBillSceneData } from '@/features/loadSceneData'
import baseConf from '@/components/bill/baseConfig/index.js'
import { mapGetters } from "vuex";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: 'FusionFrontBillWithDispatch',
  props:{
    "sceneId":{
      type: String,
      default: ''
    }, // 场景id
    "dataId":{
      type: String,
      default: ''
    }, // 数据id
    "isView":{
      type: Boolean,
      default: true
    }, // 是否查看模式,
    "templateNo":{
      type: String,
      default: ''
    },
    "tableId":{
      type: String,
      default: ''
    },// 票据No前端硬编码固定值
    "singleData":{
      type: Object,
      default: {}
    },
    "erId":{
      type: Number,
      default: 0
    },
    showEditBtn:{
      type:Boolean,
      default: true
    }
  },
  components: {
    BillDesignerDialog: () => import("../../../views/workbench/application/component/scenes/bill-designer-dialog.vue"),
    ChooseCertificate: () => import("@/components/task-model-button/chooseCertificate.vue")
  },
  data() {
    return {
      billDlgVisible: false,
      loading: true,
      curScene: {},
      invoice: {
        templateNo: -1
      },
      list: [],
      currentBill: {},
      otherInfoValue: null,
      params: {},
      answerValue: {},
      otherInfo: {} //题目信息
    }
  },

  mounted() {
    this.params = {
      ...this.$route.params
    }
    this.getBillInfo()
  },
  watch: {
    dataId: {
      handler(val) {
        if (val) {
          this.getBillInfo()
        }

      },
      // immediate: true
    },
  },
  computed: {
    ...mapGetters({
      openGlobalTask: ['getGlobalTaskFlag'],// 是否开启全局任务模式
    })
  },
  methods: {
    checkExportPermission,
    getStyle,
    handleEdit(){
      this.curScene.id = this.sceneId;
      this.billDlgVisible = true;
    },
    async handleSave(){
      this.billDlgVisible = false
      await this.getBillInfo()
      this.loading = true
      this.$nextTick(()=>{
        this.loading = false
      })
    },
    closeBillDlg(){
      this.billDlgVisible = false;
    },
    async getBillInfo() {
      this.loading = true;
      this.currentBill = {};//清空上次的票据对象，以免模板重复
      //if(!this.sceneId) return
      const { code, data, list } = await loadBillSceneData(
        this.$api,
        this.sceneId,
        this.$message,
        this.templateNo
      )
      if (code === 200) {
        this.invoice = data
        this.list = list
      }
      // TODO 待测试：向上返回模板数据
      this.$emit("resolveTemplateData", data);

      let res = await this.$api.FetchDispatchSingleData({
        erId: this.erId !==0?this.erId:this.invoice.erId,
        id: this.dataId,
        tableId:this.tableId
      })
      const tableMap = res.data?.tableMap || []
      const mainTable = tableMap.find(t => t.isMain)
      if (mainTable?.objectName) {
        this.otherInfo = res.data[mainTable.objectName];
        this.$emit("handleChildrenDataLoaded", {
          flowCode: this.otherInfo?.flowCode,
          businessNo: this.otherInfo.businessNo
        });
        this.$emit("dataLoaded");
        if (this.invoice.templateId == 58) {//银行回单统一模版
          if (!this.otherInfo.receiptBankType) {//receiptBankType 回单银行种类->具体查看bill模块 billConfig/bankOf_bankTY.js type类型
            this.$message.error("数据为空，模板【回单银行种类】未填或填错,请在对应模板银行回单中填写对应【回单银行种类】！");
            return
          }
          this.currentBill = baseConf.find(b => b.type == 58);
          this.currentBill.componentsList.forEach(listItem => {
            if (listItem.type == this.otherInfo.receiptBankType) {
              this.currentBill.component = listItem.component;
            }
          });
        } else if (this.invoice.templateId == 118) {//增值税专用发票通用模版
          if (!this.otherInfo.invoiceType || this.otherInfo.invoiceType > 3) {//invoiceType 代表联次一二三联
            this.$message.error("数据为空，模板【联次】未填或填错,请在对应增值税专用发票中填写对应【联次】！");
            return
          }
          this.currentBill = baseConf.find(b => b.type == 118);
          this.currentBill.componentsList.forEach(listItem => {
            if (listItem.type == this.otherInfo.invoiceType) {
              this.currentBill.component = listItem.component;
            }
          });
          // console.log(this.currentBill.component, 'this.currentBill.component');
        } else if (this.invoice.templateId == 119) {//增值税普通发票通用模版
          if (!this.otherInfo.invoiceType || this.otherInfo.invoiceType > 3) {//invoiceType 代表联次一二三联
            this.$message.error("数据为空，模板【联次】未填或填错,请在对应增值税普通发票中填写对应【联次】！");
            return
          }
          this.currentBill = baseConf.find(b => b.type == 119);
          this.currentBill.componentsList.forEach(listItem => {
            if (listItem.type == this.otherInfo.invoiceType) {
              this.currentBill.component = listItem.component;
            }
          });
        } else {
          this.currentBill = baseConf.find(b => b.type === this.invoice.templateId)
        }
        this.answerValue = res.data;
        // this.answerValue.otherInfoObj = res.data[mainTable.objectName]
      }

      this.loading = false
    },
    async getOriginTicket(){
    let resultData = [
        {
          id: this.invoice.id,
          key: `scene${this.dataId + 1}`,
          name: this.invoice.formName,
          type: 3, // 场景
          vocherlist:this.invoice.isBillPool==1?[]:""
        }
      ];
      const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo) || { taskInfo: { details: [] } };
      let params={
        bno: this.singleData.businessNo,
        orederNo:this.singleData.orderNo?this.singleData.orderNo:''
      }
       const{data,code}= await this.$api.GetOriginTicketVocher(params)
       if(code == 200){
       resultData.forEach(val => {
          if (newHomeWorkInfo.taskInfo.details.find(item => item.key === val.key)) {
            return;
          }
          newHomeWorkInfo.taskInfo.details.push(val);
        });
        newHomeWorkInfo.taskInfo.details.forEach((item)=>{
                  if(item.id == this.invoice.id){
                    item.catchData=data
                  }
        })
        this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
      }
    },

    handlecallback(){
     this.getOriginTicket()
    },
    addTask(taskInfo) {
      const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo) || { taskInfo: { details: [] } };
      if (Array.isArray(taskInfo)) {
        // 数组
        taskInfo.forEach(val => {
          if (newHomeWorkInfo.taskInfo.details.find(item => item.key === val.key)) {
            return;
          }
          newHomeWorkInfo.taskInfo.details.push(val);
        });
      } else {
        if (newHomeWorkInfo.taskInfo.details.find(item => item.key === taskInfo.key)) {
          return;
        }
        newHomeWorkInfo.taskInfo.details.push(taskInfo);
      }
      this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
    },

  }
}
</script>

<style lang="scss" scoped>
.bill-page {
  background-color: #fff;
  height: 100%;
  box-sizing: border-box;
  padding-top: 30px;
  position: relative;

  // 组件样式
  .component {
    > ::v-deep div {
      box-shadow: 0 0 6px 0 #eee;
    }
  }
  .task-tool{
    position: absolute;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    bottom: -10px;
    right: 0;
    background: #fff;
    width: 100%;
  }
}

</style>
