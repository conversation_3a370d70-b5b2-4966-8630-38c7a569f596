
<template>
  <el-dialog v-dialogDrag ref="elDialog" v-show="isOpen" v-if="dialogVisible" :visible.sync="dialogVisible" :width="calcWidth"
    :class="`${showMinus && isMinus ? 'minus-dialog' : ''} ${isActive ? 'dialog--active' : ''}`"
    :custom-class="`${$attrs.className || ''} base-dialog ${isFull ? 'full-dialog' : ''} `" :style="getDialogPosition()"
    :modal="$attrs.modal === true ? true : false" :fullscreen="isFull" v-bind="$attrs" destroy-on-close
    :append-to-body="appendToBody" @open="handleOpen" @opened="handleOpened" @close="handleClose" @closed="handleClosed"
    :height="height">
    <!-- 自定义title -->
    <template v-if="customTitle" v-slot:title>
      <slot name="title"></slot>
      <el-button v-if="!noFullChange" class="action-icon" type="text" :title="isFull?'退出全屏':'全屏'" @click="toggleScreen">
        <i class="iconfont" :class="isFull ? 'icon-quxiaoquanping_o' : 'icon-quanping_o' "></i>
      </el-button>
    </template>
    <!-- 默认的全屏按钮 -->
    <template v-else-if="hasFull" v-slot:title>
      <div class="base-header">
        <div class="title">{{ title }}</div>
        <slot name="headerActions"></slot>
        <el-button v-if="!noFullChange" class="action-icon" type="text" :title="isFull?'退出全屏':'全屏'" @click="toggleScreen">
          <i class="iconfont" :class="isFull ? 'icon-quxiaoquanping_o' : 'icon-quanping_o' "></i>
        </el-button>
        <el-button v-if="showMinus" class="action-icon" title="最小化" type="text" @click="handleMinus">
          <i class="el-icon-minus"></i>
        </el-button>
      </div>
    </template>
    <!-- 没有title -->
    <template v-else v-slot:title>
      <span class="base-dialog-title">{{ title }}</span>
      <slot name="headerActions"></slot>
    </template>
    <slot v-if="dialogVisible"></slot>
    <template v-if="!noFooter">
      <slot v-if="$slots.footer" name="footer"></slot>
      <div v-else slot="footer" class="dialog-footer">
        <el-button v-if="defaultButtons[0]" v-preventReClick @click="handleReset">取消</el-button>
        <el-button v-if="defaultButtons[1]" v-preventReClick type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import eventBus from '@/utils/eventBus'
export default {
  name: 'BaseDialog',
  props: {
    controlName: { // 给dialog名字，用于控制dialog的显隐
      type: String,
      default: ''
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    showMinus: {
      type: Boolean,
      default: false
    },
    hasFull: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: 'medium'
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    defaultButtons: {
      type: Array,
      default: () => {
        return [1, 1]
      }
    },
    noFooter: {
      type: Boolean,
      default: false
    },
    noFullChange: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      topicId: uuidv4(),
      isMinus: false,
      height: '',
      zIndexStyle: {},
      isFull: false // 是否全屏
    }
  },
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:visible', val)
      },
      get() {
        return this.visible
      }
    },
    isActive() {
      return this.$store?.getters["dialog/active"]?.name === this.controlName
    },
    isOpen() {
      // 通过store控制dialog的显隐
      return !this.controlName || this.$store.getters['dialog/allOpend'].find(d => d.name === this.controlName)
    },
    customTitle() {
      return this.$slots.title
    },
    calcWidth: {
      get() {
        switch (this.width) {
          case 'small':
            return '30%'
          case 'medium':
            return '70%'
          case 'min-large':
            return '55%'
          case 'large':
            return '90%'
          default:
            return this.width
        }
      }
    }
  },
  mounted() {
    // const zIndex = this.$store.getters['dialog/zIndex']
    // if (zIndex) {
    //   this.zIndex = zIndex
    //   this.zIndexStyle.zIndex = zIndex
    // }
    // if (this.controlName) {
    //   this.$store.dispatch("dialog/open", {
    //     name: this.controlName,
    //     zIndex,
    //     instance: this
    //   })
    // }
    // 初始化时否全屏
    this.isFull = !!this.fullscreen
  },
  methods: {
    recoverShow() {
      this.isMinus = false
    },
    handleMinus() {
      this.isMinus = true
      eventBus.$emit('dlg-minus-broadcast', {
        name: this.controlName,
        title: this.title,
        topicId: this.topicId,
        recoverShow: () => {
          this.recoverShow()
        }
      })
    },
    handleOpen() {
      this.$listeners.open && this.$listeners.open()
    },
    handleOpened() {
      if (this.$listeners.opened) {
        this.$listeners.opened()
      }
    },
    handleClose() {
      this.$listeners.close && this.$listeners.close()
      this.$emit('update:visible', false)
    },
    handleClosed() {
      this.$listeners.closed && this.$listeners.closed()
      this.$emit('update:visible', false)
    },
    handleReset() {
      const b = this.$attrs['before-close'] || this.$attrs.beforeClose
      if (b) {
        b()
      } else {
        this.$emit('update:visible', false)
      }
    },
    handleSubmit() {
      if (this.$listeners.submit) {
        this.$emit('submit')
      } else {
        this.$emit('update:visible', false)
      }
    },
    toggleScreen(e) {
      this.isFull = !this.isFull
      e.preventDefault();
      e.stopPropagation();
      eventBus.$emit('screenChange')
    },
    getDialogPosition() {
      return { ...this.zIndexStyle }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__wrapper {
  pointer-events: auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.minus-dialog {
  visibility: hidden !important;
}

::v-deep .base-dialog {
  min-height: 300px;
  pointer-events: auto;
  margin: 0 0 0 0;
  margin-top: 0 !important;
  align-self: center;
  box-shadow: 0 0 4px rgba(0,0,0,.3);
  .action-icon {
    i{
      font-size: 20px;
      margin-bottom: 2px;
      color: #909399;
    }
    i:hover{
      color:#2B66FF
    }
  }

  &.resize-dialog.flex-content-dialog.knowledge-dlg {
    .el-dialog__body {
      min-height: 0 !important;
      width: 100%;
      overflow: auto;
      padding: 0 20px;
    }
  }

  &.resize-dialog.flex-content-dialog {
    .el-dialog__body {
      min-height: 59vh;
      max-height: 61vh;
      height: 60vh;
      width: 100%;
      overflow: auto;
    }
  }

  &.full-dialog {
    left: 0 !important;
    top: 0 !important;
    transform: none;

    .el-dialog__body {
      min-height: 0 !important;
      max-height: calc(100% - 48px) !important;
      height: calc(100% - 48px) !important;
      width: 100%;
    }
  }

  &.flex-content-dialog {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      min-height: 0;
      display: flex;
      flex-direction: column;

      .content-wrapper {
        flex: 1 1 auto;
        min-height: 0;
      }

      .dialog-footer {
        padding: 10px 20px;
        padding-bottom: 0;
      }
    }
  }

  .el-dialog__header {
    height: 60px;
    // border-bottom: 1px solid #ebeef5;
    box-sizing: border-box;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    padding: 20px 20px 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // background-color: #ffffff;
    // background: linear-gradient(90deg, #e5faff 0%, #f5ecff 47%, #e5efff 100%);
    flex: 0 0 auto;
    .el-dialog__headerbtn {
      top: 29px;

      .el-icon-close {
        font-size: 22px;
      }
    }

    .base-header {
      display: flex;
      flex: 1 1 auto;
      justify-content: space-between;
      align-items: center;
      padding-right: 30px;

      .title {
        margin: 0;
      }

      .full-screen-icon {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }

  .el-dialog__body {
    overflow: auto;
    flex: 1 1 auto;
    // padding-top: 0;
  }

  .dialog-footer {
    display: flex;
    flex:0 0 auto;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    padding-top: 10px;
    // border-top: 1px solid #ccc;
  }
  .dynamic-spread .flow-img-btn {
    right: 70px;
    bottom: 20px;
    padding: 0;
  }
}
</style>
