
<template>
  <div class="image-gallery">
    <el-image
      class="preview-img"
      :src="url"
      :preview-src-list="images">
      <div slot="placeholder" style="font-size: 30px;">
        <i class="el-icon-picture-outline"></i>
      </div>
    </el-image>
  </div>
</template>
<script>
export default {
  name: "AttachmentPreviewer",
  data() {
    return {
    };
  },
  props: {
    url:{
      type: String,
      default: ''
    },
    images: {
      type: Array,
      default: () => []
    }
  },
};
</script>