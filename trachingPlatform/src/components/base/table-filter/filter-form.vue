<template>
  <div>
    <div v-for="(item, index) in filterList" :key="item.id" style="margin-bottom: 12px; display: flex; align-items: center;">
      <filter-form-item :item="item" :table-head="tableHead"></filter-form-item>
      <i class="el-icon-remove-outline" style="margin-left: 8px; color: #F56C6C;" @click="delItem(index)"></i>
    </div>
    <div>
      <el-button @click="addItem()">+ 添加条件</el-button>
    </div>
  </div>
</template>

<script>
import filterFormItem from "./filter-form-item.vue"

import { FilterOp } from "./filterData"

export default {
  components: { filterFormItem },
  props: {
    tableHead: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    filterList: {
      handler (val) {
        this.$emit('change', val)
      },
      deep: true
    }
  },
  data () {
    return {
      // 已设置的筛选项
      filterList: []
    }
  },
  methods: {
    addItem () {
      this.filterList.push({
        colCode: '',
        operator: FilterOp.Equal,
        queryValue: ''
      })
    },
    delItem (index) {
      this.filterList.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="scss">

</style>