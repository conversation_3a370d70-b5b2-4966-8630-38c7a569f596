<template>
  <div style="width: 700px; padding: 50px;">
    <filter-form :table-head="list" @change="changeFilter"></filter-form>
  </div>
</template>

<script>
import FilterForm from "./filter-form.vue"

export default {
  components: { FilterForm },
  data () {
    return {
      "list": [
        {
          "id": "fd_7264518369125072896",
          "_id": "fd_7264518369125072896",
          "compType": "input",
          "ele": "el-input",
          "compName": "单行文字",
          "compIcon": "el-icon-edit",
          "viewType": "text",
          "config": true,
          "showLabel": true,
          "label": "单行文本",
          "labelWidth": 80,
          "placeholder": "请输入文本",
          "required": false,
          "maxLength": 50,
          "gutter": 15,
          "span": 24,
          "width": "100%",
          "clearable": true,
          "disabled": false,
          "readonly": false,
          "status": "normal",
          "prefix-icon": "",
          "suffix-icon": "",
          "value": "",
          "rules": [],
          "rulesType": "default",
          "prepend": "",
          "append": "",
          "cssStyle": "",
          "knowledge": "",
          "dispatch": "",
          "relationTable": "",
          "classStyle": [],
          "layout": "colItem",
          "sceneId": 1770156668699444
        },
        {
          "id": "fd_7264518373642338304",
          "_id": "fd_7264518373642338304",
          "compType": "textarea",
          "ele": "el-input",
          "compName": "多行文字",
          "compIcon": "el-icon-edit-outline",
          "viewType": "text",
          "config": true,
          "showLabel": true,
          "label": "多行文本",
          "labelWidth": 80,
          "placeholder": "请输入文本",
          "type": "textarea",
          "required": false,
          "maxlength": 50,
          "show-word-limit": false,
          "gutter": 15,
          "span": 24,
          "clearable": true,
          "disabled": false,
          "readonly": false,
          "rows": 4,
          "status": "normal",
          "value": "",
          "rules": [],
          "ruleError": "该字段不能为空",
          "classStyle": [],
          "cssStyle": "",
          "layout": "colItem"
        },
        {
          "id": "fd_7264518380273532928",
          "_id": "fd_7264518380273532928",
          "compType": "inputNumber",
          "ele": "el-inputNumber",
          "compName": "数字",
          "label": "数字",
          "compIcon": "el-icon-plus",
          "viewType": "text",
          "config": true,
          "showLabel": true,
          "gutter": 15,
          "labelWidth": 80,
          "value": 0,
          "rules": [],
          "span": 24,
          "min": 0,
          "max": 100,
          "step": 1,
          "step-strictly": false,
          "precision": 0,
          "controls-position": "default",
          "disabled": false,
          "readonly": false,
          "required": false,
          "regList": [],
          "classStyle": [],
          "cssStyle": "",
          "layout": "colItem"
        },
        {
          "id": "fd_7264518404575330304",
          "_id": "fd_7264518404575330304",
          "compType": "date",
          "ele": "el-date-picker",
          "compName": "日期时间",
          "compIcon": "el-icon-date",
          "viewType": "text",
          "config": true,
          "showLabel": true,
          "label": "日期",
          "labelWidth": 80,
          "placeholder": "请选择",
          "required": false,
          "maxLength": 50,
          "gutter": 15,
          "span": 24,
          "clearable": true,
          "disabled": false,
          "readonly": false,
          "value": "",
          "rules": [],
          "type": "date",
          "format": "yyyy-MM-dd",
          "value-format": "yyyy-MM-dd",
          "range-separator": "-",
          "start-placeholder": "开始日期",
          "end-placeholder": "结束日期",
          "classStyle": [],
          "cssStyle": "",
          "layout": "colItem"
        },
        {
          "id": "fd_7264518423671996416",
          "_id": "fd_7264518423671996416",
          "compType": "radio",
          "compName": "单选框",
          "ele": "el-radio-group",
          "compIcon": "el-icon-open",
          "viewType": "component",
          "config": true,
          "label": "单选框",
          "placeholder": "请选择",
          "maxLength": 50,
          "span": 24,
          "gutter": 15,
          "labelWidth": 80,
          "showLabel": true,
          "required": false,
          "disabled": false,
          "border": false,
          "vertical": false,
          "size": "medium",
          "optionType": "default",
          "options": [
            {
              "label": "选项一",
              "value": 1
            },
            {
              "label": "选项二",
              "value": 2
            }
          ],
          "dataType": "static",
          "action": "https://www.fastmock.site/mock/51715c0157535b99010bde55f2df33c8/formDesigner/api/options",
          "value": "",
          "rules": [],
          "ruleError": "该字段不能为空",
          "classStyle": [],
          "cssStyle": "",
          "layout": "colItem"
        },
        {
          "id": "fd_7264916684870254592",
          "_id": "fd_7264916684870254592",
          "compType": "upload",
          "ele": "el-upload",
          "compName": "附件上传",
          "compIcon": "el-icon-upload2",
          "viewType": "component",
          "config": true,
          "showLabel": true,
          "label": "附件上传",
          "labelWidth": 80,
          "gutter": 15,
          "span": 24,
          "required": false,
          "action": "http://*************:9992/api/v1/UploadFiles/PostFiles",
          "multiple": false,
          "name": "file",
          "show-file-list": true,
          "list-type": "text",
          "value": "",
          "width": 100,
          "buttonText": "请上传附件",
          "showTip": false,
          "tips": "点击按钮上传附件",
          "accept": ".pdf, .doc, .docx, .xls, .xlsx",
          "fileSize": 10,
          "headers": {
            "X-Access-Token": "token"
          },
          "classStyle": [],
          "cssStyle": "",
          "layout": "colItem"
        }
      ]
    }
  },
  methods: {
    changeFilter (data) {
      console.log(data)
    }
  }
}
</script>

<style scoped lang="scss">

</style>