// 筛选类型枚举对应
export const FilterOp = {
    Like: 1,
    Equal: 2,
    NotEqual: 3,
    IsNull: 4,
    IsNotNull: 5,
    Between: 6,
    Greater: 7,
    GreaterEqual: 8,
    Less: 9,
    LessEqual: 10
}

// 筛选类型文本对应
export const FilterOpText = {
    Like: '包含',
    Equal: '等于',
    NotEqual: '不等于',
    IsNull: '为空',
    IsNotNull: '不为空',
    Between: '范围',
    Greater: '大于',
    GreaterEqual: '大于等于',
    Less: '小于',
    LessEqual: '小于等于'
}

// 类型判断
export const getFilterType = (type) => {
    return ({
        input: 'input',
        textarea: 'input',
        inputNumber: 'number',
        upload: 'file',
        date: 'date',
        radio: 'select'
    })[type] || 'input'
}

// 控件对应条件类型值
export const TypeToOpList = {
    input: ['Equal', 'NotEqual', 'Like', 'IsNull', 'IsNotNull'],
    number: ['Equal', 'NotEqual', 'Greater', 'GreaterEqual', 'Less', 'LessEqual', 'Between', 'IsNull', 'IsNotNull'],
    file: ['IsNull', 'IsNotNull'],
    date: ['Equal', 'NotEqual', 'Greater', 'GreaterEqual', 'Less', 'LessEqual', 'Between'],
    select: ['Equal', 'NotEqual', 'IsNull', 'IsNotNull'],
    staff: ['Equal', 'NotEqual', 'Like'],
}

// 获取控件类型条件列表
export const getTypeToOpList = (type) => {
    const filterType = getFilterType(type)
    return TypeToOpList[filterType].map(key => ({
        key: FilterOp[key],
        text: FilterOpText[key]
    }))
}
