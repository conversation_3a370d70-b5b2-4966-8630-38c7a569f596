<template>
  <div v-if="item" style="display: flex;">
    <!-- 字段选择 -->
    <el-select v-model="item.colCode" style="margin-right: 10px; width: 130px;" @change="changeItem()">
      <el-option v-for="col in tableHead" :key="col.id" :value="col.id" :label="col.label"></el-option>
    </el-select>
    <!-- 条件选择 -->
    <div style="width: 115px; min-width: 115px;">
      <el-select v-model="item.operator" style="margin-right: 10px;" @change="changeOperator()">
        <el-option v-for="op in getTypeToOpList(currentHeadItem.compType)" :key="op.key" :value="op.key" :label="op.text"></el-option>
      </el-select>
    </div>
    <!-- 条件值 -->
    <div style="width: 240px;">
      <div v-if="item.operator === FilterOp.IsNull">
        <el-input :value="FilterOpText.IsNull" placeholder="请输入" style="width: 100%;" disabled></el-input>
      </div>
      <div v-else-if="item.operator === FilterOp.IsNotNull">
        <el-input :value="FilterOpText.IsNotNull" placeholder="请输入" style="width: 100%;" disabled></el-input>
      </div>
      <div v-else-if="filterType === 'input'">
        <el-input v-model="item.queryValue" placeholder="请输入" style="width: 100%;" clearable></el-input>
      </div>
      <div v-else-if="filterType === 'number'">
        <div v-if="item.operator === FilterOp.Between" style="display: flex; align-items: center;">
          <el-input-number v-model="item.queryValue"
                           placeholder="请输入"
                           controls-position="right"
                           style="width: 100%;"
                           clearable>
          </el-input-number>
          <span style="padding: 0 6px;"> - </span>
          <el-input-number v-model="item.queryValue"
                           placeholder="请输入"
                           controls-position="right"
                           style="width: 100%;"
                           clearable>
          </el-input-number>
        </div>
        <el-input-number v-else
                         v-model="item.queryValue"
                         placeholder="请输入"
                         controls-position="right"
                         style="width: 100%;"
                         clearable>
        </el-input-number>
      </div>
      <div v-else-if="filterType === 'date'">
        <el-date-picker v-if="item.operator === FilterOp.Between"
                        v-model="item.queryValue"
                        type="daterange"
                        style="width: 100%;"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
        <el-date-picker v-else v-model="item.queryValue" type="date" placeholder="选择日期" style="width: 100%;"></el-date-picker>
      </div>
      <div v-else-if="filterType === 'select'">
        <el-select v-model="item.queryValue" style="width: 100%;">
          <el-option v-for="op in currentHeadItem.options || []" :key="op.value" :value="op.value" :label="op.label"></el-option>
        </el-select>
      </div>
      <div v-else-if="filterType === 'file'"></div>
    </div>
  </div>
</template>

<script>
import { FilterOp, FilterOpText, getFilterType, getTypeToOpList } from "./filterData"

export default {
  props: {
    item: {
      type: Object,
      default: () => null
    },
    tableHead: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    currentHeadItem: function () {
      return this.tableHead.find(v => v.id === this.item.colCode) || {}
    },
    filterType: function () {
      return getFilterType(this.currentHeadItem.compType)
    }
  },
  data () {
    return {
      FilterOpText,
      FilterOp
    }
  },
  methods: {
    getTypeToOpList,
    changeItem () {
      this.item.operator = getTypeToOpList(this.currentHeadItem.compType)[0].key
      this.item.queryValue = ''
    },
    changeOperator () {
      this.item.queryValue = this.item.operator === FilterOp.Between ? [] : ''
    }
  }
}
</script>

<style scoped lang="scss">

</style>