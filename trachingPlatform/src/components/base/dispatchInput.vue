<template>
  <div class="dispatch-input">
    <el-input placeholder="" size="mini" v-model="modelVal" class="input">
    </el-input>
    <div @click="handleDispatch" v-if="isDispatch">
      <img src="../../assets/dispatch/dispatch.png" alt="调度" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FusionFrontDispatchInput',
  props: ['model', 'isDispatch', 'val', 'field', 'fieldName'],
  data() {
    return {
      modelVal: '',
    }
  },

  watch: {
    val: {
      handler(val) {
        this.modelVal = val
      },
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    handleDispatch() {
      this.$emit('dispatch', {
        field: this.field,
        fieldName: this.fieldName,
        value: this.val,
      })
    },
  },
}
</script>

<style lang="scss">
.dispatch-input {
  height: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    cursor: pointer;
    margin: 0 10px;
  }
  .el-input,
  input,
  .el-input__inner {
    height: 16px !important;
    padding: 0 !important;
    border: none !important;
    background: inherit !important;
    font-size: 14px;
    color: #333;
    &:focus-visible {
      outline: auto;
    }
  }
  .dispatch-btn {
    padding-left: 10px;
  }
}
</style>
