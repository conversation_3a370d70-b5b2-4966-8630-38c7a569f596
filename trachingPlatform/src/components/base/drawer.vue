<template>
  <el-drawer
    v-bind="$attrs"
    :size="isFull ? '100%' : width"
    destroy-on-close
    :visible.sync="dialogVisible"
    :custom-class="`${$attrs.className || ''} base-drawer `"
    :modal="$attrs.modal === true ? true : false"
    v-on="$listeners">
    <!-- 自定义title -->
    <template
      v-if="customTitle"
      v-slot:title>
      <slot name="title"></slot>
    </template>
    <!-- 默认的全屏按钮 -->
    <template
      v-else-if="hasFull"
      v-slot:title>
      <div class="base-header">
        <div class="title">{{ title }}</div>
        <i
          class="full-screen-icon el-icon-full-screen"
          @click="toggleScreen"></i>
      </div>
    </template>
    <!-- 没有title -->
    <template
      v-else
      v-slot:title>
      <span class="base-drawer-title">{{ title }}</span>
    </template>
    <slot v-if="dialogVisible"></slot>
    <slot
      v-if="$slots.footer"
      name="footer"></slot>
    <div
      v-else
      slot="footer"
      class="drawer-footer">
      <el-button
        v-if="defaultButtons[0]"
        @click="handleReset"
      >取消</el-button
      >
      <el-button
        v-if="defaultButtons[1]"
        type="primary"
        @click="handleSubmit"
      >确定</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'BaseDrawer',
  props: {
    hasFull: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '50%'
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    defaultButtons: {
      type: Array,
      default: () => {
        return [1, 1];
      }
    }
  },
  data() {
    return {
      isFull: false // 是否全屏
    };
  },
  computed: {
    dialogVisible: {
      set: function (val) {
        this.$emit('update:visible', false);
      },
      get: function () {
        return this.visible;
      }
    },
    customTitle() {
      return this.$slots.title;
    }
  },
  mounted() {
    // 初始化时否全屏
    this.isFull = !!this.fullscreen;
  },
  methods: {
    handleReset() {
      const b = this.$attrs['before-close'] || this.$attrs.beforeClose;
      if (b) {
        b();
      } else {
        this.$emit('update:visible', false);
      }
    },
    handleSubmit() {
      if (this.$listeners.submit) {
        this.$emit('submit');
      } else {
        this.$emit('update:visible', false);
      }
    },
    toggleScreen() {
      this.isFull = !this.isFull;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .base-drawer {
  &.flex-content-drawer {
    display: flex;
    flex-direction: column;
    .el-drawer__body {
      flex: 1 1 auto;
      min-height: 0;
      display: flex;
      flex-direction: column;
      .content-wrapper {
        flex: 1 1 auto;
        min-height: 0;
      }
      .drawer-footer {
        padding: 10px 20px;
        padding-bottom: 0;
      }
    }
  }
  .el-drawer__header {
    height: 48px;
    border-bottom: 1px solid #ebeef5;
    box-sizing: border-box;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(90deg, #e5faff 0%, #f5ecff 47%, #e5efff 100%);
    .el-drawer__headerbtn {
      top: 13px;
      .el-icon-close {
        font-size: 22px;
      }
    }
    .base-header {
      display: flex;
      flex: 1 1 auto;
      justify-content: space-between;
      align-items: center;
      padding-right: 10px;
      .title {
        margin: 0;
      }
      .full-screen-icon {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
  .el-drawer__body {
    padding: 10px;
  }
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #ccc;
  }
}
</style>
