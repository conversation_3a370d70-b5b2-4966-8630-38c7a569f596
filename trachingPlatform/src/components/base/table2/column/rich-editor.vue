<template>
  <div class="column-rich-editor">
    <div
      @click="showEditorDlg"
      class="rich-editor-cell"
      :title="initTxtVal">
      <span class="long-text" v-html="rowData[columnInfo.prop]"></span>
      <i class="trigger-dlg-icon el-icon-edit-outline"></i>
    </div>
    <base-dialog
      :visible.sync="dialogVisible"
      @submit="handleSave">
      <wangEditor
        :value="initTxtVal"
        @input="handleChange"
        :height="380"></wangEditor>
    </base-dialog>
  </div>
</template>

<script>
export default {
  name: "ColumnWangEditor",
  components: {
    wangEditor: () => import("@/components/base/wang-editor.vue")
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    },
    columnInfo: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    rowData: {
      handler(val) {
        if (val) {
          this.initTxtVal = val[this.columnInfo.prop];
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      txtVal: "",
      initTxtVal: ""
    };
  },

  mounted() {},

  methods: {
    handleChange(val) {
      this.txtVal = val;
    },
    handleSave() {
      this.$api
        .UpdateCustomTableData({
          TableId: this.columnInfo.componentsProps?.tableId,
          PostData: [
            {
              id: this.rowData.id,
              [this.columnInfo.prop]: this.txtVal
            }
          ]
        })
        .then(res => {
          if (res.code === 200) {
            this.columnInfo.componentsProps?.refresh();
            this.$message.success("保存成功");
            this.dialogVisible = false;
          }
        });
    },
    showEditorDlg() {
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
.column-rich-editor {
  .rich-editor-cell {
    display: flex;
    align-items: center;
    .long-text {
      @include text-ellipsis(auto);
    }
  }
}
</style>
