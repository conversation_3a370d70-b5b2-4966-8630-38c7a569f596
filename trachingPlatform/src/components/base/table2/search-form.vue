
<template>
  <el-form
    ref="searchForm"
    class="search-form"
    :model="formValue"
    :label-position="formConfig.labelPosition || 'left'"
    :label-width="formConfig.labelWidth || ''"
    inline
    @submit.native.prevent>
    <div class="search-form-container">
      <div
        v-for="item in queryList"
        :key="item.prop"
        :style="itemWidthStyle"
        class="search-form-item">
        <search-form-item
          ref="searchFormItem"
          :config="item"
          :formValue="formValue"
          @submit="onSubmit" />
      </div>
      <div
        class="search-form-more"
        :style="itemWidthStyle">
        <el-form-item>
          <div class="actions-wrapper">
            <el-button
              v-if="moreList.length > 0"
              :icon="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              class="show-more"
              type="text"
              @click="toggleMore"
              >更多</el-button
            >
            <el-button
              type="primary"
              @click="onSubmit"
              >查询</el-button
            >
            <el-button @click="onReset">重置</el-button>
            <slot name="actions" />
          </div>
        </el-form-item>
      </div>
      <div
        v-for="item in moreList"
        v-show="showMore"
        :key="item.prop"
        :style="itemWidthStyle"
        class="search-form-item">
        <search-form-item
          :config="item"
          :formValue="formValue"
          @submit="onSubmit" />
      </div>
    </div>
  </el-form>
</template>
<script>
import SearchFormItem from "./search-form-item.vue";
export default {
  name: "SearchForm",
  components: { SearchFormItem },
  props: {
    formConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formValue: {},
      isSearch: [
        "el-input",
        "el-input-number",
        "el-select",
        "el-date-picker",
        "el-checkbox",
        "el-switch",
        "relationComps-select"
      ],
      queryList: [],
      moreList: [],
      showMore: false,
      itemCount: 4,
      itemWidthStyle: {}
    };
  },
  watch: {
    formConfig: {
      handler(val) {
        if (!val && !val.formItemList?.length) {
          return;
        }
        this.resetTable()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 链接：https://juejin.cn/post/7294853623849631778
    const reo = new ResizeObserver( entries => {
      entries.forEach(entry => {
        if(entry.contentRect.width){
          let count =  Math.floor(entry.contentRect.width/ 318);
          count = count < 2?2:count
          if(count !== this.itemCount){
            this.itemCount = count;
            this.resetTable()
          }
        }
      });
    });
    reo.observe(this.$el);
  },
  methods: {
    // 构建表单
    resetTable() {
      this.queryList = [];
      this.moreList = [];
      this.formConfig.formItemList.forEach(item => {
        if(!this.isSearch.includes(item.type)){
          return true;
        }
        if (this.queryList.length < this.itemCount - 1) {
            this.queryList.push(item);
        } else {
            this.moreList.push(item);
        }
      });
      this.setDefaultValue();
    },
    // 根据value设置默认值
    setDefaultValue() {
      const formData = {};
      this.formConfig.formItemList.forEach(item => {
        const { prop, value } = item;
        formData[prop] = value;
      });
      this.formValue = formData;
    },
    toggleMore() {
      this.showMore = !this.showMore;
    },
    onSubmit() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.$emit("submit", this.formValue);
        }
      });
    },
    onReset() {
      const { reset } = this.$listeners;
      // 清除关系字段的显示
      this.$refs.searchFormItem.forEach(item => {
        item.relationLabel = "";
      });
      if (!reset) {
        this.$refs.searchForm.resetFields();
      }
      this.$emit("reset", this.formValue);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 10px;
  .search-form-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: row;
    gap: 0;
    flex-wrap: wrap;
    .search-form-item {
      flex: 0 0 auto;
      // width: 16.66%;
    }
    .search-form-more {
      // width: 16.66%;
      flex: 0 0 auto;
      order: 100;
      margin-bottom: 0;
    }
  }
  ::v-deep .actions-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .show-more {
    color: #606266;
    &:hover {
      color: #0973f1;
    }
    .el-icon-arrow-down,
    .el-icon-arrow-up {
      font-weight: 600;
      font-size: 16px;
    }
  }
  ::v-deep .form-item {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    margin-bottom: 6px;
    .el-form-item__label {
      flex: 0 0 auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
