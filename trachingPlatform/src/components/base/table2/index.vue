<template>
  <div
    ref="baseTable"
    class="base-table"
    :style="baseTableHeight"
    >
    <div class="top">
      <!-- 顶部条件和操作按钮 -->
      <!-- <search-form
        v-if="!notShowSearch && queryFormConfig?.formItemList?.length > 0"
        ref="searchForm"
        :formConfig="queryFormConfig"
        @submit="querySubmit"
        @reset="queryReset">
      </search-form> -->
      <div
        class="top-actions">
        <slot name="actions" />
      </div>
      <slot name="operateTop"></slot>
    </div>
    <!--:height="eltableHeight" table的height影响数据变化高度先去掉-->
    <el-table
      ref="table2"
      border
      :fit="true"
      :data="data"
      :stripe="stripe"
      tooltip-effect="light"
      :row-key="rowKey"
      v-bind="$attrs"
      v-on="$listeners"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      :header-cell-style="{ background: '#EFF2F5', color: '#5C6075' }" height='560px'>
      <template slot="empty">
        <slot name="empty"></slot>
      </template>
      <el-table-column
        v-if="$attrs.selectable"
        type="selection"
        width="55" />
      <template v-for="(col, ind) in cols">
        <!-- 新增：slot自定义渲染（仅当columns配置slot字段时生效，不影响原有功能） -->
        <el-table-column
          v-if="col.slot"
          :key="col.prop || ind"
          :label="col.label"
          :width="col.width"
          :min-width="col.minWidth"
          :fixed="col.fixed"
          :align="col.align"
        >
          <template v-slot="scope">
            <slot :name="col.slot" v-bind="scope"></slot>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="col.type === 'index'"
          :key="ind"
          type="index"
          width="50"
          :index="getRowIndex"
          :label="col.label" />
        <el-table-column
          v-else-if="elementUi(col.type)"
          :key="'element' + col.prop"
          :min-width="col.width || '120px'"
          v-bind="col">
          <template slot-scope="scope">
            <cell-item
              v-bind="col.innerAttrs"
              :options="col.options"
              :rowData="scope.row"></cell-item>
          </template>
        </el-table-column>  
        <el-table-column
          v-else-if="col.type === 'components'"
          :key="'components' + col.prop"
          :min-width="col.width || '120px'"
          v-bind="col">
          <template slot-scope="scope">
            <component
              :is="col.components"
              :rowData="scope.row"
              :columnInfo="col"></component>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="col.formatters"
          :key="'txt2' + col.prop"
          :min-width="col.width || '120px'"
          show-overflow-tooltip
          v-bind="col">
          <template slot-scope="scope">
            <p v-html="col.formatters({},{},scope.row[col.prop])"></p>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          :key="'txt' + col.prop"
          :min-width="col.width || '120px'"
          show-overflow-tooltip
          v-bind="col">
        </el-table-column>
      </template>
      <slot name="operate"></slot>
    </el-table>
    <el-pagination
      v-if="pagination"
      class="table2-pagination"
      :layout="paginationLayout"
      :page-sizes="[10, 50, 100, 500]"
      background
      v-bind="$attrs"
      :current-page="search.pageIndex"
      :page-size="search.pageSize || 12"
      @current-change="handleChangePageIndex"
      @size-change="handleChangePageSize"
      @prev-click="handleChangePageIndex"
      @next-click="handleChangePageIndex">
    </el-pagination>
  </div>
</template>
<script>
import eventBus from "@/utils/eventBus";
import CellItem from "./cell-item";
import SearchForm from "./search-form.vue";
import cloneDeep from "lodash/cloneDeep";
export default {
  name: "Table2",
  components: { SearchForm, CellItem },
  props: {
    id: {
      type: String,
      default: ""
    },
    notShowSearch: { // 外部控制搜索框，默认显示
      type: Boolean,
      default: false
    },
    data: {
      // 表格数据绑定
      type: Array,
      required: true,
      default: () => []
    },
    columns: {
      // 表格列配置
      type: Array,
      required: true,
      default: () => []
    },
    queryFormConfig: {
      // 查询表单配置
      type: Object,
      default: () => ({})
    },
    pagination: {
      // 是否分页
      type: Boolean,
      default: true
    },
    paginationLayout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper"
    },
    firstLoad: {
      // 是否第一次需要加载
      type: Boolean,
      default: true
    },
    rowKey: {
      type: String,
      default: "id"
    },
    height: {
      type: [Number, String],
      default: "100%"
    },
    // 绑定查询条件，监听查询条件，如果查询条件发生变化，执行查询回调
    searchParams: {
      type: Object,
      default: () => ({})
    },
    stripe: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      cols: [],
      tData: [],
      search: { },
      pageSize: 12,
      pageNum: 1,
      currentRow: null,
      loaded: false,
      eltableHeight: "100%"
    };
  },
  computed: {
    baseTableHeight() {
      let height = "";
      if (typeof this.height === "number") {
        height = `${this.height}px`;
      } else if (typeof this.height === "string" && this.height !== "") {
        height = this.height;
      } else {
        height = "100%";
      }
      return { height };
    }
  },
  watch: {
    columns: {
      handler(val) {
        val.map(v => {
          // el-table-column 属性
          const {
            type,
            prop,
            // eslint-disable-next-line no-unused-vars
            index,
            columnKey,
            label,
            width,
            minWidth,
            fixed,
            renderHeader,
            sortable,
            sortMethod,
            sortBy,
            sortOrders,
            // eslint-disable-next-line no-unused-vars
            resizable,
            formatter,
            showOverflowTooltip,
            align,
            headerAlign,
            headerClassName,
            className,
            labelClassName,
            // eslint-disable-next-line no-unused-vars
            selectable,
            reserveSelection,
            filters,
            filterPlacement,
            filterMultiple,
            filterMethod,
            filteredValue,
            ...rest
          } = v;
          v.innerAttrs = { type, prop, ...rest };
        });
        this.cols = val;
       },
      immediate: true,
      deep: true
    },
    data: {
      handler(val) {
        if (val?.length > 0) {
          this.loaded = true;
          //  当前数据为任务点，待任务结束后可查看。
          const df = [];
          val.forEach((d,index)=>{
            if(d.dataLocked===true){
              df.push(index)
            }
          })
          if(df.length>0){
            this.$nextTick(()=>{
              const indexNum = this.columns.findIndex(col=>col.type==='index')
              const tables = this.$refs.table2.$el.querySelectorAll("tbody")
              tables.forEach(table=>{
                const rows = table.querySelectorAll("tr.el-table__row")
                rows.forEach((tr,index)=>{
                  if(df.includes(index)){
                    const tds = tr.querySelectorAll("td.el-table__cell")
                    tds.forEach((td,tIndex)=>{
                      if(tIndex === indexNum){
                        return true;
                      }
                      td.innerHTML = `<div class="cell" title="当前数据为任务点，待任务结束后可查看。"><i class="el-icon-lock" /></div>`
                    })
                  }
                })
              })
            })
          }
        }else {
          // 数据是空，并且不是第一页
          if(this.search?.pageIndex>1){
            this.$confirm(`第${this.search.pageIndex}页数据为空，是否返回第1页？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.search.pageIndex = 1;
              this.$emit("handleSearch", this.search);
            }).catch(() => {
              // 取消
            });
          }
        }

        this.$nextTick(() => {
          this.$refs.table2?.doLayout(); // 数据更新后重新计算布局
        });
      },
      immediate: true,
      deep: true
    },
    searchParams: {
      handler(val) {
        this.search = cloneDeep(val);
        // 覆盖查询条件，并执行回调
        if (!this.loaded) {
          // 没有加载过
          if (this.firstLoad) {
            this.$emit("handleSearch", val);
          }
        } else {
          this.$emit("handleSearch", val);
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      const topHeight =
        this.$refs?.baseTable?.querySelector(".top")?.clientHeight || 0;
      const pageHeight =
        this.$refs?.baseTable?.querySelector(".table2-pagination")
          ?.clientHeight || 0;
      this.eltableHeight = `calc(100% - (${topHeight + pageHeight})px)`;
      this.$refs.table2?.doLayout();
      eventBus.$on("screenChange", () =>
        setTimeout(() => {
          this.$refs.table2?.doLayout();
        }, 100)
      );
      eventBus.$on("tableDoScreenChange", () =>
        setTimeout(() => {
          this.$refs.table2?.doLayout();
        }, 100)
      );
    });
  },
  destroyed() {
    eventBus.$off("screenChange");
    eventBus.$off("tableDoScreenChange");
  },
  methods: {
    // 选中回显
    toggleRowSelection(rows) {
      rows.forEach((row) => {
        this.$refs.table2.toggleRowSelection(row, true)
      })
    },
    // 更新查询参数 并执行回调
    updateSearchParams(conditions) {
      for (const key in conditions) {
        if (Object.hasOwnProperty.call(conditions, key)) {
          const element = conditions[key];
          this.search[key] = element;
        }
      }
      for (const key in this.search) {
        if (Object.hasOwnProperty.call(this.search, key)) {
          const element = this.search[key];
          if (typeof element === "undefined") {
            delete this.search[key];
          }
        }
      }
      this.$emit("handleSearch", this.search);
    },
    // 刷新
    refresh() {
      this.$emit("handleSearch", this.search);
    },
    // 查询/重置表单
    querySubmit(conditions) {
      const { pageIndex } = this.search;
      if (typeof pageIndex === "number") {
        // 如果是分页，需要重置pageIndex = 1
        this.search.pageIndex = 1;
      }
      this.updateSearchParams(conditions);
    },
    queryReset(conditions) {
      for (const key in conditions) {
        if (Object.hasOwnProperty.call(conditions, key)) {
          if (
            key.indexOf("DATETIME") > -1 ||
            key.indexOf("INT") > -1 ||
            key.indexOf("DECIMAL") > -1
          ) {
            // 对应 table-preview.vue
            // valueType === "DATETIME" ? "el-date-picker" : valueType === "INT" ? "el-input-number" : "el-input",
            // todo 后面有可能出现的扩展需要将指定的查询条件，按指定的逻辑，重置为指定的类型
            conditions[key] = [];
          } else {
            conditions[key] = "";
          }
        }
      }
      const { pageIndex } = this.search;
      if (typeof pageIndex === "number") {
        // 如果是分页，需要重置pageIndex = 1
        this.search.pageIndex = 1;
      }
      this.updateSearchParams(conditions);
    },
    // 获取条件
    getSearchParams() {
      return this.search;
    },
    elementUi(type) {
      return type && type.indexOf("el-") === 0;
    },
    handleChangePageIndex(val) {
      const pageCurrentChange = this.$listeners["page-current-change"];
      if (pageCurrentChange) {
        // 兼容 page-current-change
        this.pageCurrentChange(val);
      } else {
        this.updateSearchParams({ pageIndex: val });
      }
      this.$emit('handleChangePageIndex',val,);// 页码切换事件
    },
    handleChangePageSize(val) {
      this.updateSearchParams({ pageSize: val,pageIndex: 1 });
    },
    handleSelectionChange(selection){
      this.$emit('selectionChange',selection)
    },
    handleCurrentChange(currentRow, oldCurrentRow) {
      this.currentRow = currentRow;
      if (this.$listeners["current-change"]) {
        // 兼容 current-change
        this.$emit("current-change", currentRow, oldCurrentRow);
      } else if (this.$listeners.currentChange) {
        // 兼容 currentChange
        this.$emit("currentChange", currentRow, oldCurrentRow);
      }
    },
    getCurrentRow() {
      return this.$refs.table2.currentRow;
    },
    getRowIndex(index) {
      if (this.pagination) {
        return (this.search.pageIndex - 1) * this.search.pageSize + index + 1;
      }
      return index + 1;
    },
  }
};
</script>
<style lang="scss" scoped>

.base-table {
  padding: 0;
  width: 100%;
  height: 100%;
  flex: 1 1 auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
  .top {
    flex: 0 0 auto;
    margin: 5px 0 5px 0;
    .top-actions {
      padding: 0;
      margin: 0;
    }
    .search-input {
      width: 180px;
      margin-right: 10px;
    }
    .el-button-group {
      margin-left: 10px;
    }
  }
  .el-table {
    flex: 1 1 auto;

    // 解决固定高度时滚动区域高度变化的问题
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    ::v-deep > .el-table__body-wrapper {
      flex: 1;
    }
    ::v-deep > .el-table__header-wrapper {
      height: 44px !important;
    }
    ::v-deep > .el-table__header-wrapper th.el-table__cell > .cell,
    ::v-deep .el-table__fixed-header-wrapper th.el-table__cell > .cell {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    // ::v-deep .el-table__fixed-body-wrapper,
    ::v-deep .el-table__body-wrapper {
      overflow-y: auto;
    }
  }
  .table2-pagination {
    flex: 0 0 auto;
    padding: 10px 0;
    margin-top: 20px;
    text-align: right;
  }
}
</style>
<style lang="scss">
  .url-link{
    text-decoration: underline;
    color: var(--theme_primary_color)
  }
</style>
