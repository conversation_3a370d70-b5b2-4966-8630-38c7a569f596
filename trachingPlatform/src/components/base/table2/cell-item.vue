
<template>
  <div v-if="type==='txt'">{{ rowData[field] }}</div>
  <el-select v-else-if="type==='el-select'" v-model="rowData[field]" v-bind="$attrs" :filter-method="selectFilterMethod" v-on="$listeners">
    <el-option v-for="opt in options" :key="opt.value" :label="opt.label" :value="opt.value" />
  </el-select>
  <component :is="type" v-else v-model="rowData[field]" v-bind="$attrs">
    <!-- 其他有子项的 -->
  </component>
</template>

<script>
export default {
  name: 'CellItem',
  components: { },
  props: {
    rowData: { type: Object, default: () => ({}) },
    selectFilterMethod: { type: Function, default: () => ({}) },
    options: { type: Array, default: () => ([]) }
  },
  data() {
    return {};
  },
  computed: {
    field(){
      return this.$attrs.prop || this.$attrs.field;
    },
    type(){
      return this.$attrs.type || 'txt';
    }
  },
  methods: {}
};
</script>

<style scoped>
.el-input-number{
  width: 100%;
}
</style>
