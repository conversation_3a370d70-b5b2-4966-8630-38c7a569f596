### 表格组件 ###
基于 el-dialog的 二次封装，文档中【其它】本意代表继承element-ui的属性或者事件。封装时未使用的属性和事件，没有测试过，可能会有BUG

**Attributes**只列出需要注意的

| 参数                | 必填 | 说明                                                         | 类型          | 可选值 | 默认值 |
| ------------------- | :--- | :----------------------------------------------------------- | :------------ | :----- | :----- |
| columns             | 是   | 显示列配置                                                   | array         | —      | false  |
| queryFormConfig     | 否   | 查询表单配置                                                 | object        | —      | —      |
| pagination          | 否   | 是否分页                                                     | boolean       | —      | true   |
| firstLoad           | 否   | 初始化是否加载第一页的数据                                   | boolean       | —      | true   |
| rowKey              | 否   | 行id                                                         | string        |        | id     |
| page-current-change | 否   |                                                              | boolean       |        | false  |
| height              | 否   | 列表高度，number类型时，单位是px                             | string/number |        | 100%   |
| id                  | 否   | 列表id，用于识别是哪个实例                                   | string        | —      | —      |
| searchParams        | 否   | 必须搭配回调handleSearch使用，表格查询的参数，外部修改将会触发handleSearch | object        |        |        |
| 其他                | —    | 参考el-dialog                                                | —             | —      | —      |

columns配置

| 属性名称 | 说明                            | 可选值                                    | 类型   | 默认值 |
| -------- | ------------------------------- | ----------------------------------------- | ------ | ------ |
| prop     | 列id，取值key                   | —                                         | string | —      |
| label    | 列显示名称                      | —                                         | string | —      |
| type     | 单元格类型                      | index、selection、element类型：el-input等 | string | —      |
| options  | type='el-select'需要的选项      |                                           | array  |        |
| 其它     | 参考el-table-column、el-input等 |                                           |        |        |

queryFormConfig配置

| 属性名称      | 说明                                   | 可选值 | 类型    | 默认值 |
| ------------- | -------------------------------------- | ------ | ------- | ------ |
| formItemList  | 具体值参考el-input等                   |        | array   |        |
| labelPosition |                                        |        |         | right  |
| labelWidth    |                                        |        |         | 100px  |
| itemCount     | 一行显示2-6个 按钮组合算一个，默认六个 | 2-6    | number  | 4      |
| inline        | 无法修改                               |        | Boolean | true   |
| 其他          | 可支持其他el-form属性，未实现          |        |         |        |

**slot** 

| 插槽名称   | 说明                                                         |
| ---------- | ------------------------------------------------------------ |
| operateTop | 顶部区域，通常用来插入顶部查询区域/按钮                      |
| operate    | 行内，通常用来插入行内操作列                                 |
| actions    | 在有searchForm时，在查询按钮后追加按钮，在没有searchForm时，等同于operateTop |
| 其它       |                                                              |

示例operateTop:

```vue
<template #operateTop>
    <el-button type="text" @click="handlePlus" v-if="btnList.plus?.enable">
        新增
    </el-button>
</template>
```

示例operate

```vue
<template #operate>
    <el-table-column label="操作" fixed="right" width="160" >
        <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
            <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
            </template>
        </el-table-column>
    </template>
```

示例actions

```vue
  <template #actions>
    <el-button @click="handlePlus">新增</el-button>
  </template>
```

**Events** 

| 事件名称 | 说明                 | 回调参数 |
| -------- | -------------------- | -------- |
| page-current-change | 分页改变事件，原el-pagination 和el-table都有current-change事件，将el-pagination的事件名变更为 page-current-change | —        |
| selectFilterMethod | 单元格中的el-select filter-method方法 | object   |
| handleSearch | 查询的回调，table2必须传入属性searchParams。 | searchParams |
| 其它     | 参考el-table, el-pagination | —        |

**Methods** 

| 方法名称            | 说明                                                                                                              | 回调参数     |
| ------------------- | ----------------------------------------------------------------------------------------------------------------- | ------------ |
| searchParams | 获取查询参数 | —            |


**demo**

```vue
参考 \fusion_front\src\components\designer\page-designer\table-preview.vue
```

