import { h } from 'vue'
import isArray from "lodash/isArray";

const formatDateTime = (row, column, cellValue) => {
    return cellValue ? cellValue.substring(0, 10) : "";
};
const formatDate = (row, column, cellValue) => {
    return cellValue ? cellValue.substring(0, 10) : "";
};
const formatSelect = (row, column, cellValue) => {
    // const txt = isArray(cellValue) ? cellValue ? .map(item => item.label) ? .join(", ") : cellValue
    const txt = isArray(cellValue) ? cellValue.map(item => item.label).join(", ") : cellValue
    return txt || ""
}
const formatRelation = formatSelect
    // 附件类型
const formatJSON = (row, column, cellValue) => {
    if (cellValue) {
        let value = JSON.parse(cellValue);
        let str = value.map(v => {
            return `<a class="url-link" target="_blank" href="${v.url}">${v.fileName}${v.extension}</a>`;
        }).join('<br>'); // 使用 <br> 标签换行
        return str; // 返回 HTML 字符串
    } else {
        return cellValue
    }
}
const maps = { DATE: formatDateTime, DATETIME: formatDate, SELECT: formatSelect, RELATION: formatRelation };
const formatterFunction = (key, format, decimalPlaces) => {
    if (format === 'percent' || format === 'number') {
        // decimalPlaces = decimalPlaces ? ? 2;
        decimalPlaces = decimalPlaces ? 2 : 2;
        return (row, column, cellValue) => {
            const num = +cellValue;
            if (isNaN(num)) {
                return cellValue;
            }
            if (format === 'percent') {
                return (num * 100).toFixed(decimalPlaces) + '%';
            }
            return parseFloat(num.toFixed(decimalPlaces));
        };
    }
    return maps[key];
};
export default formatterFunction;