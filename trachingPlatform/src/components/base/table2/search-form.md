### 表头查询组件 ###
基于 el-form的 二次封装，文档中【其它】本意代表继承element-ui的属性或者事件。封装时未使用的属性和事件，没有测试过，可能会有BUG。

**Attributes**只列出需要注意的

| 参数                            | 必填 | 说明                                                         | 类型    | 可选值 | 默认值 |
| ------------------------------- | :--- | :----------------------------------------------------------- | :------ | :----- | :----- |
| formConfig                      | 是   | 显示列配置                                                   | array   | —      | false  |
| formConfig.labelPosition        | 否   | 查询表单配置                                                 | object  | —      | —      |
| formConfig.labelWidth           | 否   | 是否分页                                                     | boolean | —      | true   |
| formConfig.inline               | 否   | 目前只支持true                                               | boolean | true   | true   |
| 其他el-form                     | 否   | 未支持，待完善                                               | —       | —      | —      |
| formConfig.formItemList         | 是   | 超出四个显示【更多】按钮，待开发：支持配置超出n个显示【更多】按钮 | array   |        |        |
| formConfig.formItemList.label   | 否   | 查询条件显示的label                                          | string  |        |        |
| formConfig.formItemList.type    | 是   | element组件：el-input,el-select,el-input-number等简单组件,date,time等待完善 | string  |        |        |
| formConfig.formItemList.value   | 否   | 默认值                                                       | —       | —      | —      |
| formConfig.formItemList.options | 否   | type=el-select或者其他需要options的组件，目前只测试过el-select | array   |        |        |
| 其他                            | —    | 参考el-dialog                                                | —       | —      | —      |

**Events事件**

| 事件名称 | 说明                 | 回调参数 |
| -------- | -------------------- | -------- |
| submit | 查询按钮回调{key: value}，修改回调参数会同步修改条件框的值 | object  |
| reset | 重置按钮回调{key: value}，修改回调参数会同步修改条件框的值 | object   |
| 其它     | 参考el-table, el-pagination | —        |

**demo**

```vue
参考 
```

