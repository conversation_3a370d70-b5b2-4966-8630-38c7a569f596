
<template>
  <el-form-item
    class="form-item"
    :label="config.label"
    :title="config.label"
    :prop="prop">
    <!-- 数字范围类型 -->
    <div v-if="['INT','DECIMAL'].includes(config.dataType)" class="range-number">
      <el-input-number
        v-model="formValue[prop][0]"
        clearable
        :controls="false"
        @keyup.enter.native="submit"
      >
      </el-input-number>
      -
      <el-input-number
        v-model="formValue[prop][1]"
        clearable
        :controls="false"
        @keyup.enter.native="submit"
      >
      </el-input-number>
    </div>
    <!-- 日期时间范围类型 -->
    <div v-else-if="config.dataType === 'DATETIME'" class="range-datetime">
      <el-date-picker
        v-model="formValue[prop]"
        type="datetimerange"
        value-format="yyyy-MM-dd HH:mm:ss"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="submit"
      >
      </el-date-picker>
    </div>
    <!-- 非范围类型 下拉选 -->
    <div v-else-if="config.type == 'el-select'" class="common-input">
      <el-select 
        v-model="formValue[prop]"
        :clearable="true"
        @change="submit"
      >
        <template v-if="config.options.length > 0">
          <el-option
            v-for="opt in config.options"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value" />
        </template>
      </el-select>
    </div>
    <!-- relationComps-select -->
    <div v-else-if="config.dataType === 'RELATION'" class="common-input relation-select">
      <el-input v-model="relationLabel" placeholder="请选择" clearable readonly>
        <el-button icon="el-icon-search" @click="openRelationDialog" slot="prepend"></el-button>
        <el-button v-if="!!relationLabel" icon="el-icon-close" @click="clearRelation" slot="append"></el-button>
      </el-input>
    </div>
    <div v-else class="common-input">
      <component
        :is="config.type"
        v-model="formValue[prop]"
        :clearable="true"
        @keyup.enter.native="submit"
      >
      </component>
    </div>
    
    <base-dialog
      title="关系选择"
      :visible.sync="relationVisible"
      :fullscreen="true"
      noFooter>
      <table2
        v-loading="loading"
        ref="dataTableRef"
        :columns="columnList"
        :data="dataList"
        :pagination="true"
        :total="total"
        :searchParams="searchParamsData"
        @handleSearch="handleSearchData"
        @row-dblclick="selectRelationRow">
        <template #operateTop>
          <el-select v-model="queryKey" style="margin-right: 10px;" clearable>
            <el-option v-for="col in queryColumns" :key="col.id" :value="col.columnCode" :label="col.columnName"></el-option>
          </el-select>
          <el-input
            v-model="queryValue"
            placeholder="请输入"
            clearable
            :maxlength="40"
            class="search-input" />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="queryViewData">
            查询</el-button
          >
        </template>
      </table2>
    </base-dialog>
  </el-form-item>
</template>
<script>
import cloneDeep from "lodash/cloneDeep";
import DateUtils from "../../designer/page-designer/date-utils";
export default {
  name: "SearchFormItem",
  props: {
    config: {
      type: Object,
      required: true
    },
    formValue: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      relationLabel: "",
      // 关系选择
      columnList: [],
      dataList: [],
      total: 0,
      searchParamsData: {},
      queryKey:"",// 查看数据 - 条件key
      queryValue: "",// 查看数据 - 条件值
      queryColumns:[],// 查看数据 - 条件列
      recordCount: 0,
      relationVisible: false,
    };
  },
  computed: {
    prop() {
      return this.config.prop || this.config.field;
    }
  },
  watch:{
    formValue:{
      handler(val){
        // 将绑定值格式化为数组
        if(['DATETIME','INT','DECIMAL'].includes(this.config.dataType)){
          const value = val[this.prop]
          if(value instanceof Array){
            return
          }
          if(typeof value==='string'){
            if (this.config.dataType === 'DATETIME') {
              const arr = DateUtils.getDateArray(value);
              val[this.prop] = arr.some(v => !v) ? [] : arr;
            } else {
              val[this.prop] = JSON.parse(value)
            }
          }else{
            val[this.prop] = []
          }
        }
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    submit(){
      this.$emit('submit')
    },
    // ---------------------------------------------------------关系弹窗
    // 清除
    clearRelation(){
      this.relationLabel = "";
      this.formValue[this.prop] = null;
      this.submit()
    },
    // 打开关系选择弹窗 关系字段选择数据初始化列表
    async openRelationDialog(){
      const tableColumn = await this.$api.FindByTableId({ id: this.config.RefTableId });
      const columns = tableColumn.data.filter(v => v.addType !== 2);
      this.columnList = columns.filter(v => !["scenceDes", "SELECT", "RELATION"].includes(v.columnType)).map((v)=>{
        return {
          label:v.columnName,
          prop:v.columnCode
        }
      });
      this.searchParamsData = {
        isPaging: 1,
        tableId: this.config.RefTableId,
        queryValueList: [],
        pageIndex: 1,
        pageSize: 10,
        deleted: 0
      };
      // 动态条件
      this.queryColumns = columns.filter(v => v.addType !== 2);
      this.relationVisible = true;
    },
    
    // 查看数据-查询按钮
    queryViewData(){
      if(!this.queryKey||!this.queryValue){
        // 删除条件
        const newParams = {
          ...this.searchParamsData,
          queryValueList: []
        }
        delete newParams[this.queryKey]
        delete newParams.queryKey
        this.searchParamsData = newParams
      }else{
        // 添加条件
        const newParams = {
          ...this.searchParamsData,
          pageIndex: 1,
          queryKey:this.queryKey
        }
        newParams[this.queryKey] = this.queryValue
        this.searchParamsData = newParams
      }
    },
    // 查看数据-查询
    async handleSearchData(p) {
      const params = cloneDeep(p);
      if (params.queryKey) {
        params.queryValueList = [
          {
            colCode: params.queryKey,
            queryValue: params[params.queryKey],
            operator: 1
          }
        ];
      } else {
        params.queryValueList = [];
      }
      const { data, code, recordCount } = await this.$api.GetSqlData({
        ...params
      });
      if (code === 200) {
        this.total = recordCount;
        data.map(v => {
          v.isEdit = false;
        });
        this.dataList = data;
        this.copyDataList = cloneDeep(data);
      }
    },
    // 选择数据
    selectRelationRow(row) {
      const { id:value } = row;
      this.$set(this.formValue, this.prop, value);
      this.relationLabel = row[this.config.RefColumnCode];
      this.relationVisible = false;
      this.submit()
    }
    // ---------------------------------------------------------------关系弹窗结束
  }
};
</script>

<style lang="scss" scoped>
::v-deep .form-item {
  min-width: 220px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: nowrap;
  .el-form-item__label {
    flex: 0 0 auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.range-number{
  .el-input-number{
    width:95px;
  }
}
.range-datetime ::v-deep .el-date-editor .el-range-input{
  width: 49%;
}
</style>
