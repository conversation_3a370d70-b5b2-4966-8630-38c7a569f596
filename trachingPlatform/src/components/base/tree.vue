<template>
  <div class="base-tree">
    <slot name="header"></slot>
    <el-input class="fillter-tree-input" v-model="searchKey" :placeholder="placeholder" icon="el-icon-search"
      clearable></el-input>
    <!--treeData  filteredData-->
    <el-tree :data="treeData" v-bind="$attrs" :filter-node-method="filterNode" :props="defaultProps" node-key="id"
      :current-node-key="currentKey" :expand-on-click-node="expandTrue" :highlight-current="true" ref="treeRef"
      :show-checkbox="showCheckbox" @check-change="handleCheckChange" @node-click="handleNodeClick"
      :render-content="isDispatch ? renderContent : ''" :default-checked-keys="defaultCheckedList"
      :default-expand-all="false">
      <div class="custom-tree-node" slot-scope="{ node, data }" :title="node.label">
        <slot v-if="$slots.leafNode"></slot>
        <div class="label-name leaf-label" v-else>
          {{ node.label }}
        </div>
      </div>
    </el-tree>
  </div>
</template>

<script>
import { filterNodeAndParent } from '@/utils/tree-funcs';
export default {
  props: {
    primaryKey: {
      // 主键
      type: String,
      default: "id"
    },
    treeData: {
      type: Array,
      default: [],
      required: true
    },
    placeholder: {
      type: String,
      default: ""
    },
    labelName: {
      type: String,
      default: "label"
    },
    handleNodeClick: {
      type: Function,
      default: () => { }
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          label: "label"
        };
      }
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    handleCheckChange: {
      type: Function,
      default: () => { }
    },
    handlePreview: {
      type: Function,
      default: () => { }
    },
    expandTrue: {
      type: Boolean,
      default: true
    },
    defaultCheckedList: {
      type: Array,
      default: []
    },
    isDispatch: {//是否是调度配置
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentKey: "",
      searchKey: ""
    };
  },
  watch: {
    searchKey(val) {
      this.$refs.treeRef.filter(val);
    }
  },
  // computed:{
  //   // 基于搜索文本过滤后的树数据
  //   filteredData() {
  //     if (!this.searchKey) return this.treeData;
  //     return this.treeData.filter(item => this.filterTree(item, this.searchKey));
  //   }
  // },
  mounted() { },
  methods: {
    selectFirstNode(item) {
      this.$nextTick(() => {
        if(!item)return  // 空数据判断
        this.currentKey = item[this.primaryKey];
        this.$nextTick(() => {
          if (this.$refs.treeRef && this.currentKey) {
            this.$refs.treeRef.setCurrentKey(this.currentKey);
            this.handleNodeClick(item);
          }
        });
      });
    },
    filterNode(value, data, node) {
      return filterNodeAndParent(value, data, node, this.labelName);
    },
    renderContent(h, { node, data, store }) {
      const isLeaf = !data.children || data.children.length === 0;

      return h('span', [
        // 如果是叶子节点，显示复选框
        isLeaf && data.menuType == 2 ? h('el-checkbox', {
          props: {
            value: node.checked,
            indeterminate: node.indeterminate,
          },
          on: {
            change: () => {
              node.setChecked(!node.checked);
            }
          }
        }) : null,
        isLeaf && data.menuType == 2 ? h('span', {
          class: 'el-tree-node__label',
          on: {
            click: () => this.handlePreview(data)
          }
        }, [data.label]) :
          h('span', {
            class: 'el-tree-node__label',
          }, [data.label])
      ]);

    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";

.base-tree {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: #fdfefe;
  display: flex;
  flex-direction: column;

  .fillter-tree-input {
    padding: 10px 0;
  }

  ::v-deep .el-tree {
    min-height: 0;
    flex: 1;
    overflow: auto;

    .el-tree__empty-block::before {
      content: " ";
      background: url('../../assets/images/no-result.png') no-repeat center/cover;
      display: block;
      width: 124px;
      height: 100px;
      position: absolute;
      top: calc(50% - 120px);
      left: 60px;
    }

    .el-tree-node__content {
      height: 40px;
      line-height: 40px;
    }

    &.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
      background-color: #f3f8ff;
      color: #2b66ff;
    }

    .el-tree-node__children {
      .el-tree-node {
        >.el-tree-node__content {
          background-color: #fff;
          position: relative;

          .el-checkbox:last-of-type {
            margin-right: 10px;
          }

          &:hover {
            background-color: #f3f8ff;
            color: #2b66ff;

            &::before {
              content: "";
              position: absolute;
              width: 2px;
              height: 50px;
              background: #2b66ff;
              left: 1px;
              top: 0;
            }
          }
        }
      }
    }

    .custom-tree-node {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .leaf-label {
      @include text-ellipsis(auto);
    }
  }
}
</style>
