<template>
  <div>
    <!-- 弹窗 -->
    <el-dialog
       v-if="dialogdetailVisible"
      :visible.sync="dialogdetailVisible"
      width="1200px"
      :close-on-click-modal="false"
      :before-close="handdefaultclose"
      :modal-append-to-body="false"
      custom-class="topicContainer"
      :append-to-body="true"
      top="3vh"
      @close="handdefaultclose"
    >
     <div slot="title" class="dialog-header-title">
        <div class="s-title">{{title}}</div>
      </div>
        <div class="answer-form-setting">
          <div style="margin-bottom: 15px;">
            <el-input v-model="titleCopy" maxLength="200" placeholder="标题：请输入" :size="size"></el-input>
           </div>
          <div style="margin-bottom: 15px;">
            <editor v-model="descCopy" placeholder="内容描述，非必填项"></editor>
          </div>
          <template v-if="type == 1">
            <option-set :type="type" :config="optionConfig" :size="size"></option-set>
          </template>
          <template v-if="type == 2">
            <option-set :type="type" :config="optionConfig" :size="size" multiple></option-set>
          </template>
          <template v-if="type == 8">
            <option-set :type="type" :config="optionConfig" :size="size" :can-add="false" :can-delete="false"></option-set>
          </template>
          <template v-if="type == 3">
            <content-set :config="optionConfig" :size="size"></content-set>
          </template>
          <template v-if="type == 49">
            <entries-set :config="optionConfig"   :checkanwser="answer"></entries-set>
          </template>
           <!-- 表格 -->
          <template v-if="type == 5"> 
            <table-set :config="optionConfig" :size="size"></table-set>
          </template>
          <!-- 代码填空 -->
          <template v-if="type == 18">
            <python-set :config="optionConfig" :size="size"></python-set>
          </template>
          <!-- 代码编程  -->
          <template v-if="type == 46">
            <python-code :config="optionConfig" :size="size"></python-code>
            <!-- <python-set :config="optionConfig" :showSetting="false" :size="size"></python-set> -->
            <!-- <python-editor :config="optionConfig" :size="size"></python-editor> -->
          </template>
          <!-- 简答 -->
          <template v-if="type == 47">
            <short-answer :config="optionConfig" :size="size"></short-answer>
          </template>
          <div class="answer-hint">
            <p style="line-height: 40px;">答案解析</p>
            <editor v-model="answerDetail" placeholder="输入答案解析"></editor>
          </div>
          <div class="sortmain">
               <div class="sorttite">排序</div>
               <div class="sortinput">  <el-input v-model="sort" placeholder="请输入" type="number"  maxlength="4" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" style="width: 139px;"></el-input></div>
            </div>
      </div>
        
      <!-- 底部操作按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button class="cancelbtn" @click="handlecancelModal">取 消</el-button>
        <el-button class="confrimbtn" @click="handleSaveModal">添 加</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import eventBus from "@/utils/eventBus";
import {
  getAnswerDispatch,
  questionInitConfig,
  questionTypeLabel,
  questionTypeMenu,
  getCorrectAnswer,
} from '@/components/base/question/util.js'
import { checkEmpty } from "@/utils/utils.js"
import cloneDeep from 'lodash/cloneDeep';
export default {
  props: {
    dialogdetailVisible: {
      type: Boolean,
      default: false
    },
    dataInfo: {
      type: Object,
      default: () => {}
    },
    editIndex: {
      type: Number,
      default: -1,
    },
    answer: { // 试题对应配置项
      type: String,
      default: ''
    },
    // title: {
    //   type: String,
    //   default: ''
    // },
    desc: {
      type: String,
      default: ''
    },
    defaultData: {
      type: Object,
      default: () => {}
    },
  },
  components: {
    'editor': () => import('../editor/index.vue'),
    'option-set': () => import('../options/set.vue'), 
    'python-set': () => import('../python/set.vue'), // 代码填空
    'python-code': () => import('../python/set-code.vue'), // 编程题
    'content-set': () => import('../content/set.vue'),
    'table-set': () => import('../table/set.vue'), // 表格
    'python-editor': () => import('../python/code-editor.vue'), // 代码编程
    // 'editor': () => import('..index.vue'),
    'short-answer': () => import('../short-answer/set.vue'), //简单
    'entries-set': () => import('../entries/set.vue'),

  },
  data() {
    return {
      
      tabList:[
        { name: "单选题",  id: 1 },
        { name: "多选题",  id: 2 },
        { name: "填空题", id: 3 },
        { name: "表格题", id: 5 },
        { name: "判断题", id: 8 },
        { name: "代码填空题", id: 18 },
        { name: "代码编程题", id: 46 },
        { name: "简答题", id: 47 },
        { name: "分录题", id: 49 }
      ],
      nowIndex:0,
      title:'',
      questionTypeMenu:0,
      type:0,
      optionConfig:{},
      titleCopy:'',
      descCopy:'',
      answerDetail:'',
      sort:1, // 子题目排序
      ScoringItems:[],// 表格题的自由计分项
      checkanwser:'',
      spread:'',//spread 对象
    }
  },
  provide() {
    return {
      passToChild: this.handleDataFromChild
    };
  },
  watch: {
    dataInfo: {
      handler(val) {
        if(val){
          this.title=val.name?val.name:questionTypeLabel[val.type]
          this.initData(val)
        }
      },
      immediate: true
    },
    // defaultData:{
    //   handler (newVal, oldVal) {
    //     // console.log("defaultData--------------",newVal)
    //   },
    //   deep: true,
    //   immediate:true,
    // }
  },
  created () {
    // this.initData(this.defaultData)
  },
  methods: {
    getAnswerDispatch,
    initData (data) {
      this.titleCopy = data.title || ''
      this.descCopy = data.desc || ''
      this.answerDetail = data.answerDetail || '' // 答案解析 
      this.optionConfig = data.optionConfig || cloneDeep(questionInitConfig[data.type])
      this.answer = data.answer||''; 
      this.type= data.type||0;
      this.sort = data.sort||1;
      // this.knowledge = data.knowledge || []
      // this.skill = data.skill || []
      // this.difficulty = data.difficulty || data.difficulty === 0 ? data.difficulty : 4
      // this.cateId = data.cateId || 0
    },
    getParams () {
      return {
        id: this.dataInfo.id||0, // 题目id
        type: this.type,
        title: this.titleCopy,
        desc: this.descCopy,
        optionConfig: this.optionConfig,
        answerDetail: this.answerDetail,
        // knowledge: this.knowledge,
        // skill: this.skill,
        // difficulty: this.difficulty,
        // cateId: this.cateId,
        answer: this.answer,// 题目答案
        sort:this.sort
      }
    },
    tabClick(data,index){
     this.nowIndex=index
    },
    handdefaultclose(){
      this.$emit('handledialogVisible', false)
    },
    handlecancelModal() {
      this.$emit('handledialogVisible', false)
    },
    handleconfrimModal(){
      this.$emit('handleconfrim', false)
    },
    handleSaveModal(){
      if(this.type==49){
        eventBus.$emit('handlecollect',this.type)
      }
      const data = this.getParams()
      // 表格题
      if (this.type === questionTypeMenu.table) { // 处理spread
        data.optionConfig.spread = this.spread;
      }
      const answer = getCorrectAnswer(data.type, data.optionConfig) || ''
      console.log('单个题目答案',answer,this.optionConfig)
      data.answer = answer;
      let flag =  this.handleAnswerFormat(data)
      if(data.optionConfig.spread){ // 获取答案后删除 spread 对象
        delete data.optionConfig.spread
        // 子题目的表格 配置信息中答案为空，暂时这样处理
        data.optionConfig.positionInfo.forEach((v,i)=>{
          v.value = data.answer.itemAnswer[`item${i}`]
        })
      }
      if(flag) return  // 表单校验
      if(this.type==49 && data.optionConfig?.checkListData=="") return
      this.$emit('handleSaveModal', {...data,ScoringItems:JSON.stringify(this.ScoringItems)},this.editIndex,false)
    },
    // 处理题目答案的格式
    handleAnswerFormat(data){
      if(data.type ==49) return;
      const answer = getCorrectAnswer(data.type, data.optionConfig) || ''
      try {
        // let ScoringItems = [] ;// 表格题的自由计分项
        this.ScoringItems = [] ;// 表格题的自由计分项
        if (checkEmpty(answer)) throw '未设置答案'
        // 保存值判断
        if (checkEmpty(data.title)) throw '标题不能为空'
        if ([questionTypeMenu.radio, questionTypeMenu.checkbox, questionTypeMenu.isTrue].includes(data.type)) {
          if (data.optionConfig.options.length < 2) {
            throw '选项最少需要设置两个'
          }
          const labelArr = [] // 选项值重复判断用
          data.optionConfig.options.forEach((item) => {
            if (checkEmpty(item.label)) {
              throw '选项值不能为空'
            }
            if (labelArr.includes(item.label)) {
              throw '选项值不能重复'
            }
            labelArr.push(item.label)
          })
          // 判断答案个数是否匹配
          if (data.type === questionTypeMenu.checkbox && data.answer.length < 2) {
            throw '多选题答案最少需要设置两个'
          } else if (data.answer.length === 0) {
            throw '未设置答案'
          }
        } else if (data.type === questionTypeMenu.python) {
          if (checkEmpty(data.optionConfig.settingInputArr)) {
            throw '未设置答案'
          }
          let index = 1
          for (const key in data.answer) {
            if (checkEmpty(data.answer[key])) {
              throw `第${index}个未设置答案`
            }
            index++
          }
        } else if(data.type === questionTypeMenu.pythonCode){
          // 编程题
        }  else if (data.type === questionTypeMenu.content) {
          if (checkEmpty(data.optionConfig.settingArr)) {
            throw '未设置答案'
          }
          let index = 1
          for (const key in data.answer) {
            if (checkEmpty(data.answer[key])) {
              throw `第${index}个未设置答案`
            }
            index++
          }
        }
         else if(data.type === questionTypeMenu.table){
          if(data.answer.ScoringItems){
            this.ScoringItems = data.answer.ScoringItems;
            delete data.answer.ScoringItems
          }
        }
        if (checkEmpty(data.answerDetail) || data.answerDetail === '<p><br></p>') {
          throw '答案解析不能为空'
        }
      } catch (e) {
        this.$message.warning(e)
        return true
      }
    
    },
    handleDataFromChild(spread) {
      this.spread = spread;
      // 处理数据...
    }
  }
}
</script>

<style lang="scss">

.topicContainer {
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 1px rgba(0,0,0,0.16);
  border-radius: 10px 10px 10px 10px;
  .el-dialog__body{
    padding: 20px 30px 50px 30px;
  }
  .el-dialog__header{
    height: 50px;
    background: #F7F7F7;
    border-radius: 10px 10px 0px 0px;
    padding: 15px 15px;
  }
  .el-dialog__headerbtn .el-dialog__close{
    color:#A7A7A7;
    font-size: 20px;
  }
  .el-dialog__headerbtn{
    top: 14px;
    right: 14px;
  }
  .dialog-header-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .s-title{
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
    }
  .cancelbtn{
      width: 100px;
      height: 40px;
      background: #F6F8FA;
      border: 1px solid #DDE2E9;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #222222;
  }
  .confrimbtn{
      width: 100px;
      height: 40px;
      background: #2B66FF;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }
  .answer-form-setting{
    .answer-hint{
      margin-top:20px;
    }
   .sortmain{
    display: flex;
    align-items: center;
    padding-top: 20px;
    .sortinput{
      padding-left: 15px;
      .el-input--medium .el-input__inner{
        height: 30px;
        line-height: 30px;
      }
    }
   }
  }
}
</style>