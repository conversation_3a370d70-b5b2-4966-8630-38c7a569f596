<template>
  <div class="synthesis-preview">
    <div class="question-desc" v-if="desc" style="" v-html="desc"></div>
    <div class="synthesisArrayList" v-for="(item,index) in conchildren" :key="index">
         <div class="synthesisItem">
            <div class="question-name" >
                <div class="questionborder" :title="item.title"> <span class="questionnums">第{{index + 1}}题</span>  <span class="questionnumsb">{{ item.title }}</span></div>
                <div class="questioncontainer">
                    <div class="questionbtnmain">
                      <div class="questionanswerb" v-if="isshowAnalysis" @click="handlecheckAnswer(item)">答案解析</div>
                      <div class="questionansave" v-if="isshowBtn" @click="handlesinglequestion(item)">保存</div>
                    </div>
                  
                    <div :class="[isshowBtn?'questionsetloc':'questionset']">
                      <div class="score"  v-if="showAnswer || isshowBtn">
                        <p>分值</p>
                        <el-input :readonly="readOnly||!showAnswer" @input="(value) => changescoretotal(item, index, value)" v-model="item.score" :min="0" :max="100" type="number"   placeholder="请输入分值" ></el-input>
                      </div>
                      <!--删除题目  v-if="!readOnly"  v-if="$route.query.taskId"-->
                      <div v-if="showAnswer || !isshowBtn" class="remove action-item" @click.prevent="removeQuestion(item)">
                        <el-tooltip class="item" content="移除题目" effect="light" placement="top-start">
                          <i class="iconfont icon-shanchu3" style="color: #BBB;"></i>
                        </el-tooltip>
                      </div>
                    </div>
                  <div/>
              </div>
            </div>
            <div class="synthesismain">
              <div class="questioncontent">
                <div class="contentdesc" v-html="item.desc"></div>
                <template v-if="item.type == 1">
                  <option-preview v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
                </template>

                <template v-if="item.type == 2">
                  <option-preview v-model="newValue" :config="item.optionConfig" multiple :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
                </template>

                <template v-if="item.type == 8">
                  <option-preview v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
                </template>
                <template v-if="item.type == 18">
                  <python-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></python-preview>
                </template>
                <template v-if="item.type == 46">
                  <python-preview v-if="!isDoTask"  ref="previewCompsRef" :type="item.type" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
                  :error-info="errorInfo"></python-preview>
                  <python-editor v-else ref="previewCompsRef" :type="type" :default-code="newValue" @changeVal="newValue = $event"></python-editor>
                </template>
                <template v-if="item.type == 3">
                  <content-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
                  :error-info="errorInfo"></content-preview>
                </template>

                <template v-if="item.type == 47">
                  <short-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
                   :error-info="errorInfo"></short-preview>
                </template>

                <template v-if="item.type == 5">
                  <table-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :isAnswerHint="isAnswerHint"
                  :error-info="errorInfo"></table-preview>
                </template>

                <template v-if="item.type == 49">
                  <div class="question-title" style="padding-bottom: 15px;">
                      <div class="question-la"></div>
                      <div class="question-r" v-if="item.type == 49"> <el-button :class="[isOther?'writevocher':'writeback']" @click="handleOpvocher">{{isOther?'查看凭证':'返回'}}</el-button></div>
                    </div>
                  <entries-preview ref="previewEnteriesRef" :config="item.optionConfig"  :title="title" :answerValue="item.optionConfig.checkListData?JSON.stringify(item.optionConfig.checkListData):item.answer" :confignoVal="confignoVal"  :disabled="disabled" :showAnswer="showAnswer"
                                    :error-info="errorInfo"></entries-preview>
                </template>
             </div>
             <!--  控制答案和答案解析的容器 -->
             <div v-if="isAnswerHint">
               <div class="synthesisItem-answer">答案</div>
               <div class="synthesisItem-ansinfo"  v-html="item.type == 49?'见上图': handleAnswerDispatch(item.type,item.optionConfig)"></div>
               <div class="synthesisItem-answeranly">答案解析</div>
               <div class="synthesisItem-answeranlyop" v-html="item.answerDetail"></div>
             </div>
            </div>
        </div>

<!-- 
      <template v-if="item.type == 2">
         <div class="question-name" >{{ item.title }}</div>
        <option-preview v-model="newValue" :config="item.optionConfig" multiple :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>

      </template>
      <template v-if="item.type == 8">
         <div class="question-name" >{{ item.title }}</div>
        <option-preview v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
      </template>
      <template v-if="item.type == 18">
        <div class="question-name" >{{ item.title }}</div>
        <python-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></python-preview>
      </template>
      <template v-if="item.type == 46">
        <div class="question-name" >{{ item.title }}</div>
        <python-preview v-if="!isDoTask"  ref="previewCompsRef" :type="item.type" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></python-preview>
        <python-editor v-else ref="previewCompsRef" :type="type" :default-code="newValue" @changeVal="newValue = $event"></python-editor>
      </template>
      <template v-if="item.type == 3">
        <div class="question-name" >{{ item.title }}</div>
        <content-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></content-preview>
      </template>
      <template v-if="item.type == 47">
        <div class="question-name" >{{ item.title }}</div>
        <short-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></short-preview>
      </template>
      <template v-if="item.type == 5">
        <div class="question-name" >{{ item.title }}</div>
        <table-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :isAnswerHint="isAnswerHint"
                        :error-info="errorInfo"></table-preview>
      </template>
      <template v-if="item.type == 49">
        <div class="question-title">
          <div class="question-la">{{ item.title }}</div>
          <div class="question-r" v-if="item.type == 49"> <el-button :class="[isOther?'writevocher':'writeback']" @click="handleOpvocher">{{isOther?'查看凭证':'返回'}}</el-button></div>
        </div>
      <entries-preview ref="previewEnteriesRef" :config="item.optionConfig"  :title="title" :answerValue="item.answer" :confignoVal="confignoVal"  :disabled="disabled" :showAnswer="showAnswer"
                        :error-info="errorInfo"></entries-preview>
      </template> -->
  </div>
    
  </div>
</template>

<script>
import { getAnswerDispatch} from '@/components/base/question/util.js';
import { questionTypeMenu, questionTypeLabel } from '../util.js'
import eventBus from "@/utils/eventBus";
import cloneDeep from 'lodash/cloneDeep'
import { mapGetters } from "vuex";
  export default {
    name: 'synthesis-form-preview',
    props: {
        type: { // 试题类型
        type: String | Number,
        default: ''
      },
      config: { // 试题对应配置项
        type: Object,
        default: () => {}
      },
      confighead: { // 试题对应配置项
        type: String,
        default:''
      },
      configdata: { // 试题对应配置项
        type: String,
        default:''
      },
      title: {
        type: String,
        default: ''
      },
      desc: {
        type: String,
        default: ''
      },
      answerValue: { // 答题值
        type: Array | Object | String | Number,
        default: false
      },
      showTitle: { // 是否显示title
        type: Boolean,
        default: true
      },
      sort: { // 是否显示title
        type: Number,
        default: 0
      },
      disabled: { // 是否禁用
        type: Boolean,
        default: false
      },
      showAnswer: { // 是否显示title
        type: Boolean,
        default: false
      },
      error: { // 错误答案信息
        type: String,
        default: ''
      },
      isDoTask: { // 是否是学生作答
        type: Boolean,
        default: false
      },
      isAnswerHint:{ // 是否是答案解析
        type: Boolean,
        default: false
      },
      isshowscore:{ // 是否是显示分值
        type: Boolean,
        default: false
      },
      isshowAnalysis:{ // 是否只读
        type: Boolean,
        default: false
      },
      isshowBtn:{ // 是否是显示分值
        type: Boolean,
        default: false
      },

    },
    data () {
      return {
        questionTypeMenu: questionTypeMenu,
        questionTypeLabel: questionTypeLabel,
        errorInfo: [],
        isOther:true,
        confignoVal:'',
        conchildren:[],
        coptychildren:[],
        // disabled:true,
        newValue:'',
        form: {
          score: 1,// 默认1 分
        },
        timers:null,
        taskAnswerInfo:{},
      }
    },
    components: {
      'option-preview': () => import('../options/preview.vue'),
      'python-preview': () => import('../python/preview.vue'),
      'content-preview': () => import('../content/preview.vue'),
      'short-preview': () => import('../short-answer/preview.vue'),
      'python-editor': () => import('../python/code-editor.vue'),
      'table-preview': () => import('../table/preview.vue'),
      'entries-preview': () => import('../entries/preview.vue')
    },
  computed: {
    tasks() {
      return this.$store.getters.newHomeWorkInfo.taskInfo.details;
    },
    ...mapGetters({
      newHomeWorkInfo: ["newHomeWorkInfo"]
    })
  },
  watch: {
    showAnswer: {
      handler(val) {
        console.log("showAnswer--------------------------------",val)
      },
      immediate: true
    },
    config: {
    handler (val) {
      if(val){
        let configchildren= val.children && val.children.length !==0?val.children:[]  
        this.conchildren=configchildren
        this.coptychildren=configchildren
        console.log(configchildren,'预览数据++')
      }
    },
    immediate: true  
    },
    confighead:{
    handler(val){
      this.confignoVal=val
    },
    immediate:true
  },
  configdata:{
    handler(val){
        if(val){
        let JSONval = JSON.parse(val)
        let paramsno={
          data:{
            auditItem:JSONval.data.auditItem,
            bodyItems:JSONval.data.bodyItems
          }
        }
        let params={
          data:{
            headItem:JSONval.data.headItem,
            auditItem:JSONval.data.auditItem,
            bodyItems:JSONval.data.bodyItems
          }
        }

        let lastparams =this.confighead?paramsno:params
        this.answerValue=JSON.stringify(lastparams)
      }
    },
    immediate:true
  }
  },
  methods: {
    handlecheckAnswer(item){
      console.log(item,'点击了答案解析')
    },
    handlesinglequestion(item){
      console.log(item,'点击了保存') 
    },
    removeQuestion(data) {
        this.$confirm("确定移除该题目？")
      .then(() => {
        const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo);
        newHomeWorkInfo.taskInfo.details.forEach(detail => {
          let detailchild=detail.content.children.filter(child => child.id !== data.id);
          detail.content.children=detailchild
        });
        this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
      })
      .catch(() => { });
    },
    changescoretotal(item, index, value) {
      if (this.timers !== null) clearTimeout(this.timers)
      this.timers = setTimeout(() => {
      const newValue = Number(value); // 将输入的值转换为数字
      if (!isNaN(newValue)) { // 确保输入的是有效数字
        item.score = newValue;
      }
      this.coptychildren[index].score= newValue
      if(!value) {
        this.coptychildren[index].score=''
        return
      }
        // console.log(newValue,'当前值')
        // console.log(this.coptychildren,'当前总数据')
        const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo);
          newHomeWorkInfo.taskInfo.details.forEach((detail,index)=> {
            if(this.sort == index+1){
              detail.content.children.forEach((item,index1)=>{
                this.coptychildren.forEach((el,index2)=>{
                  if(index1 == index2){
                    item.score=el.score?el.score:0
                    }
                })  
              })
            }
          });
          // console.log(newHomeWorkInfo,'修改后数据')
      this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
      }, 300)
    },
    handleAnswerDispatch(a,b){
      return getAnswerDispatch(a,b)
    },
    handleOpvocher(){
      this.isOther=!this.isOther
      this.$nextTick(() => {
          eventBus.$emit('handlepreviewDatte')
      });
    },
    handleSpreadCompare (error) {
      if (error) {
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.pythonCode:
            this.errorInfo = JSON.parse(error).map(item => item.path.split('.')[1])
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          case questionTypeMenu.table:
            this.errorInfo = JSON.parse(error)
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          default:
            this.errorInfo = JSON.parse(error).map(item => item.value)
        }
      } else {
        this.errorInfo = []
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.table:
          case questionTypeMenu.pythonCode:
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
        }
      }
    },
    // 是否展示答案按钮
    isShowAnswer(item){
      let questionType=[0,42];
      return (!questionType.includes(item.questionType)&& this.taskAnswerInfo.answerShowType == 1||this.$route.query.isPreview==1||this.$route.query.className)&&item.questionType!=50
    },
  },
  mounted(){
    this.handleSpreadCompare(this.error)
    eventBus.$on("handlepreviewstatus", data => {
      this.isOther=true
    });


    this.taskAnswerInfo = JSON.parse(sessionStorage.getItem("taskAnswerInfo") || "{}");
  }
}
</script>

<style lang="scss">
.synthesis-preview{
  // display: grid;
  grid-gap: 15px;
  width: 100%;
  .question-title, .question-desc {
    width: 100%;
    word-break: break-all;
  }
  .question-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .question-la{
      border-left: 3px solid #2b66ff;
      padding-left: 10px;
    }
    .writevocher{
        width: 96px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
      .writeback{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
    }
    .synthesisArrayList{
      .synthesisItem{
        width: 100%;
        background: #FFFFFF;
        border: 1px solid #E0E0E0;
        margin-bottom:15px;
        .question-name{
          width: 100%;
          height: 50px;
          background: #F9F9F9;
          border-bottom:1px solid #E0E0E0;
          display: flex;
          align-items: center;
          padding-left: 20px;
          justify-content: space-between;
          .questionborder{
            width: 962px;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出的内容 */
            text-overflow: ellipsis; /* 显示省略号来代表被修剪的文本 */
            border-left: 3px solid #3886FF;
            padding-left: 12px;
            .questionnums{
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
             color: #333333;
            }
            .questionnumsb{
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 16px;
              color: #333333;
              padding-left: 2px;
            }
          }

          .questioncontainer{
            display: flex;
            align-items: center;
            .questionbtnmain{
              display: flex;
              align-items: center;
              .questionanswerb{
                width: 71px;
                height: 28px;
                line-height: 28px;
                background: #2B66FA;
                border-radius: 50px;
                color:#fff;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                cursor: pointer;
                text-align: center
              }
              .questionansave{
                width: 71px;
                height: 28px;
                text-align: center;
                line-height: 27px;
                background: #2B66FA;
                border-radius: 50px;
                color:#fff;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 10px;
              }
            }
          }
          .questionset{
           display: flex;
           align-items: center;    
           padding-right: 20px;
          .score {
            display: flex;
            align-items: center;
            p {
              width: 60px;
              font-size: 14px;
              margin-left: 10px;
              color: #333;
            }
          }

            .remove {
              cursor: pointer;
              margin: 0;
              padding: 0;
              .el-icon-error {
                color: #f00;
              }
            }

            .el-input__inner {
              width: 70px;
              height: 25px;
              // border-top: none;
              // border-left: none;
              // border-bottom: 1px solid $gray-b;
              // border-right: none;
              border-radius: 20px;
              padding-right: 0;
              padding-left: 15px;
              text-align: center;
            }
          }
          .questionsetloc{
           display: flex;
           align-items: center;    
          .score {
            display: flex;
            align-items: center;
            p {
              width: 60px;
              font-size: 14px;
              margin-left: 10px;
              color: #333;
            }
          }

      .remove {
        cursor: pointer;
        margin: 0;
        padding: 0;
        .el-icon-error {
          color: #f00;
        }
      }

      .el-input__inner {
        width: 70px;
        height: 25px;
        // border-top: none;
        // border-left: none;
        // border-bottom: 1px solid $gray-b;
        // border-right: none;
        border-radius: 20px;
        padding-right: 0;
        padding-left: 15px;
        text-align: center;
      }
          }
        }
        .synthesismain{
          width: 100%;
          height: auto;
          padding-bottom: 20px;
        .questioncontent{
          padding: 15px 20px;
          .contentdesc{
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            padding: 0px 0 18px 0;
          }
        }
        .synthesisItem-answer{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 25px 20px 20px 20px;
        }
        .synthesisItem-ansinfo{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
        .synthesisItem-answeranly{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 25px 20px 20px 20px;
        }
        .synthesisItem-answeranlyop{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
      }
      }
    }
  :deep(img) {
    max-width: 100%;
  }
}
</style>