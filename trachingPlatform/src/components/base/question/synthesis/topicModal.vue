
<template>
  <div>
    <!-- 触发按钮 -->
    <!-- 弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :before-close="handdefaultclose"
      :modal-append-to-body="false"
      custom-class="topicContainer"
      :append-to-body="true"
      @close="handdefaultclose"
    >
    <div slot="title" class="dialog-header-title">
        <div class="s-title">添加题目</div>
      </div>
      <!-- 题型按钮区域 -->
      <div class="button-container">
        <div class="tabheader-l">
          <ul class="nav">
            <li v-for="(item,index) in tabList" :key="index" class="navitem"
              @click="tabClick(item, index)">
              <div :class="[nowIndex == index?'deactive':'defaultclass' ]">{{ item.name }}</div>
            </li>
          </ul>
        </div>
      </div>
      <!-- 底部操作按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button class="cancelbtn" @click="handlecancelModal">取 消</el-button>
        <el-button class="confrimbtn" @click="handleconfrimModal">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabList:[
        { name: "单选题",  type: 1 },
        { name: "多选题",  type: 2 },
        { name: "填空题", type: 3 },
        { name: "表格题", type: 5 },
        { name: "判断题", type: 8 },
        { name: "代码填空题", type: 18 },
        { name: "代码编程题", type: 46 },
        { name: "简答题", type: 47 },
        { name: "分录题", type: 49 }
      ],
      nowIndex:0,
      selectObj:{}
    }
  },
  mounted(){
    this.selectObj=this.tabList[0]
  },
  methods: {
    tabClick(data,index){
     this.selectObj= data
     this.nowIndex=index
    },
    handdefaultclose(){
      this.$emit('updatedialogVisible', false)
    },
    handlecancelModal() {
      this.$emit('updatedialogVisible', false)
    },
    handleconfrimModal(){
      this.$emit('handleconfrim', this.selectObj)
    }
  }
}
</script>

<style lang="scss">

.topicContainer {
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 1px rgba(0,0,0,0.16);
  border-radius: 10px 10px 10px 10px;
  .el-dialog__body{
    padding: 20px 20px 50px 20px;
  }
  .el-dialog__header{
    height: 50px;
    background: #F7F7F7;
    border-radius: 10px 10px 0px 0px;
    padding: 15px 15px;
  }
  .el-dialog__headerbtn .el-dialog__close{
    color:#A7A7A7;
    font-size: 20px;
  }
  .el-dialog__headerbtn{
    top: 14px;
    right: 14px;
  }
  .dialog-header-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .s-title{
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
    }
    .cancelbtn{
      width: 100px;
      height: 40px;
      background: #F6F8FA;
      border: 1px solid #DDE2E9;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #222222;
    }
    .confrimbtn{
      width: 100px;
      height: 40px;
      background: #2B66FF;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
    }
    .tabheader-l{
        width: 100%;
        .nav {
         
            position: relative;
            padding: 0 5px 1px 5px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 20px 30px;
            .navitem {
              // width: 30%;
              cursor: pointer;
              text-align: center;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #fff;
              line-height: 30px;
              .defaultclass{
                width: 100%;
                height: 30px;
                line-height: 30px;
                border-radius: 3px;
                font-family: PingFangSC, PingFang SC;
                color: #333333;
                font-weight: 400;
                font-size: 14px;
                background: #F6F8FA;
              }
              .deactive {
                width: 100%;
                height: 30px;
                line-height: 28px;
                border-radius: 3px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;     
                color: #2B66FA;
                background: #F8FBFF;
                border: 1px solid #2B66FA;
            }
            }

        

          }
        }
}
</style>