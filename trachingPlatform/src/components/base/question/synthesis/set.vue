
<template>
  <div class="synthesis">
    <!-- 这里是页面的 HTML 结构 -->
    <div class="synthesis-main">
      <div class="synthesis-title">
          题目编辑
      </div>
      <div class="synthesis-btn">
          <el-button class="addquestion" @click="OpenquestionModal">添加题目</el-button>
      </div>
    </div>
    <div class="synthesisList">
          <div class="synthesiscontainer" v-for="(item,index) in config.children" :key="index">
            <!-- <el-tooltip class="item" effect="light" :content="item.title" placement="bottom"> -->
            <div class="container-l"  @click="HandleEidtModal(item,index)">{{ handleQuestionLabel(item.type) }}</div>
            <p class="q-title" @click="HandleEidtModal(item,index)">{{item.title}}</p>
            <!-- </el-tooltip> -->
            <el-input class="set-sort" type="number" :max="10" @input="item.sort=item.sort?item.sort.replace(/^(0+)|[^\d]+/g,''):1"  v-model="item.sort"></el-input>
            <div class="icon-r" @click="handleDeletequestion(item,index)"><i class="el-icon-delete icon-delete-ce"></i></div>
          </div>
      </div>
   <TopicModal :dialogVisible="dialogVisible" @updatedialogVisible="handleupdatedialogVisible" @handleconfrim="handleconfrim"></TopicModal>
   <TopicDetailModal :dialogdetailVisible="dialogdetailVisible"  @handledialogVisible="handledialogVisibled" @handleSaveModal="handleSaveModal"
     :default-data="defaultData"
     :dataInfo="dataInfo"
     :editIndex="editIndex"
     ></TopicDetailModal>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { questionTypeLabel } from "../util"
export default {
  name: 'synthesis',
  components: {
    TopicModal: () => import('./topicModal.vue'),
    TopicDetailModal: () => import('./topicDetaildDialog.vue')
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      message: '',
      dialogVisible:false,
      dialogdetailVisible:false,
      dataInfo:{},
      editIndex:-1,//编辑题目的下标
      title: '',
      desc: '',
      defaultData: {
        type: 18,
        cateId: 0
      },
    };
  },
  computed: {
  },
  watch: {
    config: {
      handler(val) {
        console.log(val);
      },
      deep: true
    }
  },
  methods: {
    // 题目类型
    handleQuestionLabel(type){
      return questionTypeLabel[type]
    },
    OpenquestionModal() {
      // 子题目不能超过十个
      if(this.config.children.length>9){
        this.$message({
          type: 'warning',
          message: '子题目不能超过10个'
        })
        return
      }
      this.editIndex =-1;// 重置下标
      this.dialogVisible =true
    },
    handleupdatedialogVisible(data){
      this.dialogVisible=data
    },
    handledialogVisibled(val){
      this.dialogdetailVisible=val
    },
    handleconfrim(data){
      this.dataInfo={
        ...data,
        sort: this.config.children.length+1,
      }
      this.dialogVisible=false
      this.dialogdetailVisible=true
    },
    // 单个题目增加  
    handleSaveModal(data,index,istrue){
      if(index!=-1){ //  编辑题目
        this.config.children.splice(index,1,data) // 替换更新修改的题目
      }else{ // 新增题目
        this.config.children.push(data)
      }
      this.dialogdetailVisible=istrue
    },
    HandleEidtModal(data,index){
      this.defaultData = data;
      console.log("题目编辑-----------",data)
      this.dataInfo = data;
      this.editIndex = index;
      this.dialogdetailVisible=true
    },
    handleDeletequestion(task,index){
      this.config.children.splice(index,1);
      // this.$zdDialog({
      //   width:'300px',
      //   center:true,
      //   contTitle: '删除后将无法恢复，确认删除该题目?',
      // }) .then(async () => {
      //   // if(task.id){
      //   //   await this.$api.DeleteQuestion({id:task.id})
      //   // }
        
      // }).catch(() => { });
      
    }
  }
};
</script>

<style lang="scss">
/* 这里是页面的样式 */
.synthesis{
  .synthesis-main{
    width: 100%;
    display: flex;
    align-items: center;
    padding-bottom: 5px;
    .synthesis-title{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #939393;
    }
    .synthesis-btn{
     padding-left: 15px;
     .addquestion{
      width: 99px;
      height: 30px;
      background: #F8FBFF;
      border: 1px solid #2B66FA;
      font-size: 14px;
      color: #2B66FA;
      padding: 8px 10px;
     }
    }
   
  }
  .synthesisList{
      .synthesiscontainer{
        display: flex;
        align-items: center;
        padding-left:70px;
        padding-top: 10px;
        .set-sort{
          width: 90px;
          height: 30px;
          // border: 1px solid #F6F8FA;
          .el-input__inner{
            width: 90px;
            height: 30px;
          }
        }
        .container-l{
          background: #F6F8FA;
          border-radius: 3px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          padding:7px 0;
          width: 110px;
          text-align: center;
          cursor: pointer;
          margin-right:20px;
        }
        .q-title{
          width: 400px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          margin-right:20px;
          cursor: pointer;
        }
        .icon-r{
          padding: 5px 10px 5px 10px;
          margin-left: 20px;
          background: #F8FBFF;
          border-radius: 3px;
          cursor: pointer;
          .icon-delete-ce{
            font-size: 18px;
            &:hover{
              color: #2B66FA;
            }
          }
        }
      }
    }
}
</style>
