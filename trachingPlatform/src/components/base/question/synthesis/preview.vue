<template>
  <div class="synthesis-preview">
    <div class="question-desc" v-if="desc" style="" v-html="desc"></div>
    <div class="synthesisArrayList" :id="'qs' + item.id"  v-for="(item,index) in conchildren" :key="index">
         <div class="synthesisItem">
            <div class="question-name" >
                <div class="questionborder" :title="item.title"> <span class="questionnums">第{{index + 1}}题</span>  <span class="questionnumsb">{{ item.title }}</span></div>
                <div class="questioncontainer">
                    <div class="questionbtnmain">
                      <div class="questionanswerb" v-if="item?.contentData?.showAnswer" @click="handlecheckAnswer(item)">参考答案</div>
                      <el-button  v-if="item?.contentData?.questionDetail&&item?.questionType != 42&&$route.path!='/taskDetails'"  class="operate-btn" size="mini" :disabled="!item.contentData?.isSubmit" @click="resetAnswer(item, index)">重置</el-button>
                      <div class="questionansave" v-if="isshowBtn" @click="handlesinglequestion(item,index,sort)">保存</div>
                    </div>
                    <div :class="[isshowBtn?'questionsetloc':'questionset']">
                      <div class="score"  v-if="showAnswer || isshowBtn">
                        <p>分值</p>
                        <el-input :disabled="readOnly||!showAnswer" @input="(value) => changescoretotal(item, index, value)" v-model="item.score" :min="0" :max="100" type="number"   placeholder="请输入分值" ></el-input>
                      </div>
                      <!--删除题目  v-if="!readOnly"  v-if="$route.query.taskId"-->
                      <div v-if="(showAnswer || isshowDel)&&conchildren.length>1" class="remove action-item" @click.prevent="removeQuestion(item)">
                        <el-tooltip class="item" content="移除题目" effect="light" placement="top-start">
                          <i class="iconfont icon-shanchu3" style="color: #BBB;"></i>
                        </el-tooltip>
                      </div>
                    </div>
                  <div/>
              </div>
            </div>
            <div class="synthesismain">
              <div class="questioncontent" :ref="`questionContent_${item.id}`">
              <!-- item.questionDetail &&  -->
                <template  v-if="item.type !==49">
                  <answer-form-preview
                      :ref="item.id"
                      :type="item.type"
                      :config="item?.questionDetail?item.questionDetail.optionConfig:item.optionConfig"
                      :title="item.title"
                      :desc="item.description"
                      :answer-value.sync="item.questionDetail.studentAnswer"
                      :error="item?.questionDetail?item.questionDetail.answerCompareInfo:''"
                      :isDoTask="isDoTask"
                      :disable="false"
                      :showAnswer="false"
                      :showTitle="false"
                      :sort="index"
                      :isshowscore="true"
                      :isshowAnalysis="false"
                      :isshowBtn="true"
                      >
                  </answer-form-preview>
                  <!--子题目批阅 -->
                  <!-- <div v-if="item.questionType==46||item.questionType==47"> -->
                  <div v-if="item.questionType==47||item.noAutoScore">
                      <!--手动评分 v-if="handleShowManual(item)"  -->
                      <manualScore 
                      v-if="handleShowManual(item)"
                      :score="item.score" 
                      :scored="item.contentData?.gradeDetails?item.contentData.gradeDetails[0].score:0" 
                      :teacherComment="item.contentData?.gradeDetails?item.contentData?.gradeDetails[0].teacherComment:''"
                      :reviewed="item.contentData?.gradeDetails?item.contentData?.gradeDetails[0].reviewed:false"
                      :gradeDetailId="item.contentData?.gradeDetails?item.contentData?.gradeDetails[0].id:0"/>
                    </div>
                    <!-- <bottomBar v-if="$route.query.review==1&&$route.query.isReview=='true'" /> -->
                </template>
                <template v-else>
                  <div class="question-title" style="padding-bottom: 15px;">
                      <div class="question-la"></div>
                      <div class="question-r" v-if="item.type == 49"> <el-button :class="[item.isOther?'writevocher':'writeback']" @click="handleOpvocher(index)">{{item.isOther?'查看凭证':'返回'}}</el-button></div>
                    </div>
                    <!-- :answerValue="item.optionConfig?.checkListData ? JSON.stringify(item.optionConfig.checkListData):JSON.stringify(item.answer) !=='{}' ? item.answer : item.questionDetail?.studentAnswer" -->
                  <entries-preview ref="previewEnteriesRef" 
                  :config="item?.questionDetail?item.questionDetail.optionConfig:item.optionConfig"  
                  :title="title"
                  :answerValue="item.questionDetail.optionConfig?.checkListData ? JSON.stringify(item.questionDetail.optionConfig.checkListData):JSON.stringify(item.answer) !=='{}' ? item.answer : item.questionDetail?.studentAnswer"
                  :answerResultv="item?.questionDetail?.vocherResult"
                  :confignoVal="confignoVal"  :disabled="disabled" :showAnswer="showAnswer"
                    :error-info="errorInfo"
                    :vocherIndex="index"
                    :entriesIndex="entriesIndex"
                    :sort="sort"
                    
                    ></entries-preview>
                </template>
              </div>
              <!--  控制答案和答案解析的容器 -->
              <div v-if="isAnswerHint">
                <div class="synthesisItem-answer">答案</div>
                <div class="synthesisItem-ansinfo"  v-html="item.type == 49?'见上图': handleAnswerDispatch(item.type,item?.optionConfig)"></div>
                <div class="synthesisItem-answeranly">答案解析</div>
                <div class="synthesisItem-answeranlyop" v-html="item.answerDetail"></div>
              </div>
            </div>
     </div>

<!-- 
      <template v-if="item.type == 2">
         <div class="question-name" >{{ item.title }}</div>
        <option-preview v-model="newValue" :config="item.optionConfig" multiple :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>

      </template>
      <template v-if="item.type == 8">
         <div class="question-name" >{{ item.title }}</div>
        <option-preview v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></option-preview>
      </template>
      <template v-if="item.type == 18">
        <div class="question-name" >{{ item.title }}</div>
        <python-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :error-info="errorInfo"></python-preview>
      </template>
      <template v-if="item.type == 46">
        <div class="question-name" >{{ item.title }}</div>
        <python-preview v-if="!isDoTask"  ref="previewCompsRef" :type="item.type" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></python-preview>
        <python-editor v-else ref="previewCompsRef" :type="type" :default-code="newValue" @changeVal="newValue = $event"></python-editor>
      </template>
      <template v-if="item.type == 3">
        <div class="question-name" >{{ item.title }}</div>
        <content-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></content-preview>
      </template>
      <template v-if="item.type == 47">
        <div class="question-name" >{{ item.title }}</div>
        <short-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer"
        :error-info="errorInfo"></short-preview>
      </template>
      <template v-if="item.type == 5">
        <div class="question-name" >{{ item.title }}</div>
        <table-preview ref="previewCompsRef" v-model="newValue" :config="item.optionConfig" :disabled="disabled" :showAnswer="showAnswer" :isAnswerHint="isAnswerHint"
                        :error-info="errorInfo"></table-preview>
      </template>
      <template v-if="item.type == 49">
        <div class="question-title">
          <div class="question-la">{{ item.title }}</div>
          <div class="question-r" v-if="item.type == 49"> <el-button :class="[isOther?'writevocher':'writeback']" @click="handleOpvocher">{{isOther?'查看凭证':'返回'}}</el-button></div>
        </div>
      <entries-preview ref="previewEnteriesRef" :config="item.optionConfig"  :title="title" :answerValue="item.answer" :confignoVal="confignoVal"  :disabled="disabled" :showAnswer="showAnswer"
                        :error-info="errorInfo"></entries-preview>
      </template> -->
  </div>
    
  <base-dialog
      v-if="previewDialogVisible"
      :visible.sync="previewDialogVisible"
      :hasFull="true"
      v-dialogDrag
      :close-on-click-modal="false"
      :fullscreen="false"
      width="1200px"
      className="answer-hint-content-dialog resize-dialog flex-content-dialog"
      title="查看答案"
      :before-close="handleClose">
      <div>
        <answerHint
          v-if="currentQuestion?.type !== 42 && currentQuestion?.type !== 49"
          :questionInfo="currentQuestion"></answerHint>
        <answervoucher
         v-else
          :editanwser="currentQuestion"></answervoucher>
      </div>
      <div slot="footer"></div>
    </base-dialog>
  </div>
</template>

<script>
import { getAnswerDispatch, questionTypeMenu, questionTypeLabel, getInitAnswerValue, getCorrectAnswer} from '@/components/base/question/util.js';
// import { questionTypeMenu, questionTypeLabel } from '../util.js'
import {initApiQuestion} from "@/components/question/utils";
import eventBus from "@/utils/eventBus";
import cloneDeep from 'lodash/cloneDeep'
import { mapGetters } from "vuex";
  export default {
    name: 'synthesis-form-preview',
    props: {
        type: { // 试题类型
        type: String | Number,
        default: ''
      },
      config: { // 试题对应配置项
        type: Object,
        default: () => {}
      },
      confighead: { // 试题对应配置项
        type: String,
        default:''
      },
      configdata: { // 试题对应配置项
        type: String,
        default:''
      },
      title: {
        type: String,
        default: ''
      },
      desc: {
        type: String,
        default: ''
      },
      answerValue: { // 答题值
        type: Array | Object | String | Number,
        default: false
      },
      showTitle: { // 是否显示title
        type: Boolean,
        default: true
      },
      sort: { // 是否显示title
        type: Number,
        default: 0
      },
      disabled: { // 是否禁用
        type: Boolean,
        default: false
      },
      readOnly: { // 是否禁用
        type: Boolean,
        default: false
      },
      showAnswer: { // 是否显示title
        type: Boolean,
        default: false
      },
      error: { // 错误答案信息
        type: String,
        default: ''
      },
      isDoTask: { // 是否是学生作答
        type: Boolean,
        default: false
      },
      isAnswerHint:{ // 是否是答案解析
        type: Boolean,
        default: false
      },
      isshowscore:{ // 是否是显示分值
        type: Boolean,
        default: false
      },
      isshowAnalysis:{ // 是否显示答案解析
        type: Boolean,
        default: false
      },
      isshowDel:{ // 是否只读
        type: Boolean,
        default: false
      },
      isshowBtn:{ // 是否是显示分值
        type: Boolean,
        default: false
      },
      gradeId:{ // 作答记录Id
        type: Number,
        default: 0
      },
      publishTask:{ // 是否是任务发布
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        questionTypeMenu: questionTypeMenu,
        questionTypeLabel: questionTypeLabel,
        errorInfo: [],
        isOther:true,
        confignoVal:'',
        conchildren:[],
        coptychildren:[],
        // disabled:true,
        newValue:'',
        form: {
          score: 1,// 默认1 分
        },
        timers:null,
        taskAnswerInfo:{},
        spread:null, // spread 表格题
        subAnswer:'',
        entriesIndex:0,
        taskStatus:1,// 任务状态
        previewDialogVisible:false,
        currentQuestion:{},
        currentAnswer:'',
        currenttype:0
      }
    },
    components: {
      'option-preview': () => import('../options/preview.vue'),
      'python-preview': () => import('../python/preview.vue'),
      'content-preview': () => import('../content/preview.vue'),
      'short-preview': () => import('../short-answer/preview.vue'),
      'python-editor': () => import('../python/code-editor.vue'),
      'table-preview': () => import('../table/preview.vue'),
      'entries-preview': () => import('../entries/preview.vue'),
      'baseDialog' :() => import("@/components/base/dialog.vue"),
      answerFormPreview: () => import('@/components/base/question/answer-form-preview.vue'), // 题库部分题型
      'answervoucher' : () => import("@/views/student/tasks/components/vochersort/voucher.vue"),
      'answerSynthesis' : () => import("@/views/student/tasks/components/answerHint.vue"),
      'manualScore': () => import('@/components/base/question/manual-score/index.vue'),// 批阅组件
      'bottomBar': () => import('@/components/base/question/manual-score/bottom-bar.vue'),// 批阅得分
      answerHint : () => import("@/views/student/tasks/components/answerHint.vue"),//答案解析
    },
  computed: {
    tasks() {
      return this.$store.getters.newHomeWorkInfo.taskInfo.details;
    },
    ...mapGetters({
      newHomeWorkInfo: ["newHomeWorkInfo"],
      voucherWorkInfo: ["voucherWorkInfo"]
    })
  },
  watch: {
    // showAnswer: {
    //   handler(val) {
    //     // console.log("showAnswer--------------------------------",val)
    //   },
    //   immediate: true
    // },
    config: {
      handler (val) {
        if(val){
          //  console.log("config--------------------------------",val)
         let configchildren= val.children && val.children.length !==0?val.children:[]  
          // console.log("子题目预览",configchildren)
          this.conchildren=configchildren.map(v=>{
            // 解决单个题目重复发布的造成相互影响的问题 --主要针对分录
            if (!v.hasOwnProperty('isOther')) { 
              this.$set(v, 'isOther', true)
            }
            const data = {
              isSubmit: v.isSubmit, // 题目
              ...v,
              // contentData: contentData,
              // type: contentData.type
              type: v.type
            };
            if(v.contentData){
              let contentData = JSON.parse(v.contentData);
              data.contentData = contentData;
              data.type = contentData.questionType;
              if(this.isDoTask){ //  任务作答
                data.answer = getInitAnswerValue(data.questionType)
              }
              if(data.questionType==46&&!data.contentData.gradeDetails){
                // data.optionConfig.html = "";
                data.questionDetail.optionConfig.html = "";
              }
              // const data = {
              //   isSubmit: v.isSubmit, // 题目
              //   ...v,
              //   contentData: contentData,
              //   // type: contentData.type
              //   type: v.questionType
              // };
              // data.questionDetail = data.questionDetail?data.questionDetail:initApiQuestion(contentData.content)
              // const initAns = getInitAnswerValue(data.questionType)
              // if (data.gradeDetails && data.gradeDetails[0] && data.gradeDetails[0].answer) { // 学生作答记录
              //   // data.questionType==5  表格题
              //   let ans = null
              //   if(data.questionType==5){
              //     ans = JSON.parse(data.gradeDetails[0].answer)
              //   }else if(data.questionType==46||data.questionType==47){ // 变成题或简答题
              //     ans = JSON.parse(data.gradeDetails[0].answer)
              //   }else{
              //     ans = JSON.parse(data.gradeDetails[0].answer).longArray
              //   }
              //   data.questionDetail.studentAnswer = typeof initAns === 'string' ? ans[0] : ans
              //   // 判断是否显示答案对比
              //   data.questionDetail.answerCompareInfo = data.showError?data.gradeDetails[0].answerCompareInfo:null
              // } else {
              //   data.questionDetail.studentAnswer = initAns
              // }
              // console.log("构建后的题目信息------",data)
              return data;
            }else{
              // 处理子题目预览初始化答案格式  分录单独处理
              if(data.type!=49){
                // data.answer = getInitAnswerValue(data.questionType)
                if(this.publishTask){
                  data.questionDetail.studentAnswer = data.questionDetail.answer
                }else{
                  data.questionDetail.studentAnswer = getInitAnswerValue(data.questionType)
                }
              }
              // if(!this.gradeId&&data.type!=49){ // 预览
              //   // const initAns = getInitAnswerValue(data.questionType)
              //   // let ans = null
              //   // if(data.questionType==5){
              //   //   ans = data.questionDetail.answer
              //   // }else if(data.questionType==46||data.questionType==47){ // 变成题或简答题
              //   //   ans =data.questionDetail.answer
              //   // }else{
              //   //   ans = data.questionDetail.answer.longArray
              //   // }
              //   data.questionDetail.studentAnswer = this.handleSubQuestionAnswer(data)
              // }
              // console.log("data--------",data)
              return data;
            }
          })
          console.log(this.conchildren,'预览数据++')
          this.coptychildren=configchildren

          // let voucherWorkInfo= this.$store.getters.voucherWorkInfo
          // voucherWorkInfo.vocherInfo.details=this.conchildren
          // this.$store.commit("voucherWorkInfo", voucherWorkInfo);
        }
      },
      immediate: true  
    },
    confighead:{
      handler(val){
        this.confignoVal=val
      },
      immediate:true
    },
    configdata:{
      handler(val){
          if(val){
          let JSONval = JSON.parse(val)
          let paramsno={
            data:{
              auditItem:JSONval.data.auditItem,
              bodyItems:JSONval.data.bodyItems
            }
          }
          let params={
            data:{
              headItem:JSONval.data.headItem,
              auditItem:JSONval.data.auditItem,
              bodyItems:JSONval.data.bodyItems
            }
          }

          let lastparams =this.confighead?paramsno:params
          this.answerValue=JSON.stringify(lastparams)
        }
      },
      immediate:true
    }
  },
  mounted(){
    this.handleSpreadCompare(this.error)
    eventBus.$on("handlepreviewstatus", data => {
      this.isOther=true
    });


    this.taskAnswerInfo = JSON.parse(sessionStorage.getItem("taskAnswerInfo") || "{}");

    // 在组件挂载后调用设置高度的方法
    this.$nextTick(() => {
      setTimeout(() => {
        this.setQuestionContentHeight();
      },800);
    });
  },
  // updated() {
  //   // 在组件更新后调用设置高度的方法
  //   this.$nextTick(() => {
  //     this.setQuestionContentHeight();
  //   });
  // },
  methods: {
   async handlecheckAnswer(item){
      this.currenttype=item.type
      let { data, code } = await this.$api.GetQuestionAnswer({
        taskId: item.taskId||this.$route.query.id,
        questionId: item.id,
        gradeDetailId: item.gradeDetails && item.gradeDetails[0] ? item.gradeDetails[0].id : 0
      });
      if (code == 200) {
        this.previewDialogVisible = true
        let contentData = JSON.parse(data.contentData);
        // 深拷贝 题目信息
        this.currentQuestion = cloneDeep({
          ...item,
          ...data,
          contentData: {
            ...contentData,
            content:{
              ...item,
              ...data,
            },
          },
          type: item.type
        });
        console.log(this.currentQuestion,'currentQuestion')
    }
  },
  async handlesinglequestion(qsInfo,index,sort){
      if(qsInfo.type == 49){
        eventBus.$emit('handlequestionData',index,sort)
        let voucherInfo=this.voucherWorkInfo.vocherInfo.details
       // console.log(voucherInfo,'voucherInfoOPOPOP')
        voucherInfo.forEach((item,i)=>{
         if(i==sort){   
          if(item.children && item.children.length>0){
            item.children.forEach((item2,i2)=>{
              if(i2==index){
                this.subAnswer=item2.subAnswer
              }
            })
          }
          }
        })
      }
      if (!qsInfo.contentData && qsInfo.type !== 49) return this.$message.error("请展开题目详情后作答");
      const answer = this.getQuestionAnswer(qsInfo);
      // console.log("题目答案-------------",answer)
      // 简答题 为空则不让提交 
      if(qsInfo.questionType == '47'){
        if(answer=='{"answer":"<p><br></p>"}'||answer=='{"answer":""}'){
          return this.$message({ type: "warning", message: "请填写答案后提交" });
        }
      }
      if (!answer) return this.$message({ type: "warning", message: "请填写答案后提交" });

      const params = {
        id: qsInfo.gradeDetails ? qsInfo?.gradeDetails[0]?.id : qsInfo.gradeDetailsId || 0,
        gradeId: this.gradeId||qsInfo.gradeId,
        questionsId: qsInfo.id,
        questionType: qsInfo.questionType,
        answer:qsInfo.type == 49?this.subAnswer:answer,
        noAutoScore: qsInfo.questionType==47?1:0,//是否自动判分
        isReadonly: qsInfo.isReadonly||0
      };
      console.log("题目提交的参数",params)
      const { data, code,msg } = await this.$api.GetSaveStudentAnswerData(params);
      if (code === 200) {
        this.$message({
          type: "success",
          message: "提交成功！"
        });
        // 保存成功后，更新作答记录id
        // console.log("子题目信息------",qsInfo)
    
        
        // 获取作答后的题目信息
        setTimeout(()=>{

          this.openQuestionDetail(qsInfo, index)
          qsInfo.contentData.isSubmit = true ;// 提交状态
          eventBus.$emit('changeSubQuestionStatus',{
            parentId:qsInfo.parentId,
            isSubmit:true,
            index:index,
            answerScore:data.score
          })
          // 作答记录手动更新
          qsInfo.contentData.gradeDetails = [{
            ...data,
            answer:params.answer,
          }];
          // 得分显示
          this.handleQuestionScore(qsInfo.showScore, data, qsInfo.isReadonly,qsInfo.questionType)
          qsInfo.questionDetail.answerCompareInfo = data.answerCompareInfo;
          qsInfo.questionDetail.studentAnswer = this.handleSubQuestionAnswer({gradeDetails:[{answer:params.answer}],questionType:qsInfo.questionType})

          this.$set(this.conchildren,index,qsInfo)
        },500);
      }
    },
    // 作答重置
    resetAnswer(qsInfo,index){
      this.$confirm('重置后，作答记录将被清空，确认重置？').then(_=>{
        let gradeDetailId = qsInfo.contentData.gradeDetails[0].id;//当前子题目作答记录id
        this.$api.ResettingAnswer({gradeDetailId,}).then(async res=>{
          if(res.code==200){
            this.$message({type:'success',message:'重置成功'});
            // 获取单个题目信息
            // qsInfo.questionDetail.studentAnswer = getInitAnswerValue(qsInfo.questionType);
            qsInfo.questionDetail.studentAnswer = getInitAnswerValue(qsInfo.questionType);
            qsInfo.questionDetail.answerCompareInfo = '';// 错误信息重置
            qsInfo.contentData.gradeDetails = null;
            qsInfo.contentData.isSubmit = false;
            eventBus.$emit('changeSubQuestionStatus',{
              parentId:qsInfo.parentId,
              isSubmit:false,
              index:index,
              answerScore: 0,
              gradeDetails:null,
            })
            this.$set(this.conchildren,index,qsInfo)
            
            await this.openQuestionDetail(qsInfo, index);
            // 重置更新单个题目的分数
            // this.questionList = this.resetSingleQuestion(this.questionList, index);
            // 需要重置答题情况
            // qsInfo.gradeDetails= null; //  直接重置作答记录字段
            if (qsInfo.questionDetail.optionConfig) {
              // 填空题需要清空缓存数据
              // if (qsInfo.questionDetail.optionConfig.settingArr) {
              //   const ansIds = qsInfo.questionDetail.optionConfig.settingArr.map(op => op.answerId)
              //   const courseContents = sessionStorage.getItem('courseContents')
              //   let settingArr = courseContents ? JSON.parse(courseContents) : []
              //   settingArr.forEach((sa) => {
              //     if (ansIds.includes(sa.answerId)) {
              //       sa.answerCont = ''
              //     }
              //   })
              //   sessionStorage.setItem('courseContents', JSON.stringify(settingArr))
              // }
              // 表格题或代码编程题内容重置
              // if(qsInfo.questionDetail.type==46||qsInfo.questionDetail.type==47){
              //   qsInfo.questionDetail.studentAnswer.answer = '';
              // }

              // this.$nextTick(() => {
              //   this.$refs[qsInfo.id][0].handleSpreadCompare('');
              // })
            }
          }
        })
      }).catch((err) => {
        console.log('err',err);
        
      });
    },
    // 处理题目得分显示
    handleQuestionScore(showScore, data, isReadonly,questionType) {
      // 编程题和简答题 不提示得分
      if(questionType==questionTypeMenu.pythonCode||questionType==questionTypeMenu.shortAnswer)return 
      if (showScore && isReadonly != 1) {
        // 只读题不显示得分 显示得分
        this.$notify({
          title: "提示",
          message: `当前题目总分：${data.topicScore}，得分：${data.score}`,
          type: data.answerResult == 4 ? "warning" : "success"
        });
      }
    },
    // 获取题目的答案
    getQuestionAnswer(qsInfo) {
      // 题库
      if (questionTypeLabel[qsInfo.questionType]) {
        let answer = null;
        // 填空题特殊处理
        if (qsInfo.questionType === questionTypeMenu.content) {
          const courseContents = sessionStorage.getItem('courseContents')
          answer = getCorrectAnswer(questionTypeMenu.content, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: courseContents ? JSON.parse(courseContents) : []
          })
        }else if(qsInfo.questionType === questionTypeMenu.python){
          const courseContents = sessionStorage.getItem('courseContents')
          answer = getCorrectAnswer(questionTypeMenu.python, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: courseContents ? JSON.parse(courseContents) : []
          })
        }
        else if(qsInfo.questionType === questionTypeMenu.table){ // 表格题
          // 区分富文本表格题 和spread 表格题
          const courseContents = sessionStorage.getItem('courseContents')
          // let contentData = JSON.parse(qsInfo.contentData.content.contentData)
          let contentData = qsInfo.contentData

          if(contentData.tableType==1){ //spread 表格题
            qsInfo.questionDetail.optionConfig.spread = this.spread;
            answer = getCorrectAnswer(questionTypeMenu.table, {
              ...qsInfo.questionDetail.optionConfig,
              settingArr: courseContents ? JSON.parse(courseContents) : []
            })
          }else{ // 富文本表格题
            qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.questionTablePreview.innerHTML
            answer = getCorrectAnswer(questionTypeMenu.table, {
              ...qsInfo.questionDetail.optionConfig,
              settingArr: courseContents ? JSON.parse(courseContents) : []
            })
          }
          return JSON.stringify(answer) // 表格题答案
        }else if(qsInfo.questionType === questionTypeMenu.pythonCode){ // 编程题
          qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.monaco.getVal();
          answer = getCorrectAnswer(questionTypeMenu.pythonCode, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: []
          })
          return JSON.stringify(answer)
        }else if(qsInfo.questionType === questionTypeMenu.shortAnswer){// 简答题
          qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.shortEditor.html;
          answer = getCorrectAnswer(questionTypeMenu.shortAnswer, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: []
          })
          return JSON.stringify(answer)
        } else{
          answer = qsInfo.questionDetail.studentAnswer
        }
        
        return JSON.stringify({
          longArray: typeof getInitAnswerValue(qsInfo.questionType) === 'object' ? answer : [answer]
        })
      }
      let questionId = qsInfo.id;
      console.log("this.$refs[questionId][0]---",this.$refs[questionId][0])
      const data = this.$refs[questionId][0].getAnswer();
      if (qsInfo.isReadonly == 1) {
        return data;
      } else {
        return JSON.stringify(data);
      }
    },
    // 获取 spread 对象
    handleDataFromSpread(spread){
      this.spread = spread;
    },
    removeQuestion(data) {
        this.$confirm("确定移除该题目？")
      .then(() => {
        const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo);
        newHomeWorkInfo.taskInfo.details.forEach(detail => {
          let detailchild=detail.content.children.filter(child => child.id !== data.id);
          detail.content.children=detailchild
        });
        this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
      })
      .catch(() => { });
    },
    changescoretotal(item, index, value) {
      if (this.timers !== null) clearTimeout(this.timers)
      this.timers = setTimeout(() => {
      const newValue = Number(value); // 将输入的值转换为数字
      if (!isNaN(newValue)) { // 确保输入的是有效数字
        item.score = newValue;
      }
      this.coptychildren[index].score= newValue
      if(!value) {
        this.coptychildren[index].score=''
        return
      }
        // console.log(newValue,'当前值')
        // console.log(this.coptychildren,'当前总数据')
        const newHomeWorkInfo = cloneDeep(this.$store.getters.newHomeWorkInfo);
          newHomeWorkInfo.taskInfo.details.forEach((detail,index)=> {
            if(this.sort == index+1){
              detail.content.children.forEach((item,index1)=>{
                this.coptychildren.forEach((el,index2)=>{
                  if(index1 == index2){
                    item.score=el.score?el.score:0
                    }
                })  
              })
            }
          });
          // console.log(newHomeWorkInfo,'修改后数据')
      this.$store.commit("newHomeWorkInfo", newHomeWorkInfo);
      }, 300)
    },
    handleAnswerDispatch(a,b){
      return getAnswerDispatch(a,b)
    },
    handleOpvocher(index){
      this.entriesIndex=index
      this.$nextTick(() => {
          eventBus.$emit('handlepreviewDatte')
      });
      const newValue = !this.conchildren[index].isOther
      this.$set(this.conchildren[index], 'isOther', newValue)
      //console.log(this.conchildren)
    },
    handleSpreadCompare (error) {
      if (error) {
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.pythonCode:
            this.errorInfo = JSON.parse(error).map(item => item.path.split('.')[1])
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          case questionTypeMenu.table:
            this.errorInfo = JSON.parse(error)
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
          default:
            this.errorInfo = JSON.parse(error).map(item => item.value)
        }
      } else {
        this.errorInfo = []
        switch (this.type) {
          case questionTypeMenu.content:
          case questionTypeMenu.python:
          case questionTypeMenu.table:
          case questionTypeMenu.pythonCode:
            if (this.$refs.previewCompsRef) {
              this.$nextTick(() => {
                this.$refs.previewCompsRef.init()
              })
            }
            break
        }
      }
    },
    // 是否展示答案按钮
    isShowAnswer(item){
      let questionType=[0,42];
      return (!questionType.includes(item.questionType)&& this.taskAnswerInfo.answerShowType == 1||this.$route.query.isPreview==1||this.$route.query.className)&&item.questionType!=50
    },
    // 处理 是否展示教师批语
    handleShowManual(item){
      if(this.$route.query.review==1||(item.contentData&&item.contentData.gradeDetails&&item.contentData.gradeDetails[0]&&item.contentData.gradeDetails[0].reviewed)){
        return true
      }
      return false
    },
    // 更新单个题目信息
    async openQuestionDetail(qsInfo, index) {
      //console.log("更新单个题目信息---",qsInfo)
      if(qsInfo.questionType==42)return;
      const params = {
        taskId: qsInfo.contentData.taskId,
        questionId: qsInfo.contentData.id,
        gradeId: this.gradeId
      };
      const res = await this.$api.GetAccountTaskData(params);
      // console.log("题目作答记录---",res.data.gradeDetails)
      qsInfo.contentData.gradeDetails = res.data.gradeDetails;
    
      if(qsInfo.questionType==49){
        if(res.data.gradeDetails && res.data.gradeDetails.length !==0){
          qsInfo.contentData.isSubmit=true
        }else{
          qsInfo.contentData.isSubmit=false
        }
        qsInfo.questionDetail.vocherResult =  res.data.gradeDetails && res.data.gradeDetails.length !==0?res.data.gradeDetails[0]:{};
        qsInfo.questionDetail.vocherResult.showError=qsInfo.contentData.showError
        qsInfo.questionDetail.studentAnswer=res.data.gradeDetails &&res.data.gradeDetails.length !==0?res.data.gradeDetails[0].answer:''
        qsInfo.isSubmit = true;
      }
      // console.log("题目作答记录---",qsInfo)
      this.$set(this.conchildren, index, qsInfo);
    },
    // 作答后的子题目结构
    handleSubQuestionAnswer(data){
      const initAns = getInitAnswerValue(data.questionType)
      if(!data.gradeDetails) return initAns;
      // console.log("handleSubQuestionAnswer------",data)
      let ans = null
      if(data.questionType==5){
        ans = JSON.parse(data.gradeDetails[0].answer)
      }else if(data.questionType==46||data.questionType==47){ // 变成题或简答题
        ans = JSON.parse(data.gradeDetails[0].answer)
      }else if(data.questionType==49){
        ans = data.gradeDetails[0].answer
      }
      else{
        ans = JSON.parse(data.gradeDetails[0].answer).longArray
      }

      return typeof initAns === 'string' ? ans[0] : ans
    },
    // 计算容器高度
    setQuestionContentHeight() {
      // if (this.$refs.questionContent) {
      //   const contentHeight = this.$refs.questionContent.scrollHeight;
      //   this.$refs.questionContent.style.height = `${contentHeight}px`;
      // }
      this.conchildren.forEach((item) => {
      const refName = `questionContent_${item.id}`;
      const questionContent = this.$refs[refName];
      // 检查对应的容器是否存在
      if (questionContent) {
        // 若 ref 指向的是一个数组，取第一个元素
        const container = Array.isArray(questionContent) ? questionContent[0] : questionContent;
        if(container){
          const contentHeight = container.scrollHeight;
          container.style.height = `${contentHeight}px`;
        }
      }
    });
    }
  },
}
</script>

<style lang="scss">
.synthesis-preview{
  // display: grid;
  grid-gap: 15px;
  width: 100%;
  .question-title, .question-desc {
    width: 100%;
    word-break: break-all;
  }
  .question-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .question-la{
      border-left: 3px solid #2b66ff;
      padding-left: 10px;
    }
    .writevocher{
        width: 96px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
      .writeback{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
    }
    .synthesisArrayList{
      .synthesisItem{
        width: 100%;
        background: #FFFFFF;
        border: 1px solid #E0E0E0;
        margin-bottom:15px;
        .question-name{
          width: 100%;
          height: 50px;
          background: #F9F9F9;
          border-bottom:1px solid #E0E0E0;
          display: flex;
          align-items: center;
          padding-left: 20px;
          justify-content: space-between;
          .questionborder{
            width: 962px;
            white-space: nowrap; /* 防止文本换行 */
            overflow: hidden; /* 隐藏溢出的内容 */
            text-overflow: ellipsis; /* 显示省略号来代表被修剪的文本 */
            border-left: 3px solid #3886FF;
            padding-left: 12px;
            .questionnums{
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
             color: #333333;
            }
            .questionnumsb{
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 16px;
              color: #333333;
              padding-left: 2px;
            }
          }

          .questioncontainer{
            display: flex;
            align-items: center;
            .questionbtnmain{
              display: flex;
              align-items: center;
              .questionanswerb{
                width: 80px;
                height: 30px;
                line-height: 30px;
                // background: #2B66FA;
                background: #fff;
                border-radius: 4px;
                // color:#fff;
                color:#606266;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                // color: #FFFFFF;
                cursor: pointer;
                text-align: center;
                border:1px solid #dcdfe6;
                font-size: 14px;
              }
              .operate-btn {
                width: 80px;
                height: 30px;
                padding: 0;
                margin-left: 10px;
              }
              .questionansave{
                width: 80px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                background: #2B66FA;
                border-radius: 4px;
                color:#fff;
                font-family: "PingFang SC, PingFang SC";
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                cursor: pointer;
                margin-left: 10px;
              }
            }
          }
          .questionset{
           display: flex;
           align-items: center;    
           padding-right: 20px;
          .score {
            display: flex;
            align-items: center;
            p {
              width: 60px;
              font-size: 14px;
              margin-left: 10px;
              color: #333;
            }
          }

            .remove {
              cursor: pointer;
              margin: 0;
              padding: 0;
              .el-icon-error {
                color: #f00;
              }
            }

            .el-input__inner {
              width: 70px;
              height: 25px;
              // border-top: none;
              // border-left: none;
              // border-bottom: 1px solid $gray-b;
              // border-right: none;
              border-radius: 20px;
              padding-right: 0;
              padding-left: 15px;
              text-align: center;
            }
          }
          .questionsetloc{
           display: flex;
           align-items: center;    
          .score {
            display: flex;
            align-items: center;
            p {
              width: 60px;
              font-size: 14px;
              margin-left: 10px;
              color: #333;
            }
          }

      .remove {
        cursor: pointer;
        margin: 0;
        padding: 0;
        .el-icon-error {
          color: #f00;
        }
      }

      .el-input__inner {
        width: 70px;
        height: 25px;
        // border-top: none;
        // border-left: none;
        // border-bottom: 1px solid $gray-b;
        // border-right: none;
        border-radius: 20px;
        padding-right: 0;
        padding-left: 15px;
        text-align: center;
      }
          }
        }
        .synthesismain{
          width: 100%;
          height: auto;
          padding-bottom: 20px;
        .questioncontent{
          padding: 15px 20px;
          .contentdesc{
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            padding: 0px 0 18px 0;
          }
        }
        .synthesisItem-answer{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 25px 20px 20px 20px;
        }
        .synthesisItem-ansinfo{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
        .synthesisItem-answeranly{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 25px 20px 20px 20px;
        }
        .synthesisItem-answeranlyop{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
      }
      }
    }
  :deep(img) {
    max-width: 100%;
  }
}
</style>