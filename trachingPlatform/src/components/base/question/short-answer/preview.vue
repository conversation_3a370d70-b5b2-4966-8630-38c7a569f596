<template>
  <div class="content-preview">
    <ZdEditor
        ref="shortEditor"
        v-if="!loading"
        :editor-default-html="value"
        :editorState="'edit'">
    </ZdEditor>
  </div>
</template>

<script>
import {getCorrectAnswer, questionTypeMenu} from "@/components/base/question/util";

export default {
  name: 'content-preview',
  components: {
    'ZdEditor': () => import("@/components/zd-editor/index.vue")
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    showAnswer: { // 是否显示答案
      type: Boolean,
      default: true
    },
    errorInfo: { // 错误信息
      type: Array,
      default: () => []
    },
    clearStorage: {
      type: Boolean,
      default: true
    }
  },
  watch:{
    value: {
      handler (val) {
        this.init()
      },
      immediate: true,
      deep: true
    }
  },
  data () {
    return {
      loading: true
    }
  },
  mounted () {
    // this.init()
  },
  methods: {
    init () {
      // 避免多个填空题时,答案被替换
      this.loading = true
      const courseContents = sessionStorage.getItem('courseContents')
      let settingArr = courseContents ? JSON.parse(courseContents) : []
      const answerIds = settingArr.map(item => item.answerId)

      if (this.config.settingArr && this.showAnswer) {
        settingArr = settingArr.concat(this.config.settingArr.filter(item => !answerIds.includes(item.answerId)))
      } else if (this.config.settingArr && !this.showAnswer) {
        settingArr = settingArr.concat(this.config.settingArr
            .filter(item => !answerIds.includes(item.answerId))
            .map((item) => ({
              ...item,
              answerCont: this.value[item.answerId] || ''
            }))
        )
      }
      settingArr.forEach((item) => {
        item.isCorrect = true
        if (this.errorInfo.includes(item.answerId)) {
          item.isCorrect = !this.errorInfo.includes(item.answerId)
        }
      })
      sessionStorage.setItem('courseContents', JSON.stringify(settingArr))
      this.$nextTick(() => {
        this.loading = false
      })
    },
    // changeValue () {
    //   const courseContents = sessionStorage.getItem('courseContents')
    //   const ans = getCorrectAnswer(questionTypeMenu.content, {
    //     settingArr: courseContents ? JSON.parse(courseContents) : []
    //   })
    //   this.$emit('input', ans)
    // }
  },
  destroyed () {
    // if (this.clearStorage) {
    //   sessionStorage.removeItem('courseContents')
    // }
  }
}
</script>

<style scoped lang="scss">
.content-preview {
  :deep(.zd-editor) {
    p {
      line-height: 36px;
    }
  }
}
</style>