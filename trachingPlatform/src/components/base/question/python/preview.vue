<template>
  <div class="python-preview" id="pythonPreview">
    <div v-if="type==46">
      <code-editor title="编程题答案设置区" :disabled="disabled" :config="config" :isBindEvent="true" :defaultCode="defaultCode"></code-editor>
    </div>
    <div v-else>
      <div v-if="showRun" class="p-header">
        <div>python代码编辑器</div>
        <div>
          <el-button type="primary" size="mini" style="padding: 4px 8px;" @click="run()">
            <i class="el-icon-caret-right"></i> 执行
          </el-button>
        </div>
      </div> 
      <code-set ref="codeSetRef"
                v-if="!loading"
                :type="type"
                :config="config"
                :disabled="true"
                :showAnswer="showAnswer"
                :showSetting="false"
                @inputMeth="changeValue">
      </code-set>
    </div>
   
    <base-dialog width="800px"
                 title="代码执行结果查看"
                 :visible.sync="runningModal"
                 :close-on-click-modal="false"
                 :no-footer="true"
                 className="python-preview-running-modal">
      <code-editor v-if="runningModal"
                   :isPreview="true"
                   :defaultCode="runningCode"
                   :address="config.address"
                   :showHeader="false"></code-editor>
    </base-dialog>
  </div>
</template>

<script>
import {getCorrectAnswer, questionTypeMenu} from "@/components/base/question/util";
import hljs from 'highlight.js';
export default {
  name: 'python-preview',
  components: {
    'code-set': () => import('./code-set.vue'),
    'code-editor': () => import('./code-editor.vue'),
    'base-dialog': () => import('@/components/base/dialog.vue')
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    showRun: { // 是否显示运行
      type: Boolean,
      default: true
    },
    showAnswer: { // 是否显示答案
      type: Boolean,
      default: true
    },
    errorInfo: { // 错误信息
      type: Array,
      default: () => []
    },
    type: { // 题目类型
      type: String,
      default: ''
    }
  },
  data () {
    return {
      runningModal: false,
      runningCode: '',
      loading: true,
      defaultCode:'',
    }
  },
  watch: {
    // config: {
    //   handler (val) {
    //     // this.changeValue(val)
    //   },
    //   deep: true,
    //   immediate: true
    // }
    value:{ // 监听作答记录的更新
      handler(val){
        if(val){
          if(this.$route.query.review==1&&this.type==46){
            this.config.html = val
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.loading = true
      if (this.config && this.config.html && !this.showAnswer) {
        // 清空错误
        this.config.html = this.config.html.replaceAll(
            'class="question-input-span question-input-span-error"',
            'class="question-input-span"'
        )

        // 设置为可编辑
        this.config.html = this.config.html.replaceAll(
          'class="question-input-span"',
          'class="question-input-span" contenteditable'
        )

        const divEle = document.createElement('div');
        divEle.innerHTML = this.config.html
        const spanList = divEle.querySelectorAll('.question-input-span')
          spanList.forEach(span => {
            const id = span.id
            span.innerHTML = (this.value&&JSON.stringify(this.value)!='{}')?this.value[id]:''
            if (this.errorInfo.includes(id)) {
              span.className = 'question-input-span question-input-span-error'
            }
          })
        this.config.html = divEle.innerHTML
        // console.log("this.config.html---------",this.config.html);
        // this.config.settingInputArr.forEach((item) => {
        //   let beforeText = `data-value="${item}" class="question-input-span"(.*?)</span>`
        //   let afterText = `data-value="${item}" class="question-input-span" contenteditable>${this.value[item] || ''}</span>`
        //   if (this.errorInfo.includes(item)) {
        //     afterText = `data-value="${item}" class="question-input-span question-input-span-error" contenteditable>${this.value[item] || ''}</span>`
        //   }
        //   const reg = new RegExp(beforeText, 'gi')
        //   this.config.html = this.config.html.replaceAll(reg, afterText)
        // })
        if(this.type==46){
           const doc = new DOMParser().parseFromString(`<div>${this.config.html}</div>`, 'text/html');
          this.defaultCode = this.formatPythonCode(doc.body.innerText);
        }
      }
      this.$nextTick(() => {
        this.loading = false
      })
    },
    run() {
      this.runningCode = this.$refs.codeSetRef.$refs.pythonSettingRef.innerText
      this.runningModal = true
    },
    changeValue (val) {
      const ans = getCorrectAnswer(questionTypeMenu.python, val)
      this.$emit('input', ans)
    },
    formatPythonCode(code) {
      try {
        // 使用 highlight.js 对 Python 代码进行高亮处理
        let code_new =  hljs.highlight(code, { language: 'python' }).value;
        const doc = new DOMParser().parseFromString(`<div>${code_new}</div>`, 'text/html');
        // return code_new;
        return doc.body.textContent;
        // return code_new.replace(/<[^>]*>/g, '')
      } catch (error) {
        console.error('代码格式化失败:', error);
        // 格式化失败时直接返回原始代码
        return code;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.python-preview {
  width: 100%;
  overflow-x: auto;
  .p-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    color: #ccc;
    font-size: 13px;
    background-color: #333;
  }
}

:deep(.python-preview-running-modal) {
  .el-dialog__header {
    background-color: #1e1e1e;
    color: #ccc;
  }
  .el-dialog__body {
    background-color: #1e1e1e;
    padding: 0;
  }
}
</style>