<template>
  <div class="answer-python-set">
    <code-editor :disabled="disabled" :defaultCode="defaultCode"></code-editor>
    <div style="padding: 10px 0; font-weight: 700;">设置区域</div>
    <code-set :config="config" :showSetting="showSetting" :disabled="disabled"></code-set>
  </div>
</template>

<script>
export default {
  name: 'answer-python-set',
  components: {
    'code-editor': () => import('./code-editor.vue'),
    'code-set': () => import('./code-set.vue')
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showSetting: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      settingInputArr: [],
      // defaultCode:'',
    }
  },
  mounted () {
    // const doc = new DOMParser().parseFromString(`<div>${this.config.html}</div>`, 'text/html');
    // this.defaultCode = doc.body.innerText
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
.answer-python-set {
}
</style>