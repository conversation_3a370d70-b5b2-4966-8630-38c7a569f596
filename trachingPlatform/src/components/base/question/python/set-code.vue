<template>
  <div class="answer-python-code-set">
    <code-editor title="编程题答案设置区" :disabled="disabled" :config="config" :isBindEvent="true" :defaultCode="defaultCode"></code-editor>
  </div>
</template>

<script>
import hljs from 'highlight.js';
export default {
  name: 'answer-python-code-set',
  components: {
    'code-editor': () => import('./code-editor.vue'),
    // 'code-set': () => import('./code-set.vue')
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    showSetting: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      defaultCode:'',
    }
  },
  mounted () {
    const doc = new DOMParser().parseFromString(`<div>${this.config.html}</div>`, 'text/html');
    // this.defaultCode = doc.body.innerText
    // this.defaultCode = doc.body.textContent;
    this.defaultCode = this.formatPythonCode(doc.body.innerText);
    console.log(doc.body.innerText);
    console.log(this.formatPythonCode(doc.body.innerText));
  },
  methods: {
    formatPythonCode(code) {
      try {
        // 使用 highlight.js 对 Python 代码进行高亮处理
        let code_new =  hljs.highlight(code, { language: 'python' }).value;
        const doc = new DOMParser().parseFromString(`<div>${code_new}</div>`, 'text/html');
        // return code_new;
        return doc.body.textContent;
        // return code_new.replace(/<[^>]*>/g, '')
      } catch (error) {
        console.error('代码格式化失败:', error);
        // 格式化失败时直接返回原始代码
        return code;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.answer-python-code-set {
}
</style>