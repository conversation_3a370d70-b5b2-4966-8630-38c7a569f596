<template>
  <div v-if="!reloadMonaco"
       :class="`answer-python-code-editor ${ isFullscreen ? 'answer-python-code-editor-fullscreen' : '' }`">
    <div v-if="showHeader" class="monaco-head">
      <div>{{title}}</div>
      <div style="display: flex; align-items: center;">
        <el-tooltip class="item" effect="dark" content="复制" placement="top">
          <i class="iconfont icon-fadingzhudonggongkaineirong" @click="copyCode()"></i>
        </el-tooltip>
        <el-tooltip v-if="!isFullscreen" class="item" effect="dark" content="全屏" placement="top">
          <i class="iconfont icon-quanping_o" @click="changeFull()"></i>
        </el-tooltip>
        <el-tooltip v-if="isFullscreen" class="item" effect="dark" content="取消全屏" placement="top">
          <i class="iconfont icon-quxiaoquanping_o" @click="changeFull()"></i>
        </el-tooltip>
        <el-button class="run-code" type="primary" size="mini" style="padding: 5px 12px;" @click="runCode()">
          <i class="el-icon-caret-right"></i> 执行
        </el-button>
      </div>
    </div>
    <div class="monaco-body">
      <el-input v-model="inputAdress"
                class="address-input"
                placeholder="引入数据文件地址"
                @change="changeAddress">
      </el-input>
      <!-- <monaco class="pythonEditor" ref="monaco" :opts="opts"  @handleMonacoChange="handleMonacoChange" :isBindEvent="isBindEvent"></monaco> -->
      <div v-if="showResult" class="result-dv">
        <div class="title">执行结果如下：</div>
        <div class="text-area-2">
<!--          <textarea v-show="result" v-model="result" disabled> </textarea>-->
          <div v-show="result">
            <pre style="">{{ result }}</pre>
          </div>
          <div class="src-box" v-if="srcList.length">
            <div class="ig" v-for="(item, index) in srcList" :key="index" style="margin-right: 20px;">
              <el-image style="width: 200px; height: 200px" :preview-src-list="realSrcList"
                        :src="item.targetFile"></el-image>
            </div>
          </div>
          <div class="resilt-box" v-if="fileList.length > 0">
            <div class="file-item" v-for="(item, index) in fileList" :key="index">
              <a :href="`${FileBase}${item.srcFile}&TargetFile=${item.targetFile}`" style="color: #338aff">
                {{ item.srcFile }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="code-content" ref="codeContent" v-html="defaultCode"></div>
  </div>
</template>

<script>
// import {pythonMixins} from "@/components/pythonTool/pythonMixins";
import { title } from "@/settings";

export default {
  name: 'answer-python-code-editor',
  components: {
    // monaco: () => import('@/components/pythonTool/monacoeditor.vue'),
  },
  props: {
    isPreview: { // 是否为预览模式，会自动运行代码，故必须传defaultCode 且不能为空
      type: Boolean,
      default: false
    },
    defaultCode: { // 默认值
      type: String,
      default: ''
    },
    address: { // 默认值
      type: String,
      default: ''
    },
    showHeader: { // 是否显示头部信息
      type: Boolean,
      default: true
    },
    disabled: { // 是否禁止编辑
      type: Boolean,
      default: false
    },
    showResult: { // 是否显示结果框
      type: Boolean,
      default: true
    },
    config:{ // 仅代码编程题的配置信息
      type: Object,
      default: () => {}
    },
    isBindEvent:{ // 是否绑定事件
      type: Boolean,
      default: false
    },
    title:{ // 标题
      type: String,
      default: 'python代码编辑器'
    }
  },
  watch:{
    defaultCode:{
      handler(val){
        if(!val){
          this.reloadMonaco = true
          setTimeout(()=>{
            this.reloadMonaco = false
          })
        }
        this.$nextTick(()=>{
          // this.opts.value = this.$refs.codeContent.innerHTML.split('\n').map(line => line.trimStart()).join('\n');
          // this.opts.value = val;
          this.opts.value = val.replace(/<\/?div\b[^>]*>/g, '');
        })
      },
      immediate: true,
      deep: true,
    },
  },
  data () {
    return {
      isFullscreen: false, // 是否全屏
      reloadMonaco: false, // 重新加载编辑器
      isShowPython: false,
      code: '',
      opts: {
        value: '',
        readOnly: false, // 是否禁止编辑
        language: 'python', // 语言类型
        theme: 'vs-dark', // 编辑器主题
        overviewRulerBorder: false, // 滚动是否有边框
        automaticLayout: true, // 自动布局
        minimap: { // 关闭代码缩略图
          enabled: true // 是否启用预览图
        },
        lineNumbers: 'on', // 控制行号的显隐
        scrollBeyondLastLine: false, // 禁用额外滚动区
        scrollbar: {
          verticalScrollbarSize: 4, // 垂直滚动条宽度，默认px
          horizontalScrollbarSize: 4 // 水平滚动条高度
        },
        contextmenu: true, // 禁用右键菜单
        autoClosingBrackets: 'always',
        wordBasedSuggestionsOnlySameLanguage: true,
        acceptSuggestionOnCommitCharacter: true,
        wordBasedSuggestions: true,
        selectOnLineNumbers: true,
        renderSideBySide: false,
        mimetypes: ["application/json"]
      }
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if (this.defaultCode) {
        this.$nextTick(()=>{
          // this.opts.value = this.$refs.codeContent.innerHTML.replace(/<\/?div\b[^>]*>/g, '');
          this.opts.value = this.defaultCode.replace(/<\/?div\b[^>]*>/g, '');
        })
        if (this.isPreview) {
            this.$nextTick(() => {
              this.runCode()
            })
          }
      }
      this.opts.readOnly = !!this.disabled
    },
    changeFull () {
      const text = this.$refs.monaco.getVal()
      this.isFullscreen = !this.isFullscreen
      this.reloadMonaco = true
      this.$nextTick(() => {
        this.opts.value = text;
        this.reloadMonaco = false
      })
    },
    copyCode () {
      const text = this.$refs.monaco.getVal()
      this.$copyText(text)
      this.$message({
        type: 'success',
        message: '当前代码复制成功',
        offset: 300
      })
    },
    runCode () {
      const code = this.$refs.monaco?.getVal() || this.defaultCode
      if (!code) return this.$message({
        type: 'warning',
        message: '请输入内容后再执行',
        offset: 300
      })
      this.code = code
      this.inputAdress = this.address || this.inputAdress
      this.isShowPython = true;
      this.$nextTick(() => {
        this.Online()
      })
    },
    handleMonacoChange(value) { // python编程题
      this.config.html = value;
    }
  }
}
</script>

<style scoped lang="scss">
.answer-python-code-editor {
  background-color: #1e1e1e;
  border-radius: 4px;
  width: 100%;
  .code-content{
    position: absolute;
    left:0;
    top: -6000px;
  }
  .monaco-head {
    display: flex;
    justify-content: space-between;
    padding: 8px 20px;
    background-color: rgba(255, 255, 255, 0.2);
    align-items: center;
    color: #ccc;
    font-size: 15px;
    .icon-fadingzhudonggongkaineirong,.icon-quanping_o,.icon-quxiaoquanping_o {
      font-size: 20px;
      margin-right: 10px;
      cursor: pointer;
    }

    .run-code{
      background-color: var(--theme_primary_color);
      color: #fff;
      &:focus{
        background-color: var(--theme_primary_color)!important;
        color: #fff!important;
        border-color: var(--theme_primary_color)!important;
      }
      &:hover{
        background-color: #fff!important;
        color:var(--theme_primary_color)!important
      }
    }
  }
  .monaco-body {
    height: 510px;
    display: flex;
    flex-direction: column;
    padding: 10px 0;
    .pythonEditor {
      flex: 1;
      width: 100%;
      background-color: #1e1e1e;
      font-size: 14px;
      color: #ffffff;
      font-family: PingFangSC-Regular, PingFang SC;

      .monaco-editor {
        width: 100% !important;
        height: 100% !important;
      }
    }
    .result-dv {
      margin: 10px;
      border: 2px solid #333;
      height: 40%;
      .title {
        color: #ccc;
        background-color: #333;
        padding: 6px;
      }
      .text-area-2 {
        height: calc(100% - 40px);
        overflow-y: auto;
        width: 100%;
        background-color: #1e1e1e;
        padding: 10px;
        :deep(pre) {
          width: 100%;
          background-color: #1e1e1e;
          font-size: 14px;
          color: #ccc;
          resize: none;
          border: none;
        }
        .src-box {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
        }
        .resilt-box {
          display: flex;
          .file-item {
            margin-right: 10px;
            border: #338aff 1px solid;
            padding: 6px;
            padding-top: 2px;
            padding-bottom: 2px;
          }
        }
      }
    }
    :deep(.address-input) {
      margin: 0 15px 12px 15px;
      width: calc(100% - 24px);
      input {
        background: rgba(255, 255, 255, 0.06);
        border-radius: 4px;
        color: #ccc;
        outline: none;
        border: none;
        font-size: 13px;
        &::placeholder {
          color: #999;
        }
      }
    }
  }

  :deep(.python-title) {
    width: 100%;
  }
  :deep(.pythonBox) {
    overflow-y: hidden;
  }
}
.answer-python-code-editor-fullscreen {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  top: 0;
  z-index: 1000;
  .monaco-body {
    height: calc(100vh - 40px);
  }
}
</style>