<template>
  <div class="python-code-set">
    <div v-if="showSetting" class="p-header">
      <div>
        <el-button type="primary" size="mini"
                   style="padding: 4px 8px;"
                   @click="replaceInput()">插入文本框</el-button>
      </div>
      <div>
        <span>Python编译器运行代码无误后，请复制到此区域设置填空区城</span>
        <i class="el-icon-bottom"></i>
        <span style="color: #9BB8FF;text-decoration-line: underline; cursor: pointer;"
              @click="showExampleImg = true;">示例</span>
      </div>
    </div>
    <div class="p-body">
      <div>
        <el-input v-model="address"
                  class="address-input"
                  placeholder="引入数据文件地址"
                  @change="changeAddress">
        </el-input>
        <div ref="pythonSettingRef"
             :contenteditable="!disabled"
             @input="inputMeth">
        </div>
      </div>
    </div>
    <base-dialog width="600px"
                 title="示例"
                 :visible.sync="showExampleImg"
                 :close-on-click-modal="false"
                 :no-footer="true"
                 className="add-question-modal">
      <!-- <img src="@/assets/question/code-example.jpg" style="width: 100%" /> -->
    </base-dialog>
  </div>
</template>

<script>
export default {
  name: 'python-code-set',
  components: {
    'code-editor': () => import('./code-editor.vue'),
    'base-dialog': () => import('@/components/base/dialog.vue')
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showSetting: { // 是否显示头部设置项
      type: Boolean,
      default: true
    },
    type:{
      type: Number,
      default: 0,
    }
  },
  data () {
    return {
      settingInputArr: [],
      showExampleImg: false,
      isFocus: false,
      address: '',
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      // console.log("代码题-------------------------------",this.config)
      if (this.config && this.config.html) {
        // 判断是编辑部分的预览还是做题部分的预览
        const regex = /<\/?[a-z][\s\S]*>/i;
        regex.test(this.config.html)? this.$refs.pythonSettingRef.innerHTML = this.config.html:this.$refs.pythonSettingRef.innerText = this.config.html
        // this.$refs.pythonSettingRef.innerHTML = this.config.html
        // this.$refs.pythonSettingRef.innerText = this.config.html
        this.settingInputArr = this.config.settingInputArr
        // 文件地址
        this.address = this.config.address || ''


        this.$emit('init-success', '')


       
      }
    },
    inputMeth () {
      const html = this.$refs.pythonSettingRef.innerHTML
      // this.$emit('update:config', {
      //   ...this.config,
      //   html: html || '',
      //   settingInputArr: this.settingInputArr
      // })
      this.config.html = html || ''
      this.config.settingInputArr = this.settingInputArr.filter(item => html.includes(item))
      this.$emit('inputMeth', this.config)
    },
    replaceInput () {
      // 获取选中的文本
      const selection = window.getSelection()
      if (!selection) {
        return
      }

      // 判断光标位置是否是可编辑div
      const gbPos= selection.getRangeAt(0).getBoundingClientRect()
      const divPos= this.$refs.pythonSettingRef.getBoundingClientRect()
      if (gbPos.top > 0 && (gbPos.bottom > divPos.bottom || gbPos.top < divPos.top)) {
        return
      }

      // const selectedText = selection.toString()
      const spanId = 'input_' + new Date().getTime()
      this.settingInputArr.push(spanId)

      // 创建一个临时的元素，用于存放替换后的内容
      const tempElement = document.createElement('span')
      tempElement.setAttribute('id', spanId)
      tempElement.setAttribute('data-value', spanId)
      tempElement.className = 'question-input-span'

      // 将选中的文本放入临时元素中
      tempElement.appendChild(selection.getRangeAt(0).cloneContents())

      // 将选中的内容替换为指定的内容
      selection.getRangeAt(0).deleteContents()
      // tempElement.innerHTML = tempElement.innerHTML.replace(selectedText,'')
      selection.getRangeAt(0).insertNode(tempElement.cloneNode(true))

      const html = this.$refs.pythonSettingRef.innerHTML
      if (html.slice(-1) === '>') {
        this.$refs.pythonSettingRef.innerHTML = html + '&nbsp;'
      }
      this.inputMeth()
    },
    changeAddress () {
      this.config.address = this.address || ''
      this.inputMeth()
    }
  }
}
</script>

<style scoped lang="scss">
.python-code-set {
  min-height: 300px;
  max-height: 500px;
  background-color: #1e1e1e;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  .p-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    color: #ccc;
    font-size: 13px;
    background-color: #333;
  }
  .p-body {
    padding: 15px;
    flex: 1;
    color: #ccc;
    overflow-y: auto;
    :deep(.address-input) {
      input {
        background: rgba(255, 255, 255, 0.06);
        border-radius: 4px;
        color: #ccc;
        outline: none;
        border: none;
        margin-bottom: 12px;
        font-size: 13px;
        &::placeholder {
          color: #999;
        }
      }
    }
  }
}
:deep(.question-input-span) {
  word-break: normal;
  width: auto !important;
  word-wrap: break-word;
  line-height: 16px;
  display: inline-block;
  border: 1px dashed #3886ff;
  height: 20px;
  min-width: 60px;
  text-align: center;
}
:deep(.question-input-span-error) {
  border-color: var(--color-danger);
}
</style>