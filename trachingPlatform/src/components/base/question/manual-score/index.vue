<template>
  <div class="manual-score-com">
    <div class="commentary">
      <h4 class="title">批语</h4>
      <editor v-model="params.teacherComment" placeholder="阅卷批语请输入"></editor>
      <!-- v-if="!reviewed" -->
      <!-- <div class="teacherComment" v-else v-html="params.teacherComment"></div> -->
    </div> 
    <div class="set-score-info" v-if="userInfo.userType!=1">
      <h4 class="title">得分</h4>
      <div class="manual-info">
        <!-- :disabled="reviewed" -->
        <p class="set-score">
          <span class="label">得分：</span> 
          <el-input-number  
            @input="limitLength"
            v-model="params.score" 
            controls-position="right" 
            :min="0" 
            :step="0.01" 
            :max="score">
          </el-input-number>
        </p>
        <!-- v-if="!reviewed"  -->
        <el-dropdown :popper-append-to-body="false" split-button class="manual-type" trigger="click" @command="handleCommand">
          批阅
          <el-dropdown-menu slot="dropdown" class="self-dropdown">
            <el-dropdown-item command="type1"><i style="color: #2B66FA;" class="iconfont icon-gouxuan1"></i> 正确</el-dropdown-item>
            <el-dropdown-item command="type2"><i style="color: #FF3636;" class="iconfont icon-cuowu"></i> 错误</el-dropdown-item>
            <el-dropdown-item command="type3"><i style="color: #2B66FA;" class="iconfont icon-a-zu27577"></i> 基本正确</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name:'manualScore',
  components:{
    'editor': () => import('@/components/base/question/editor/index.vue')
  },
  props:{
    score:{ // 题目分数
      type:Number,
      default: 0
    },
    gradeDetailId:{
      type: Number,
      default: 0
    },
    reviewed:{ // 是否批阅
      type: Boolean,
      default: false
    },
    scored:{// 批阅的得分 
      type:Number,
      default: 0
    },
    teacherComment:{ // 教师批语
      type: String,
      default: ""
    }
  },
  computed: {
    ...mapGetters({
      courseInfo: ["getCourseInfo"],
      userInfo:['userInfo'],

    })
  },
  watch:{
    teacherComment:{
      handler(val){
        this.params.teacherComment = val;
      },
      immediate: true,
    },
    scored:{
      handler(val){
        // this.params.score = this.reviewed?this.scored:this.score
        this.params.score = this.reviewed?this.scored:0
      },
      // immediate: true,
    }
  },
  mounted(){
    // this.params.score = this.reviewed?this.scored:this.score
    this.params.score = this.reviewed?this.scored: 0
    // this.params.teacherComment = this.teacherComment;
  },
  data(){
    return {
      params:{
        gradeDetailId: 0,
        teacherComment: "", // 评语
        answerResult: 0,
        score: 0 // 得分
      },
      maxLength:4,
    }
  },
  methods:{
    async handleCommand(command){
      // if(!teacherComment) return this.$message.warning('请输入评语')
      // this.params.gradeDetailId = this.$route.query.gradeId
      if(!this.gradeDetailId)return this.$message({
        type:'warning',
        message:'学生未作答，无需批阅',
        offset:260
      })
      this.params.gradeDetailId = this.gradeDetailId
      switch(command){
        case 'type1':
          this.params.answerResult = 2;
          this.params.score = this.score;// 正确 得满分
          break;
        case 'type2':
          this.params.answerResult = 4;
          this.params.score = 0;// 错误 得0分
          break;
        case 'type3':
          this.params.answerResult = 3;
          break;
        default:
          break;
      }
      let { code }=  await this.$api.TeacherReview(this.params)
      if(code === 200){
        this.$message.success('批阅成功')
        // 正确或者部分对 则分数累加
        // if(command=='type1'||command=='type3'){
          // 重新获取个人分数
          let res = await this.$api.GetStudentGrade({
            gradeId:  this.$route.query.gradeId,
            gradeDetailId: 0,
            studentId: this.$route.query.userId,
          });
          this.$router.replace({
            path: this.$route.path,
            query:{
              ...this.$route.query,
              gradeScore:res.data.score,
            }
          })
          // let res = await this.$api.GetStudentsByTaskId({
          //   pageIndex: 1,
          //   pageSize: 300,
          //   orderby: 'SubmitTime desc',
          //   params: {
          //     courseId: this.courseInfo.id,
          //     gradeId: this.$route.query.gradeId,
          //     taskId: this.$route.query.id,
          //     userName: this.$route.query.userName,
          //   }
          // });
          // console.log("res---------------",res)

        // }
        // this.$router.push({name: 'gradeDetail', query: {gradeId: this.params.gradeDetailId}})
      }
    },
    limitLength(value) {
      if (value && value.toString().length > this.maxLength) {
        this.params.score = parseFloat(value.toString().slice(0, this.maxLength), 10);
      }
      // console.log("this.params------",this.params)
    },
  }
}
</script>
<style scoped lang="scss">
  .manual-score-com{
    background-color: #fff;
    padding-top: 45px;
    .commentary{
      border-top: 1px dashed #DBDBDB;
      margin-bottom: 20px;
      .title{
        font-size: 16px;
        position: relative;
        padding-left: 14px;
        &::before{
          content: '';
          width: 3px;
          height: 15px;
          background: #3886FF;
          top: 0;
          left: 0;
          position: absolute;
        }
      }

      .teacherComment{
        min-height: 100px;
        border: 1px solid #DBDBDB;
        padding: 10px;
        word-break: break-all;
        line-height: 28px;
      }
    }
    .set-score-info{
      margin-bottom: 20px;
      .title{
        font-size: 16px;
        position: relative;
        padding-left: 14px;
        &::before{
          content: '';
          width: 3px;
          height: 15px;
          background: #3886FF;
          top: 0;
          left: 0;
          position: absolute;
        }
      }
      .manual-info{
         display: flex;
         justify-content: space-between;
         margin-bottom: 20px;
         overflow: inherit;
         ::v-deep .el-input-number--medium{
          width: 120px;
         }
         .manual-type{
          position: relative;
          top: 4px;
          ::v-deep .el-button-group .el-button{
            height: 34px;
            background: #2B66FA;
            color: #fff;
          }
          ::v-deep .el-button-group>.el-button:not(:last-child){
            width: 96px;
          }

         }
         .set-score{
           margin-bottom: 0;
           display: flex;
            justify-content: space-between;
            align-items: center;
           .label{
            display: inline-block;
           }
         }
         ::v-deep .el-input-number.is-controls-right[class*=medium] [class*=decrease]{
          border-bottom-right-radius: 18px;
          border: 1px solid #dcdfe6;
         }
         ::v-deep .el-input-number.is-controls-right[class*=medium] [class*=increase]{
          border-top-right-radius: 18px;
          border: 1px solid #dcdfe6;
         }
         ::v-deep .el-input-number{
          border-radius: 30px;
          overflow: hidden;
          padding: 1px;
         }
         ::v-deep .el-input-number .el-input__inner{
          border-radius: 30px;
         }
       }
     }
 
  }

  ::v-deep .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
    background-color: #F4F8FF;
    color: #333;
  }
</style>