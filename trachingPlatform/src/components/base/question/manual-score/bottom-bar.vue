<template>
  <div class="check-student-bar">
    <div class="score-info">
      <p class="all-score"><span>卷总分 </span><span class="score-box">{{totalScore}}</span><span>分</span></p>
      <el-divider direction="vertical"></el-divider>
      <p class="had-score"><span>得分</span><span class="score-box">{{gradeScore}}</span></p>
    </div>
    <div class="operate-box">
      <el-button v-if="isReview" :disabled='!isReview' class="submit-btn" @click="submit">提交</el-button>
      <el-button class="check-btn" @click="prev">上一份</el-button>
      <el-button class="check-btn" @click="next">下一份</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  data(){
    return{
      totalScore: this.$route.query.score, //总分
      
    }
  },
  props:{
    isReview:{
      type:Boolean,
      default:false,
    }
  },
  mounted(){ },
  computed:{  
    ...mapGetters({
      'reviewList':['getReviewList']
    }),
    currentStudentId(){ // 当前学生id
      return this.$route.query.userId;
    },
    gradeScore(){   // 得分
      return this.$route.query.gradeScore;
    } 
  },
  methods:{
    // 重新统计当前试卷的得分
    async submit(){
      let res = await this.$api.GetStudentGrade({
        gradeId:  this.$route.query.gradeId,
        gradeDetailId: 0,
        studentId: this.$route.query.userId,
      });
      if(res.code==200){
        this.$message.success('统计成功！')
        this.$router.replace({
          path: this.$route.path,
          query:{
            ...this.$route.query,
            gradeScore:res.data.score,
          }
        })
      }
    },
    prev(){
      let checkPerson = null;
      this.reviewList.forEach((el,index) => {
          if(el.userId == this.currentStudentId){
            if(index==0){
              this.$message.success('当前已是第一份')
            }else if(this.reviewList[index-1]){
              checkPerson = this.reviewList[index-1]
            }
          }
      });
      this.$router.replace({
          path: this.$route.path,
          query:{
            ...this.$route.query,
            ...checkPerson
          }
      })
    },
    next(){
      let checkPerson = null;
      this.reviewList.forEach((el,index) => {
          if(el.userId == this.currentStudentId){
            if(index==this.reviewList.length-1){
              this.$message.success('当前已是最后一份')
            }else if(this.reviewList[index+1]){
              checkPerson = this.reviewList[index+1]
            }
          }
      });

      this.$router.replace({
          path: this.$route.path,
          query:{
            ...this.$route.query,
            ...checkPerson
          }
      })
    },
  }
}
</script>
<style scoped lang="scss">
.check-student-bar{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  background: #FFFFFF;
  box-shadow: 0px -3px 6px 1px rgba(0,0,0,0.08);
  display: flex;
  justify-content: center;
  z-index: 2;

  .score-info{
    display: flex;
    justify-content: center;
    align-items: center;

    ::v-deep .el-divider--vertical{
      margin: 0 15px;
    }
    .all-score,
    .had-score{
      display: flex;
      span{
        display: inline-block;
        height: 24px;
        line-height: 24px;
      }
      .score-box{
        text-align: center;
        width: 63px;
        margin: 0 10px;
        border-radius: 14px;
        background: #F7F7F7;
        border: 1px solid #E2E2E2;
      }
    }
  }
  .operate-box{
    position: absolute;
    right: 10px;
    top: 8px;
    .submit-btn{
      width: 80px;
      height: 34px;
      background: #2B66FF;
      color: #fff;
    }
    .check-btn{
      width: 80px;
      height: 34px;
      background: #FFFFFF;
      border: 1px solid #DDE2E9;
    }
  }
}
</style>