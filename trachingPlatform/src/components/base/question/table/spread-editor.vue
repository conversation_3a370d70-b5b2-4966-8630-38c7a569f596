<template>
  <div class="answer-table-spread-editor">
    <div class="spread-dv" :style="isFull?'position: fixed;width: 100%!important;height: 100%;left:0;top: 0;z-index:3001;':''" :class="isSetScore ? 'isSetScore-spread-dv' : ''">
      <div class="actions">
        <el-button size="mini" type="default" @click="fullScreen"><i class="iconfont" :class="isFull ? 'icon-quxiaoquanping_o' : 'icon-quanping_o' "></i></el-button>
        <el-button v-if="!isSetScore" size="mini" type="primary" @click="isSetScore = true">选择计分单元格</el-button>
        <el-button v-else size="mini" type="primary" @click="isSetScore = false">完成设置</el-button>
      </div>
      <spreadSheet :style="isFull?'width: 100%!important;':''" :isSetCheckPoint="true"
                   :isHideToolBar="false"
                   @initSpread="initSpread"
                   @sizeRefresh="sizeRefresh">
      </spreadSheet>
    </div>
    <div class="spread-score">
      <div class="check-point">
        <span>计分点：  <el-checkbox v-model="checked">自由计分</el-checkbox></span>
        <div class="point-list">
          <span class="item-point" v-for="item in checkPoint" :key='`${item.row}_${item.col}`'>
            {{item.colLabel}}{{item.rowLabel}}  
            <el-input type="number" min="0" max="101" @blur="item.score = item.score&&item.score>0? item.score:0;" @change="syncTag(item)" class="item-score" v-model="item.score" /><i class="remove-point el-icon-circle-close" @click="removePoint(item)"></i>  </span>
        </div>
      </div>
      <div class="total-score">
        <p>总分： <el-button type="text" @click="setAverageScore">
          一键平均分  
          <el-tooltip class="item" effect="dark" content="将当前总分平均分至已选中的考核单元格。" placement="top-start">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-button></p> 
        <el-input v-model="allScore" @blur="setAverageScore" placeholder="总分"></el-input>
        <span style="display: none;">{{totalScore}}</span> 
      </div>
    </div>
  </div>
</template>

<script>
import {updateScrollbars} from "@/tool/spreadTool.js"
export default {
  name: 'answer-table-spread-editor',
  components: {
    'spreadSheet': () => import("@/components/spread-js/index.vue")
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    }
  },
  inject: ['passToChild'],
  data () {
    return {
      spread: null,
      isSetScore: false,
      checkPoint:[],// 考核点
      checked:true,// 是否自由计分
      allScore:0, // 总分
      isFull:false,// 是否全屏 
    }
  },
  mounted(){
    this.$nextTick(()=>{
      setTimeout(()=>{
        this.initSpreadTable(this.config)
      },800);
    })
  },
  computed:{
    totalScore(){
      let score = 0;
      this.checkPoint.forEach(v=>{
        score+= Number(v.score)
      })
      this.allScore = score.toFixed(2);
      return score;
    }
  },
  methods: {
    fullScreen(){
      this.isFull = !this.isFull
    },
    initSpread (spread) {
      spread.options.newTabVisible = false; // 隐藏新增子表按钮
      this.spread = spread;
      this.passToChild(spread);
      const _this = this
      // spread.bind(GC.Spread.Sheets.Events.CellClick, function (sender, args) {
      //   console.log(sender, args)
      //   if (_this.isSetScore) {
      //     const sheet = spread.getActiveSheet()
      
      //     let style = sheet.getStyle(args.row, args.col) || new GC.Spread.Sheets.Style()
      //     style.backColor = "rgb(239,244,255)";
      //     style.borderTop = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      //     style.borderRight = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      //     style.borderBottom = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      //     style.borderLeft = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      
      //     sheet.setStyle(args.row, args.col, style)
      //   }
      // })
      spread.bind(GC.Spread.Sheets.Events.SelectionChanged, function (sender, args) {
        if (_this.isSetScore) {
          const sheet = spread.getActiveSheet()
          console.log(args)
          args.newSelections.forEach((v) => {
            let style = sheet.getStyle(v.row, v.col) || new GC.Spread.Sheets.Style()
            let cellTag = sheet.getTag(v.row, v.col)
            if(cellTag){ //删除
              // style.backColor = "rgb(255,255,255)";
              // sheet.setTag(v.row, v.col, '');
              // _this.checkPoint = _this.checkPoint.filter(point=>!(point.row == item.row&&point.col==item.col))
            }else{ // 新增 
              sheet.setTag(v.row, v.col, JSON.stringify({checkPoint:true}));
              style.backColor = "rgb(239,244,255)";
              style.borderTop = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
              style.borderRight = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
              style.borderBottom = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
              style.borderLeft = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
              _this.checkPoint.push({
                row: v.row,
                col: v.col,
                rowLabel: v.row+1,
                colLabel: _this.getAlphaLabel(v.col+1),
                score:0,// 每空的分数
              })
            }
            sheet.setStyle(v.row, v.col, style)
          })
        }
      })
      // 默认隐藏详情工具栏
      this.$nextTick(()=>{
        let ribbonBar = document.querySelectorAll(".gc-ribbon-bar")
        ribbonBar.forEach(v=>{
          v.classList.add("collapsed");
        })
      })
    },
    getAlphaLabel(index) {
      let result = "";
      while (index > 0) {
          // 取余数，将数字映射到26个字母上
          let remainder = index % 26;
          if (remainder === 0) {
              remainder = 26;
              index -= 26;
          }
          index = Math.floor(index / 26) - 1;
          // 将余数转换为大写字母，并添加到结果字符串的前面
          result = String.fromCharCode(64 + remainder) + result;
      }
      return result;
    },
    // 一键平均计分
    setAverageScore(){
      let len = this.checkPoint.length;
      let avg = (this.allScore/len).toFixed(1);
      this.checkPoint.forEach((v,i)=>{
        if(i==len-1){ // 最后一个为剩下的比例
          v.score = this.allScore - avg*(len-1);
        }else{
          v.score = avg
        }
        this.syncTag(v)
        // 触发单元格分数修改
      })
    },
    // 移除考核点
    removePoint(item){
      let sheet = this.spread.getActiveSheet();
      let style = sheet.getStyle(item.row, item.col) || new GC.Spread.Sheets.Style()
      const newStyle = style.clone();
      newStyle.backColor = "rgb(255,255,255)";
      sheet.setTag(item.row, item.col, '');
      this.checkPoint = this.checkPoint.filter(point=> !(point.row == item.row&&point.col==item.col))
      sheet.setStyle(item.row, item.col, newStyle);
    },
    // 单个单元格的分数修改
    syncTag(item){
      let sheet = this.spread.getActiveSheet();
      sheet.setTag(item.row, item.col, JSON.stringify(item));
    },
    // 初始化回显 表格题
    initSpreadTable(config){
      let { positionInfo, spread, spreadStr } = config;

      if(!spreadStr)return;
      this.spread.fromJSON(JSON.parse(spreadStr))//回显保存的模板
      updateScrollbars(this.spread); //  滚动调配置
      let sheet = this.spread.getActiveSheet();
      let style = new GC.Spread.Sheets.Style()
      style.backColor = "rgb(239,244,255)";
      style.borderTop = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      style.borderRight = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      style.borderBottom = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      style.borderLeft = new GC.Spread.Sheets.LineBorder("#ccc", GC.Spread.Sheets.LineStyle.thin);
      this.checkPoint = positionInfo.map(v=>{
        sheet.setStyle(v.row, v.col, style)
        sheet.setValue(v.row, v.col, v.value)
        sheet.setTag(v.row, v.col, JSON.stringify(v))
        return {
          row: v.row,
          col: v.col,
          rowLabel: v.rowLabel,
          colLabel: v.colLabel,
          score:v.score,
        }
      })
    },
    sizeRefresh () {
    }
  }
}
</script>

<style scoped lang="scss">
.answer-table-spread-editor {
  .spread-dv {
    height: 500px;
    border: 1px solid #DBDBDB;
    border-radius: 4px;
    position: relative;
    padding: 4px;
    .actions {
      position: absolute;
      top: 6px;
      z-index: 3000;
      right: 10px;
      button {
        padding: 4px 8px !important;
      }
    }

    ::v-deep .gc-designer-container{
      overflow: visible;
    }
  }
  .isSetScore-spread-dv {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    &:before {
      position: absolute;
      top: 0;
      left: 0;
      content: "选择计分单元格操作中，请单击需计分的单元格或框选";
      padding: 26px 10px;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.6);
      z-index: 4;
      width: 100%;
      text-align: center;
    }
    :deep(.ribbon-panel) {
      display: none;
    }
  }
  :deep(.gc-ribbon-bar .ribbon-navigation) {
    margin-top: -1px !important;
    padding: 0 !important;
  }

  .spread-score{
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    .check-point{
      .point-list{
        margin-top:14px;
      }
      // 积分点
      .item-point{
        margin-right:10px;
        margin-bottom: 6px;
        display: inline-block;
        position: relative;
        .item-score{
          display: inline-block;
          height: 20px;
          width: 40px;
          ::v-deep .el-input__inner{
            height: 20px;
            width: 40px;
            padding: 0;
            text-align: center;
          }
        }
        .remove-point{
          position: absolute;
          right: -10px;
          top: -8px;
          cursor: pointer;
        }
      }
    }

    .total-score{
      width: 130px;
    }
  }
  
}
</style>