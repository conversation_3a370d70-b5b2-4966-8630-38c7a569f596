<template>
  <div ref="questionTablePreview" class="question-table-preview">
    <div style="width:100%;" v-if="config.tableType==1">
    </div>
    <div v-else v-html="previewContent"></div>
  </div>
</template>

<script>
import { scoreRadioMenu } from "@/components/base/question/util.js"
const classMenu = {
  tdSuo: 'table-suo-class',
  tdContent: 'table-td-content-dv',
  tdScore: 'table-td-score-dv'
}

export default {
  name: 'content-preview',
  components: {
    'ZdEditor': () => import("@/components/zd-editor/index.vue")
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    showAnswer: { // 是否显示答案
      type: Boolean,
      default: true
    },
    errorInfo: { // 错误信息
      type: Array,
      default: () => []
    },
    clearStorage: {
      type: Boolean,
      default: true
    },
    isAnswerHint:{ // 是否是答案解析
      type: Boolean,
      default: false
    }
  },
  inject: ['passToSpread'],
  data () {
    return {
      loading: true,
      previewContent: '',
      spread:null,
    }
  },
  watch:{
    value:{
      handler(val){
        this.init()
      },
      deep:true,
      // immediate:true
    },
    // errorInfo:{
    //   handler(val){
    //     this.init()
    //   },
    //   deep:true,
    // }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      if(this.config?.tableType==1){ // spread表格题
        setTimeout(()=>{
          if(this.spread&&this.config.spreadStr){
            let { spreadStr, } = this.config;
            this.spread.fromJSON(JSON.parse(spreadStr));
            updateScrollbars(this.spread); //  滚动调配置
            this.fillAnswer()
          }
        },800)
      }else{ // 富文本表格题
        this.previewContent = this.getPreviewContent()
        
      }
      
    },
    // spread 初始化
    initSpread(spread){
      this.spread = spread;
      if(this.passToSpread&&!this.isAnswerHint){ // 非解析弹窗则抛出实例化对象
        this.passToSpread(spread);
      }
    },
    // 预览数据
    getPreviewContent () {
      const tableWrapper = document.createElement('div');
      tableWrapper.innerHTML = this.config.html || ''
      const tables = tableWrapper.querySelectorAll('table')
      
      if (tables.length === 0) {
        this.$message('请先在富文本框中加入表格')
      }
      tables.forEach((table) => {
        const tds = table.querySelectorAll('td')
        tds.forEach(td => {
          td.style.paddingLeft = '0'
          td.style.borderColor = '#ccc'// 重置边框颜色
          const el1 = td.querySelectorAll(`.${classMenu.tdSuo}`)[0]
          if (el1) {
            el1.style.display = `none`
          }
          const el2 = td.querySelectorAll(`.${classMenu.tdScore}`)[0]
          if (el2) {
            el2.style.display = `none`
          }
          const el3 = td.querySelectorAll(`.${classMenu.tdContent}`)[0]
          
          if (el3 && !el3.innerHTML.includes('contenteditable')) {
            
            if (el3?.style['border-top-style'] === 'dashed') {
              el3.innerHTML = `<div contenteditable="true" style="min-width:50px;"><br></div>`
            }
          }
        })
      })
      this.fillAnswer(tableWrapper)
      return tableWrapper.innerHTML
    },
    // 填写答案 
    fillAnswer(dom){
      if(dom){
        // 预览处理
        // const doc = new DOMParser().parseFromString(`<div>${this.config.html}</div>`, 'text/html');
        Array.from(dom.querySelectorAll('.table-suo-class')).forEach(v=>v.style.display='none');// 锁隐藏
        // 表格的第一行隐藏
        Array.from(dom.querySelectorAll('tr')).forEach(v=>{
          let tdList = v.querySelectorAll('td');
          tdList[0].style.display = 'none';
        });
      }
      // if(JSON.stringify(this.value)=='{}')return
      let answer = null;
      try {
        answer = JSON.parse(this.value);
      } catch (error) {
        answer = this.value;
      }

        if(this.config.scoreRadio){
          let targetObj = null;
          switch(this.config.scoreRadio){
            case scoreRadioMenu.Row:
                targetObj = answer.rowAnswer||''
              break;
            case scoreRadioMenu.Col:
                targetObj = answer.colAnswer|| ''
              break;
            case scoreRadioMenu.Td:
                targetObj = answer.itemAnswer||''
              break;
            case scoreRadioMenu.Freedom:
                targetObj = answer.itemAnswer||''
              break;
          }
          // 数据填充
          // let doc = handleTableAnswer(this.config,targetObj,dom,this.errorInfo)
          // this.$refs.questionTablePreview.innerHTML = '';
          // this.$refs.questionTablePreview.appendChild(doc.querySelector('table'));
  
        }

    }
  }
}
</script>

<style scoped lang="scss">
.question-table-preview {
  ::v-deep .gc-designer-container{
    width:1100px!important;
    min-height: 600px!important;
  }
  :deep(.zd-editor) {
    p {
      line-height: 36px;
    }
    .table-container {
      border: none;
      padding: 2px;
      th, td {
        border-right-width: 1px !important;
      }
    }
  }
}
</style>