<template>
  <div class="answer-table-set">
    <div style="margin-bottom: 20px;">
      <span style="margin-right: 20px;">表格形式</span>
      <el-radio-group v-model="tableType" @change="changeTableType">
        <el-radio :label="1">EXCEL自由表格</el-radio>
        <el-radio :label="2">固定表格</el-radio>
      </el-radio-group>
    </div>
    <div v-if="!loading">
      <div v-show="tableType === 1">
        <spreadEditor :config="config"></spreadEditor>
      </div>
      <div v-show="tableType === 2">
        <tableQuillEditor :config="config"></tableQuillEditor>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'answer-table-set',
  components: {
    'tableQuillEditor': () => import("./quill-editor.vue"),
    'spreadEditor': () => import("./spread-editor.vue")
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: true,
      tableType: 2
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      this.loading = false
      if(this.config.tableType){
        this.tableType = this.config.tableType
      }else{
        this.changeTableType(this.tableType) // 默认传统模式
      }
    },
    // 记录表格展现类型
    changeTableType(val){
      this.config.tableType = val
    }
  }
}
</script>

<style scoped lang="scss">
.answer-table-set {
  padding-top: 6px;
  :deep(.gc-designer-container) {
    width: 1100px !important;
  }
}
</style>