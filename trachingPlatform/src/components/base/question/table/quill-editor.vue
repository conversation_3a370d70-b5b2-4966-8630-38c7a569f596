<template>
  <div id="asd-tinymce" class="asd-tinymce">
    <div style="margin-bottom: 20px;">
      <span style="margin-right: 20px;">答案编辑</span>
      <span style="margin-right: 10px; color: #767676;">表格宽度</span>
      <el-input v-model="tableWidth" size="mini" style="width: 89px; margin-right: 15px;" @keyup.enter.native="changeWidth()"></el-input>
      <el-button style="margin-right: 20px;" size="mini" type="primary" plain @click="bindTable">绑定表格</el-button>
      <span style="color: #aaa; float: right; font-size: 13px; line-height: 28px;">步骤:请先在编辑器中插入表格设置行与列数。再点击【绑定表格】后设置计分方式</span>
    </div>
    <div style="margin-bottom: 20px;">
      <VueTinymce ref="VueTinymceTableRef" v-model="config.html" :setting="settings" placeholder="请输入内容" />
    </div>
    <div style="margin-bottom: 20px;">
      <span style="margin-right: 20px;">计分方式</span>
      <el-radio-group v-model="scoreRadio" @change="bindRadio">
        <el-radio :label="scoreRadioMenu.Row">按行计算</el-radio>
        <el-radio :label="scoreRadioMenu.Col">按列计算</el-radio>
        <el-radio :label="scoreRadioMenu.Td">按单元格平均计算</el-radio>
        <el-radio :label="scoreRadioMenu.Freedom">自由计算</el-radio>
      </el-radio-group>
    </div>
    <div style="display: flex; align-items: center;">
      <div style="margin-right: 20px;">开关锁
        <el-popover placement="bottom-start" width="200" trigger="hover">
          <div style="font-size: 13px; color: #999; line-height: 24px;">
            <div>开锁：全部单元格为考核点</div>
            <div>关锁：全部单元格不考核</div>
          </div>
          <i slot="reference" class="iconfont icon-wenhao" style="font-size: 13px;"></i>
        </el-popover>
      </div>
      <div style="display: flex; align-items: center;">
        <el-switch v-model="openKey" @change="changeAllSuo()"></el-switch>
        <div v-if="openKey" style="padding-left: 4px; color: var(--theme_primary_color); margin-top: 1px;">全开</div>
        <div v-else style="padding-left: 4px; color: #999; margin-top: 1px;">全关</div>
      </div>
    </div>
  </div>
</template>

<script>

import { settings } from '@/utils/tinymceSettings.js'
import { scoreRadioMenu } from "@/components/base/question/util.js"
const classMenu = {
  tdSuo: 'table-suo-class',
  tdContent: 'table-td-content-dv',
  tdScore: 'table-td-score-dv'
}

export default {
  components: {
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    // const scoreRadioMenu = {
    //   Row: '5',
    //   Col: '6',
    //   Td: '2',
    //   Freedom: '4'
    // }
    return {
      tableWidth: '500',
      scoreRadio: scoreRadioMenu.Row,
      scoreRadioMenu: scoreRadioMenu,
      openKey: 1,
      settings: {
        ...settings,
        toolbar: `
             formatselect
             | bold italic underline strikethrough forecolor backcolor
             | fontselect fontsizeselect
             | numlist bullist lineheight
             | alignleft aligncenter alignright alignjustify
             | image media table
             | undo redo
             | fullscreen
            `,
        height: 300,
        menubar: false, // 禁用菜单栏
        contextmenu: `link | image | inserttable | cell row column | advtablesort | tableprops deletetable`,
        quickbars_insert_toolbar : false, // 禁用快速插入工具栏
      },
      notSuoUrl: 'http://cdn-ccoptc.yunsx.com/2024-10-11/eedec610e9ba41faa7152df91b4b2693.png!zd',
      suoUrl: 'http://cdn-ccoptc.yunsx.com/2024-10-10/5c7753c2397d43e19ed0b04a40d05bd0.png!zd',
      isBindClick: false // 是否绑定了点击事件，避免重复添加
    }
  },
  mounted() {
    if(this.config.scoreRadio){ // 计分方式
      this.scoreRadio = this.config.scoreRadio
    }
  },
  methods: {
    // 根据html获取表格标签内容
    handleTablesElement (handleMeth) {
      const tableWrapper = document.createElement('div');
      tableWrapper.innerHTML = this.config.html
      const tables = tableWrapper.querySelectorAll('table')

      if (tables.length === 0) {
        this.$message.closeAll()
        this.$message('请先在富文本框中加入表格')
        return
      }
      tables.forEach((table) => {
        handleMeth(table)
      })
      this.config.html = tableWrapper.innerHTML
    },
    // 改变表格宽度
    changeWidth () {
      this.handleTablesElement((table) => {
        table.style.width = this.tableWidth + 'px'
      })
    },
    // 绑定表格，初始化表格样式及 加锁标签
    bindTable () {
      this.handleTablesElement((table) => {
        table.style.width = this.tableWidth + 'px'
        table.style.lineHeight = '26px'
        table.style.borderColor = '#cccccc'
        const tds = table.querySelectorAll('td')
        tds.forEach((td, index) => {
          if (td.getElementsByClassName(classMenu.tdSuo).length === 0) {
            // 给每个单元格加上锁
            const div = document.createElement('div')
            div.className = classMenu.tdSuo
            div.style.background = `url('${this.suoUrl}') no-repeat center`
            div.style.backgroundSize = '100% 100%'
            div.style.opacity = '0.5'
            div.style.width = '18px'
            div.style.height = '15px'
            div.style.position = 'absolute'
            div.style.left = '0'
            div.style.top = 'calc(50% - 8px)'
            div.style.cursor = 'pointer'
            div.style.zIndex = '10'
            div.style.display = 'none'

            td.id = 'suo' + new Date().getTime() + index
            td.style.position = 'relative'
            td.style.paddingLeft = '0'

            // 给每个单元格套上一层输入样式
            if (td.getElementsByClassName(classMenu.tdContent).length === 0) {
              td.innerHTML = `<div class="${classMenu.tdContent}" style="margin: 5px; border-radius: 4px; border: none;">${td.innerHTML}</div>`
            }
            td.appendChild(div)

            // 给每个单元格加上分数设置框
            if (td.getElementsByClassName(classMenu.tdScore).length === 0) {
              const scoreDiv = document.createElement('div')
              scoreDiv.className = classMenu.tdScore
              scoreDiv.style.position = 'absolute'
              scoreDiv.style.right = '0'
              scoreDiv.style.bottom = '0'
              scoreDiv.style.minWidth = '30px'
              scoreDiv.style.zIndex = '11'
              scoreDiv.style.backgroundColor = '#2b66ff'
              scoreDiv.style.borderRadius = '2px'
              scoreDiv.style.height = '16px'
              scoreDiv.style.lineHeight = '16px'
              scoreDiv.style.color = '#fff'
              scoreDiv.style.fontSize = '13px'
              scoreDiv.style.textAlign = 'center'
              scoreDiv.style.display = 'none'
              scoreDiv.innerHTML = '<span contenteditable="false" class="editable" style="float: right">分</span>'
              td.appendChild(scoreDiv)
            }
          }
        })
      })
      this.$nextTick(() => {
        this.bindRadio()
        this.bindClick()
      })
    },
    // 绑定锁标签事件
    bindClick () {
      if (!this.isBindClick) {
        const iframe = document.getElementsByTagName('iframe')[0];
        if (iframe) {
          // 获取iframe的document对象
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
          iframeDoc.addEventListener('click', (event) => {
            // 获取触发事件的元素
            const clickedEle = event.target

            if (clickedEle && clickedEle.className.includes(classMenu.tdSuo)) {
              const tdEleId = clickedEle.parentNode.id
              if (clickedEle.style.background.includes(this.suoUrl)) {
                this.setSuoTd(tdEleId, false)
              } else {
                this.setSuoTd(tdEleId, true)
              }
            }
          })
          this.isBindClick = true
        }
      }
    },
    // 根据计分方式 显示锁
    bindRadio () {
      this.config.scoreRadio = this.scoreRadio;// 计分方式
      this.handleTablesElement((table) => {
        // 隐藏全部锁
        const tds = table.querySelectorAll('td')
        tds.forEach(td => {
          this.setSuoDisplay(td, false)
          this.changeConSty(td, true)
          this.setScoreDivDisplay(td, true)
        })
        // 根据计分方式开锁
        const trs = table.querySelectorAll('tr')

        if (this.scoreRadio === this.scoreRadioMenu.Row) {
          trs.forEach((tr, i1) => {
            if (i1 > 0) {
              const trtds = tr.querySelectorAll('td')
              if (trtds[0]) {
                this.setSuoDisplay(trtds[0], true)
                
              }
            }
          })
        } else if (this.scoreRadio === this.scoreRadioMenu.Col) {
          if (trs[0]) {
            const trtds = trs[0].querySelectorAll('td')
            trtds.forEach((td, i2) => {
              if (i2 > 0) {
                this.setSuoDisplay(td, true)
                
              }
            })
          }
        } else if (this.scoreRadio === this.scoreRadioMenu.Td) {
          trs.forEach((tr, i1) => {
            if (i1 > 0) {
              const trtds = tr.querySelectorAll('td')
              trtds.forEach((td, i2) => {
                if (i2 > 0) {
                  this.setSuoDisplay(td, true)
                }
              })
            }
          })
        }else if(this.scoreRadio === this.scoreRadioMenu.Freedom){
          trs.forEach((tr, i1) => {
            if (i1 > 0) {
              const trtds = tr.querySelectorAll('td')
              trtds.forEach((td, i2) => {
                if (i2 > 0) {
                  this.setSuoDisplay(td, true)
                }
              })
            }
          })
        }
        this.openKey = false
      })
    },
    // 显示锁
    setSuoDisplay (td, show) {
      const div = td.querySelectorAll(`.${classMenu.tdSuo}`)[0] // 锁
      const scoreDiv = td.querySelectorAll(`.${classMenu.tdScore}`)[0] // 设分的div
      if(scoreDiv){
        // 重置分数
        scoreDiv.innerHTML = '<span contenteditable="false" class="editable" style="float: right">分</span>'
      }
      if (div) {
        div.style.display = show ? `block` : 'none'
        td.style.paddingLeft = show ? '18px' : '0'
        if (!show) {
          div.style.background = `url('${this.suoUrl}') no-repeat center`
          div.style.backgroundSize = `100% 100%`
          div.style.opacity = '0.5'
        }
      }
    },
    // 单元格样式修改
    changeConSty (td, isSuo) {
      const div = td.querySelectorAll(`.${classMenu.tdContent}`)[0]
      if (div) {
        div.style.border = isSuo ? 'none' : '1px dashed #2b66ff'
        div.style.padding = isSuo ? '0' : '0 10px'
      }
    },
    // 单元格样式修改
    setScoreDivDisplay (td, isSuo) {
      const div = td.querySelectorAll(`.${classMenu.tdScore}`)[0]
      if (div) {
        div.style.display = isSuo ? `none` : 'block'
      }
    },
    // 开锁关锁事件
    setSuoTd (eleId, isSuo) {
      this.handleTablesElement((table) => {
        const clickTdEle = table.querySelectorAll(`#${eleId}`)[0]
        if (!clickTdEle) return
        // 锁的样式修改
        clickTdEle.querySelectorAll(`.${classMenu.tdSuo}`).forEach(ele1 => {
          ele1.style.background = `url('${isSuo ? this.suoUrl : this.notSuoUrl}') no-repeat center`
          ele1.style.backgroundSize = `100% 100%`
          ele1.style.opacity = isSuo ? '0.5' : '1'
        })
        // 计分方式不同 样式不同
        if (this.scoreRadio === this.scoreRadioMenu.Row) {
          const trEle = clickTdEle.parentNode
          const tds = trEle?.querySelectorAll('td')
          if (tds) {
            for (let i = 1; i < tds.length; i++) {
              this.changeConSty(tds[i], isSuo)
             
              // this.handleAnswerFormat(this.scoreRadio,tds[i], isSuo)
            }
          }

        } else if (this.scoreRadio === this.scoreRadioMenu.Col) {
          const trEle = clickTdEle.parentNode
          const colIndex = Array.prototype.indexOf.call(trEle.children, clickTdEle)
          const tBodyEle = trEle.parentNode
          const trs = tBodyEle?.querySelectorAll('tr')
          if (trs) {
            for (let i = 1; i < trs.length; i++) {
              const tds = trs[i].querySelectorAll('td')
              this.changeConSty(tds[colIndex], isSuo)


              // this.handleAnswerFormat(this.scoreRadio,tds[colIndex], isSuo)
            }
          }
        } else if (this.scoreRadio === this.scoreRadioMenu.Td){// 平均计分
          this.changeConSty(clickTdEle, isSuo)
          // this.handleAnswerFormat(this.scoreRadio,clickTdEle, isSuo)
          // this.setScoreDivDisplay(clickTdEle, isSuo)
        }else if(this.scoreRadio === this.scoreRadioMenu.Freedom){ // 自由计分
          this.changeConSty(clickTdEle, isSuo)
          // this.handleAnswerFormat(this.scoreRadio,clickTdEle, isSuo)
          this.setScoreDivDisplay(clickTdEle, isSuo)
        }
      })
    },
    // 全部开锁/关锁
    changeAllSuo () {
      const iframe = document.getElementsByTagName('iframe')[0];
      if (iframe) {
        // 获取iframe的document对象
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
        const suoList = iframeDoc.getElementsByClassName(classMenu.tdSuo)
        for (let i = 0; i < suoList.length; i++) {
          // 全部开锁
          if (this.openKey) {
            // 关锁状态的改为开锁
            if (suoList[i].style.display === 'block' && suoList[i].style.background.includes(this.suoUrl)) {
              const tdEleId = suoList[i].parentNode.id
              this.setSuoTd(tdEleId, false)
            }
          } else {
            // 开锁状态的改为关锁
            if (suoList[i].style.display === 'block' && suoList[i].style.background.includes(this.notSuoUrl)) {
              const tdEleId = suoList[i].parentNode.id
              this.setSuoTd(tdEleId, true)
            }
          }
        }
      }
    },
    // 处理答案格式
    /**
     * 
     * @param type  // 计分类型
     * @param td // 答案的dom
     * @param isSuo  // 锁标识
     */
    handleAnswerFormat(type, td, isSuo){
      // table-td-content-dv
      switch(type){
        case this.scoreRadioMenu.Row:
          this.handleRowAnswer(td, isSuo)
          break;
        case this.scoreRadioMenu.Col:
          this.handleColAnswer(td, isSuo)
          break;
        case this.scoreRadioMenu.Td:
          this.handleTdAnswer(td, isSuo)
          break;
        case this.scoreRadioMenu.Freedom:
          this.handleFreedomAnswer(td, isSuo)
          break;
        default:
          break;
      }
      console.log('单元格计分',td.querySelector('.table-td-content-dv').innerText)
    },
    // 行计分答案
    handleRowAnswer(td, isSuo){
    },
    // 列计分答案
    handleColAnswer(td, isSuo){

    },
    // 平均计分答案
    handleTdAnswer(td, isSuo){

    },
    //  自由计分答案
    handleFreedomAnswer(td, isSuo){

    },
  }
}
</script>

<style scoped lang="scss">
.asd-tinymce {
  :deep(.tox-editor-header) {
    //border-bottom: 1px solid #ccc;
    svg {
      transform: scale(0.8);
    }
    .tox-tbtn--select {
      margin-right: 10px;
      .tox-tbtn__select-label {
        width: auto !important;
      }
    }
  }
  :deep(.tox-statusbar) {
    border-top: none;
    .tox-statusbar__text-container {
      display: none;
    }
    .tox-statusbar__resize-handle {
      cursor: n-resize;
    }
  }
}
</style>