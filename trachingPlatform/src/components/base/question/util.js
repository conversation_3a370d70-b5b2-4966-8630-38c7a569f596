/*
 * 题库相关公共数据方法
 * */

// 前端类型标识与后端的映射
export const questionTypeMenu = {
        // SingleChoice, MultipleChoice, FillIn, ShortAnswer
        radio: 'SingleChoice',
        checkbox: 'MultipleChoice',
        content: 'FillIn',
        shortAnswer: 'ShortAnswer', // 简答题
        // radio: 1,
        // checkbox: 2,
        // content: 3,
        // shortAnswer: 47, // 简答题
    }
    // 表格题的计分方式
export const scoreRadioMenu = {
    Row: '5',
    Col: '6',
    Td: '2',
    Freedom: '4'
}

// 类型对应的中文名称
export const questionTypeLabel = {
    [questionTypeMenu.radio]: '单选题',
    [questionTypeMenu.checkbox]: '多选题',
    [questionTypeMenu.content]: '填空题',
    [questionTypeMenu.shortAnswer]: '简答题',
}

// 类型对应的初始化配置
export const questionInitConfig = {
    [questionTypeMenu.radio]: {
        options: [
            { label: '', value: 'A', isAnswer: false },
            { label: '', value: 'B', isAnswer: false },
            { label: '', value: 'C', isAnswer: false },
            { label: '', value: 'D', isAnswer: false }
        ]
    },
    [questionTypeMenu.checkbox]: {
        options: [
            { label: '', value: 'A', isAnswer: false },
            { label: '', value: 'B', isAnswer: false },
            { label: '', value: 'C', isAnswer: false },
            { label: '', value: 'D', isAnswer: false }
        ]
    },
    [questionTypeMenu.content]: {
        settingArr: [],
        html: ''
    },
    [questionTypeMenu.isTrue]: {
        options: [
            { label: '', value: 'A', isAnswer: false },
            { label: '', value: 'B', isAnswer: false }
        ]
    },
    [questionTypeMenu.python]: {
        settingInputArr: [],
        html: ''
    },
    [questionTypeMenu.pythonCode]: {
        html: ''
    },
    [questionTypeMenu.table]: {
        html: ''
    },
    [questionTypeMenu.shortAnswer]: {
        html: ''
    },



}

// 选项值
export const optionKeyList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N']

// 答案初始化
export function getInitAnswerValue(type) {
    return {
        [questionTypeMenu.radio]: '',
        [questionTypeMenu.checkbox]: [],
        [questionTypeMenu.isTrue]: '',
        [questionTypeMenu.content]: {},
        [questionTypeMenu.python]: {},
        [questionTypeMenu.pythonCode]: {},
        [questionTypeMenu.table]: {},
    }[type]
}

// 获取正确答案
export function getCorrectAnswer(type, config, returnType) {
    // switch (type) {}
    if (type === questionTypeMenu.radio || type === questionTypeMenu.isTrue || type === questionTypeMenu.checkbox) {
        let arr = [];
        (config.options || []).forEach((option) => {
            if (option.isAnswer) {
                arr.push(option.value)
            }
        })
        return arr
    }
    if (type === questionTypeMenu.shortAnswer) { // 简答题
        const obj = {}
        const { html } = config
        obj.answer = html;
        return obj
    }
    if (type === questionTypeMenu.content) { // 填空题
        const obj = {};
        (config.settingArr || []).forEach((item) => {
            if (config.html && config.html.includes(item.answerId)) {
                obj[item.answerId] = item.answerCont || ''
            }
        })
        return obj
    }

    return {}
}


// 获取答案回显文字
export function getAnswerDispatch(type, config) {
    return {
        [questionTypeMenu.radio]: getCorrectAnswer(questionTypeMenu.radio, config).join(''),
        [questionTypeMenu.checkbox]: getCorrectAnswer(questionTypeMenu.checkbox, config).join(''),
        [questionTypeMenu.content]: (Object.values(getCorrectAnswer(questionTypeMenu.content, config))).join('，'),
        [questionTypeMenu.shortAnswer]: ''

    }[type]
}

// 难易度列表
// 易,适中,难
export const difficultyOptions = [
    { label: '易', value: 'Easy' },
    { label: '适中', value: 'Moderation' },
    { label: '难', value: 'Difficult' },
]

// 获取难易度回显文字
export function getDifficultyDispatch(val) {
    return difficultyOptions.find(op => op.value === val).label
}
// 
export function hasClosedHtmlTags(text) {
    // 正则表达式匹配完整闭合的 HTML 标签
    const closedTagRegex = /<([a-z][a-z0-9]*)\b[^>]*>(.*?)<\/\1>/gi;
    return !closedTagRegex.test(text);
}

// 根据文本获取 value
export function getValueByLabel(label) {
    // const option = difficultyOptions.find(op => op.label === label);
    const option = difficultyOptions.find(op => op.label.indexOf(label) > -1);
    return option ? option.value : null;
}