
<template>
  <div class="answer-form-setting">
    <p class="part-label">题干</p>
    <div style="margin-bottom: 15px;">
      <el-input v-model="titleCopy" maxLength="200" placeholder="请输入标题" :size="size"></el-input>
    </div>
    <div style="margin-bottom: 15px;">
      <editor v-model="descCopy" placeholder="请输入内容描述及要求，非必填项"></editor>
    </div>
    <template v-if="type === questionTypeMenu.radio">
      <option-set :config="config" :type="type" :size="size"></option-set>
    </template>
    <template v-else-if="type === questionTypeMenu.checkbox">
      <option-set :config="config" :type="type" :size="size" multiple></option-set>
    </template>
    <template v-else-if="type === questionTypeMenu.content">
      <content-set :config="config" :size="size"></content-set>
    </template>
    <template v-else-if="type === questionTypeMenu.shortAnswer">
      <short-answer :config="config" :size="size"></short-answer>
    </template>
  </div>
</template>

<script>
import { questionTypeMenu } from './util.js';

export default {
  name: 'answer-form-setting',
  props: {
    type: { // 试题类型
      type: String | Number,
      default: ''
    },
    config: { // 试题对应配置项
      type: Object,
      default: () => {}
    },
    answer: { // 试题对应配置项
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: ''
    }
  },
  components: {
    'option-set': () => import('./options/set.vue'),
    'content-set': () => import('./content/set.vue'),
    'editor': () => import('./editor/index.vue'),
    'short-answer': () => import('./short-answer/set.vue'),
  },
  computed: {
    titleCopy: {
      get () {
        return this.title
      },
      set (val) {
        this.$emit('update:title', val)
      }
    },
    descCopy: {
      get () {
        return this.desc
      },
      set (val) {
        this.$emit('update:desc', val)
      }
    }
  },
  data () {
    return {
      questionTypeMenu: questionTypeMenu,
      vocheranswer:''
    }
  },
  watch:{
    answer: {
      handler(val) {
         this.vocheranswer=val
      },
      immediate:true
    },
  },
  created() {
  
    console.log(this.type,'默认--')
  }
}
</script>

<style scoped lang="scss">
.answer-form-setting {
  .part-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    line-height:40px;
    margin-bottom:10px;
  }
}
</style>