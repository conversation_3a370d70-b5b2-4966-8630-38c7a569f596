<template>
  <base-dialog :fullscreen="true"
               :visible.sync="dialogShow"
               :close-on-click-modal="false"
               :custom-title="true"
               :no-footer="true"
               :no-full-change="true"
               className="add-question-modal">
    <template slot="title">
      <div class="back" @click="close()"><i class="el-icon-arrow-left"></i> {{ backText }}</div>
      <div class="title">{{ title }}</div>
      <div>
        <slot name="rightSlot" v-if="$slots.rightSlot"></slot>
        <i v-else class="el-icon-close" @click="close()"></i>
      </div>
    </template>
    <div style="padding: 25px 0; background-color: #f9fafc; min-height: 100%;">
      <div class="question-body">
        <slot name="contentLeftSlot"></slot>
        <div class="question-content" :style="{ width: width }">
          <slot name="contentSlot"></slot>
        </div>
        <slot name="contentRightSlot"></slot>
      </div>
    </div>
  </base-dialog>
</template>

<script>
export default {
  name: 'question-full-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    backText: {
      type: String,
      default: '返回题库'
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '1200px'
    }
  },
  components: {
    'base-dialog': () => import('@/components/base/dialog.vue')
  },
  computed: {
    dialogShow: {
      get () {
        return this.visible
      },
      set (val) {
        this.$emit('update:visible', val)
      }
    },
  },
  data () {
    return {}
  },
  methods: {
    close () {
      this.dialogShow = false
      this.$emit('close', '')
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.add-question-modal) {
  .el-dialog__headerbtn {
    display: none !important;
  }
  .el-dialog__body {
    padding: 0 !important;
    max-height: 100% !important;
    height: calc(100% - 44px) !important;
  }
  .el-button--primary.is-plain {
    border-color: var(--theme_primary_color) !important;
  }
  .el-dialog__header {
    padding: 0 24px;
    height: 48px;
    align-items: center;
    background-color: #d3e4f5;
    color: var(--theme_primary_color);
    font-size: 15px;
    font-weight: normal;
    cursor: default !important;
    .back {
      font-size: 13px;
      cursor: pointer;
    }
    .title {
      font-weight: 700;
      max-width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-icon-close {
      color: #aaa;
      font-size: 16px;
    }
  }
  .question-body {

    height: 100%;
    display: flex;
    justify-content: center;
    overflow-y: auto;
    .question-content {
      background-color: #fff;
      border-radius: 12px;
      padding: 25px 50px;
    }
  }
}
</style>