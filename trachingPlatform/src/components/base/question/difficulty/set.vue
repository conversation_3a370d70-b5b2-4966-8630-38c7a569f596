<template>
  <div class="difficulty-set">
    <el-select v-model="newValue" placeholder="请选择" :size="size" style="width: 250px;" @change="changeValue">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>
    <base-dialog
      title="自定义"
      append-to-body
      :showToScreen="true"
      :visible.sync="showDiyModal"
      :close-on-click-modal="false"
      v-dialogDrag
      width="400px"
      @submit="diyConfirm()">
      <div>
        <el-input v-model="diyValue"></el-input>
        <div>
          <el-tag size="mini" type="primary" effect="dark">难</el-tag>
          <el-tag size="mini" type="info">中</el-tag>
          <el-tag size="mini" type="info">易</el-tag>
        </div>
      </div>
    </base-dialog>
  </div>
</template>

<script>
import { difficultyOptions } from '@/components/base/question/util';

export default {
  name: 'difficulty-set',
  components: {
    'base-dialog': () => import('@/components/base/dialog.vue')
  },
  props: {
    value: {
      type: String | Number,
      default: ''
    },
    options: {
      type: Array,
      default: () => difficultyOptions
    },
    size: {
      type: String,
      default: ''
    },
  },
  data () {
    return {
      showDiyModal: false,
      diyValue: '',
      diyDiff: '难'
    }
  },
  computed: {
    newValue: {
      get () {
        return (this.value || this.value === 0) ? this.value : ''
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    changeValue (val) {
      if (val === 'diy') {
        this.showDiyModal = true
        this.diyValue = ''
        this.diyDiff = '难'
      }
    },
    diyConfirm () {
      const text = this.diyValue + this.diyDiff
      for (const option of this.options) {
        if (option.value === text) {
          this.$message.warning('该难易程度已存在！')
          return
        }
      }
      this.options.push({
        label: text,
        value: text
      })
    }
  }
}
</script>

<style scoped lang="scss">
.difficulty-set {}
::v-deep .base-dialog {
  min-height: 100px;
}
</style>