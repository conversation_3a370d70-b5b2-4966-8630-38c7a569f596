<template>
  <div
    :class="['option-select-tag', isActive ? 'option-select-tag-active' : '', customClassName || '']"
    @click="changeActive()"
  >{{ text }}</div>
</template>

<script>
export default {
  name: 'option-select-tag',
  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    customClassName: {
      type: String,
      default: ''
    }
  },
  methods: {
    changeActive () {
     this.$emit('changeActive', !this.isActive)
    }
  }
}
</script>

<style scoped lang="scss">
.option-select-tag {
  $wh: 30px;
  width: $wh;
  height: $wh;
  min-width: $wh;
  border: 1px solid #e2e2e2;
  line-height: $wh;
  text-align: center;
  border-radius: $wh;
  margin-right: 10px;
  cursor: default;
  font-size: 14px;
  &:hover {
    background-color: #fff;
    border: 1px solid #0070FC;
    color: #0070FC;
  }
}
.option-select-tag-active {
  background-color: var(--theme_primary_color);
  border: 1px solid var(--theme_primary_color);
  color: #fff;
}
</style>