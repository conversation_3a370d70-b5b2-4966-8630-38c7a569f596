<template>
  <div class="question-editor">
    <ZdEditor
        :editor-default-html="newValue"
        :editor-config="{
          placeholder: placeholder,
          height: height,
          resize: resize
        }"
        :editorState="'edit'"
        @editorHtml="editorHtml">
    </ZdEditor>
  </div>
</template>

<script>
export default {
  name: 'question-editor',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String | Array,
      default: ''
    },
    resize: {
      type: String,
      default: 'none'
    },
    height: {
      type: String,
      default: '240px'
    }
  },
  components: {
    'ZdEditor': () => import("@/components/zd-editor/index.vue")
  },
  watch:{
    value:{
      handler(val){
        this.newValue = val
      },
      immediate: true,
    }
  },
  computed: {
    newValue: {
      get () {
        // value类型判断没做
        return this.value || ''
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    editorHtml (data) {
      this.newValue = data
    }
  }
}
</script>

<style scoped lang="scss">
.question-editor {
  :deep(.w-e-bar-item button) {
    font-size: 13px;
    padding: 0 1px;
  }
}
</style>