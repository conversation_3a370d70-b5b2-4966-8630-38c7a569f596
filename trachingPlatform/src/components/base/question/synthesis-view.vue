<template>
  <div class="synthesis-view">
    <div class="question-title">
      <div class="question-l" v-if="showTitle">{{ title }}</div>
      <!-- <div class="question-r" v-if="type==49"> <el-button :class="[isOther?'writevocher':'writeback']" @click="handleOpvocher">{{isOther?'查看凭证':'返回'}}</el-button></div> -->
    </div>
    <div class="question-desc" v-if="desc" style="" v-html="desc"></div>
    <div class="synthesisArrayList" v-for="(item,index) in conchildren" :key="index">
         <div class="synthesisItem">
            <div class="question-name" >
                <div class="questionborder"> <span class="questionnums">第{{index + 1}}题</span>  <span class="questionnumsb">{{ item.title }}</span></div>
            </div>
            <div class="synthesismain">
              <div class="synthesisItem-answer">答案</div>
              <div class="synthesisItem-ansinfo"  v-html="item.type == 49?'见上图': handleAnswerDispatch(item.type,item.optionConfig)"></div>
              <div class="synthesisItem-answeranly">答案解析</div>
              <div class="synthesisItem-answeranlyop" v-html="item.answerDetail"></div>
            </div>
        </div>
  </div>
    
  </div>
</template>

<script>
import { getAnswerDispatch} from '@/components/base/question/util.js';
import eventBus from "@/utils/eventBus";
  export default {
    name: 'synthesis-form-preview',
    props: {
      type: { // 试题类型
      type: String | Number,
      default: ''
    },
    config: { // 试题对应配置项
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    }
    },
    data () {
      return {
      conchildren:[],
      }
    },
    watch: {
      config: {
      handler (val) {
        if(val){
          let configchildren= val.children && val.children.length !==0?val.children:[]
          this.conchildren=configchildren
          //console.log(configchildren,'预览数据++')
        }
      },
      immediate: true
      },
    },
    methods: {
      handleAnswerDispatch(a,b){
        return getAnswerDispatch(a,b)
      },
      handleOpvocher(){
          this.isOther=!this.isOther
          this.$nextTick(() => {
            eventBus.$emit('handlepreviewDatte')
        });
      }
    }
  }
</script>

<style lang="scss">
.synthesis-view{
  display: grid;
  grid-gap: 15px;
  width: 100%;
  .question-title, .question-desc {
    width: 100%;
    word-break: break-all;
  }
  .question-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .question-la{
      border-left: 3px solid #2b66ff;
      padding-left: 10px;
    }
    .writevocher{
        width: 96px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
      .writeback{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
    }
    .synthesisArrayList{
      .synthesisItem{
        width: 100%;
        background: #FFFFFF;
        border: 1px solid #E0E0E0;
        margin-bottom:15px;
        .question-name{
          width: 100%;
          height: 50px;
          background: #F9F9F9;
          border-bottom:1px solid #E0E0E0;
          display: flex;
          align-items: center;
          padding-left: 20px;
          .questionborder{
            border-left: 3px solid #3886FF;
            padding-left: 12px;
            .questionnums{
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
             color: #333333;
            }
            .questionnumsb{
              font-family: PingFang SC, PingFang SC;
              font-weight: 600;
              font-size: 14px;
              color: #333333;
              padding-left: 2px;
            }
          }
        }
        .synthesismain{
          width: 100%;
          height: auto;
          padding-bottom: 20px;
        .synthesisItem-answer{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 20px 20px 20px 20px;
        }
        .synthesisItem-ansinfo{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
        .synthesisItem-answeranly{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #939393;
          padding: 20px 20px 20px 20px;
        }
        .synthesisItem-answeranlyop{
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          padding: 0 0 0 20px;
        }
      }
      }
    }
  :deep(img) {
    max-width: 100%;
  }
}
</style>