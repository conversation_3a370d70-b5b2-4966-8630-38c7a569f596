<template>
  <div class="answer-set-options">
    <div v-if="!disabled && canAdd" class="answer-title">
      <p>答案</p>
      <el-button type="text" plain size="mini" style="padding: 6px 8px;color:#07C392;font-size:14px;" @click="addOption()"> <i class="iconfont icon-xinjian" style="font-size:14px;"></i>添加选项</el-button>
    </div>
    <div v-if="type == 8" class="select-value-content">
      <p class="label">选择：</p>
      <el-select class="default-options" @change="selectOptions">
        <el-option v-for="item in labelList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div v-for="(option, index) in optionsList" :key="option.value" class="options-list-item">
      <el-popover placement="left" trigger="hover"
                  content="点击设为答案"
                  popper-class="option-select-tip-pop">
        <option-select-tag slot="reference" :is-active="option.isAnswer"
                           :text="option.value"
                           @changeActive="changeActive(option)">
        </option-select-tag>
      </el-popover>
      <div class="option-input">
        <el-input v-model="option.label"
                  :size="size"
                  :disabled="disabled"
                  type="textarea"
                  :autosize="{ minRows: 1, maxRows: 10}"
                  @blur="currentFocusIndex = -1"
                  @focus="currentFocusIndex = index">
        </el-input>
      </div>
      <i v-if="!disabled && canDelete" class="el-icon-remove-outline" @click="delOption(index)"></i>
    </div>

  </div>
</template>

<script>
import OptionSelectTag from '../option-tag/index.vue';
import { optionKeyList } from '../util.js';

export default {
  name: 'answer-set-options',
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    canAdd: {
      type: Boolean,
      default: true
    },
    canDelete: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    limit: { // 限制选项个数,大于0才生效
      type: Number,
      default: optionKeyList.length
    },
    overLimitDelete: { // limit>0时，对已存在的数据超出限制个数是否删除
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: ''
    },
    type: { // 试题类型
      type: String | Number,
      default: ''
    },
  },
  components: {
    OptionSelectTag
  },
  computed: {
    optionsList: {
      get () {
        const list = this.config.options || []
        return this.limit && this.overLimitDelete ? list.slice(0, this.limit - 1) : list
      },
      set (val) {
        this.$emit('update: config', {
          ...this.config,
          options: val
        })
      }
    }
  },
  data () {
    return {
      valueList: optionKeyList,
      currentFocusIndex: -1,
      labelList:[
        {
          label: '正确或错误',
          value: 1,
        },{
          label: '对或错',
          value: 2,
        },{
          label: '是或否',
          value: 3,
        },{
          label: '√或×',
          value: 4, 
        },{
          label: '自定义',
          value: 5, 
        }
      ]
    }
  },
  methods: {
    selectOptions(val) {
      if(val == 5) { // 自定义
        this.config.options.forEach((item) => {
          item.label = ''
        })
      }else{
        let target = this.labelList.find(item => item.value == val)
        let list = target.label.split('或')
        this.config.options.forEach((item, index) => {
          item.label = list[index]
        })
      }
    },
    async changeActive (option) {
      if (this.disabled) return
      // await this.$confirm(`确定设置【${option.value}.${option.label}】为答案吗?`, "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      // })
      this.$message.closeAll()
      if (this.multiple) {
        option.isAnswer = !option.isAnswer
        this.$message.success(
          option.isAnswer ?
            `成功将【${option.value}】设置为答案` :
            `成功取消【${option.value}】为答案`
        )
      } else {
        this.optionsList.forEach((op) => {
          op.isAnswer = op.value === option.value
        })
        this.$message.success(`成功将【${option.value}】设置为答案`)
      }

      this.$emit('change', option)
    },
    addOption () {
      if (this.limit && this.optionsList.length >= this.limit) {
        this.$message.warning(`选项最多可添加${this.limit}个！`)
        return
      }

      const val = this.valueList[this.optionsList.length]
      this.optionsList.push({
        value: val,
        label: '',
        isAnswer: false
      })
    },
    delOption (index) {
      this.optionsList.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="scss">
.answer-set-options {
  .answer-title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    p{
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 15px;
      color: #333;
    }
  }
  .options-list-item {
    display: flex;
    margin-bottom: 12px;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    .option-input {
      flex: 1;
      margin-right: 10px;
    }
    .el-icon-remove-outline {
      color: #ccc;
      &:hover {
        color: var(--color-danger);
      }
    }
    .focus-tag-sty {
      background-color: var(--theme_primary_color) !important;
      border-color: var(--theme_primary_color) !important;
      color: #fff;
    }
  }

  .select-value-content{
    display: flex;
    justify-self: flex-start;
    height: 50px;
    line-height: 50px;
    margin-bottom: 10px;
    .label{
      margin-right: 10px;
    }
  }
  .default-options{
    margin-bottom: 20px;
  }
}
</style>
<style>
.option-select-tip-pop {
  min-width: 10px;
  font-size: 13px;
  padding: 5px 12px;
}
</style>