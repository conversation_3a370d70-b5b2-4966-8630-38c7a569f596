
<template>
  <div class="answer-entries-set">
     <div class="uploadfiles">
        <div class="uploaditem">
          <ul class="nav1">
              <li v-for="(item,index) in uploadList" :key="index" class="navitem"
                  @click="tabClick(item, index)">
                  <div :class="[nowIndex == index?'deactive':'defaultclass' ]">{{ item.name }}</div>
                </li>
          </ul>
        </div>
        <div class="uploadarea">
          <el-upload
              class="upload-demo"
              drag
              :headers="{ token: token }"
              :action="actionUrl"          
               multiple
              :on-change="handleFileList"
              :on-success="handlesuccessFile"
              :before-upload="beforeUpload"
              :data="groupData" 
              ref="upload"
              >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                <p><em>点击</em>或将文件拖拽至此区域上传</p>
                <p class="flletype">上传支件支持jpg、png、doc、pdf、xlsx、xls、ppt</p>
              </div>
            </el-upload>
         <div class="upload-listfile" v-if="fileList.length !== 0">
           <div class="uploadjolp" v-if="fileList.length !== 0">文件：共 <span class="uploadnum">{{filenum}}</span> 个</div>
            <div class="uploadshow">
                <div class="uploadel" v-for="(item,index) in fileList">
                   <div class="uploadel-l">
                      <!-- <div class="uppic" v-if="getfilename(item.name) ==='jpg'"><img src="../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='png'"><img src="../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='jpeg'"><img src="../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='docx'"><img src="../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='doc'"><img src="../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='ppt'"><img src="../../../../assets/material/ppt.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='pdf'"><img src="../../../../assets/material/pdf.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xlsx'"><img src="../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xls'"><img src="../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else><img src="../../../../assets/material/video.png"/></div> -->
                      <div class="upwor">
                         <div class="upworfilename" :title="item.name">{{item.name}}</div>
                         <div class="upworsize">{{item.size}}kb</div>
                      </div>
                   </div>
                   <div class="uploadel-ricon">
                     <el-row>
                      <i class="el-icon-view iconsiez" v-if="(getfilename(item.name) ==='png') || (getfilename(item.name) ==='jpg') || (getfilename(item.name) ==='jpeg')" @click="handpriview(item)"></i>
                      <i class="el-icon-download iconsiez" @click="handledownload(item)"></i>
                      <i class="el-icon-circle-close iconsiez1" @click="handleDeletefile(item)"></i>
                     </el-row>
                   </div>
                </div>
            </div>
        </div>
        </div>
        <div class="vocherarea">
            <vocherSet @getEntriesData="handleInitData" :editanwser="editanwser"   :billnum="billnum" :filenum="filesnum"></vocherSet>
        </div>
     </div>

     <div class="preview-img-box"  v-show="isPreview" v-vocherDrag>
        <div class="img-box">
          <img :src="imgurl" />
        </div>
        <div class="icon-box" @click="isPreview = false">
          <i class="el-icon-circle-close icon"></i>
        </div>
      </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/token.js";
import vocherSet from './component/voucher.vue'
export default {
  name: 'answer-entries-set',
  components: {
    vocherSet
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    checkanwser: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      token: getToken(),
      actionUrl: window.FILEIP,
      tableType: 2,
      nowIndex:0,
      isPreview:false,
      uploadList:[
        {
          id:0,
          name:'单据上传'
        },
        {
          id:1,
          name:'附件上传'
        }
      ],
      fileList:[],
      fileTypeList:[],
      editfileList:[],
      filenum:0,
      questionfiles:[],
      lastfileList:[],
      groupData:{
        type:1
      },
      filesnum:0,
      billnum:0,
      imgurl:'',
      editanwser:'',
      editDataanwser:'',
      cacherfile:[],
      cachersufile:[],
      editArray:[],
      resultData:[],
      resultDataload:[],

    }
  },
  created () {
  },
  watch:{
    config: {
      handler(val) {
        if(val){
        //  this.editDataanwser=val?.checkListData
         this.editfileList=val.questionfiles && val.questionfiles.length !== 0?val.questionfiles:val
         this.editArray=val.questionfiles && val.questionfiles.length !== 0?val.questionfiles:val
         this.lastfileList=val.questionfiles && val.questionfiles.length !== 0?val.questionfiles:Array.isArray(val)?val:[]
         let questionFile=val.questionfiles && val.questionfiles.length !== 0?val.questionfiles:Array.isArray(val)?val:null
         if(questionFile){
         if(this.nowIndex==0){
           this.fileList= Array.isArray(questionFile)?questionFile.filter(item => item.type === 1):questionFile.questionfiles.filter(item => item.type === 1)
           this.filenum=this.fileList.length
         }else{
          this.fileList=Array.isArray(questionFile)?questionFile.filter(item => item.type === 0):questionFile.questionfiles.filter(item => item.type === 0)
          this.filenum=this.fileList.length
         }
         const count =  Array.isArray(questionFile)? questionFile.filter(item => item.type === 1).length:questionFile.questionfiles.filter(item => item.type === 1).length
         const num =  Array.isArray(questionFile)? questionFile.filter(item => item.type === 0).length:questionFile.questionfiles.filter(item => item.type === 0).length
         this.billnum=count
         this.filesnum=num
        }
      }
        
      },
      immediate: true,
    },
    checkanwser: {
      handler(val) {
        if(val && val !==''){
          this.editanwser=val
        }
      },
      immediate: true,
    },
  },
  methods: {
    tabClick(data,index){
      this.nowIndex=data.id
      if(data.id==0){
        if(Array.isArray(this.editfileList)){
          this.groupData.type=1
          if(this.fileTypeList.length !==0){
          let editfileList= this.editfileList   
          let lastfileData= this.lastfileList
          // if(lastfileData.length !== 0){
          //   let editArray= this.editArray.filter(item => item.type === 1)
          //   console.log(editArray,'默认数据1---')
          //   if(editArray.length !==0){
          //   this.resultData = editfileList.filter(item1 => 
          //     lastfileData.some(item2 => item2.uid !== item1.uid))
          //   }else{
          //     this.resultData=this.fileTypeList
          //   }

          // }else{
          //   this.resultData = editfileList.filter(item1 => 
          //   lastfileData.some(item2 => item2.uid == item1.uid)
          //   )
          // }
         this.resultData=this.fileTypeList
         let editfileLisdata=this.fileTypeList.concat(this.resultData)
          let hash = {};
          let newArrData = editfileLisdata.reduce((item, next) => {
            hash[next.name] ? '' : hash[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrData?.filter(item => item.type === 1)
          this.lastfileList=newArrData
          this.filenum=this.fileList?.length
          }else{
           let editfileList=this.fileTypeList.length !==0?this.editfileList.concat(this.fileTypeList):this.editfileList
          let lastfileData= this.lastfileList
          const resultData = editfileList.filter(item1 => 
           lastfileData.some(item2 => item2.uid == item1.uid)
          );
          let hash = {};
          let newArrData = resultData.reduce((item, next) => {
            hash[next.name] ? '' : hash[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrData?.filter(item => item.type === 1)
          this.lastfileList=newArrData
          this.filenum=this.fileList?.length
          }
       

        }else{
          this.groupData.type=1
          this.fileList=this.fileTypeList.filter(item => item.type === 1)
          this.filenum=this.fileList?.length
        }
       
      }else{
        if(Array.isArray(this.editfileList)){
          if(this.fileTypeList.length !==0){
          this.groupData.type=0
          let editfileList= this.editfileList   
          let lastfileData= this.lastfileList
          // if(lastfileData.length !== 0){
          //   this.resultData = editfileList.filter(item1 => 
          //     lastfileData.some(item2 => item2.uid !== item1.uid)
          // );
          // }else{
          //   this.resultData = editfileList.filter(item1 => 
          //   lastfileData.some(item2 => item2.uid == item1.uid)
          //   )
          // }
          console.log(this.fileTypeList)
          this.resultData=this.fileTypeList
          let editfileLisdata=this.fileTypeList.concat(this.resultData)
          let hash = {};
          let newArrData = editfileLisdata.reduce((item, next) => {
            hash[next.name] ? '' : hash[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrData?.filter(item => item.type === 0)
          this.lastfileList=newArrData
          this.filenum=this.fileList?.length
        }else{

          this.groupData.type=0
          let editfileList=this.fileTypeList.length !==0?this.editfileList.concat(this.fileTypeList):this.editfileList
          let lastfileData= this.lastfileList
          const resultData = editfileList.filter(item1 => 
           lastfileData.some(item2 => item2.uid == item1.uid)
          )
          let hash = {};
          let newArrData = resultData.reduce((item, next) => {
            hash[next.name] ? '' : hash[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrData?.filter(item => item.type === 0)
          this.lastfileList=newArrData
          this.filenum=this.fileList?.length
        }

        }else{
          this.groupData.type=0
          this.fileList=this.fileTypeList.filter(item => item.type === 0)
          this.filenum=this.fileList?.length
        }
      }
    },
    getfilename(filename){
     let laststr=filename.split('.').pop()
      return laststr
    },
    handpriview(item){
     this.imgurl=item.response?item.response.data:item.url
     this.isPreview=true
    },
    handledownload(item){
      let fileUrl = item.response?item.response.data:item.url;
      let fileName =item.name;
          fetch(fileUrl)
              .then((response) => response.blob())
              .then((blob) => {
                // 创建一个临时的URL对象
                const url = URL.createObjectURL(blob);
                // 创建一个隐藏的<a>标签，并设置其href属性为临时URL
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName; // 设置下载的文件名
                a.style.display = 'none';
                // 将<a>标签添加到文档中，并模拟点击下载
                document.body.appendChild(a);
                a.click();
                // 下载完成后，移除<a>标签和临时URL对象
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              })
              .catch((error) => {
                console.error('下载文件时出错:', error);
          });
    },
    beforeUpload(file) {
      // jpg,.png,.doc,.pdf,.xlsx,.xls,.ppt
      //console.log(file,'SOIS--')
      const allowedTypes = ['image/jpeg', 'image/png','image/jpg','application/msword','application/pdf','application/vnd.ms-powerpoint','application/vnd.ms-excel','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',];
      const isAllowed = allowedTypes.includes(file.type);

      if (!isAllowed) {
        this.$message.error('不支持的文件格式，请上传 JPEG、JPG、PNG、DOC、PDF、XLSX格式的图片或文件');
        return false; // 阻止上传
      }
      return true; // 允许上传
    },
    handleFileList(file, fileList){
      if (file.status === 'success') {
        this.cacherfile.push(file) 
      }
      let fileListdata=this.cacherfile
      fileListdata.forEach(item => {
        item.size = Math.floor(item.size/1024) 
        item.type=(item.name.split('.').pop() === 'jpg')||(item.name.split('.').pop() === 'png')?1:0
      });
      let hash = {};
      let newArr = fileListdata.reduce((item, next) => {
        hash[next.name] ? '' : hash[next.name] = true && item.push(next);
        return item
      }, [])
      if(this.nowIndex==0){
        this.groupData.type=1
        if(Array.isArray(this.editfileList)){
          let editfileList=this.editfileList.filter(item => item.type === 1)
          let editArray= this.editArray
          if(editArray.length !==0){
             this.resultDataload = editArray
          }else{
            this.resultDataload = editfileList.filter(item1 => 
            editArray.some(item2 => item2.uid == item1.uid)
          );
          }
          let fileList=newArr.filter(item => item.type === 1)
          let fileDataLists=fileList.concat(this.resultDataload)
          let hashs = {};
          let newArrDatas = fileDataLists.reduce((item, next) => {
            hashs[next.name] ? '' : hashs[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrDatas.filter(item => item.type === 1) 
          let fileTypeLists=newArr.concat(this.resultDataload)
          let hash1s = {};
          let newArr1s = fileTypeLists.reduce((item, next) => {
            hash1s[next.name] ? '' : hash1s[next.name] = true && item.push(next);
            return item
          }, [])

          this.fileTypeList=newArr1s
          this.filenum=this.fileList.length
          this.billnum=this.fileList.length
          this.lastfileList=newArrDatas
          }else{
          this.fileList=newArr.filter(item => item.type === 1)
          this.fileTypeList=newArr
          this.filenum=this.fileList.length
          this.billnum=this.fileList.length
          this.lastfileList=newArr
          let fileList=newArr.filter(item => item.type === 0)
           if(fileList.length!==0){
             this.filesnum=fileList.length
           }
          }

      }else{
        if(Array.isArray(this.editfileList)){
          let editfileList=this.editfileList.filter(item => item.type === 0)
          let editArray= this.editArray
          if(editArray.length !==0){
             this.resultDataload =editArray
        
          }else{
            this.resultDataload = editfileList.filter(item1 => 
            editArray.some(item2 => item2.uid == item1.uid)
          );
        }

          let fileList=newArr.filter(item => item.type === 0)
          let fileDataList=fileList.concat(this.resultDataload)
          let hash = {};
          let newArrData = fileDataList.reduce((item, next) => {
            hash[next.name] ? '' : hash[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileList=newArrData.filter(item => item.type === 0)
          let fileTypeList=newArr.concat(this.resultDataload)
          let hash1 = {};
          let newArr1 = fileTypeList.reduce((item, next) => {
            hash1[next.name] ? '' : hash1[next.name] = true && item.push(next);
            return item
          }, [])
          this.fileTypeList=newArr1
          this.filenum=this.fileList.length
          this.filesnum=this.fileList.length
          this.lastfileList=newArrData
        }else{
          this.groupData.type=0
          this.fileList=newArr.filter(item => item.type === 0)
          this.fileTypeList=newArr
          this.filenum=this.fileList.length
          this.filesnum=this.fileList.length
          this.lastfileList=newArr
          let fileList=newArr.filter(item => item.type === 1)
           if(fileList.length!==0){
             this.billnum=fileList.length
           }
         }
      }
    },
    handlesuccessFile(response, file, fileList){
      if (file.status === 'success') {
        this.cachersufile.push(file) 
      } 
     let fileListdata=this.cachersufile
     let hash = {};
     let newArr = fileListdata.reduce((item, next) => {
        hash[next.name] ? '' : hash[next.name] = true && item.push(next);
        return item
      }, [])
     //let filearr=[]
     let fileuid=[]
     newArr.forEach(item => {
        item.size = Math.floor(item.size/1024) 
        // filearr.push({
        //      url:item.response.data,
        //      type:(item.name.split('.').pop() === 'jpg')||(item.name.split('.').pop() === 'png')?1:0
        //   })
        fileuid.push({
          uid:item.uid,
          name:item.name,
          size:item.size,
          url:item.response.data,
          type:(item.name.split('.').pop() === 'jpg')||(item.name.split('.').pop() === 'png')?1:0
        })
       })
      this.lastfileList=fileuid
      this.config.questionfiles={questionfiles:fileuid}
    },
    handleDeletefile(task){
      this.fileList = this.fileList.filter(item => item.uid !== task.uid);
      this.fileTypeList = this.fileTypeList.filter(item => item.uid !== task.uid);
      this.cacherfile = this.cacherfile.filter(item => item.uid !== task.uid);
      this.lastfileList= this.lastfileList.filter(item => item.uid !== task.uid)
      this.config.questionfiles={questionfiles: this.lastfileList}
      const count =  this.lastfileList.filter(item => item.type === 1).length;
      const num =  this.lastfileList.filter(item => item.type === 0).length;
      this.filenum= this.nowIndex==0 ?count:num
      this.editArray=this.lastfileList
      this.filesnum=num
      this.billnum=count
    },
    handleInitData(val){
      if(!val){
      this.$message.error('凭证借贷不平衡，请检查');
      }
      this.config.checkListData = val || ''
      this.config.questionfiles={questionfiles:this.lastfileList}
    }
  }
}
</script>

<style lang="scss">
.answer-entries-set {
  padding-top: 6px;
  :deep(.gc-designer-container) {
    width: 1100px !important;
  }
  .uploadfiles{
    .uploaditem{
      .nav1 {
            display: flex;
            position: relative;
            padding: 0 5px 1px 5px;
            overflow-x: auto;
            white-space: nowrap;
            ::-webkit-scrollbar {
                display: none;
            }
            .navitem {      
              cursor: pointer;
              text-align: center;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              line-height: 40px;
              margin-right: 11px;
              .defaultclass{
                width: 100%;
                height: 24px;
                line-height: 24px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                padding: 0 12px;
                color: #333;
                background: #E6E6E6;
                border-radius: 3px 3px 3px 3px;  
              }
              .deactive {
                width: 100%;
                height: 24px;
                line-height: 24px;
                border-radius: 4px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;     
                padding: 0 12px;
                color: #fff;
                border-radius: 3px 3px 3px 3px;  
                background-color: #2B66FA;
            }
            }
        }
    }
    .uploadarea{
        padding-top: 20px;
        .el-upload{
          width: 100%;
        .el-upload-dragger{
          width: calc(100% - 1px);
        }
      }
      .el-upload-list{
        display: none;
      }
      .el-upload__text{
        .flletype{
          font-family: Source Han Sans SC, Source Han Sans SC;
          font-weight: 400;
          font-size: 12px;
          color: #A7A7A7;
          padding-top: 5px;
        }
      }

      .upload-listfile{
        padding-top: 15px;
        .uploadjolp{
          font-family: Source Han Sans SC, Source Han Sans SC;
          font-weight: 400;
          font-size: 14px;
          color: #A7A7A7;
          .uploadnum{
            font-family: Source Han Sans SC, Source Han Sans SC;
            font-weight: 400;
            font-size: 14px;
            color: #2B66FA;
          }
        }
        .uploadshow{
          padding-top: 20px;
          .uploadel{
             display: flex;
             justify-content: space-between;
             align-items: center;
             margin-bottom: 20px;
             .uploadel-l{
              display: flex;
              align-items: center;
              .upwor{
                padding-left: 10px;
                .upworfilename{
                  max-width: 720px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  padding-bottom: 1px;
                }
                .upworsize{
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #B1B1B1;
                }
              }
              }
             .uploadel-ricon{
               .iconsiez{
                padding-right: 14px;
                font-size: 19px;
                color: #909090;
                cursor: pointer;
               }
               .iconsiez1{
                font-size: 19px;
                color: #909090;
                cursor: pointer;
               }
             }
          }
        }
      }
    }
    .vocherarea{
       padding-top: 20px;
    }
  }
  .preview-img-box {   
      cursor: grab;
      user-select: none; /* 防止文字被选中 */
      position: absolute;
      left: 22%;
      top: 22%;
      z-index: 10;
      .img-box {
        max-width: 1100px;
        height: auto;

        img {
          width: 100%;
          height: 100%;
        }
      }
      .icon-box {
        width: 26px;
        height: 26px;
        position: absolute;
        // right: -20px;
        // top: -23px;
        right: 0;
        top: 0;
        cursor: pointer;
        .icon {
          font-size: 26px;
          color: #222;
        }
      }
    }
}
</style>