<template>
  <el-dialog v-dialogDrag ref="elDialog" v-show="isOpen" v-if="dialogVisible" :visible.sync="dialogVisible" :width="calcWidth"
    :class="`${showMinus && isMinus ? 'minus-dialog' : ''} ${isActive ? 'dialog--active' : ''}`"
    :custom-class="`${$attrs.className || ''} base-dialogs ${isFull ? 'full-dialog' : ''} `" 
    :modal="$attrs.modal === true ? true : false" :fullscreen="isFull" v-bind="$attrs" destroy-on-close
    :append-to-body="appendToBody" @open="handleOpen" @opened="handleOpened" @close="handleClose" @closed="handleClosed"
    :height="height"
    :show-close="false"
    >

    <!-- 自定义title -->
    <template v-if="customTitle" v-slot:title>
      <slot name="title"></slot>
    </template>

    <!-- 没有title -->
    <template v-else v-slot:title>
      <span class="base-dialog-title">{{ title }}</span>
      <slot name="headerActions"></slot>
    </template>
    <slot v-if="dialogVisible"></slot>

  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import eventBus from '@/utils/eventBus'
export default {
  name: 'BaseDialog',
  props: {
    controlName: { // 给dialog名字，用于控制dialog的显隐
      type: String,
      default: ''
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    showMinus: {
      type: Boolean,
      default: false
    },
    hasFull: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: 'medium'
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    defaultButtons: {
      type: Array,
      default: () => {
        return [1, 1]
      }
    },
    noFooter: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      topicId: uuidv4(),
      isMinus: false,
      height: '',
      zIndexStyle: {},
      isFull: false // 是否全屏
    }
  },
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:visible', val)
      },
      get() {
        return this.visible
      }
    },
    isActive() {
      return this.$store?.getters["dialog/active"]?.name === this.controlName
    },
    isOpen() {
      // 通过store控制dialog的显隐
      return !this.controlName || this.$store.getters['dialog/allOpend'].find(d => d.name === this.controlName)
    },
    customTitle() {
      return this.$slots.title
    },
    calcWidth: {
      get() {
        switch (this.width) {
          case 'small':
            return '30%'
          case 'medium':
            return '28%'
          case 'min-large':
            return '40%'
          case 'large':
            return '90%'
          default:
            return this.width
        }
      }
    }
  },
  watch:{
    fullscreen: {
      handler(n, o) {
        this.isFull = n;
      },
      immediate: true,
    },
  },
  mounted() {
    // const zIndex = this.$store.getters['dialog/zIndex']
    // if (zIndex) {
    //   this.zIndex = zIndex
    //   this.zIndexStyle.zIndex = zIndex
    // }
    // if (this.controlName) {
    //   this.$store.dispatch("dialog/open", {
    //     name: this.controlName,
    //     zIndex,
    //     instance: this
    //   })
    // }
    // 初始化时否全屏
    this.isFull = !!this.fullscreen
  },
  methods: {
    recoverShow() {
      this.isMinus = false
    },
    handleMinus() {
      this.isMinus = true
      eventBus.$emit('dlg-minus-broadcast', {
        name: this.controlName,
        title: this.title,
        topicId: this.topicId,
        recoverShow: () => {
          this.recoverShow()
        }
      })
    },
    handleOpen() {
      this.$listeners.open && this.$listeners.open()
    },
    handleOpened() {
      if (this.$listeners.opened) {
        this.$listeners.opened()
      }
    },
    handleClose() {
      this.$listeners.close && this.$listeners.close()
      this.$emit('update:visible', false)
    },
    handleClosed() {
      this.$listeners.closed && this.$listeners.closed()
      this.$emit('update:visible', false)
    },
    handleReset() {
      const b = this.$attrs['before-close'] || this.$attrs.beforeClose
      if (b) {
        b()
      } else {
        this.$emit('update:visible', false)
      }
    },
    handleSubmit() {
      if (this.$listeners.submit) {
        this.$emit('submit')
      } else {
        this.$emit('update:visible', false)
      }
    },
    toggleScreen(e) {
      this.isFull = !this.isFull
      e.preventDefault();
      e.stopPropagation();
      eventBus.$emit('screenChange')
    },
    getDialogPosition() {
      return { ...this.zIndexStyle }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__wrapper {
  pointer-events: auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
  // z-index: 2100 !important;
}

.minus-dialog {
  visibility: hidden !important;
}

::v-deep .base-dialogs {
  min-height: 475px;
  pointer-events: auto;
  margin: 0 0 0 0;
  margin-top: 0 !important;
  align-self: center;
  // box-shadow: 0 1px 3px rgba(0,0,0,.9);


  &.resize-dialog.flex-content-dialogs {
    .el-dialog__body {
      // min-height: 59vh;
      // max-height: 61vh;
      // height: 60vh;
      // width: 100%;
      // overflow: auto;
    }
  }

  &.full-dialog {
    left: 0 !important;
    top: 0 !important;
    transform: none;

    .el-dialog__body {
      min-height: 0 !important;
      max-height: calc(100% - 48px) !important;
      height: calc(100% - 48px);
      width: 100%;
    }
  }

  &.flex-content-dialogs {
    display: flex;
    flex-direction: column;

    border-radius: 4px;
    background: #232531;
    box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.5);
    // border-top: 2px solid;
    // border-image: linear-gradient( 179deg, #2B66FF 0%, #2BC5FF 100%) 1;
    .el-dialog__body {
      
      min-height: 0;
      display: flex;
      flex-direction: column;

      .content-wrapper {
        flex: 1 1 auto;
        min-height: 0;
      }

      .dialog-footer {
        padding: 10px 20px;
        padding-bottom: 0;
      }
    }
  }

  .el-dialog__header {
    height: 48px;
    box-sizing: border-box;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    padding: 0 2px 0 2px;
    flex: 0 0 auto;
    .el-dialog__headerbtn {
      top: 29px;

      .el-icon-close {
        font-size: 22px;
      }
    }

    .base-header {
      display: flex;
      flex: 1 1 auto;
      justify-content: space-between;
      align-items: center;
      padding-right: 30px;

      .title {
        margin: 0;
      }

      .full-screen-icon {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }

  .el-dialog__body {
    overflow: auto;
    flex: 1 1 auto;
    padding: 10px 20px 15px 20px;
    // padding-top: 0;
  }

  .dialog-footer {
    display: flex;
    flex:0 0 auto;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    padding-top: 10px;
    // border-top: 1px solid #ccc;
  }
  .dynamic-spread .flow-img-btn {
    right: 70px;
    bottom: 20px;
    padding: 0;
  }
}
</style>
