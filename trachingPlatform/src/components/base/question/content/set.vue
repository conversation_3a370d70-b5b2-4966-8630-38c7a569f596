<template>
  <div class="answer-content-set">
    <ZdEditor
        v-if="!loading"
        :editor-default-html="config.html"
        :editor-config="{
          height: '200px',
          placeholder: '填空题编辑区'
        }"
        :comp-config="{
          toolbarKeys: [],
          insertKeys: {
            keys: ['compInputTag', 'compSelectTag']
          }
        }"
        :editorState="'edit'"
        @editorHtml="editorHtml">
    </ZdEditor>
    <el-popover placement="bottom-start" title="说明" width="380" trigger="hover">
      <div style="font-size: 13px;">
        <div style="padding-top: 5px; padding-bottom: 10px;">
          <span style="font-weight: 700;">插入文本框：</span>若有多个答案时，用“|”隔开。如三元五角 | 3.5元
        </div>
        <div>
          <span style="font-weight: 700;">插入单选下拉框：</span>设置选中的为正确答案，如
          <el-select value="1" placeholder="请选择" size="mini" style="width: 80px;">
            <el-option label="3.5元" value="1"></el-option>
            <el-option label="三元五角" value="2"></el-option>
          </el-select>
        </div>
      </div>
      <div slot="reference" class="tip-span">说明 <i class="iconfont icon-wenhao"></i></div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'answer-content-set',
  components: {
    'ZdEditor': () => import("@/components/zd-editor/index.vue")
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: true
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      if (this.config.settingArr) {
        sessionStorage.setItem('courseContents', JSON.stringify(this.config.settingArr))
      } else {
        sessionStorage.setItem('courseContents', JSON.stringify([]))
      }
      this.loading = false
    },
    editorHtml (val) {
      this.config.html = val || ''
      const courseContents = sessionStorage.getItem('courseContents')
      this.config.settingArr = courseContents ? JSON.parse(courseContents) : []
    }
  },
  destroyed () {
    sessionStorage.removeItem('courseContents')
  }
}
</script>

<style scoped lang="scss">
.answer-content-set {
  position: relative;
  .tip-span {
    position: absolute;
    top: 14px;
    right: 11px;
    font-size: 13px;
    color: #999;
    cursor: pointer;
    i {
      font-size: 13px;
    }
  }
  :deep(.w-e-bar-item button) {
    font-size: 13px;
    padding: 0 1px;
  }
  :deep(.w-e-bar svg) {
    width: 30px;
    height: 30px;
  }
  :deep(.zd-editor) {
    p {
      line-height: 36px;
    }
  }
}
</style>