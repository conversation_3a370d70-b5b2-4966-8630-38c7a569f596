<template>
  <el-select
    v-bind="$attrs"
    clearable
    placeholder="请选择"
    v-on="$listeners"
    @change="handleChange"
  >
    <el-option
      v-for="item in columnOptions"
      :key="item.value"
      :label="item.label+'('+item.value+')'"
      :value="item.value">
    </el-option>
  </el-select>
</template>

<script>
import { loadErColumnsById } from '@/features/loadErColumns';
export default {
  name: 'FusionFrontErSelect',
  props: {
    curScene: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableId: '',
      columnOptions: []
    };
  },

  mounted() {
    this.loadColumns();
  },

  methods: {
    handleChange(val) {
      this.$emit("selectChange", {
        item: this.columnOptions.find(c => c.value === val),
        tableId: this.tableId
      });
    },
    async loadColumns() {
      const { columnOptions, tableId } = await loadErColumnsById(this.$api, this.curScene.erId, col => {
        return {
          value: col.columnCode,
          label: col.columnName
        };
      });
      this.tableId = tableId;
      this.columnOptions = columnOptions;
    }
  },
};
</script>

<style lang="scss" scoped></style>
