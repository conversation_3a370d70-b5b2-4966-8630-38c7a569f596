
<template>
  <div class="base-scene-page">
    <div class="scene-page-header">
      <div
        class="scene-title"
        v-if="title">
        {{ title }}
      </div>
      <div class="toolbar">
        <ToMobileButton
          v-if="mobileData"
          :getMobileDynamicConfig="getMobileDynamicConfig"
          class="to-mobile-button"
          :item="mobileData"
          :typeName="mobileData.typeName"
          :contentType="mobileData?.contentType" />
        <ScreenButton
          class="btn-item screen-btn"
          icon="action-icon iconfont icon-toupingshebei"
          type="primary"
          size="small"
          @click="handleDataScreen"
          title="选择数据投屏"
          >数据投屏</ScreenButton
        >
        <ScreenButton
          class="btn-item screen-btn"
          icon="action-icon iconfont icon-touping"
          type="primary"
          size="small"
          title="投放当前内容"
          @click="handleScreen"
          >投屏</ScreenButton
        >
      </div>
    </div>

    <div class="scene-content-wrapper">
      <slot :selectable="selectable"></slot>
    </div>
    <ScreenChooseDlg
      v-if="screenVisible"
      :visible.sync="screenVisible"
      :url="url"
      :config="config" />
    <BatchScreen
      v-if="dialogVisibleBatchScreen"
      :visible.sync="dialogVisibleBatchScreen"
      :source="dataItems"
      @submit="handleBatchScreenSubmit"></BatchScreen>
    <ClassroomChooseDialog
      v-if="classroomDialogVisible"
      :visible.sync="classroomDialogVisible"
      @confirm="handleClassroomConfirm" />

      <!-- 53客服 -->
      <serverDialog />
  </div>
</template>

<script>
import ToMobileButton from "@/components/mobile/to-mobile.vue";
import ScreenButton from "@/components/screen/screen-button.vue";
import ClassroomChooseDialog from "@/components/screen/classroom-choose-dialog.vue";
import eventBus from "@/utils/eventBus";
import BatchScreen from "@/components/screen/batch-screen-data-dlg.vue";
import ScreenChooseDlg from "@/components/screen/screen-choose-dlg.vue";
import serverDialog from "@/components/server-dialog/serve-btn.vue"
import { CLASSROOM_ID, getCookies } from "@/utils/cookies";
import sortBy from "lodash/sortBy";
import { getToken } from "@/utils/token.js";
import { mapGetters } from "vuex";

export default {
  name: "FusionFrontPage",
  components: {
    ToMobileButton,
    ScreenButton,
    ClassroomChooseDialog,
    BatchScreen,
    ScreenChooseDlg,
    serverDialog
  },
  props: {
    getMobileDynamicConfig: {
      type: Function,
    },
    title: {
      // 页面标题
      type: String,
      default: ""
    },
    mobileData: {
      type: Object,
      default: () => {}
    },
    url: {
      // 投屏的url
      type: String,
      default: ""
    },
    config: {
      // 投屏的页面配置
      type: Object,
      default: () => {}
    },
    getBatchScreenData: {
      // 需要投屏的数据
      type: Function,
      default: () => {}
    }
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"]
    })
  },
  data() {
    return {
      deviceList: [],
      screenType: "", // 投屏类型，批量batch或者单个single
      classroomDialogVisible: false,
      dataItems: [],
      classRoomId: getCookies(CLASSROOM_ID),
      dialogVisibleBatchScreen: false,
      screenVisible: false
    };
  },
  async created() {},
  mounted() {
    eventBus.$on("classroomChoosed", () => {
      this.classRoomId = getCookies(CLASSROOM_ID);
    });
    this.getDeviceList();
  },
  destroyed() {
    eventBus.$off("classroomChoosed");
  },
  methods: {
    async getDeviceList() {
      if (getCookies(CLASSROOM_ID)) {
        const res = await this.$api.GetClassRoomDeviceList({
          params: {
            classRoomId: getCookies(CLASSROOM_ID)
          }
        });
        if (res.code === 200) {
          const subs = [];
          res.data.forEach(item => {
            // 避免数据key重复
            item.deviceId = item.id;
            item.deviceName = item.name;
            if (item.isMain) {
              // this.mainDevice = item;
            } else {
              subs.push(item);
            }
          });
          this.deviceList = sortBy(subs, "sort");
        }
      }
    },
    handleClassroomConfirm(classRoomId) {
      this.classRoomId = classRoomId;
      if (this.screenType === "batch") {
        // this.selectable = true;
        // this.dataItems = this.getBatchScreenData();
        // this.dialogVisibleBatchScreen = true;
        this.doBatchScreen();
      } else {
        this.screenVisible = true;
      }
    },
    handleProcess() {},
    // 投屏回调 screen: array[7]
    handleBatchScreenSubmit() {
      // todo
      this.dialogVisibleBatchScreen = false;
    },
    doBatchScreen() {
      const { items, msg } = this.getBatchScreenData();
      if (items.length > 0) {
        this.dataItems = items;
        this.$api
          .BatchScreenProjection({
            devices: this.deviceList
              .map((item, index) => {
                const rowData = this.dataItems[index];
                return {
                  deviceId: item.id,
                  data: {
                    deviceId: item.id,
                    classRoomId: this.classRoomId,
                    isMain: item.isMain,
                    deviceName: item.name,
                    token: getToken(),
                    url: rowData?.url,
                    config: item.config
                  }
                };
              })
              .filter(d => d.data.url)
          })
          .then(res => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "投屏成功。"
              });
            }
          });
      } else {
        this.$message.warning(msg || "请最少选择一条数据");
      }
    },
    handleDataScreen() {
      this.screenType = "batch";
      if (this.classRoomId) {
        // this.selectable = true;
        // this.dataItems = this.getBatchScreenData();
        // this.dialogVisibleBatchScreen = true;
        this.doBatchScreen();
      } else {
        this.classroomDialogVisible = true;
      }
    },
    async handleScreen() {
      this.screenType = "single";
      if (this.classRoomId) {
        this.screenVisible = true;
      } else {
        this.classroomDialogVisible = true;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.base-scene-page {
  height: 100%;
  display: flex;
  background: #fff;
  padding: 10px;
  border-radius: 5px;
  padding-bottom: 0;
  flex-direction: column;
  position: relative;

  .scene-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
  }

  .screen-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
  }

  .splitpanes__pane {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Helvetica, Arial, sans-serif;
    color: rgba(255, 255, 255, 0.6);
    font-size: 5em;
  }

  .toolbar {
    // height: 50px;
    display: flex;
    flex: 1 1 auto;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px;
    position: absolute;
    right:0;
    top: -25px;
  }

  .knowledge-btn {
    margin-left: 20px;
  }

  .scene-content-wrapper {
    height: 100%;
    min-height: 0;
    flex: 1 1 auto;
  }
}
</style>
