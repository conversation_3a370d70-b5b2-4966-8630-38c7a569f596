<template>
    <div>
        <!-- <el-button class="import-btn" type="primary" @click="openFileSelector"> 
            <i class="iconfont icon-shujudaoru"></i> 导入
        </el-button> -->
        <!-- 隐藏的文件输入框 -->
        <input
            ref="fileInput"
            type="file"
            style="display: none;"
            @change="handleFileSelect"
        />

        <el-upload
            :action="actionUrl"
            :headers="headers"
            :data="data"
            :show-file-list="false"
            :on-success="handleSuccess">
            <el-button class="import-btn" type="primary" > 
                <i class="iconfont icon-shujudaoru"></i> 导入
            </el-button>
        </el-upload>
    </div>
</template>

<script>
import token from "@/utils/token.js";
export default {
    props: {
        type: { // 
            type: String,
            default: 'Student', // 
        } 
    },
    data() {
        return {
            // 可按需添加其他数据,
            actionUrl:`${window.PUBLICHOSTA}/Excel/Import`,
            data:{
                type:this.type, //
                schoolId: token.getSchoolId() || "673583370671685",
            },
            headers:{
                Authorization: token.getToken(),
                schoolId: token.getSchoolId() || "673583370671685",
            },
        }
    },
    mounted() {
        // 初始化时设置文件输入框的accept属性，根据type的值动态设置
        this.data.type = this.type; // 初始化data
    },
    methods: {
        // 打开文件选择器
        openFileSelector() {
            this.$refs.fileInput.click();
        },
        // 处理文件选择事件
        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                // 这里可以添加文件验证逻辑，例如文件类型、大小等
                // 触发导入事件，将文件传递给父组件
                this.$emit('importEvent', file);
                // 清空文件输入框，以便下次选择相同文件时也能触发事件
                this.$refs.fileInput.value = '';
            }
        },
        handleSuccess(res, file, fileList) {
            if(res.errCode==0){
                if(res.data.errors){
                    let message = res.data.errors.map(item=>{return item.msgs}).join(';')
                    this.$message({
                        message: message,
                        type:'error', 
                    })
                }else{
                    this.$message({
                        message: '导入成功',
                        type:'success',
                    })
                }
                this.$emit('importEvent');
            }
           
        }
    }
}
</script>

<style scoped lang="scss">
.import-btn {
    width: 80px;
    height: 38px;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E7E7E7; 
    padding:0;
    color:#333;
    margin-left: 10px;
    &:hover{
        background: #E7E7E7;
    }
}
</style>