<template>
    <div>
        <el-button :style="{width:widths}" @click="reflashEvent" class="reflash-btn-item" type="primary"> <i class="iconfont icon-shuaxin"></i> {{label}}</el-button>
    </div>
</template>

<script>
export default {
    props: {
        // 新增按钮
        label: {
            type: String,
            default: '刷新'
        },
        widths: {
            type: String,
            default: '80px'
        },
    },
    data() {
        return {
            // 新增按钮
        }
    },
    methods: {
        // 刷新按钮
        reflashEvent() {
            this.$emit('reflashEvent');
        } 
    }
}
</script>

<style lang="scss" scoped>
.reflash-btn-item {
    width: 80px;
    height: 38px;
    background: #fff;
    border-radius: 4px;
    padding: 0;
    margin-left: 10px;
    border: none;
    color: #333;
    &:hover{
        background: #E7E7E7;
    }
}
</style>