<template>
    <div>
        <el-button :style="{width:widths}" @click="addEvent" class="add-btn-item" type="primary"> <i class="iconfont icon-xinjian"></i> {{label}}</el-button>
    </div>
</template>

<script>
export default {
    props: {
        // 新增按钮
        label: {
            type: String,
            default: '添加'
        },
        widths: {
            type: String,
            default: '80px'
        },
    },
    data() {
        return {
            // 新增按钮
        }
    },
    methods: {
        // 新增按钮
        addEvent() {
            this.$emit('addEvent');
        } 
    }
}
</script>

<style lang="scss" scoped>
.add-btn-item {
    width: 80px;
    height: 38px;
    background: #07C392;
    border-radius: 4px;
    padding: 0;
    border: none;
    color: #fff;
    &:hover{
        background: #10D3A0;
    }
}
</style>