<template>
    <div>
        <el-button class="export-btn" type="primary" @click="exportEvent"> <i class="iconfont icon-daochu"></i> 导出</el-button>
    </div>
</template>

<script>
export default {
    data() {
        return {
            
        }
    },
    methods: {
        exportEvent() {
            this.$emit('exportEvent')
        } 
    }
}
</script>

<style lang="scss" scoped>
.export-btn{
    width: 80px;
    height: 38px;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E7E7E7; 
    padding:0;
    color:#333;
    margin-left: 10px;
    &:hover{
        background: #E7E7E7;
    }
}
</style>