### 弹窗组件 ###
基于 el-dialog的 二次封装

**Attributes**只列出需要注意的

| 参数            | 必填 | 说明                                                         | 类型     | 可选值    | 默认值 |
| --------------- | :--- | :----------------------------------------------------------- | :------- | :-------- | :----- |
| visible         | 是   | 是否显示 Dialog，                                            | boolean  | —         | false  |
| title           | 否   | Dialog 的标题，slot="title" 只能二选一                       | string   | —         | —      |
| modal           | 否   | el-dialog 默认值为true，本组件改为false                      | boolean  | —         | false  |
| before-close    | 否   | 需要在beforeClose中设置visible=false，且会被默认取消按钮会执行 | function | —         | —      |
| default-buttons | 否   | 没有slot="footer"时，使用默认的【取消】【确定】按钮，0表示不显示，1表示显示。 | Array    | [1/0,1/0] | [1,1]  |
| hasFull         | 否   | 是否含有全屏按钮，初始化为fullscream的值                     | boolean  |           | false  |
| drag         | 否   | 是否支持拖动/缩放/全屏，为true时，可以穿透点击遮罩下的元素                     | boolean  |           | false  |
| 其他            | —    | 参考el-dialog                                                | —        | —         | —      |

**slot** 参考el-dialog

**Events** 

| 事件名称 | 说明                 | 回调参数 |
| -------- | -------------------- | -------- |
| submit   | 默认【确定】按钮回调，默认值等同于visible = false | —        |
| reset    | 默认【取消】按钮回调，默认值等同于visible = false | —        |
| 其它     | 参考el-dialog        | —        |

**demo**

```vue
<template>
  <div class="test-base">
  <el-button type="primary" @click="visible=true">test dialog</el-button>
  <base-dialog
    :visible.sync="visible" 
    :fullscreen="isFull"
    :before-close="beforeClose"
    @opened="testDialogOpend"
  >
    <template slot="title">
      <div class="header-bar">
        <div class="title">{{ title }}</div>
        <i class="full-screen-icon el-icon-full-screen" @click="toggleScreen"></i>
      </div>
    </template>
    这是一段内容
    <!--   如果需要自定义footer ，需要自己写弹窗关闭事件，如果有before-close也需要自定义执行
    <template slot="footer">
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
    -->
  </base-dialog>
  </div>
</template>
<script>
export default {
  name: 'TestBase',
  data() {
    return {
      visible: false,
      title: '弹窗演示',
      isFull: false,
    }
  },
  methods: {
    beforeClose() {
      console.log("dialog before closed")
      this.visible = false;
    },
    testDialogOpend() {
      console.log("dialog opend")
    },
    toggleScreen() {
      this.isFull = !this.isFull
    },
  }
}
</script>
 
<style lang="scss" scoped>
.test-base {
  .header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 30px;

    .right-action {
      padding-right: 10px;

      .full-screen-icon {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
}
</style>
```

