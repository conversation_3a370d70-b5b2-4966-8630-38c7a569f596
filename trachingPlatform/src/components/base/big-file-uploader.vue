

<template>
  <div class="big-file-uploader">
    <base-dialog
      :modal="true"
      :title="title"
      :visible.sync="visible"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      append-to-body
      width="520px">
      <el-form
        ref="fileForm"
        class="dialog-form fileForm">
        <el-upload
          :accept="accept"
          v-show="!uploading"
          class="avatar-uploader"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange">
          <el-button
            slot="trigger"
            icon="el-icon-folder-opened"
            class="selectFileBtn"
            >选择文件...</el-button
          >
          <el-tooltip
            v-if="uploadTip"
            effect="light"
            :content="uploadTip"
            placement="top-start">
            <i class="el-icon-question tip"></i>
          </el-tooltip>
        </el-upload>
        <div
          class="file-info"
          v-if="file">
          <div>
            文件：<img
              v-if="fileIcon"
              :src="fileIcon" />{{ file.name }}
          </div>
          <div>
            <i
              class="el-icon-loading"
              v-if="uploading"></i
            >{{ progressTitle }}
          </div>
          <el-progress :percentage="percentage"></el-progress>
        </div>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button
          v-show="!uploading && file"
          type="primary"
          @click="requestUpload"
          >开始上传</el-button
        >
      </span>
    </base-dialog>
  </div>
</template>

<script>
export default {
  name: "BigFileUploader",
  data() {
    return {
      uploadTip: null,
      file: null,
      fileIcon: null,
      uploading: false,
      uploadId: null,
      startTime: 0,
      uploadedLength: 0,
      formatFileSize: null,
      percentage: 0,
      progressTitle: null,
      chunks: [],
      uploadedCount: 0,
      chunkCount: 0,
      abort: false,
      abortRequest: false
    };
  },
  props: {
    title: {
      type: String,
      default: "上传文件"
    },
    visible: {
      type: Boolean,
      default: false
    },
    /**
     * 文件类型（默认所有类型）
     */
    accept: {
      type: String,
      default: "*"
    },
    /**
     * 文件大小限制，单位 MB（默认无限制）
     */
    maxFileSize: {
      type: Number,
      default: 0
    },
    /**
     * 分片大小，单位字节（默认 1MB）
     */
    chunkSize: {
      type: Number,
      default: 1024 * 1024 * 1
    },
    /**
     * 并发上传数量（默认 2）
     */
    concurrencyCount: {
      type: Number,
      default: 2
    },
    bucketName: {
      type: String,
      default: null
    },
    /**
     * 存储系统目录
     */
    systemDir: {
      type: String,
      default: "default"
    },
    /**
     * 存储模块目录
     */
    moduleDir: {
      type: String,
      default: "default"
    }
  },
  mounted() {
    this.getUploadTip();
  },
  methods: {
    async requestUpload() {
      this.uploading = true;
      this.startTime = Date.now();
      this.progressTitle = `初始化上传...（0/${this.formatFileSize}）`;
      await this.initUpload();
      this.uploadChunks();
    },
    async initUpload() {
      const data = {
        systemDir: this.systemDir,
        moduleDir: this.moduleDir,
        fileName: this.file.name
      };
      if (this.bucketName) {
        data.bucketName = this.bucketName;
      }

      this.uploadId = await this.sendCommonRequest(
        window.FileChunkUpload.InitUpload,
        data
      );
    },
    uploadChunks() {
      this.getChunkFormDatas();
      if (this.chunks.length === 0) {
        return;
      }
      this.chunkCount = this.chunks.length;
      this.uploadedCount = 0;
      this.progressTitle = `正在上传...（0/${this.formatFileSize}）`;
      const initCount = Math.min(this.concurrencyCount, this.chunks.length);
      for (let i = 0; i < initCount; i++) {
        this.uploadChunk(this.chunks.shift());
      }
    },
    getChunkFormDatas() {
      this.progressTitle = `正在分片...（0/${this.formatFileSize}）`;
      const totalChunks = Math.ceil(this.file.size / this.chunkSize);
      for (let i = 0; i < totalChunks; i++) {
        const fileChunk = this.file.slice(
          i * this.chunkSize,
          (i + 1) * this.chunkSize,
          this.file.type
        );
        const formData = new FormData();
        formData.append("uploadId", this.uploadId);
        formData.append("chunkNumber", i + 1);
        formData.append("file", fileChunk, this.file.name);
        this.chunks.push(formData);
      }
    },
    uploadChunk(formData) {
      fetch(window.FileChunkUpload.UploadChunk, {
        method: "POST",
        body: formData
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(result => {
          if (result.success) {
            if (this.abort) {
              if (!this.abortRequest) {
                this.abortRequest = true;
                this.abortUpload();
              }
              return;
            }
            this.uploadedLength += formData.get("file").size;
            this.progressTitle = `正在上传...（${this.getUploadProgressTitle()}）`;
            if (this.file) {
              this.percentage = Math.floor(
                (this.uploadedLength / this.file.size) * 100
              );
            }
            this.uploadedCount++;
            const nextChunk = this.chunks.shift();
            if (nextChunk) {
              this.uploadChunk(nextChunk);
            } else if (this.uploadedCount === this.chunkCount) {
              this.completeUpload();
            }
          } else {
            this.$message.error(result.msg);
            this.uploadChunk(formData);
          }
        });
    },
    async completeUpload() {
      this.progressTitle = `正在合并文件...`;
      const result = await this.sendCommonRequest(
        `${window.FileChunkUpload.CompleteUpload}?uploadId=${this.uploadId}`
      );
      this.$emit("successUpload", {
        name: this.file.name,
        size: this.file.size,
        type: this.file.type,
        url: result.downloadUrl
      });
      this.reset();
      this.$emit("update:visible", false);
    },
    async abortUpload() {
      const result = await this.sendCommonRequest(
        `${window.FileChunkUpload.AbortUpload}?uploadId=${this.uploadId}`
      );
      this.reset();
    },
    async sendCommonRequest(url, data = null) {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: data ? JSON.stringify(data) : null
      });
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        return result.data;
      } else {
        this.$message.error(result.msg);
        return null;
      }
    },
    handleFileChange(file) {
      const rawFile = file.raw;
      if (this.maxFileSize && rawFile.size > this.maxFileSize * 1024 * 1024) {
        this.$message.error(`文件大小不能超过 ${this.maxFileSize} MB`);
        return;
      }
      const periodIndex = rawFile.name.lastIndexOf(".");
      const extension =
        periodIndex !== -1 ? rawFile.name.substr(periodIndex) : "";
      if (this.accept && this.accept !== "*") {
        const accept = this.accept.split(",").map(p => p.trim());
        if (!accept.includes(extension)) {
          this.$message.error(`文件类型必须是 ${this.accept} 中的一种`);
          return;
        }
      }
      const formatFileSize = this.formatSize(rawFile.size);
      this.fileIcon = this.getFileIcon(extension);
      this.file = rawFile;
      this.uploading = false;
      this.uploadId = null;
      this.startTime = 0;
      this.uploadedLength = 0;
      this.formatFileSize = formatFileSize;
      this.percentage = 0;
      this.progressTitle = `准备就绪（0/${formatFileSize}）`;
      this.chunks = [];
    },
    handleCancel() {
      if (this.uploading) {
        this.$confirm("上传正在进行中，确定中断吗?", "提示", {
          type: "warning"
        })
          .then(() => {
            this.progressTitle = `正在取消上传...`;
            this.abort = true;
            this.abortRequest = false;
          })
          .catch(_ => {});
      } else {
        this.reset();
        this.$emit("update:visible", false);
      }
    },
    getUploadProgressTitle() {
      if (!this.file) {
        return "";
      }
      const elapsedTime = Date.now() - this.startTime;
      const speed = this.formatSize((this.uploadedLength / elapsedTime) * 1000);
      const remainingTime =
        (((this.file.size - this.uploadedLength) / this.uploadedLength) *
          elapsedTime) /
        1000;
      const hours = Math.floor(remainingTime / 60 / 60);
      const minutes = Math.floor((remainingTime / 60) % 60);
      const seconds = Math.floor(remainingTime % 60);
      return `${speed}/s - ${this.formatSize(this.uploadedLength)}/${
        this.formatFileSize
      }，剩余 ${this.padLeftZero(hours)}:${this.padLeftZero(
        minutes
      )}:${this.padLeftZero(seconds)}`;
    },
    formatSize(size) {
      if (size < 1000) {
        return `${size} B`;
      }
      const k = 1024;
      if (size < 1000 * k) {
        return `${(size / k).toFixed(2)} KB`;
      } else if (size < 1000 * k * k) {
        return `${(size / k / k).toFixed(2)} MB`;
      }
      return `${(size / k / k / k).toFixed(2)} GB`;
    },
    padLeftZero(num) {
      return num < 10 ? "0" + num : num;
    },
    getUploadTip() {
      let tip =
        this.accept && this.accept !== "*" ? ` ${this.accept} 类型` : ``;
      if (this.maxFileSize) {
        if (tip) {
          tip += "且";
        }
        tip += `大小不超过 ${this.maxFileSize} MB `;
      }
      if (tip) {
        this.uploadTip = `支持上传${tip}的文件。`;
      }
    },
    getFileIcon(extension) {
      switch (extension?.toLowerCase()) {
        case ".doc":
        case ".docx":
          return require("@/assets/material/doc.png");
        case ".xls":
        case ".xlsx":
          return require("@/assets/material/excel-icon.png");
        case ".ppt":
        case ".pptx":
          return require("@/assets/material/ppt.png");
        case ".pdf":
          return require("@/assets/material/pdf.png");
        case ".zip":
        case ".rar":
          return require("@/assets/material/zip.png");
        case ".txt":
          return require("@/assets/material/txt.png");
        case ".jpg":
        case ".jpeg":
        case ".png":
        case ".gif":
        case ".bmp":
          return require("@/assets/material/img.png");
        case ".mp3":
        case ".wav":
        case ".wma":
        case ".ogg":
        case ".ape":
        case ".flac":
        case ".mp4":
        case ".avi":
        case ".rmvb":
        case ".rm":
        case ".flv":
        case ".mov":
        case ".mkv":
        case ".wmv":
          return require("@/assets/material/video.png");
        default:
          return null;
      }
    },
    reset() {
      this.file = null;
      this.fileIcon = null;
      this.uploading = false;
      this.uploadId = null;
      this.startTime = 0;
      this.uploadedLength = 0;
      this.formatFileSize = null;
      this.percentage = 0;
      this.progressTitle = null;
      this.chunks = [];
      this.uploadedCount = 0;
      this.chunkCount = 0;
      this.abort = false;
      this.abortRequest = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.fileForm {
  .selectFileBtn {
    border-style: dashed;
  }
}
::v-deep .base-dialog {
  min-height: auto;
  .avatar-uploader {
    margin-bottom: 10px;
    .tip {
      font-size: 16px;
      margin-left: 5px;
      vertical-align: super;
      cursor: pointer;
    }
  }
  .file-info {
    & > div:not(:first-child) {
      margin-top: 15px;
    }
    img {
      vertical-align: bottom;
      margin-right: 5px;
    }
    i {
      margin-right: 5px;
    }
    &:last-child {
      margin-bottom: 15px;
    }
  }
}
</style>
