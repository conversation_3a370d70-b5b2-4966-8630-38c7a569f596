<template>
  <div class="dispatch-page">
    <div class="dispatch-content-wrapper">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { Splitpanes, Pane } from 'splitpanes';
export default {
  name: 'FusionFrontPage',
  components: {
    Splitpanes,
    Pane
  },
  data() {
    return {};
  },

  mounted() {},

  methods: {
    handleProcess() {},
    handleKnowledge() {}
  }
};
</script>
<style>
@import './css/splitpanes.css';
</style>
<style lang="scss" scoped>
.dispatch-page {
  height: 100%;
  display: flex;
  background: #fff;
  padding: 10px;
  border-radius: 5px;
  padding-bottom: 0;
  flex-direction: column;
  .splitpanes__pane {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Helvetica, Arial, sans-serif;
    color: rgba(255, 255, 255, 0.6);
    font-size: 5em;
  }
  .toolbar {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px;
  }
  .dispatch-content-wrapper {
    height: calc(100% - 50px);
  }
}
</style>
