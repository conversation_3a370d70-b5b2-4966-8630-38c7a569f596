<template>
  <span>{{ value }}</span>
</template>

<script>
export default {
  name: 'amount-span',
  props: ['initValue'],
  components: {

  },
  data() {
    return {
      value: this.initValue
    }
  },
  watch: {
    initValue: {
      handler(val) {
        this.init(val)
      },
      immediate: true
    }
  },
  computed: {

  },
  methods: {
    init(val) {
      if (!val) {
        this.value = val;

        return
      }

      val = String(val).replace(/[^\-0-9.]/g, '');

      if (val) {
        val = parseFloat(val).toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      }

      this.value = val;
    },
  }
}
</script>

<style lang="scss"></style>