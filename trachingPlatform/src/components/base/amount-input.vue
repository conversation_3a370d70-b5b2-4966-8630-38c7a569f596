
<template>
  <el-input class="amount-input" v-model="inputValue" @input="inputAmount" @blur="formatValue" :disabled="disabled"
    :maxlength="10"></el-input>
</template>

<script>
export default {
  name: 'amount-input',
  props: ['initValue', 'rowIndex', 'fieldName', 'disabled'],
  components: {

  },
  data() {
    return {
      inputValue: this.initValue
    }
  },
  watch: {
    initValue: {
      handler(val) {
        this.init(val)
      },
      immediate: true
    }
  },
  computed: {

  },
  methods: {
    init(val) {
      if (!val) {
        this.inputValue = val;

        return
      }

      val = String(val).replace(/[^\-0-9.]/g, '');

      if (val) {
        val = parseFloat(val).toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      }

      this.inputValue = val;
    },

    inputAmount() {
      let value = this.inputValue;
      if (!value) {
        return
      }

      value = String(value).replace(/[^\-0-9.]/g, '');

      this.inputValue = value;
    },

    formatValue() {
      let value = this.inputValue;
      if (!value) {
        this.$emit('changeValue', 0, this.rowIndex, this.fieldName)

        return
      }

      // 格式化为千分位并保留两位小数
      value = parseFloat(value.replace(/,/g, '')).toFixed(2);

      this.inputValue = value;

      this.$emit('changeValue', parseFloat(this.inputValue.replace(/,/g, '')), this.rowIndex, this.fieldName)
    }
  }
}
</script>

<style lang="scss">
.amount-input.el-input {
  width: 200px;

  input {
    text-align: right;
  }
}
</style>