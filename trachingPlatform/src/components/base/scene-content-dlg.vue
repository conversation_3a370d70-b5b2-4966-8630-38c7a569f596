<template>
  <div>
    <!--   -->
    <base-dialog
      append-to-body
      :noFooter="true"
      :showToScreen="true"
      :visible.sync="dialogVisible"
      :fullscreen="fullscreen"
      :hasFull="hasFull"
      :close-on-click-modal="false"
      v-dialogDrag
      :title="title"
      :width="width"
      className="resize-dialog scene-content-dialog flex-content-dialog">
      <template slot="headerActions">
        <div class="center-actions">
          <el-button
            type="text"
            title="知识图谱"
            @click="handleKnowledgeClick">
            <i class="iconfont icon-people-network"></i>
          </el-button>
          <el-button
            v-if="flowCode && flowByCode"
            type="text"
            title="流程图"
            @click="handleFlowClick">
            <i class="iconfont icon-liucheng"></i>
          </el-button>
          <ExportButton title="导出" type="text" :contentType="sceneSchema.formType" />
          <!-- <el-button title="导出" type="text"><i class="iconfont icon-daoru"></i></el-button> -->
          <ScreenButton 
            @click="handleToScreen"
            title="投屏"
            type="text">
            <i class="action-icon iconfont icon-touping"></i>
          </ScreenButton>
          <ToMobileButton
            :item="{ title: title, url: mobileUrl }"
            contentType="scene"
            :isIcon="true"
            :typeName="mobileTypeName"
            type="text" />
        </div>
      </template>
      <sceneContent
        :title="title"
        :singleData="singleData"
        :sceneSchema="sceneSchema"
        :erId="sceneSchema.erId"
        :formShowType="formShowType"
        :handleChildrenDataLoaded="handleChildrenDataLoaded"
        :formatDataSourceBtns="formatDataSourceBtns"
        @resolveTemplateData="resolveTemplateData" />
    </base-dialog>
    <flowDialog
      v-if="businessNo"
      :visible.sync="flowVisible"
      :flowCode="flowCode"
      :businessNo="businessNo">
    </flowDialog>
    <screenChooseDlgVue
      v-if="screenVisible"
      :visible.sync="screenVisible"
      :url="screenUrl" />
    <ClassroomChooseDialog
      v-if="classroomDialogVisible"
      :visible.sync="classroomDialogVisible"
      @confirm="handleClassroomConfirm" />

    <!-- 知识图谱 -->
    <knowledgeGraphNormal
      v-if="knowledgeGraphVisible"
      :visible.sync="knowledgeGraphVisible"
      :nodeLoaded="false"
      :graphData="graphData"
      :dataId="singleData?.id" />
  </div>
</template>
<script>
import ToMobileButton from "@/components/mobile/to-mobile.vue";
import ScreenButton from "@/components/screen/screen-button.vue";
import ExportButton from "@/components/export/export-file.vue"; //文件导出

import ClassroomChooseDialog from "@/components/screen/classroom-choose-dialog.vue";
import knowledgeGraphNormal from "@/components/knowledge-graph/full-screen.vue";

import eventBus from "@/utils/eventBus";
import { getSceneJumpPath } from "@/utils/sceneJump.js";
import { CLASSROOM_ID, getCookies } from "@/utils/cookies";
import { getCurrentCaseId } from "@/utils/token.js";
export default {
  name: "sceneContentDlg",
  components: {
    ToMobileButton,
    ScreenButton,
    ClassroomChooseDialog,
    knowledgeGraphNormal,
    screenChooseDlgVue: () =>
      import("@/components/screen/screen-choose-dlg.vue"),
    baseDialog: () => import("@/components/base/dialog.vue"),
    sceneContent: () => import("./scene-content.vue"),
    flowDialog: () => import("@/components/flow/flow-dialog.vue"),
    ExportButton, //文件导出
  },
  props: {
    formShowType: {
      type: String,
      default: "detail"
    },
    drag: {
      type: Boolean,
      default: true
    },
    hasFull: {
      type: Boolean,
      default: true
    },
    fullscreen: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    width: {
      type: String,
      default: undefined
    },
    singleData: {
      type: Object,
      default: () => {}
    },
    sceneSchema: {
      type: Object,
      default: () => {}
    },
    formatDataSourceBtns: {
      type: Function
    },
    erId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      classroomDialogVisible: false,
      screenUrl: "", // 场景投屏url
      mobileUrl: "", // 移动端url
      screenVisible: false,
      businessNo: "",
      mobileTypeName: "",
      flowCode: "",
      flowByCode: false,
      flowVisible: false,
      classRoomId: getCookies(CLASSROOM_ID),
      knowledgeGraphVisible: false,
      graphData: null, // 知识图谱数据
      templateData: null // 模板数据
    };
  },
  computed: {
    dialogVisible: {
      set: function (val) {
        this.$emit("update:visible", false);
      },
      get: function () {
        return this.visible;
      }
    }
  },
  mounted() {
    eventBus.$on("classroomChoosed", () => {
      this.classRoomId = getCookies(CLASSROOM_ID);
    });
    let screenUrl = "";
    // moduleId:dataId, caseId, typeId:5
    const params = {
      caseId: getCurrentCaseId()
    };
    if (this.sceneSchema.sceneType == 2) {
      // 数据源
      this.mobileTypeName = "数据源";
      screenUrl = getSceneJumpPath({
        ...params,
        moduleId: this.sceneSchema.id,
        typeId: 5
      });
    } else {
      if (this.sceneSchema.formType == 1) {
        // 动态表单
        this.mobileTypeName = "动态表单";
        screenUrl = getSceneJumpPath({
          ...params,
          moduleId: this.sceneSchema.id,
          typeId: 1
        });
        screenUrl = `${screenUrl}?id=${this.singleData?.id}&operate=detail`;
      } else if (this.sceneSchema.formType == 2) {
        // 葡萄城
        this.mobileTypeName = "复杂表单";
        screenUrl = getSceneJumpPath({
          ...params,
          moduleId: this.sceneSchema.id,
          typeId: 2
        });
        screenUrl = `${screenUrl}?id=${this.singleData?.id}&operate=detail`;
      } else if (this.sceneSchema.formType == 3) {
        // 通用票据
        this.mobileTypeName = "通用票据";
        screenUrl = getSceneJumpPath({
          ...params,
          moduleId: this.sceneSchema.id,
          typeId: 3
        });
        screenUrl = `${screenUrl}?id=${this.singleData?.id}&operate=detail`;
      }
    }
    this.mobileUrl = screenUrl;
    this.screenUrl = `/device${screenUrl}`;
  },
  destroyed() {
    eventBus.$off("classroomChoosed");
  },
  methods: {
    handleClassroomConfirm(classRoomId) {
      this.classRoomId = classRoomId;
      this.screenVisible = true;
    },
    // 投屏
    handleToScreen() {
      if (this.classRoomId) {
        this.screenVisible = true;
      } else {
        this.classroomDialogVisible = true;
      }
    },
    // 点击流程图标，显示流程
    handleFlowClick(e) {
      e.preventDefault();
      e.stopPropagation();
      this.flowVisible = true;
    },
    // 通过子节点获取businessNo和flowCode（图标显示在dlg的headerbar）
    handleChildrenDataLoaded(data) {
      this.businessNo = data.businessNo;
      this.flowCode = data.flowCode;
      this.$api.GetFlowByCodes({ flowCode: this.flowCode }).then(val=>{
        if(val.data.length){
          this.flowByCode = true
        }
      })
    },

    // 点击知识图谱
    handleKnowledgeClick(e) {
      e.preventDefault();
      e.stopPropagation();
      if (!this.templateData) {
        this.$message.warning("知识图谱没有数据");
        return;
      }
      // 只有场景关联的知识图谱
      this.graphData = {
        node0: {
          id: "node0",
          label: this.templateData.formName,
          mType: 2,
          mAttrs: { ...this.templateData, mType: 2, mTypeKey: "form" }
        }
      };
      this.knowledgeGraphVisible = true;
    },
    // 子组件返回的模板数据
    resolveTemplateData({ formName, id, erId, typeId }) {
      if (id) this.templateData = { formName, id, erId, formType: typeId };
      else this.templateData = null;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .scene-content-dialog {
  // display: flex;
  // flex-direction: column;
  &.resize-dialog.flex-content-dialog{
    .el-dialog__body {
      min-height: 59vh !important;
      max-height:100% !important;
    }
  }
  .base-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .center-actions {
      flex: 1 1 auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 6px;
    }
    .iconfont {
      font-size: 20px;
      color: #909399;
    }
    .iconfont:hover {
      color: #2b66ff;
    }
    .icon-liucheng,
    .icon-people-network {
      font-size: 16px;
    }
  }
}
</style>
