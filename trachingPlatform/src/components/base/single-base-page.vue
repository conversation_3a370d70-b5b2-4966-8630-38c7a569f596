<template>
  <div class="single-base-page">
    <div class="page-header-wrapper" v-if="actions.length">
      <div class="page-header">
        <div class="toolbar right-toolbar">
          <div
            v-if="actions.includes('reset')"
            class="rest-btn iconfont icon-a-15Jhuanyuan btn"
            @click="reset"
            title="重置"></div>
          <div
            v-if="actions.includes('zoomOut')"
            class="zoom-out-btn iconfont icon-xinzeng btn"
            @click="zoomOut"
            title="放大"></div>
          <div
            v-if="actions.includes('zoomIn')"
            class="zoom-in-btn btn iconfont icon-jianshao"
            @click="zoomIn"
            title="缩小"></div>
        </div>
      </div>
    </div>

    <div
      class="page-content"
      ref="contentRef">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "FusionFrontSinglePage",
  props: {
    title: {
      type: String,
      default: ""
    },
    actions: {
      type: Array,
      default: () => ["reset", "zoomIn", "zoomOut"]
    },
    zoomStep: {
      type: Number,
      default: 0.1
    },
    maxScale: {
      type: Number,
      default: 1.5
    },
    minScale: {
      type: Number,
      default: 0.1
    }
  },
  data() {
    return {
      scale: 1,
      initialScale: 1,
      lastScale: 1,
      contentStyle: {
        transform: "scale(1)",
        transformOrigin: "0 0" // 缩放中心，默认左上角
      }
    };
  },
  methods: {
    onTouchStart(event) {
      event.preventDefault();
      if (event.touches.length === 2) {
        // 计算初始距离
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];
        this.initialScale = this.scale;
        this.lastScale = Math.hypot(
          touch2.clientX - touch1.clientX,
          touch2.clientY - touch1.clientY
        );
      }
    },
    onTouchMove(event) {
      event.preventDefault();
      if (event.touches.length === 2) {
        // 计算当前距离
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];
        const currentScale = Math.hypot(
          touch2.clientX - touch1.clientX,
          touch2.clientY - touch1.clientY
        );

        // 计算缩放比例
        let newScale = this.initialScale * (currentScale / this.lastScale);

        // 应用最小和最大缩放比例限制
        newScale = Math.max(this.minScale, Math.min(this.maxScale, newScale));

        // 更新缩放样式
        this.contentStyle.transform = `scale(${newScale})`;

        this.scale = newScale; // 更新当前缩放比例

        // 更新内容区域的高度适应缩放
        this.updateContentHeight();
      }
    },
    onTouchEnd(event) {
      event.preventDefault();
      // 重置初始缩放比例和最后缩放距离
      this.initialScale = this.scale;
      this.lastScale = 1;
    },
    zoom(event) {
      event.preventDefault();
      const rect = this.$refs.contentRef.getBoundingClientRect();
      const offsetX = event.clientX - rect.left;
      const offsetY = event.clientY - rect.top;
      let newScale = this.scale * (event.deltaY > 0 ? 0.9 : 1.1); // 根据滚轮方向调整缩放比例

      // 应用最小和最大缩放比例限制
      newScale = Math.max(this.minScale, Math.min(this.maxScale, newScale));

      this.contentStyle.transformOrigin = `${offsetX}px ${offsetY}px`;
      this.contentStyle.transform = `scale(${newScale})`;

      this.scale = newScale; // 更新当前缩放比例

      this.updateScale();
    },
    onMouseWheel(event) {
      event.preventDefault();
      this.zoom(event);
    },
    handleReturn() {
      this.$router.push("/case-list");
    },
    reset() {
      this.scale = 1;
      this.updateScale();
    },
    zoomOut() {
      if (this.scale < this.maxScale) {
        this.scale += this.zoomStep;
        this.updateScale();
      }
    },
    zoomIn() {
      if (this.scale > this.minScale) {
        this.scale -= this.zoomStep;
        this.updateScale();
      }
    },
    updateScale() {
      // this.contentStyle.transform = `scale(${this.scale})`;
      this.$emit("zoom", this.scale, this.$refs.contentRef);
    }
  }
};
</script>
<style lang="scss" scoped>
.single-base-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  .page-header-wrapper {
  }
  .page-header {
    z-index: 10;
    position: fixed;
    top: 5px;
    left: 5px;
    background: #f0f0f0;
    border-radius: 10px;
    opacity: 0.4;
    height: 50px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    &:hover {
      opacity: 1;
      .iconfont:hover {
        background-color: #1989fa;
        color: #fff;
      }
    }
    .right-toolbar {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .btn {
      cursor: pointer;
      border: 1px solid #d9d9d9;
      padding: 5px;
      border-radius: 6px;
      font-size: 24px;
      color: #333;
    }
  }

  .page-content {
    z-index: 1;
    flex: 1;
    min-height: 0;
    overflow: auto;
  }
}
</style>
