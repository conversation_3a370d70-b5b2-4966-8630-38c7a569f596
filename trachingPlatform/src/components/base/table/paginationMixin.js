export const paginationMixin = {
  data() {
    return {
      totalCount: 0,
      pagination: {
        pageSize: 20,
        currentPage: 1,
      },
    };
  },
  watch: {
    pagination: {
      handler(newVal) {
        if (this.dialogVisible) {
          this.getTableData(newVal);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handlePageChange(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: val,
      };
    },
    handleSizeChange(val) {
      this.pagination = {
        ...this.pagination,
        pageSize: val,
      };
    },
    handlePrev(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: this.pagination.currentPage - 1,
      };
    },
    handleNext(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: this.pagination.currentPage + 1,
      };
    },
  }
};
