<template>
  <div class="base-table-wrapper">
    <div
      v-if="title"
      class="title">
      {{ title }}
    </div>
    <div
      v-if="search && search.formConfig?.formItemList.length"
      class="search-container">
      <searchForm
        ref="searchFormRef"
        :span="search.span"
        :formConfig="search.formConfig"
        :formValue="search.formValue"
        @submit="handleSearch">
      </searchForm>
    </div>
    <div
      v-if="toolBarActions"
      class="top-operate">
      <el-button
        v-for="(item, i) in toolBarActions"
        :key="i"
        :type="item.type"
        :size="item.size"
        @click="item.click()">
        <i
          v-if="item.icon"
          :class="item.icon"></i>
        {{ item.name }}
      </el-button>
    </div>
    <el-table
      v-if="isTree"
      ref="baseTableRef"
      :data="tData"
      :max-height="height"
      :row-key="rowKey"
      :lazy="lazy"
      :load="load"
      :tree-props="treeProps"
      class="table_bsm"
      :row-disabled="isRowDisabled"
      border
      stripe
      :header-cell-style="{ background: '#F2F3F5', color: '#222222' }"
      @selection-change="handleSelectionChange">
      <el-table-column
        v-if="selectable"
        type="selection"
        :reserve-selection="reserveSelection === false || reserveSelection ? reserveSelection : true"
        width="55">
      </el-table-column>
      <el-table-column
        v-if="indexable"
        type="index"
        label="序号"
        width="55"
        align="center"
        :index="getRowIndex">
      </el-table-column>

      <el-table-column
        v-for="(col, index) in cols"
        :key="index"
        :width="col.width"
        :min-width="col.minWidth"
        :align="col.align || 'left'"
        :prop="col.field"
        :label="col.label">
        <template slot-scope="scope">
          <cellItem
            :field="col.field"
            :item="col"
            :colData="scope"></cellItem>
        </template>
      </el-table-column>
      <el-table-column
        v-if="action"
        :fixed="action.fixed"
        :label="action.label"
        :align="action.align || 'left'"
        :width="action.width">
        <template slot-scope="scope">
          <div class="table-row-actions">
            <div
              v-for="(item, i) in action.items"
              :key="i"
              :class="item.class">
              <el-button
                v-show="item.showCondition ? item.showCondition(scope) : true"
                :plain="item.plain"
                :class="item.class"
                :type="item.type"
                :size="item.size"
                @click="item.click(scope)">
                {{ item.name }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-table
      v-else
      ref="baseTableRef"
      :data="tData"
      :max-height="height"
      row-key="id"
      :reserve-selection="true"
      class="table_bsm"
      :tree-props="treeProps"
      border
      stripe
      :header-cell-style="{ background: '#F2F3F5', color: '#222222' }"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange">
      <template slot="empty">
        <zdDefaultPage size="middle" msg="暂无数据" type="noResult"></zdDefaultPage>
      </template>
      <el-table-column
        v-if="selectable"
        type="selection"
        :selectable="isRowDisabled"
        :reserve-selection="reserveSelection === false || reserveSelection ? reserveSelection : true"
        width="55">
      </el-table-column>
      <el-table-column
        v-if="indexable"
        type="index"
        label="序号"
        width="55"
        align="center"
        :index="getRowIndex">
      </el-table-column>

      <el-table-column
        v-for="(col, index) in cols"
        :key="index"
        :width="col.width"
        :min-width="col.minWidth"
        :align="col.align || 'left'"
        :prop="col.field"
        :sortable="col.sortable || false"
        :label="col.label">
        <template slot-scope="scope">
          <slot v-if="col.slotName" :name="col.slotName" :scope="scope"></slot>          
          <cellItem v-else
            :field="col.field"
            :item="col"
            :colData="scope"></cellItem>
        </template>
      </el-table-column>
      <el-table-column
        v-if="action"
        :fixed="action.fixed"
        :label="action.label"
        :align="action.align || 'left'"
        :width="action.width">
        <template slot-scope="scope">
          <slot name="actionsSlot" v-if="$slots.actionsSlot"></slot>
          <div v-else class="table-row-actions">
            <div
              v-for="(item, i) in action.items"
              :key="i"
              :class="item.class">
              <el-button
                v-show="item.showCondition ? item.showCondition(scope) : true"
                :plain="item.plain"
                :class="item.class"
                :type="item.type"
                :size="item.size"
                @click="item.click(scope)">
                <span :style="item.style?item.style(scope):''">
                  {{ item.status?item.status(scope):item.name }}
                </span>
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="pagination"
      layout="total, sizes, prev, pager, next, jumper"
      background
      :hide-on-single-page="hideSinglePage||false"
      :page-size="pageSize"
      :total="total"
      :current-page.sync="pageIndex"
      @current-change="handleChangePage"
      @size-change="handleSizeChange"
      @prev-click="handlePrev"
      @next-click="handleNext">
    </el-pagination>
  </div>
</template>

<script>
import cellItem from "./cellItem";
import searchForm from "../form/searchForm.vue";
export default {
  name: "BaseTable",
  components: {
    searchForm,
    cellItem
  },
  props: [
    "height",
    "search",
    "isTree",
    "rowKey",
    "lazy",
    "load",
    "treeProps",
    "toolBarActions",
    "action",
    "title",
    "columns",
    "tableData",
    "total",
    "pageSize",
    "pagination",
    "primaryKey",
    "actionable",
    "selectable",
    "indexable",
    "reserveSelection",
    "pageNumber",
    "hideSinglePage",
  ],
  data() {
    return {
      selectedRows: [], // 选中行
      selectedRowKeys: [], // 选中行key
      cols: [],
      tData: [],
      // pageIndex: 1
    };
  },
  computed:{
    pageIndex(){
      return this.pageNumber || 1
    }
  },
  watch: {
    columns: {
      handler(val) {
        this.cols = val;
      },
      immediate: true
    },
    tableData: {
      handler(val) {
        this.tData = val;
        this.$nextTick(() => {
          // 恢复当前页选中状态
          val.forEach(row => {
            const isSelected = this.selectedRows.some(
              selected => selected.id === row.id
            );
            this.$refs.baseTableRef.toggleRowSelection(row, isSelected);
          });
        });
      },
      immediate: true
    },
    height: {
      handler(val) {
        // console.log("-------------", val);
      },
      immediate: true
    }
  },
  mounted() {
    // console.log(this);
  },
  methods: {
    //根据条件判断是否禁用行的复选框
    isRowDisabled(row) {
      // 示例：如果题目为综合题，则禁用复选框
      // return row.questionType !== 50;
      return true;
    },
    sortChange(val){
      this.$emit('sortChange',val)
    },
    getRowIndex(index) {
      if (this.pagination) {
        return (this.pageIndex - 1) * this.pageSize + index + 1;
      }
      return index + 1;
    },
    getRowKey() {
      return this.rowKey || "id";
    },
    toggleRowSelection(rows) {
      rows.forEach(row => {
        this.$refs.baseTableRef.toggleRowSelection(row, true);
      });
    },
    clearSelection() {
      this.$refs.baseTableRef.clearSelection();
    },
    handleSearch() {
      const val = this.$refs.searchFormRef.formValue;
      this.$emit("handleSearch", val);
    },
    handelSearchEmpty() {
      this.$refs.searchFormRef.resetFields();
    },
    handleSelectionChange(val) {
      this.$emit('selectedRows',val)
      this.selectedRows = val;
      this.selectedRowKeys = val.map(v => v[this.primaryKey]);
    },
    // 新增行
    addItem() {
      this.$emit("addItem", {
        data: {
          idx: this.tData.length + 1
        },
        cbk: data => {
          this.tData.push(data);
        }
      });
    },
    // 删除行
    delItems() {
      if (this.selectedRowKeys.length) {
        this.$confirm("确认删除选中行？")
          .then(async _ => {
            // let { data, returnStatus } = await this.$api.TableColumnRemoveById(
            //   ids
            // )
            // if (returnStatus == 1) {
            //   this.initPage()
            // }
            // // 未保存的字段删除
            // let noSaveKey = this.selectedKey.map((v) => !v.id)
            this.data = this.data.filter(
              d => !this.selectedRowKeys.includes(d[this.primaryKey])
            );
          })
          .catch(_ => {});
      } else {
        this.$message.warning("请最少选中一行");
      }
    },

    // 分页
    handleChangePage(val) {
      this.$emit("handlePageChange", val);
      this.pageIndex = val;
    },
    handleSizeChange(val) {
      this.$emit("handleSizeChange", val);
    },
    handlePrev(val) {
      this.$emit("handlePrev", val);
    },
    handleNext(val) {
      this.$emit("handleNext", val);
    }
  }
};
</script>
<style lang="scss"></style>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.base-table-wrapper {
  ::v-deep .search-container {
    .action-container {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
  }
  ::v-deep .cell {
    display: flex;
    align-items: center;
  }
  ::v-deep .el-table__empty-block {
    width: 100% !important;
  }
  ::v-deep .del-button button {
    color: red;
  }
}
.base-table-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .table-row-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
  }
  .title {
    padding: 10px;
  }
}
</style>
