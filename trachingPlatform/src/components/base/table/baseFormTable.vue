<template>
  <div>
    <el-form
      :model="formModel"
      ref="formRef"
      :rules="rules"
      label-width="108px"
      :inline="true"
    >
      <el-table
        :data="formModel.tableData"
        :max-height="height"
        :row-key="getRowKey()"
        ref="baseTableRef"
        class="table_bsm"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :reserve-selection="true"
          width="55"
          v-if="selectable"
        >
        </el-table-column>

        <el-table-column
          v-for="(col, index) in cols"
          :width="col.width"
          :align="col.align || 'left'"
          :key="index"
          :prop="col.field"
          :label="col.label"
        >
          <template slot-scope="scope">
            <cellItem
              :editable="col.editable"
              :formModel="formModel.tableData[scope.$index][col.field]"
              :field="col.field"
              :item="col"
              :colData="scope"
            ></cellItem>
          </template>
        </el-table-column>
        <el-table-column
          :fixed="action.fixed"
          :label="action.label"
          :width="action.width"
          v-if="action"
        >
          <template slot-scope="scope">
            <el-button
              v-for="(item, i) in action.items"
              :key="i"
              v-show="item.showCondition ? item.showCondition(scope) : true"
              @click="item.click(scope)"
              :type="item.type"
              :size="item.size"
            >
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item class="footer">
        <slot />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import cellItem from './cellItem'
export default {
  name: 'baseFormTable',
  components: {
    cellItem,
  },
  props: [
    'height',
    'search',
    'rowKey',
    'toolBarActions',
    'action',
    'title',
    'columns',
    'tableData',
    'primaryKey',
    'actionable',
    'selectable',
  ],
  watch: {
    columns: {
      handler(val) {
        this.cols = val
      },
      immediate: true,
    },
  },
  mounted() {
    console.log(this.formModel)
  },
  computed: {
    formModel: {
      get: function () {
        return this.tableData
      },
      set: function (val) {
        this.$emit('update:tableData', val)
      },
    },
  },
  data() {
    return {
      selectedRows: [], // 选中行
      selectedRowKeys: [], // 选中行key
      cols: [],
      rules: {
        // name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      },
    }
  },
  methods: {
    getRowKey() {
      return this.rowKey || 'id'
    },
    handleSelectionChange(val) {
      this.selectedRows = val
      this.selectedRowKeys = val.map((v) => v[this.primaryKey])
    },
    toggleRowSelection(rows) {
      rows.forEach((row) => {
        this.$refs.baseTableRef.toggleRowSelection(row, true)
      })
    },
    clearSelection() {
      this.$refs.baseTableRef.clearSelection()
    },
    //保存
    save() {
      this.$refs.formRef.validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          //如果valid为true，表示校验通过，可以提交表单，调取接口进行保存
          console.log('表单校验通过')
        } else {
          this.$message.warning('请填写完整的数据')
        }
      })
    },
    //添加
    addTable() {
      let newArr = [
        {
          name: '',
          age: '',
          sex: '',
        },
      ]
      this.formModel.tableData.push(...newArr)
    },
    //删除
    delTable(i) {
      this.formModel.tableData.splice(i, 1)
    },
  },
}
</script>

<style>
.footer {
  margin-top: 50px;
}
</style>
