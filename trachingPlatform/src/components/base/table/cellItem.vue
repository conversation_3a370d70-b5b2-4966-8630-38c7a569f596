<template>
  <el-form-item
    v-if="item.form"
    :prop="'tableData.' + colData.$index + '.' + field"
  >
    <el-input
      v-if="item.form.type === 'input'"
      :type="item.form.type"
      :size="item.form.size"
      :disabled="item.form.disabled"
      :clearable="item.form.clearable"
      :placeholder="item.form.placeholder"
      v-model="colData.row[field]"
    ></el-input>
    <el-select
      v-else-if="item.form.type === 'select'"
      :multiple="item.form.multiple"
      :collapse-tags="item.form.collapseTags"
      :disabled="item.form.disabled"
      v-model="colData.row[field]"
      :clearable="item.form.clearable"
      :multiple-limit="item.form.multipleLimit"
    >
      <el-option
        v-for="(o) in item.form.options"
        :key="'select' + colData.$index + '-' + field + o.value"
        :label="o.label"
        :value="o.value"
        :disabled="o.disabled"
      ></el-option>
    </el-select>
    <el-switch
      v-else-if="item.form.type === 'switch'"
      v-model="colData.row[field]"
      :disabled="item.form.disabled"
    ></el-switch>
    <el-radio-group
      v-else-if="item.form.type === 'radio'"
      :disabled="item.form.disabled"
      v-model="colData.row[field]"
    >
      <component
        :is="item.form.button ? 'el-radio-button' : 'el-radio'"
        v-for="o in item.form.options"
        :key="'radio' + colData.$index + '-' + field + o.code"
        :label="o.code"
        :disabled="o.disabled"
        :border="item.form.border"
        >{{ o.name }}</component
      >
    </el-radio-group>

    <el-checkbox-group
      v-else-if="item.form.type === 'checkbox'"
      :min="item.form.min"
      :max="item.form.max"
      v-model="colData.row[field]"
    >
      <component
        :is="item.form.button ? 'el-checkbox-button' : 'el-checkbox'"
        v-for="o in item.options || ajaxOptions"
        :key="'checkbox' + colData.$index + '-' + field + o.value"
        :disabled="o.disabled"
        :label="o.code"
        :border="item.form.border"
        >{{ o.name }}</component
      >
    </el-checkbox-group>

    <el-time-picker
      v-else-if="item.form.type === 'time'"
      :is-range="item.form.isRange"
      range-separator="-"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :value-format="item.form.valueFormat"
      :format="item.form.valueFormat"
      default-time="12:00:00"
      :placeholder="item.form.placeholder"
      :picker-options="item.form.pickerOptions"
      v-model="colData.row[field]"
    ></el-time-picker>

    <el-date-picker
      v-else-if="item.form.type === 'date'"
      :type="item.form.subtype"
      range-separator="-"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :value-format="item.form.valueFormat"
      :format="item.form.format"
      :placeholder="item.form.placeholder"
      :picker-options="item.form.pickerOptions"
      v-model="colData.row[field]"
      :disabled="item.disabled"
    ></el-date-picker>
    <!-- <el-input
      type="text"
      size="small"
      placeholder="请输入姓名"
      v-model="colData.row[field]"
    ></el-input>
    <form-ex-slot
      :render="item.render"
      :row="colData.row"
      :index="colData.$index"
      :value="colData.row[field]"
      :column="item"
    /> -->
  </el-form-item>

  <ex-slot
    v-else-if="item.render"
    :render="item.render"
    :row="colData.row"
    :index="colData.$index"
    :column="item"
  />
  <div v-else>{{ colData.row[field] }}</div>
</template>

<script>
import formItem from '../form/formItem.vue'
const formExSlot = {
  functional: true,
  props: {
    formModel: Object,
    row: Object,
    render: Function,
    index: Number,
    column: {
      type: Object,
      default: null,
    },
  },
  render: function (h, data) {
    console.log(this)
    const params = {
      row: data.props.row,
      index: data.props.index,
    }
    if (data.props.column) params.column = data.props.column
    return data.props.render(h, params, this)
  },
}
const exSlot = {
  functional: true,
  props: {
    row: Object,
    render: Function,
    index: Number,
    column: {
      type: Object,
      default: null,
    },
  },
  render: (h, data) => {
    const params = {
      row: data.props.row,
      index: data.props.index,
    }
    if (data.props.column) params.column = data.props.column
    return data.props.render(h, params)
  },
}
export default {
  name: 'iCell',
  props: {
    field: String,
    item: Object,
    colData: Object,
    columnMinWidth: {
      type: String,
    },
  },
  components: {
    formItem,
    exSlot,
    formExSlot,
  },
  data() {
    return {
      eventName: 'click',
    }
  },
  methods: {
    // 动态绑定操作按钮的点击事件

    handleClick(i) {
      let onClick = i
      this[onClick]()
    },

    // 新增
    add() {
      alert('新增~')
    },
    // 编辑
    edit() {
      alert('编辑~')
    },
    // 删除
    delete() {
      alert('删除~')
    },
  },
}
</script>

<style scoped></style>
