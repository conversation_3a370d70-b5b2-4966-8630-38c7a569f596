<template>
  <el-form-item
    v-show="descriptor.show ? descriptor.show(prop) : !descriptor.hidden"
    :ref="prop"
    class="dynamic-form-item"
    :label="labelWidth === '0px' ? '' : label || prop"
    :prop="prop"
    :size="size"
    :language="language"
    :rules="descriptor.rules"
    :required="typeDescriptor.required"
    :label-width="labelWidth"
    :show-message="
      showOuterError ||
        !isComplexType(typeDescriptor.type) ||
        <PERSON>olean(typeDescriptor.component)
    "
    :class="descriptor.class">
    <template slot="label" v-if="findTypeDescriptor(typeDescriptor).label">
      <el-tooltip class="item" effect="dark" :content="findTypeDescriptor(typeDescriptor).label" placement="top">
        <div class="dynamic-form-label ">
          {{ findTypeDescriptor(typeDescriptor).label}}
          <template v-if="descriptor.required">
            <sup class="required">*</sup>
          </template>
        </div>
      </el-tooltip>
    </template>
    <dynamic-input
      v-if="
        !isComplexType(typeDescriptor.type) || <PERSON><PERSON><PERSON>(typeDescriptor.component)
      "
      v-model="_value"
      :prop="prop"
      :size="size"
      :descriptor="typeDescriptor">
    </dynamic-input>
    <!-- complex type, object or array -->
    <template v-else>
      <!-- normal object or hashmap object -->
      <template v-if="typeDescriptor.type === 'object'">
        <!-- normal object with known keys -->
        <div
          v-if="!typeDescriptor.defaultField"
          class="sub-dynamic-form"
          :style="{ backgroundColor: subFormBackgroundColor }">
          <dynamic-form-item
            v-for="(_descriptor, key) in typeDescriptor.fields"
            :key="key"
            v-model="_value[key]"
            :label="findTypeDescriptor(_descriptor).label || key"
            :prop="prop ? prop + '.' + key : key"
            :descriptor="_descriptor"
            :language="language"
            :label-width="getLabelWidth(typeDescriptor.fields, fontSize)"
            :background-color="subFormBackgroundColor"
            :show-outer-error="showOuterError">
          </dynamic-form-item>
        </div>
        <!-- hashmap object -->
        <div
          v-else
          class="sub-dynamic-form hashmap"
          :style="{ backgroundColor: subFormBackgroundColor }">
          <dynamic-form-item
            v-for="(temp, key) in _value"
            :ref="prop + '.' + key"
            :key="key"
            v-model="_value[key]"
            :label="key"
            :prop="prop ? prop + '.' + key : key"
            :deletable="true"
            :descriptor="typeDescriptor.defaultField"
            :language="language"
            :label-width="getLabelWidth(_value, fontSize)"
            :background-color="subFormBackgroundColor"
            :show-outer-error="showOuterError"
            @delete="deleteKey(key)">
          </dynamic-form-item>
          <el-form-item>
            <div class="add-key-input-group">
              <el-input
                v-model="hashMapKey"
                :placeholder="language.addKeyPlaceholder"
                :size="size"></el-input>
              <el-button
                type="primary"
                icon="el-icon-plus"
                :size="size"
                :disabled="!hashMapKey || _value[hashMapKey] !== undefined"
                plain
                @click="addHashMapKey"
              >{{ language.addKeyButtonText }}</el-button
              >
            </div>
          </el-form-item>
        </div>
      </template>
      <!-- array -->
      <template v-else>
        <div
          v-if="
            typeDescriptor.defaultField.type === 'enum' &&
              typeDescriptor.defaultField.multiple
          "
          class="multi-select">
          <dynamic-input
            v-model="_value"
            :size="size"
            :descriptor="typeDescriptor.defaultField">
          </dynamic-input>
        </div>
        <div
          v-else
          class="sub-dynamic-form array"
          :style="{ backgroundColor: subFormBackgroundColor }">
          <dynamic-form-item
            v-for="(temp, key) in _value"
            :key="key"
            v-model="_value[key]"
            :prop="prop ? prop + '.' + key : key"
            :deletable="true"
            :descriptor="typeDescriptor.defaultField"
            :language="language"
            label-width="0px"
            :background-color="subFormBackgroundColor"
            :show-outer-error="showOuterError"
            @delete="deleteItem(key)">
          </dynamic-form-item>
          <div class="add-key-input-group">
            <el-button
              type="primary"
              icon="el-icon-plus"
              :size="size"
              plain
              @click="addArrayItem"
            >{{ language.addArrayItemButtonText }}</el-button
            >
          </div>
        </div>
      </template>
    </template>
    <el-button
      v-if="deletable"
      class="delete-button"
      type="text"
      icon="el-icon-close"
      @click="emitDelete"></el-button>
  </el-form-item>
</template>

<script>
import {
  isComplexType,
  getLabelWidth,
  darkenColor,
  parseDescriptor,
  findTypeDescriptor
} from '../utils';
import DynamicInput from '../dynamic-input/input';

export default {
  name: 'DynamicFormItem',
  components: {
    DynamicInput
  },
  props: {
    value: {
      required: true
    },
    prop: {
      type: String,
      default: ''
    },
    label: String,
    /**
     * descriptor of value, extend from https://github.com/yiminghe/async-validator
     */
    descriptor: {
      type: [Object, Array],
      required: true
    },
    /**
     * size of the input component
     */
    size: {
      type: String,
      default: 'small'
    },
    /**
     * font-size of form
     */
    fontSize: {
      type: Number,
      default: 14
    },
    /**
     * background-color of form
     */
    backgroundColor: {
      type: String,
      default: '#FFFFFF'
    },
    /**
     * darken sub-form's background-color with offset if got positive integer
     */
    bgColorOffset: {
      type: Number,
      default: 8
    },
    /**
     * whether show parent component's error, default true
     */
    showOuterError: {
      type: Boolean,
      default: true
    },
    deletable: {
      type: Boolean,
      default: false
    },
    labelWidth: String,
    language: Object
  },
  data() {
    return {
      hashMapKey: ''
    };
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      }
    },
    typeDescriptor() {
      return findTypeDescriptor(this.descriptor);
    },
    subFormBackgroundColor() {
      return this.bgColorOffset
        ? darkenColor(this.backgroundColor, this.bgColorOffset)
        : 'none';
    }
  },
  watch: {
    hashMapKey(val) {
      // el-form-item's prop not support "."
      if (val.indexOf('.') !== -1) {
        this.hashMapKey = this.hashMapKey.replace(/\./g, '');
      }
    }
  },
  mounted() {},
  created() {},
  methods: {
    isComplexType,
    getLabelWidth,
    findTypeDescriptor,
    clearValidate() {
      this.$refs[this.prop].clearValidate();
    },
    resetField() {
      this.$refs[this.prop].resetField();
    },
    addHashMapKey() {
      this.$set(
        this._value,
        this.hashMapKey,
        parseDescriptor(this.typeDescriptor.defaultField)
      );
      this.hashMapKey = '';
      this.$refs[this.prop].resetField(); // reset field to clear validate status while adding fist hashmap key
    },
    addArrayItem() {
      this._value.push(parseDescriptor(this.typeDescriptor.defaultField));
    },
    emitDelete() {
      this.$emit('delete');
    },
    deleteKey(key) {
      this.$delete(this._value, key);
    },
    deleteItem(index) {
      this._value.splice(index, 1);
    }
  }
};
</script>

<style lang="scss" scoped>
.sub-dynamic-form {
  border-radius: 5px;
  padding: 10px;
  .el-form-item:last-child {
    margin-bottom: 0;
  }
}

::v-deep .dynamic-form-item {
  flex: 1;
  flex-shrink: none;
  min-width: 0;
  display: flex;
  flex-direction: column;
  .dynamic-form-label {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}

::v-deep .flex-row > .el-form-item__content > .sub-dynamic-form {
  display: flex;
}
::v-deep .flex-row > .el-form-item__content > .sub-dynamic-form .dynamic-input {
  width: 100%;
}
.add-key-input-group {
  display: flex;
  margin-top: 10px;

  .el-input {
    width: 250px;
    margin-right: 10px;
  }
}
.add-key-input-group:first-child {
  margin-top: 0;
}
.delete-button {
  position: absolute;
  top: 0;
  right: 5px;
  font-size: 20px;
  color: #f56c6c;
  padding: 5px 0;
}
.delete-button:hover {
  color: #666;
}
.dynamic-input + .delete-button {
  top: auto;
  right: auto;
}
</style>
