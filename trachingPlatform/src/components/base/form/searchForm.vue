

<template>
  <el-form
    @submit.native.prevent
    class="search-form full-width-form"
    ref="searchForm"
    :model="formValue"
    :label-position="formConfig.labelPosition || 'right'"
    :label-width="formConfig.labelWidth || '100px'"
    inline
  >
    <el-row :gutter="20">
      <el-col v-for="item in queryList" :span="item.span || 6" :key="item.key">
        <!-- <formItem
          :item="item"
          :style="{ minWidth: columnMinWidth }"
          @input="handleInput($event, item.key)"
          :value="formValue[item.key]"
        >
        </formItem> -->
        <el-form-item
          class="form-item"
          :label="item.label"
          :title="item.label"
          :prop="item.key"
          v-if="!item.isHide"
        >
          <component
            :is="componentsType(item.type)"
            v-model="formValue[item.key]"
          >
            <template v-if="item.type === 'select' && item.options.length > 0">
              <el-option
                v-for="opt in item.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </template>
            <template
              v-if="item.type === 'checkbox' && item.options.length > 0"
            >
              <el-checkbox
                v-for="opt in item.options"
                :key="opt.value"
                :label="opt.value"
                >{{ opt.label }}</el-checkbox
              >
            </template>
          </component>
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item>
          <div class="actions-wrapper">
            <el-button
              :icon="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
              class="show-more"
              type="text"
              @click="toggleMore"
              v-if="moreList.length > 0"
              >更多</el-button
            >
            <el-button @click="onReset">重置</el-button>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-show="showMore">
      <el-col v-for="item in moreList" :span="6" :key="item.key">
        <el-form-item
          class="form-item"
          :label="item.label"
          :title="item.label"
          :prop="item.key"
          v-if="!item.isHide"
        >
          <component
            :is="componentsType(item.type)"
            v-model="formValue[item.key]"
          >
            <template v-if="item.type === 'select' && item.options.length > 0">
              <el-option
                v-for="opt in item.options"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </template>
            <template
              v-if="item.type === 'checkbox' && item.options.length > 0"
            >
              <el-checkbox
                v-for="opt in item.options"
                :key="opt.value"
                :label="opt.value"
                >{{ opt.label }}</el-checkbox
              >
            </template>
          </component>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
export default {
  data() {
    return {
      formValue: {},
      // 'cascader'
      isElementUI: ['input', 'select', 'cascader'],
      isSearch: [
        'input',
        'select',
        'cascader',
        'date',
        'daterange',
        'checkbox',
      ],
      queryList: [],
      moreList: [],
      showMore: false,
    }
  },
  props: {
    formConfig: {
      type: Object,
      required: true,
    },
  },
  watch: {
    formConfig: {
      handler(val) {
        if (!val && !val.formItemList?.length) {
          return
        }
        val.formItemList.forEach((item, index) => {
          if (this.queryList.length < 4) {
            if (this.isSearch.includes(item.type)) {
              this.queryList.push(item)
            }
          } else {
            if (this.isSearch.includes(item.type)) {
              this.moreList.push(item)
            }
          }
        })
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.setDefaultValue()
  },
  methods: {
    componentsType(type) {
      if (type === 'switch' || type === 'checkbox') {
        return 'el-checkbox-group'
      } else if (type === 'time') {
        return 'el-time-picker'
      } else if (type === 'date') {
        return 'el-date-picker'
      } else if (this.isElementUI.includes(type)) {
        return 'el-' + type
      } else {
        return type
      }
    },

    // 根据value设置默认值
    setDefaultValue() {
      const formData = {}
      this.formConfig.formItemList.forEach((item) => {
        const { prop: key, value } = item
        formData[key] = value
      })
      this.formValue = formData
    },
    toggleMore() {
      this.showMore = !this.showMore
    },
    onSubmit() {
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.$emit('submit', this.formValue)
        }
      })
    },
    onReset() {
      const { reset } = this.$listeners
      if (reset) {
        this.$emit('reset', this.formValue)
      } else {
        this.$refs.searchForm.resetFields()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px;
  .el-row {
    width: 100%;
  }
  ::v-deep .actions-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .show-more {
    color: #606266;
    &:hover {
      color: #0973f1;
    }
    .el-icon-arrow-down,
    .el-icon-arrow-up {
      font-weight: 600;
      font-size: 16px;
    }
  }
  ::v-deep .form-item {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    .el-form-item__label {
      flex: 0 0 auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
