<template>
  <el-form-item
    v-if="!item.isHide"
    class="form-itemo"
    :rules="Rules"
    :label="item.label"
    :prop="item.key"
    :class="{ block: item.block }"
  >
    <el-input
      v-if="item.type === 'input'"
      v-model="value"
      v-bind="$attrs"
      :type="item.subtype"
      :min="item.min"
      :max="item.max"
      :minlength="item.minlength"
      :maxlength="item.maxlength"
      autocomplete="off"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :readonly="item.readonly"
      :autosize="item.autosize"
      :clearable="item.clearable"
      class="inputo"
      v-on="$listeners"
    >
      <template v-if="item.append" slot="append">{{ item.append }}</template>
    </el-input>

    <el-select
      v-else-if="item.type === 'select'"
      v-bind="$attrs"
      :multiple="item.multiple"
      :collapse-tags="item.collapseTags"
      :disabled="item.disabled"
      :props="item.props"
      :clearable="item.clearable"
      :multiple-limit="item.multipleLimit"
      class="selecto"
      v-on="$listeners"
    >
      <el-option
        v-for="o in item.options || ajaxOptions"
        :key="o[item.props.value]"
        :label="o[item.props.label]"
        :value="o[item.props.value]"
        :disabled="o.disabled"
      ></el-option>
    </el-select>

    <el-cascader
      v-else-if="item.type === 'cascader'"
      v-bind="$attrs"
      :options="item.options || ajaxOptions"
      :filterable="item.filterable"
      :change-on-select="item.changeOnSelect"
      :props="item.props"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :clearable="true"
      class="selecto"
      v-on="$listeners"
    ></el-cascader>

    <el-checkbox
      v-else-if="item.type === 'switch' && item.appearance === 'checkbox'"
      v-bind="$attrs"
      :disabled="item.disabled"
      v-on="$listeners"
    ></el-checkbox>

    <el-switch
      v-else-if="item.type === 'switch'"
      v-bind="$attrs"
      :disabled="item.disabled"
      v-on="$listeners"
    ></el-switch>

    <el-rate
      v-else-if="item.type === 'rate'"
      v-bind="$attrs"
      :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
      text-color="#ff9900"
      v-on="$listeners"
    ></el-rate>

    <el-color-picker
      v-else-if="item.type === 'color'"
      v-bind="$attrs"
      :show-alpha="item.showAlpha"
      :color-format="item.format"
      v-on="$listeners"
    ></el-color-picker>

    <el-slider
      v-else-if="item.type === 'slider'"
      v-bind="$attrs"
      :range="item.isRange"
      :show-stops="item.showStops"
      :step="item.step"
      :min="item.min"
      :max="item.max"
      v-on="$listeners"
    ></el-slider>

    <el-radio-group
      v-else-if="item.type === 'radio'"
      :disabled="item.disabled"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <component
        :is="item.button ? 'el-radio-button' : 'el-radio'"
        v-for="o in item.options || ajaxOptions"
        :key="o.code"
        :label="o.code"
        :disabled="o.disabled"
        :border="item.border"
      >{{ o.name }}</component
      >
    </el-radio-group>

    <el-checkbox-group
      v-else-if="item.type === 'checkbox'"
      :min="item.min"
      :max="item.max"
      v-bind="$attrs"
      :props="item.props"
      v-on="$listeners"
    >
      <component
        :is="item.button ? 'el-checkbox-button' : 'el-checkbox'"
        v-for="o in item.options || ajaxOptions"
        :key="o.value"
        :disabled="o.disabled"
        :label="o.code"
        :border="item.border"
      >{{ o.name }}</component
      >
    </el-checkbox-group>

    <el-time-picker
      v-else-if="item.type === 'time'"
      :is-range="item.isRange"
      range-separator="-"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :value-format="item.valueFormat"
      :format="item.valueFormat"
      default-time="12:00:00"
      :placeholder="item.placeholder"
      :picker-options="item.pickerOptions"
      v-bind="$attrs"
      v-on="$listeners"
    ></el-time-picker>

    <el-date-picker
      v-else-if="item.type === 'date'"
      :type="item.subtype"
      range-separator="-"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :value-format="item.valueFormat"
      :format="item.format"
      :placeholder="item.placeholder"
      :picker-options="item.pickerOptions"
      v-bind="$attrs"
      :disabled="item.disabled"
      v-on="$listeners"
    ></el-date-picker>
    <span v-else-if="item.type === 'txt'">{{ item.txt }}</span>
    <span v-else>未知控件类型</span>
  </el-form-item>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
    value: {},
  },
  data() {
    return {
      ajaxOptions: [],
      provinceData: [],
      city: [],
      area: [],
    };
  },
  computed: {
    Rules() {
      var that = this;
      const rules = that.item.rules;
      if (rules === undefined) return undefined;
      const R = [];
      rules.forEach((rule) => {
        // 请求验证
        if (rule.sql) {
          /* 请求验证 */
          const validator = (rule2, value, callback) => {
            that.$http
              .post(rule.sql, {
                // key: rule2.field,
                value,
                // sql: rule.sql.replace(/{key}/gi, rule2.field)
              })
              .then((res) => {
                // eslint-disable-next-line
                callback(!res.data.returnMsg || undefined)
              })
              .catch((err) => {
                console.log(err);
                that.$message.error(err.message);
                // eslint-disable-next-line
                callback(false)
              });
          };

          R.push({
            validator,
            message: rule.message,
            trigger: 'blur',
          });
          // 正则验证
        } else if (rule.pattern) {
          var reg = rule.pattern;
          const validator = (rule2, value, callback) => {
            if (value === '') {
              callback(new Error('必填哦'));
            } else if (!reg.test(value)) {
              callback(new Error(rule.message));
            } else {
              callback();
            }
          };
          R.push({
            validator,
            message: rule.message,
            trigger: 'blur',
          });
        } else {
          R.push(rule);
        }
      });
      return R;
    },
  }
};
</script>
<style lang="scss" scope>
.block {
  display: block !important;
  display: flex !important;
}
.inputo,
.selecto {
  width: 200px !important;
}
</style>
