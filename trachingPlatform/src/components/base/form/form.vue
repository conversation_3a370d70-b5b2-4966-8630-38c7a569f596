
<template>
  <el-form ref="baseForm" :model="formModel" :rules="rules" label-width="120px">
    <el-row v-if="tempFlag" :gutter="16">
      <el-col v-for="(item, i) in temp" :key="i" :span="12">
        <el-form-item
          :key="i"
          :label="item"
          :prop="'processColumns[' + i + '].userValue'"
          :rules="[
            { required: true, message: '输入不能为空', trigger: 'change' },
          ]"
        >
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  name: 'BaseForm',
  data() {
    return {
      formModel: {},
      rules: {},
    };
  },

  mounted() {},

  methods: {},
};
</script>

<style lang="scss" scoped></style>
