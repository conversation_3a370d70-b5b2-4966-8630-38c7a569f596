<template>
  <div class="scene-content">
    <!-- 数据源 -->
    <div class="scene-content-wrapper data-source-wrapper" v-if="sceneSchema.sceneType == 2">
      <outerDataSource @handleChildrenDataLoaded="handleChildrenDataLoaded" openType="dialog"
        :dataSourceId="sceneSchema.id" :formatBtns="formatDataSourceBtns" />
    </div>
    <div class="scene-content-wrapper scene-form-wrapper" v-else>
      <!-- 动态表单 -->
      <outerForm :notShowFlow="true" @handleChildrenDataLoaded="handleChildrenDataLoaded"
        v-if="sceneSchema.formType == 1" :type="formShowType" :moduleId="sceneSchema.id" :dataId="singleData.id"
        :getSingleData="getFormSingleData" :submitData="submitFormData" v-on="$listeners">
      </outerForm>
      <!-- 葡萄城 -->
      <outerSheet :title="title" :notShowFlow="true" @handleChildrenDataLoaded="handleChildrenDataLoaded"
        v-else-if="sceneSchema.formType == 2" :erId="erId" :dataId="singleData.id" :type="formShowType"
        :moduleId="sceneSchema.id" v-on="$listeners" :singleData="singleData">
      </outerSheet>
      <!-- 通用票据 -->
      <billWithDispatch :notShowFlow="true" @handleChildrenDataLoaded="handleChildrenDataLoaded"
        v-else-if="sceneSchema.formType == 3" :sceneId="sceneSchema.id" :dataId="singleData.id" :singleData="singleData"
        :isView="formShowType === 'detail'" v-on="$listeners" />
      <!--附件类型的场景 -->
      <showAttachment v-else-if="sceneSchema.formType == 7" :attachmentId="sceneSchema.id"></showAttachment>
    </div>
  </div>
</template>
<script>

export default {
  name: "sceneContent",
  components: {
    billWithDispatch: () => import("@/components/base/bill/bill-with-dispatch.vue"),
    outerDataSource: () => import('@/components/designer/page-designer/outer-usage.vue'),
    outerForm: () => import('@/components/designer/form-designer/outer-usage.vue'),
    // outerSheet: () => import('@/components/designer/sheets-designer/outer-usage.vue'),
    outerSheet: () => import('@/components/designer/sheets-designer/outer-usage.vue'),
    showAttachment: () => import("@/components/attachment/show-attachment.vue"),// 附件类型的场景
  },
  props: {
    // 场景的标题，可用与葡萄城导出的文件名
    title: {
      type: String,
    },
    // 动态表单显示的类型，edit，add，detail
    formShowType: {
      type: String,
      default: 'detail',
    },
    // 显示动态表单或者葡萄城时需要填充的数据
    singleData: {
      type: Object,
      default: () => ({ })
    },
    // 场景或者数据源描述
    sceneSchema: {
      type: Object,
      default: () => { }
    },
    // 子节点获取数据之后的回调，用于父组件相关控制逻辑的实现
    handleChildrenDataLoaded: {
      type: Function,
    },
    // 定制数据源行显示的按钮
    formatDataSourceBtns: {
      type: Function,
    },
    // TODO：葡萄城需要erId，该属性应该可以优化掉
    erId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
    };
  },
  computed: {
    dialogVisible: {
      set: function (val) {
        this.$emit("update:visible", false);
      },
      get: function () {
        return this.visible;
      }
    },
  },
  mounted() {
    // console.log("sceneSchema-------",this.sceneSchema);
    // console.log("singleData-------",this.singleData);
  },

  methods: {
    // 动态表单获取数据
    async getFormSingleData(params) {
      const res = await this.$api.FetchDispatchSingleData({
        id: this.singleData.id,
        ...params
      })
      return new Promise(function (resolve, reject) {
        resolve(res)
      })
    },
    // 动态表单提交数据
    async submitFormData(req) {
      const res = await this.$api.SaveDynamicFormData(req)
      return new Promise(function (resolve, reject) {
        resolve(res)
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.scene-content{
  // display: flex;
  // flex-direction: column;
  flex: 1 1 auto;
  min-height: 0;
  height:100%;
  .scene-content-wrapper {
    height:100%;
  }
}

</style>
