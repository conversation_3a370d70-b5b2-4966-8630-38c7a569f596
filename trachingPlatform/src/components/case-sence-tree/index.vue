
<template>
  <div class="case-sence-tree">
    <baseTree
      v-loading="loading"
      ref="sceneTree"
      labelName="menuName"
      :treeData="treeData"
      placeholder="搜索业务场景"
      :defaultProps="defaultProps"
      :handleNodeClick="handleNodeClick">
      <div slot="header" class="case-name">
        {{ courseInfo?.cases[0]?.caseName }}
      </div>
      <div slot-scope="{ node, data }" class="custom-tree-node" :title="node.label">
        <span class="label-name leaf-label"><i :class="['iconfont', data.sceneType == 1 ? 'icon-changjing' : data.sceneType == 2 ? 'icon-a-renwuqingdan2' : '']"></i>
          {{ node.label }}</span>
      </div>
    </baseTree>
  </div>
</template>

<script>
import baseTree from "@/components/base/tree.vue";
import { flatData } from '@/utils/base';
import { mapGetters } from "vuex";
export default {
  name: "",
  components: { baseTree },
  props: {},
  data() {
    return {
      treeData: [],
      currentKey: "",
      defaultProps: {
        children: "children",
        // label: "name"
        label: "menuName"
      },
      loading:false,
    };
  },
  computed: {
    ...mapGetters({
      courseInfo: ["getCourseInfo"]
    })
  },
  watch: {
    searchKey(val) {
      this.$refs.sceneTree.filter(val);
    }
  },
  mounted() {
    this.initScene();
  },
  methods: {
    //  初始化界面
    async initScene() {
      this.loading = true;
      // this.treeData = await loadModuleWithScene(this.$api, this.$message);
      const { data } = await this.$api.GetMenuSceneTree({ });
      if (data) {
        this.treeData = data;
        const flatD = flatData(data);
        if (this.$refs.sceneTree){
          this.$refs.sceneTree.selectFirstNode(flatD.find(f => f.level == 2));
        }
        this.loading = false;
      }
    },
    // 选中树的第一条数据
    getFirstItem() {

    },
    /**
     * 树节点点击事件
     */
    handleNodeClick(data) {
      this.$emit("clickSceneNode", data);
    }
  }
};
</script>

<style scoped lang="scss">
.case-sence-tree {
  width: 270px;
  height: 100%;
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
  background-color: #fdfefe;
  display: flex;
  flex-direction: column;

  .case-name {
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    font-family: " PingFang SC, PingFang SC";
    font-weight: 400;
    color: #333;
    background: #f7f7f7;
    text-align: center;
  }
}
</style>
