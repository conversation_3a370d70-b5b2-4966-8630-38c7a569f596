<!-- 饼状图 -->
<template>
  <div id="chartPie"></div>
</template>

<script>
export default {
  props: {
    //饼状图数据
    pieOptionsData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {

    };
  },
  watch: {
    pieOptionsData: {
      handler(val) {
        this.$nextTick(() => {
          this.drawPieChart();
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /**
         * 生成饼状图
         */
    drawPie<PERSON>hart() {
      const chartPie = echarts.init(document.getElementById("chartPie"));
      chartPie.setOption(
        {
          title: {
            text: '',
            subtext: '',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20,
            data: this.pieOptionsData.legendData
          },
          series: [
            {
              name: '名称',
              type: 'pie',
              radius: '55%',
              center: ['40%', '50%'],
              data: this.pieOptionsData.seriesData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
      );
    },

  }
};
</script>

<style lang="scss" scoped>
#chartPie {
    width: 100%;
    height: 650px;
}
</style>
