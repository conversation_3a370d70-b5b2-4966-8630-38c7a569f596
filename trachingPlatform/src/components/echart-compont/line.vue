<template>
  <section>
    <div class="Eharts">
      <div id="chartLine" style="width:100%; height:650px;"></div>
    </div>
  </section>
</template>
<script>
export default {
  components: {},
  props: {
    lineOptionsData: {
      type: Object,
      default: {}
    },
  },
  data() {
    return {
      LineOptions: {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {},
        dataZoom: [
          {
            show: true,
            realtime: true,
            start: 0,
            end: 60
          },
          {
            type: 'inside',
            realtime: true,
            start: 0,
            end: 60,
            moveOnMouseWheel: true
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.lineOptionsData.xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: [...this.lineOptionsData.seriesData]
      },
      chartObj: null,
    };
  },
  computed: {

  },
  watch: {
    lineOptionsData: {
      handler(val) {
        if (this.chartObj) {
          this.chartObj.dispose();
        }
        this.$nextTick(() => {
          this.LineOptions.xAxis.data = this.lineOptionsData.xAxisData;
          this.LineOptions.series = [...this.lineOptionsData.seriesData];
          this.drawLineChart();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {

  },
  methods: {
    drawLineChart() {
      this.chartObj = echarts.init(document.getElementById("chartLine"));
      this.chartObj.setOption(this.LineOptions);
    },
  },
};
</script>
<style lang="scss" >
.Eharts {}
</style>
