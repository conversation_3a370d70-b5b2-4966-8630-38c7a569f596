<template>
  <el-dialog class="echart-dialog" :title="chartOptionData.title" @close="handleClose" v-if="isChartDialog"
    :visible.sync="isChartDialog" width="1000px" :show-close="true" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <div class="choseecharts">
      <el-select v-model="chartType" placeholder="请选择图表" @change="handleEcharts">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <Lines v-if="chartType == 1" :lineOptionsData="chartOptionData" />
    <pie v-if="chartType == 3" :pieOptionsData="chartOptionData" />
    <bar v-if="chartType == 2" :barOptionsData="chartOptionData" />
    <unit-pie v-if="chartType == 4" :dataset="chartOptionData" />
  </el-dialog>
</template>

<script>
import pie from "./pie.vue"
import bar from "./bar.vue"
import Lines from "./line.vue"
import UnitPie from "./UnitPie.vue"
export default {
  props: {
    //数据源
    chartOptionData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    //弹窗开关
    isChartOpen: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  components: { pie, UnitPie, bar, Lines },
  data() {
    return {
      options: [{
        value: 1,
        label: '折线图'
      }, {
        value: 2,
        label: '柱状图'
      }, {
        value: 3,
        label: '饼图'
      }, {
        value: 4,
        label: '数据集'
      }],
      chartType: 1,
      isChartDialog: false,
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.handleEcharts(1);
    });
  },
  watch: {
    isChartOpen: {
      handler(val) {
        this.isChartDialog = val;
      },
      immediate: true
    },
    isChartDialog: {
      handler(val) {
        if (!val) {
          this.chartType = 1;
        }
      },
      immediate: true
    },
  },
  methods: {
    handleEcharts(e) {
      this.ehartsType = e;
      this.$emit('selectType', e);
    },
    handleClose() {
      this.isChartDialog = false;
      this.$emit('close-active');
    },
  }
}
</script>

<style lang="scss"></style>