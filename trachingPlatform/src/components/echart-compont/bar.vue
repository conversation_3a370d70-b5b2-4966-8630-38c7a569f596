<!--
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-10 08:53:59
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-02-23 13:40:27
 * @FilePath: \fusion_front\src\components\echart-compont\bar.vue
 * @Description: 饼状图
-->

<template>
  <section :class="this.barOptionsData.width ? 'active-page' : 'bar-page'">
    <div id="chartPie" :style="`width:100%; height:${this.barOptionsData.width ? this.barOptionsData.width : ''};`"></div>
  </section>
</template>
<script>
export default {
  props: ['barOptionsData'],
  data() {
    return {
      barOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        series: [...this.barOptionsData.seriesData],
      },
      chartPieB: null,
    };
  },
  watch: {
    barOptionsData: {
      handler(val) {
        if (this.chartPieB) {
          this.chartPieB.dispose();
        }
        this.$nextTick(() => {
          this.initBar();
        });
      },
      immediate: true,
      deep: true,
    }

  },
  mounted() {
    // this.initBar();
  },
  methods: {
    /**
     * 初始化 柱状图
     */
    initBar() {
      this.chartPieB = echarts.init(document.getElementById("chartPie"));
      this.chartPieB.setOption(this.barOption);
    }
  }
};
</script>

<style lang="scss">
.bar-page {
  width: 960px;
}

.active-page {
  width: 100%;
}
</style>
