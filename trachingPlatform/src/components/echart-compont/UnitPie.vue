
<!-- 集合饼状图 -->
<template>
  <div id="chartPieC" style="width:100%; height:650px;"></div>
</template>

<script>
export default {
  props: {
    dataset: {
      type: Object,
      default: {},
    }
  },
  data() {
    return {
      datasetObject: {}
    }
  },
  mounted() {
    this.drawConChart();
  },
  watch: {
    dataset: {
      handler(val) {
        if (val) {
          this.datasetObject = val;

          console.log('this.datasetObject', this.datasetObject);
        }
      },
      immediate: true,
    },
  },
  methods: {
    drawConChart() {
      let chartPie = echarts.init(document.getElementById("chartPieC"));
      chartPie.on('updateAxisPointer', function (event) {
        const xAxisInfo = event.axesInfo[0];
        if (xAxisInfo) {
          const dimension = xAxisInfo.value + 1;
          chartPie.setOption({
            series: {
              id: 'pie',
              label: {
                formatter: '{b}: {@[' + dimension + ']} ({d}%)'
              },
              encode: {
                value: dimension,
                tooltip: dimension
              }
            }
          });
        }
      });
      chartPie.setOption(
        {
          legend: {},
          tooltip: {
            trigger: 'axis',
            showContent: false
          },
          dataset: {
            source: this.datasetObject.dataSource
          },
          dataZoom: [
            {
              show: true,
              realtime: true,
              start: 0,
              end: 20
            },
            {
              type: 'inside',
              realtime: true,
              start: 0,
              end: 20,
              moveOnMouseWheel: true
            }
          ],
          xAxis: { type: 'category' },
          yAxis: { gridIndex: 0 },
          grid: { top: '55%' },
          series: [
            {
              type: 'line',
              smooth: true,
              seriesLayoutBy: 'row',
              emphasis: { focus: 'series' }
            },
            {
              type: 'line',
              smooth: true,
              seriesLayoutBy: 'row',
              emphasis: { focus: 'series' }
            },
            {
              type: 'line',
              smooth: true,
              seriesLayoutBy: 'row',
              emphasis: { focus: 'series' }
            },
            {
              type: 'line',
              smooth: true,
              seriesLayoutBy: 'row',
              emphasis: { focus: 'series' }
            },

            {
              type: 'pie',
              id: 'pie',
              radius: '30%',
              center: ['50%', '25%'],
              emphasis: {
                focus: 'self'
              },
              label: {
                formatter: '{b}: {@2012} ({d}%)'
              },
              encode: {
                itemName: this.datasetObject.columname,
                value: this.datasetObject.defaultname,
                tooltip: this.datasetObject.defaultname
              }
            }
          ]
        }
      )
    },
  }
}
</script>

<style lang="scss" scoped>
#chartPieC {
  width: 100%;
  height: 650px;
}
</style>