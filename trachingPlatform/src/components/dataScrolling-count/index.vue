
<template>
  <span ref="counter"></span>
</template>

<script>
import { CountUp } from 'countup.js';

export default {
  props: {
    endValue: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      countUpInstance: null
    };
  },
  mounted() {
    this.initCountUp();
  },
  watch: {
    endValue(newValue) {
      setTimeout(() => {
        if (this.countUpInstance) {
          this.countUpInstance.update(newValue);
        }
      }, 400);
    }
  },
  methods: {
    initCountUp() {
      this.countUpInstance = new CountUp(this.$refs.counter, this.endValue, {
        duration: 2, // 动画持续时间（秒）
        separator: ',', // 逗号分隔符
        decimal: '.', // 小数点符号
        prefix: '', // 前缀
        suffix: '' // 后缀
      });
      if (!this.countUpInstance.error) {
        this.countUpInstance.start();
      } else {
        console.error(this.countUpInstance.error);
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>