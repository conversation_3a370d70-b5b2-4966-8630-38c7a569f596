<template>
  <div class="select">
    <!--@visible-change="(v) => visibleChange(v, 'template')"-->
    <!--<span style="line-height: 12px;-->
    <!--color: red;-->
    <!--height: 14px;-->
    <!--width: 14px;-->
    <!--text-align: center;-->
    <!--border: 1px solid red;-->
    <!--border-radius: 16px;-->
    <!--font-size: 8px;-->
    <!--position: absolute;-->
    <!--top: 6px;-->
    <!--left: 0;-->
    <!--z-index: 2;">{{inputIndex +1}}</span>-->
    <el-select
        v-model="courseSelectContent.answerCont"
        placeholder="请选择选项！"
        ref="template"
        :style="{ width: '200px' }"
        filterable
        clearable
        @change="onChange"
    >
      <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
      >
        <span class="span-style">{{ item.label }}</span>
        <span v-if="!item.label && editorState !=='view'" style="float: right;margin-left: 10px" @click.stop="addItem()"><i class="el-icon-circle-plus-outline"/></span>
        <span v-if="item.label && editorState !=='view'" style="float: right;" @click.stop="deleteItem(item)"><i class="el-icon-remove-outline"/></span>
        <span v-if="item.label && editorState !=='view'" style="float: right; margin: 0 10px" @click.stop="editItem(item)"><i class="el-icon-edit-outline"/></span>
      </el-option>
    </el-select>
    <div v-if="!courseSelectContent.answerCont && editorState !=='view'" class="const-rules">请选择正确答案！</div>
  </div>
</template>

<script>
export default {
  props:{
    courseSelectContent:{
      type: Object,
      default: ()=>{
        return {}
      }
    },
    editorState:{
      type: String,
      default: ''
    },
    // inputIndex:{
    //   type: Number,
    //   default: 0
    // }
  },
  data(){
    return {
      options: []
    }
  },
  created(){
    this.options = this.courseSelectContent?.selectOptions || []
    if(window.sessionStorage.getItem('editorState') !== 'view'){
      this.options.unshift({label:''})
    }
  },
  methods: {
    onChange(e){
      this.$emit('selectValue', e)
    },
    editItem(item) {
      // 本地编辑
      this.addItem(item.value);
    },
    deleteItem(item) {
      this.$confirm("确定要删除" + "' " + item.label + " '" + "吗？", "删除", {
        type: "warning",
      })
          .then(() => {
            // 本地静态删除
            if(this.courseSelectContent.answerCont === item.value){
              this.courseSelectContent.answerCont = null
              this.$emit('selectValue', null)
            }
            this.options = this.options.filter((e) => e.value != item.value);
            this.$emit('selectOptions',this.options)
          })
          .catch(() => {});
    },
    // 添加产品
    addItem(val) {
      if (val && val != null) {
        // 编辑修改
        let index = this.options.findIndex((e) => e.value == val);
        let obj = this.options[index];
        this.$prompt("请编辑选项", "编辑", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: obj.label,
        }).then(async ({ value }) => {
          if (!value || value == "") {
            this.$message({
              type: "error",
              message: "选项不能为空!",
            });
          } else {
            let index = this.options.findIndex((e) => e.value == val);
            this.options[index].label = value;
            this.$emit('selectOptions',this.options)
          }
        });
      } else {
        // 新增
        this.$prompt('请输入新选项', "新增", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(async ({ value }) => {
          // 提交内容为空时提示
          if (!value || value == "") {
            this.$message({
              type: "error",
              message: "选项不能为空!",
            });
          } else {
            // 本地静态新增，正常使用可以调接口新增
            let obj = {
              value: value,
              label: value,
            };
            this.options.push(obj);
            this.$emit('selectOptions',this.options)
          }
        });
      }
    },
    // // 下拉时显示底下的新增按钮
    // visibleChange(visible, refName) {
    //   if (visible) {
    //     const ref = this.$refs[refName];
    //     let product = ref.$refs.popper;
    //     if (product.$el) product = product.$el;
    //     if (
    //         !Array.from(product.children).some(
    //             (v) => v.className === "el-template-menu__list"
    //         )
    //     ) {
    //       const el = document.createElement("div");
    //       el.className = "el-template-menu__list";
    //       el.style =
    //           "border-top:2px solid rgb(219 225 241); padding:0; color:rgb(64 158 255);font-size: 13px";
    //       el.innerHTML = `<li class="el-cascader-node text-center" style="height:37px;line-height: 50px;text-align: center">
    //         <span class="el-cascader-node__label"><i class="font-blue el-icon-plus"></i> 新增选项</span>
    //         </li>`;
    //       // el.innerHTML = `
    //       //              <div style="display: flex;justify-content: center;align-content: center;height: 50px;text-align: center;font-weight: 400;font-size: 14px;">
    //       //               <el-button style="cursor: pointer;border: 1px solid #DDE2E9;width: 80px;height: 34px;line-height: 34px;color: #333333;border-radius: 5px">取消</el-button>
    //       //               <el-button style="margin-left: 20px;cursor: pointer;width: 80px;height: 34px;line-height: 34px;background: #2B66FF;color: #fff;border-radius: 5px" type="primary;">确定</el-button>
    //       //              </div>
    //       //               `;
    //       product.appendChild(el);
    //       // 新增按钮点击事件
    //       el.onclick = () => {
    //         this.addItem(null);
    //       };
    //     }
    //   }
    // },
  },
};
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
  height: 100%;
  display: flex;
  //justify-content: center;

  .const-rules{
    text-align: left;
    width: 120px;
    height: 12px;
    font-size: 10px;
    color: #f60505;
    position: absolute;
    left: 3px;
    top:25px
  }
}
::v-deep .el-select-dropdown__list{
  padding: 16px 0 !important;
}
.span-style{
  float: left;
  height: 28px;
  line-height: 28px;
  padding-left: 4px;
  z-index: 9;
}

</style>
