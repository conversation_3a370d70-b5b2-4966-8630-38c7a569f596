# 全局缺省页

## 组件说明
> 全局的缺省页 类型包含暂无案例 暂无内容 搜索无结果 暂无数据 未连接网络 网页正在建设中

## 组件使用
  已经注册全局组件可以直接使用
```js
import zdDefaultPage from "@/components/zd-default-page/index.vue";
```
```vue
   <zdDefaultPage size="small" msg="暂无数据" type="noData"></zdDefaultPage>
```
# Attributes

| 参数        | 类型      | 可选值     | 默认值   | 说明      | 备注     |
|------------|-----------|------------|---------|-----------|----------|
| loading    | Boolean   |            |       |         |          |
| msg    | String   |            |       |         |          |
| type    | String   |  'noResult', 'empty' ,'noContent' ,'noNetwork','developing','noData'      | noResult     |         |          |
| size    | String   |  small, middle, large        |    small   |         |          |

# Events
  > 无