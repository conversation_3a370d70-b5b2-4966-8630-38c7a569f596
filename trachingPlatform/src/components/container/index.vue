<template>
  <div id="container" ref="container"></div>
</template>

<script>
import { Graph, Shape } from "@antv/x6";
import { Dnd } from '@antv/x6-plugin-dnd'
import { Transform } from '@antv/x6-plugin-transform'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { Selection } from '@antv/x6-plugin-selection'
import { History } from '@antv/x6-plugin-history'
export default {
  data() {
    return {
      graph: null,
      dnd: null,
      shape: null,
    };
  },
  props:{
    fromJson:{
      type: Object,
      default: ()=>{
        return {
          nodes: [],
        }
      }
    },
    dndContainer: {
      type: HTMLElement,
      default: null
    }
  },
  created() {
    this.$nextTick(()=>{
      // 创建容器及配置
      this.graph = new Graph({
        container: this.$refs.container,
        grid: {
          visible: true,
          size: 20,
          args: {
            color: '#e8e8e8',
            thickness: 2.5
          }
        },
        autoResize: true,
        translating: {
          restrict: true,
        },
        mousewheel: {
          enabled: true,
          modifiers: 'Ctrl',
          maxScale: 4,
          minScale: 0.2,
        },
        background: {
          color: '#fafafa', // 设置画布背景颜色
        },
        embedding: {
          enabled: true,
          findParent({ node }) {
            const bbox = node.getBBox()
            return this.getNodes().filter((node) => {
              const data = node.getData()
              if (data && data.parent) {
                const targetBBox = node.getBBox()
                return bbox.isIntersectWithRect(targetBBox)
              }
              return false
            })
          },
        },
        // grid: {
        //   size: 10,      // 网格大小 10px
        //   visible: true, // 渲染网格背景
        // },
        panning: {enabled: true,
          modifiers: [],
          eventTypes: ['leftMouseDown'],
        },
        connecting: {
          snap: true, // 是否自动吸附
          allowMulti: true, // 是否允许在相同的起始节点和终止之间创建多条边
          allowNode: false, // 是否允许边链接到节点（非节点上的链接桩）
          allowBlank: false, // 是否允许连接到空白点
          allowLoop: false, // 是否允许创建循环连线，即边的起始节点和终止节点为同一节点，
          allowEdge: false, // 是否允许边链接到另一个边
          highlight: true, // 拖动边时，是否高亮显示所有可用的连接桩或节点

          router: {
            name: 'manhattan',
            args: {
              startDirections: ['top', 'right', 'bottom', 'left'],
              endDirections: ['top', 'right', 'bottom', 'left'],
            },
          },
          connector: {
              name: 'rounded',
              // args: { radius: 10, },
          },
          anchor: 'center',
          connectionPoint: 'anchor',
          createEdge() {
            return new Shape.Edge({
              attrs: {
                line: {
                  stroke: '#6366F1',
                  strokeWidth: 2,
                  targetMarker: {
                    name: 'block',
                    width: 12,
                    height: 8,
                  },
                },
              }
            })
          },
          validateConnection ({ targetMagnet }) {
            return !!targetMagnet
          },
        },
        highlighting: {
          // 连接桩可以被连接时在连接桩外围围渲染一个包围框
          magnetAvailable: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#fff',
                stroke: '#A4DEB1',
                strokeWidth: 4,
              },
            },
          },
          // 连接桩吸附连线时在连接桩外围围渲染一个包围框
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#fff',
                stroke: '#31d0c6',
                strokeWidth: 4,
              },
            },
          },
        },
      });
      // 节点大小变换
      this.graph.use(
        new Transform({
          resizing: {
            enabled: true,
            grid: 1,
            // preserveAspectRatio: true
          },
        }),
      )
      // 对齐线
      this.graph.use(
          new Snapline({
            enabled: true,
          }),
      )
      // 快捷键
      this.graph.use(
          new Keyboard({
            enabled: true,
          }),
      )
      // 撤销/重做
      this.graph.use(
          new History({
            enabled: true,
          }),
      )
      // 框选
      this.graph.use(
          new Selection({
            enabled: true,
            showNodeSelectionBox: true,
          }),
      )
      // 复制/黏贴
      this.graph.use(
          new Clipboard({
            enabled: true,
          }),
      )
      // 复制
      this.graph.bindKey('ctrl+c', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          // 加上当前节点id  便于粘贴时可以知道来源节点id
          cells.forEach(ce => {
            this.$set(ce, 'label', ce.id)
          })
          this.graph.copy(cells)
        }
        return false
      })
      // 黏贴
      this.graph.bindKey('ctrl+v', () => {
        if (!this.graph.isClipboardEmpty()) {
          const cells = this.graph.paste({ offset: 32 })
          this.graph.cleanSelection()
          this.graph.select(cells)
          this.$emit('copyNode', cells)
        }
        return false
      })
      // 删除
      this.graph.bindKey('backspace', () => {
        const cells = this.graph.getSelectedCells()
        if (cells.length) {
          this.graph.removeCells(cells)
          this.$emit('removeNode', '')
        }
      })

      // 链接桩配置
      const ports = {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
        },
        items: [
          {
            group: 'top',
          },
          {
            group: 'right',
          },
          {
            group: 'bottom',
          },
          {
            group: 'left',
          },
        ],
      }
      const portsPolygon = {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#5F95FF',
                strokeWidth: 1,
                fill: '#fff',
                style: {
                  visibility: 'hidden',
                },
              },
            },
          },
        },
        items: [
          {
            group: 'top',
          },
          {
            group: 'bottom',
          },
          {
            group: 'left',
          },
        ],
      }
      const showPorts = (ports, show) => {
        for (let i = 0, len = ports.length; i < len; i += 1) {
          ports[i].style.visibility = show ? 'visible' : 'hidden'
        }
      }
      // 自定义节点
      // 群组
      Graph.registerNode(
          'custom-circle-parent',
          {
            inherit: 'rect',
            ports: { ...ports },
          },
          true,
      )
      // 矩形节点
      Graph.registerNode(
          'custom-circle-rect',
          {
            inherit: 'rect',
            // attrs 可自定义
            ports: { ...ports },
          },
          true,
      )
      // 菱形节点
      Graph.registerNode(
          'custom-circle-polygon',
          {
            inherit: 'polygon',
            points: '0,10 10,0 20,10 10,20',
            ports: {
              ...portsPolygon,
            },
          },
          true,
      )
      // 圆形节点
      Graph.registerNode(
          'custom-circle-start',
          {
            inherit: 'circle',
            ports: { ...ports },
          },
          true,
      )
      // 图片节点
      Graph.registerNode(
          'custom-circle-image',
          {
            inherit: 'rect',
            ports: { ...ports },
          },
          true,
      )

      this.graph.on('node:change:parent', ({ node }) => {
        // 群组子节点变化监听
      })
      // 添加节点事件
      this.graph.on('node:added', ({ cell }) => {
        this.$emit('addedNode', cell)
      })
      // 移入事件
      this.graph.on('cell:mouseenter', ({ cell }) => {
        this.shape = cell
        const container = this.$refs.container
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, !cell.attrs.typeName);
      })
      // 节点移出事件
      this.graph.on('node:mouseleave', ({node}) => {
        const container = this.$refs.container
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })
      // 连接线移出事件
      this.graph.on('edge:mouseleave', ({ cell }) => {
        const container = this.$refs.container
        const ports = container.querySelectorAll('.x6-port-body')
        showPorts(ports, false)
      })
      // 节点点击事件
      this.graph.on('node:click', ({ x, y, node, cell }) => {
        this.currentCell = cell;
        if (cell.isNode() && !cell.attrs.typeName) {
          // 点击节点时和右侧表单交互的效果
          this.$emit('clickNode', node)
        }
        if (cell.hasTool('button')) {
          cell.removeTool('button');
        }
      })
      // 画布点击事件
      this.graph.on('blank:click', () => {
        this.$emit('clickNode', false)
        // this.currentCell && this.currentCell.removeTools();
        if (this.currentCell) {
          this.currentCell.removeTool('button');
          this.currentCell.removeTool('button-move');
        }
        this.isClose = true;
        this.isGloable = true;
      })
      // 条件节点上下连接线label处理
      this.graph.on('edge:connected', ({ isNew, edge }) => {
        const source = edge.getSourcePortId()
        if (isNew) {
          if(this.shape.shape === 'custom-circle-polygon'){
            let text = ''
            if(this.shape.ports.items[0].id === source && this.shape.ports.items[0].group === 'top'){
              text = 'true'
            }else if(this.shape.ports.items[1].id === source && this.shape.ports.items[1].group === 'bottom'){
              text = 'false'
            }
            const labels = edge.getLabels();
            if(!labels.length){
              labels.push({attrs:{label:{text:text}}})
            }else{
              labels[0].attrs.label.text = 'false'
            }
            edge.setLabels(labels)
          }
        }
        this.$emit('addEdge', '')
      })
      this.graph.on('edge:removed', () => {
        this.$emit('removeEdge', '')
      })
      // 数据回显
      this.graph.fromJSON(this.fromJson)
    })
  },
  methods: {
    // 拖拽节点
    startDrag(type, e){
      const parentNode = this.graph.createNode({
        shape: 'custom-circle-parent',
        width: 500,
        height: 140,
        zIndex: 1,
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#3EB349',
            fill: 'rgba(255,255,255,0)',
            rx: 10,
            ry: 10,
          },
          label: {
            refX: 10,
            refY: 10,
            textAnchor: 'left',
            text: '循环体',
            textVerticalAnchor: 'top',
            fontSize: 14,
            fill: '#3EB349',
          },
        },
        data: {
          parent: true,
        },
      })
      const imageNode = this.graph.createNode({
        shape: 'custom-circle-image',
        width: 52,
        height: 52,
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'image',
          },
          {
            tagName: 'text',
            selector: 'label',
          },
        ],
        attrs: {
          body: {
            stroke: 'rgb(95,149,255)',
            fill: 'rgba(95,149,255,0)',
          },
          image: {
            // "xlink:href": require("./antv.png"),
            'xlink:href':
                'https://gw.alipayobjects.com/zos/antfincdn/FLrTNDvlna/antv.png',
            width: 26,
            height: 26,
            refX: 13,
            refY: 16,
          },
          label: {
            refX: 3,
            refY: 2,
            textAnchor: 'left',
            textVerticalAnchor: 'top',
            fontSize: 12,
            fill: '#fff',
          },
        },
      })
      const startNode = this.graph.createNode({
        shape: 'custom-circle-start',
        width: 50,
        height: 50,
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'image',
          },
          {
            tagName: 'text',
            selector: 'label',
          },
        ],
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#2ECC71',
            fill: '#2ECC71',
            rx: 10,
            ry: 10,
          },
          image: {
            "xlink:href": require("../../assets/container/start.png"),
            width: 50,
            height: 50,
          },
          label: {
            text: '开始',
            fontSize: 13,
            refY: 50,
            textVerticalAnchor: 'bottom',
          }
        },
      })
      const polygonNode = this.graph.createNode({
        shape: 'custom-circle-polygon',
        width: 88,
        height: 60,
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#F59B22',
            fill: 'rgb(255, 233, 208)',
            rx: 10,
            ry: 10,
          },
          label: {
            text: '条件节点',
            fontSize: 13,
            fill: '#F59B22'
            // refY: 30,
          },
        },

      })
      const rectNode = this.graph.createNode({
        shape: 'custom-circle-rect',
        width: 120,
        height: 40,
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'image',
          },
          {
            tagName: 'text',
            selector: 'label',
          },
        ],
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: 'rgb(99, 102, 241)',
            fill: 'rgb(238, 242, 255)',
            rx: 10,
            ry: 10,
          },
          // image: {
          //   "xlink:href": require("../../assets/container/numbers.png"),
          //   width: 50,
          //   height: 50,
          // },
          label: {
            text: '数据节点',
            fontSize: 13,
            fill: 'rgb(99, 102, 241)',
            refY: 20,
            // textVerticalAnchor: 'bottom',
          }
        },
      })
      const endNode = this.graph.createNode({
        shape: 'custom-circle-start',
        width: 50,
        height: 50,
        key: 'end',
        markup: [
          {
            tagName: 'rect',
            selector: 'body',
          },
          {
            tagName: 'image',
          },
          {
            tagName: 'text',
            selector: 'label',
          },
        ],
        attrs: {
          body: {
            strokeWidth: 1,
            stroke: '#2ECC71',
            fill: '#2ECC71',
            rx: 10,
            ry: 10,
          },
          image: {
            "xlink:href": require("../../assets/container/end.png"),
            width: 50,
            height: 50,
          },
          label: {
            text: '结束',
            fontSize: 13,
            refY: 50,
            textVerticalAnchor: 'bottom',
          }
        },
      })
      let dragNode;
      if (type === 'start') {
        dragNode = startNode
      } else if (type === 'end') {
        dragNode = endNode
      } else if (type === 'rect') {
        dragNode = rectNode
      } else if (type === 'polygon') {
        dragNode = polygonNode
      } else if (type === 'image') {
        dragNode = imageNode
      } else if (type === 'parent') {
        dragNode = parentNode
      }
      this.dnd = new Dnd({
        target: this.graph,
        dndContainer: this.dndContainer
      })
      this.dnd.start(dragNode, e)
    },
    // 撤销
    onUndo() {
      this.graph.undo()
    },
    // 重做
    onRedo() {
      this.graph.redo()
    },
    // 获取画布json数据
    onToJson(){
      return this.graph.toJSON()
    },
    // 回显画布数据
    onFromJson(data){
      this.graph.fromJSON(data)
    },
    // 获取节点json数据
    getNodesJson(){
      return this.graph.getNodes()
    },
    // 获取边线json数据
    getEdgesJson(){
      return this.graph.getEdges()
    },
    // 放大
    onZoomLarge(){
      this.graph.zoom(0.1)
      this.graph.centerContent()
    },
    // 缩小
    onZoomSmall(){
      this.graph.zoom(-0.1)
      this.graph.centerContent()
    },
    // 复原
    onZoomTo(){
      this.graph.zoom(1)
      this.graph.centerContent()
    }
  },
};
</script>

<style lang="scss" scoped>
#container {
  width: 100%;
  height: 100%;
}
</style>
