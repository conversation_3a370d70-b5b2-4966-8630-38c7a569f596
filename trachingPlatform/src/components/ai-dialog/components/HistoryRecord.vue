<template>
  <div class="historycontent">
      <div class="hisheader">
        <div class="hisheader-l">历史会话</div>
        <div class="hisheader-r"><i class="el-icon-arrow-right arrowesize" @click="handlehisclose"></i></div>
      </div>

      <div class="hisrecordList">
          <div :class="[currentchatIndex == index?'recordItemactive recordItem':'recordItem']" v-for="(item,index) in chatList" :key="index" @click="handlechatItem(item,index)">
              <div class="recordItem-l">
                <img src="../../../assets/discussion/recordicon.png"/>
              </div>
              <div class="recordItem-m">
                {{item.title}}
              </div>
              <div class="recordshowdom"></div>
              <div class="recordItem-r">
                <el-popover
                popper-class="recordpopper"
                placement="right"
                width="95"
                trigger="manual"
                v-model="item.visible"
                >
                    <div class="recordOperate">
                       <div class="recordOperate-t" @click="handlesetTop(item,index)">
                          <!-- <div class="recordOperateiocn"><i class="iconfont icon-zhidingzhanshi"></i></div> -->
                          <div class="recordOperateiocn"><i class="iconfont icon-zhidingzhanshi iconfix"></i></div>
                          <div class="recordOperword">置顶</div>
                       </div>
                       <div class="recordOperate-t1" @click="handleResetName(item)">
                          <div class="recordOperateiocn"><i class="iconfont icon-xiugai2"></i></div>
                          <div class="recordOperword">重命名</div>
                       </div>
                       <div class="recordOperate-t2" @click="handleRecordDelete(item)">
                          <div class="recordOperateiocn"><i class="iconfont icon-shanchu"></i></div>
                          <div class="recordOperword">删除</div>
                       </div>
                    </div>
                 <i class="el-icon-more  iconmorecolor" slot="reference"  @click.stop="handleMoreClick(index)"></i>
                 </el-popover>
              </div>
          </div>
      </div>
      <el-dialog
        title="提示"
        :visible.sync="deleteSummaryVisible"
        custom-class="deletefileDialog"
        :append-to-body="true"
        width="366px">
        <div>确定<span style="color:#FF0000">删除</span>这条历史记录吗,删除后不可恢复</div>
        <div
          slot="footer"
          class="dialog-footer"
          >
          <el-button @click="deleteSummaryVisible = false">取 消</el-button>
          <el-button
            type="primary"
            @click="getcancleQuestion"
            >确 定</el-button
          >
        </div>
      </el-dialog>
      <el-dialog
        title="编辑对话名称"
        :close-on-click-modal="false"
        :visible.sync="editNameVisible"
        custom-class="deletefileDialog"
        :append-to-body="true"
        width="366px">
        <el-input v-model.trim="inputName" placeholder="编辑对话名称" maxlength="20"></el-input>
        <div
          slot="footer"
          class="dialog-footer"
          >
          <el-button @click="editNameVisible = false">取 消</el-button>
          <el-button
            type="primary"
            @click="handleconfrimEdit"
            >确 定</el-button
          >
        </div>
      </el-dialog>
  </div>
</template>

<script>
  export default {
    name: "historyRecord",
    components: {},
    props:{
      chatSessionList:{
        type:Array,
        default:()=>[]
      }
    },
    data() {
      return {
        deleteSummaryVisible: false,
        editNameVisible:false,
        inputName:'',
        currentChatId:0,
        visible:false,
        chatList:[],
        currentchatIndex:-1
      }
    },
    computed:{

    },
    watch:{
      chatSessionList:{
        handler(newVal){
          if(newVal && newVal.length>0){
            newVal.forEach(item=>{
              item.visible=false
            })
            let newValdata=JSON.parse(JSON.stringify(newVal))
            this.chatList=newValdata
         }
        },
        immediate:true
      }

    },
    mounted(){
      document.addEventListener('click', this.handleDocumentClick);
    },
    beforeDestroy(){
      document.removeEventListener('click', this.handleDocumentClick);
    },
      methods: {
        handleDocumentClick(event) {
          const popovers = document.querySelectorAll('.recordpopper');
          const isClickInsidePopover = Array.from(popovers).some(popover => popover.contains(event.target));
          const isClickOnMoreIcon = event.target.closest('.el-icon-more');
          
          if (!isClickInsidePopover && !isClickOnMoreIcon) {
            this.chatList.forEach(item => {
              item.visible = false;
            });
          }
        },
        handleMoreClick(currentIndex) {
          this.$nextTick(()=>{
            this.chatList.forEach((person, index) => {
               if (index === currentIndex) {
                  person.visible = true;
                } else {
                  person.visible = false;
                }
            });
          })
        },  
        handlechatItem(val,index){
          this.currentchatIndex=index
          this.$emit('Getappointchatdata',val)
        },
        handlehisclose() {
          this.$emit('handlehisToryclose')
        },
        handlesetTop(val,topindex){
          this.$emit('handlehisToggleTop',val)
            this.$nextTick(()=>{
            this.chatList.forEach((person, index) => {
               if (index === topindex && person.visible) {
                  person.visible = false;
                } 
            });
          })
        },
        handleResetName(data){
          this.currentChatId=data.chatId
          this.inputName=data.title
          this.editNameVisible=true   
        },
        handlecloseEditModal(){
         this.editNameVisible=false
        },
        handleconfrimEdit(){
          this.$emit('HandleEditChatTitle',{
            chatId:this.currentChatId,
            title:this.inputName
          })
          //this.editNameVisible=false 
        },
        handleRecordDelete(data){
          this.currentChatId=data.chatId
          this.deleteSummaryVisible=true
        },
        getcancleQuestion(){
          this.$emit('HandleDelChatSession',{
            chatId:this.currentChatId
          })
          this.deleteSummaryVisible=false
        }

      }
  }
</script>

<style lang="scss">
  .historycontent{
    height: 100%;
    .hisheader{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      border-bottom: 1px solid #E3E3E3;
      .hisheader-l{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #222222;
        padding-left: 20px;
      }
      .hisheader-r{
        padding-right: 18px;
        .arrowesize{
          font-size: 18px;
          color:#A5A5A7;
          cursor: pointer;
        }
      }
    }
    .hisrecordList{
      width: 100%;
      height: calc(100% - 50px);
      overflow-y: auto;
      padding: 12px 10px 12px 6px;
      .recordItem{
        display: flex;
        align-items: center;
        justify-content: space-around;
        padding: 9px 8px;
        border-radius: 8px;
        position: relative;
        .recordItem-l{
         padding-top: 3px;
        }
        .recordItem-m{
          max-width: 286px;   
          white-space: nowrap; 
          overflow: hidden; 
          flex:1;
          text-overflow: clip;
          // text-overflow: ellipsis; 
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          cursor: pointer;
        }
        .recordshowdom{
          background: linear-gradient(90deg, rgba(255,255,255, 0.1) 0%, #fff 50%, #fff 100%);
          width: 10px;
          content: "";
          pointer-events: none;
          border-top-left-radius: 15px;
          border-bottom-left-radius: 15px;
          position: absolute;
          top: 0;
          bottom: 0;
          right: 32px;
        }
        .recordItem-r{
          .iconmorecolor{
            color: #A5A5A7;
            font-size: 18px;
            cursor: pointer;
          }
        }
        &:hover{
          .recordshowdom{
          background: linear-gradient(90deg, rgba(255,255,255, 0.1) 0%, #F6F7FC 32%, #F6F7FC 100%); 
          }
          background: #F6F7FC;
        }
      }
      .recordItemactive{
        background: #F6F7FC;
        .recordshowdom{
          background: linear-gradient(90deg, rgba(255,255,255, 0.1) 0%, #F6F7FC 32%, #F6F7FC 100%); 
          }
      }
    }
  }
  .recordpopper{
    min-width: 95px;
    .recordOperate{
      cursor: pointer;
      .recordOperate-t{
        display: flex;
        align-items:center ;
        padding-bottom: 15px;
        .iconfix{
          font-size: 18px;
        }
        .recordOperword{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        padding-left: 8px;
      }
        &:hover{
        .recordOperword{
        color: #2B61FA;
        }
        i{
          color: #2B61FA;
        }
      }
    }
      .recordOperate-t1{
        display: flex;
        align-items:center ;
        padding-bottom: 15px;
        .recordOperword{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        padding-left: 8px;
      }
        &:hover{
        .recordOperword{
        color: #2B61FA;
        }
        i{
          color: #2B61FA;
        }
      }
    }
      .recordOperate-t2{
        display: flex;
        align-items:center;
        .recordOperword{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        padding-left: 8px;
      }
        &:hover{
        .recordOperword{
        color: #2B61FA;
        }
        i{
          color: #2B61FA;
        }
      }
    }
    }
  }
</style>