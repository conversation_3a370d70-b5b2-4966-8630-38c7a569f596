<template>
  <div class="base-info">
    <el-form
      ref="baseInfoForm"
      :inline="true"
      label-width="120px"
      :model="formModel"
      :rules="formRules">
      <div class="form-content">
        <el-card class="box-card">
          <div
            slot="header"
            class="clearfix">
            <span>基础设置</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="列表名称"
                prop="pageName">
                <el-input
                  id="pageName"
                  v-model="formModel.pageName"
                  placeholder="请输入列表名称"
                  :maxlength="50"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="列表编码"
                prop="pageNo">
                <el-input
                  id="pageNo"
                  v-model="formModel.pageNo"
                  placeholder="请输入列表编码"
                  :maxlength="50"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-tabs v-model="formModel.activeName">
            <el-tab-pane label="自定义SQL" :name="0">
              <el-form-item
                  label="自定义SQL"
                  props="sqlStr"
                  class="full">
            <textarea
                id="sqlEditor"
                ref="sqlEditor"
                v-model="formModel.sqlStr"
                style="visibility: hidden"></textarea>
              </el-form-item>
              <el-form-item label=" ">
                <el-button
                    type="primary"
                    @click="handleSql"
                >解析SQL</el-button
                >
                <el-button @click="findSqls">SQL辅助</el-button>
              </el-form-item>
              <el-row>
                <el-col :span="8">
                  <el-form-item
                      label="排序语句"
                      prop="sqlDesc"
                      class="flex-row">
                    <el-input
                        id="sqlDesc"
                        v-model="formModel.sqlDesc"
                        placeholder="请输入排序语句"
                        :maxlength="50" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="关联表配置" :name="1">
              <el-row>
                <el-col :span="8">
                  <el-form-item
                      label="主表"
                      props="mainTable"
                      class="full">
                    <el-select filterable v-model="formModel.mainTable" placeholder="请选择">
                      <el-option
                          v-for="item in allTables"
                          :key="item.id"
                          :label="item.tableName"
                          :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-button @click="addRelated" icon="el-icon-plus">添加表关联</el-button>
                </el-col>
              </el-row>
              <template v-for="(i,index) in formModel.fieldList">
                <div style="border: 1px solid #f3f3f3;padding-top: 20px;margin-bottom: 10px" :key="index+'field'">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item
                          label="关联关系"
                          props="sqlStr"
                          class="full">
                        <el-select v-model="i.affiliate" placeholder="请选择">
                          <el-option
                              v-for="item in relationsOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <div style="color: #fff;">`</div>
                    </el-col>
                    <el-col :span="8">
                      <el-button @click="deleteRelated(i.id)" size="mini" icon="el-icon-close" circle></el-button>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item
                          label="主表"
                          props="subTable"
                          class="full">
                        <el-select filterable @change="v => mainTableChange(v, i)" v-model="i.mainTable" placeholder="请选择">
                          <el-option
                              v-for="item in getMainTableOptions(i.id)"
                              :key="item.id"
                              :label="item.tableName"
                              :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item
                          label="从表"
                          props="subTable"
                          class="full">
                        <el-select filterable @change="v => subTableChange(v, i)" v-model="i.subTable" placeholder="请选择">
                          <el-option
                              v-for="item in allTables"
                              :key="item.id"
                              :label="item.tableName"
                              :value="item.id">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item
                          label="主表字段"
                          props="sqlStr"
                          class="full">
                        <el-select filterable v-model="i.mainField" placeholder="请选择">
                          <el-option
                              v-for="item in i.mainFieldOptions"
                              :key="item.columnCode"
                              :label="item.columnName"
                              :value="item.columnCode">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item
                          label="从表字段"
                          props="sqlStr"
                          class="full">
                        <el-select filterable v-model="i.subsheetField" placeholder="请选择">
                          <el-option
                              v-for="item in i.subFieldOptions"
                              :key="item.columnCode"
                              :label="item.columnName"
                              :value="item.columnCode">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </template>
              <el-form-item label=" ">
                <el-button
                    type="primary"
                    @click="handleAffiliateSql"
                >解析SQL</el-button
                >
                <el-button @click="demonstrateSqls">显示SQL语句</el-button>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-card>
        <el-card class="box-card">
          <div
            slot="header"
            class="clearfix">
            <span>其它配置</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="弹框标题"
                prop="title">
                <el-input
                  id="title"
                  v-model="formModel.pageConfig.title"
                  placeholder="请输入"
                  :maxlength="50"></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item
                label="自动加载数据"
                prop="searchable">
                <el-switch
                  v-model="formModel.pageConfig.firstLoad"
                  :active-value="1"
                  active-text="加载"
                  inactive-text="不加载"
                  :inactive-value="0">
                </el-switch>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item
                label="分页显示"
                prop="pagination">
                <el-switch
                  id="pagination"
                  v-model="formModel.pageConfig.pagination"
                  :active-value="1"
                  active-text="显示"
                  inactive-text="隐藏"
                  :inactive-value="0">
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="操作列宽度"
                prop="operateWidth">
                <el-input-number
                  id="operateWidth"
                  v-model="formModel.pageConfig.operateWidth"
                  :step="12"
                  :min="44"
                  :max="500"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-form>
    <base-dialog
        width="800px"
        :visible.sync="dialogSqlVisible"
        title="SQL语句"
    >
      <textarea
          id="sqlEditor2"
          ref="sqlEditor2"
          v-model="sqls"
          ></textarea>
      <!--</el-form-item>-->
    </base-dialog>
    <base-dialog
      :visible.sync="dialogVisible"
      title="SQL辅助"
    >
      <div class="sql-dialog">
        <div class="module-tree">
          <!-- 模块分类 -->
          <el-tree
            ref="eltree"
            node-key="id"
            default-expand-all
            highlight-current
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            :current-node-key="currentNodeKey"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
        <table2
          ref="sqlTable"
          class="sql-table"
          highlight-current-row
          :data="tableData"
          :columns="tableColumns"
          :queryFormConfig="sqlQueryConfig"
          :pagination="false"
          :searchParams="sqlSearchParams"
          @current-change="handleCurrentChange"
          @handleSearch="handlerSqlSearch"
        >
        </table2>
      </div>
      <template slot="footer">
        <div class="dialog-footer">
          <el-button type="primary" @click="appendSql">确定</el-button>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import CodeMirror from "codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/addon/selection/active-line.js";
import "codemirror/mode/sql/sql";
import pinyin from 'js-pinyin';
pinyin.setOptions({ checkPolyphone: false, charCase: 1 }); // 汉字首字母小写
export default {
  name: "BaseInfo",
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      editor: undefined,
      editorSql: undefined,
      formRules: {
        pageNo: [{ required: true, message: "请输入列表编码" }],
        pageName: [{ required: true, message: "请输入列表名称" }]
      },
      relationsOptions:[
        {
          value: 'left',
          label: '左关联'
        },
        {
          value: 'right',
          label: '右关联'
        },
        {
          value: 'in',
          label: '内关联'
        }
      ],
      treeData: [],
      currentNodeKey: 0, // 默认选中的
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      tableData: [],
      allTables: [],
      tableColumns: [{ prop: 'tableName', label: "表名" }, { prop: 'sql', label: "SQL语句" }],
      sqlSearchParams: {},
      sqls: '',
      dialogVisible: false,
      dialogSqlVisible: false,
      sqlQueryConfig: { formItemList: [{ type: 'el-input', prop: 'tableName', label: '表名' }], labelPosition: 'left', itemCount: 3 },
      currentSql: ""
    };
  },
  computed: {
    formModel: {
      get: function () {
        return this.formData;
      },
      set: function (val) {
        this.$emit("update:formData", val);
      }
    }
  },
  watch: {
    'formModel.pageName': {
      handler(val){
        if (val){
          this.formData.pageNo = pinyin.getCamelChars(val);
        } else {
          this.formData.pageNo = '';
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.sqlEditor && this.editor === undefined) {
        this.editor = CodeMirror.fromTextArea(this.$refs.sqlEditor, {
          theme: "dracula", // 主题，对应主题库 JS 需要提前引入
          tabSize: 4, // 缩进格式
          indentUnit: 4,
          smartIndent: true,
          width: "100%",
          lineHeight: "24px",
          readOnly: false,
          matchBrackets: true,
          lineNumbers: true, // 显示行号
          line: true,
          styleActiveLine: true // 高亮选中行
        });

        this.editor.setSize("auto", 200);
        this.editor.getDoc().setValue(this.formModel.sqlStr || "");
        this.editor.refresh();
      }
    });
    this.loadTreeData();
  },
  created(){
    this.formModel.activeName = this.formModel.type || 0
    if(this.formModel.type === 1){
      this.sqls = this.formModel.sqlStr
      this.formModel.sqls = this.formModel.sqlStr
    }
    if(!this.formModel.tableJoinConfig){
      this.formModel.fieldList = []
    }else{
      const tableJoinConfig = JSON.parse(this.formModel.tableJoinConfig)
      this.formModel.mainTable = tableJoinConfig.mainTable
      this.formModel.fieldList = tableJoinConfig.AffiliateTables
    }
    this.GetCustom()
  },
  methods: {
    async GetCustom() {
     const {code, data} =  await this.$api.GetCustomTablePageList({
        pageSize:1000,
        pageIndex: 1
      });
     if(code === 200){
       this.allTables = data
     }
    },

    async getConfigById(id) {
      const { code, data } = await this.$api.GetTableColumns({
        id: id
      });
      if(code === 200){
        return data
      }
    },
    async mainTableChange(id, item){
      const data = await this.getConfigById(id)
      this.$set(item, 'mainFieldOptions', data)
    },

    async subTableChange(id, item){
      const data = await this.getConfigById(id)
      this.$set(item, 'subFieldOptions', data)
    },
    demonstrateSqls(){
      if(this.dialogSqlVisible) {
        return;
      }
      if(this.sqls){
        this.dialogSqlVisible = true
        this.$nextTick(() => {
          this.editorSql = CodeMirror.fromTextArea(this.$refs.sqlEditor2, {
              theme: "dracula", // 主题，对应主题库 JS 需要提前引入
              tabSize: 4, // 缩进格式
              indentUnit: 4,
              smartIndent: true,
              width: "100%",
              lineHeight: "24px",
              readOnly: false,
              matchBrackets: true,
              lineNumbers: true, // 显示行号
              line: true,
              styleActiveLine: true // 高亮选中行
            });
            this.editorSql.setSize("auto", 450);
            this.editorSql.getDoc().setValue(this.sqls || "");
            this.editorSql.refresh();
        });
      }else{
        this.$message.warning('请先解析sql语句！')
      }
    },
    async handleAffiliateSql(){
      let info = []
      this.formModel.fieldList.forEach(val=>{
        info.push({
          relations:val.affiliate,
          mainField:val.mainField,
          mainTable:val.mainTable,
          subTable:val.subTable,
          subField:val.subsheetField,
        })
      })
      const obj = {mainTable:this.formModel.mainTable,AffiliateTables:info}
      const {code, data:{sql,tableFields}} = await this.$api.AnalyseTableJoinInfo(obj)
      if(code === 200){
        this.sqls = sql
        this.formModel.sqls = sql
        const columns = [];
        tableFields.forEach(d=>{
            d.tableColumnList.map(col => {
              col.tableAlias = d.alias;
              col.tableName = d.tableName;
              col.tableDisplayName = d.tableDisplayName;
              columns.push(col);
            });
          });
        this.setDisplayConfig(columns);
      }
    },
    setDisplayConfig(columns) {
      const cols = columns.map(col => {
            const {id, tableId, isDispatch, columnAlias, columnCode, columnName, columnType, addType, ...rest } = col;
            return {
              ...rest,
              id: id,
              tableId: tableId,
              isDispatch: isDispatch,
              prop: columnAlias,
              code: columnCode,
              label: columnName,
              valueType: columnType,
              columnName,
              queryName: columnName,
              isShow: addType === 0 ? 1 : 0,
              addType: addType,
              format: ''
            };
          });
          cols.forEach(col=>{
            const oldCol = this.formModel.displayConfig.find(p=>p.prop === col.prop);
            if (oldCol) {
              const {isShow,label,order,columnWidth,format, decimalPlaces,fixed,isQuery,queryName,queryDefaultValue,isDispatch,dispatchConfig} = oldCol;
              Object.assign(col, {isShow,label,order,columnWidth,format, decimalPlaces,fixed,isQuery,queryName,queryDefaultValue,isDispatch,dispatchConfig});
            }
          });
          this.$set(this.formModel, "displayConfig", cols);
          this.$message.success('解析成功！');
    },
    async handleSql() {
      const sql = this.editor.getDoc().getValue();
      if (!sql){
        this.$message.error("请输入sql语句");
        return;
      }
      const res = await this.$api.VerifySql({
        sqlStr: sql
      });

      if (res.code === 200) {
        this.formModel.sqlStr = sql;
        const columns = [];
        res.data.forEach(d => {
          d.tableColumnList.map(col => {
            col.tableAlias = d.alias;
            col.tableName = d.tableName;
            col.tableDisplayName = d.tableDisplayName;
            columns.push(col);
          });
        });
        this.setDisplayConfig(columns);
      }
    },
    // 设置SQL
    handleChooseTable(sql) {
      this.formModel.sqlStr = sql;
    },
    // 辅助查看SQL
    findSqls(){
      this.dialogVisible = true;
      this.sqlSearchParams = {
        moduleId: this.currentNodeKey
      };
    },
    // 左侧模块树
    async loadTreeData() {
      const { data, code } = await this.$api.getMIBTypeList();
      if (code === 200) {
        const rootNodes = data.filter(item => item.parentId === 0);
        this.currentNodeKey = rootNodes[0].id;
        for (const rootNode of rootNodes) {
          this.loadChildNodes(data, rootNode);
        }
        this.treeData = rootNodes;
      }
    },
    loadChildNodes(list, node) {
      node.children = list.filter(item => item.parentId === node.id);
      for (const childNode of node.children) {
        this.loadChildNodes(list, childNode);
      }
    },
    handleNodeClick(node){
      this.currentNodeKey = node.id;
      const conditions = this.$refs.sqlTable.getSearchParams();
      this.sqlSearchParams = { ...conditions, moduleId: node.id };
    },
    // 左侧模块树 结束
    async handlerSqlSearch(params){
      const { code, data } = await this.$api.FindAllTableSqls(params);
      if (code === 200){
        this.tableData = data;
      }
    },
    // 行选中
    handleCurrentChange({ sql }){
      this.currentSql = sql;
    },
    appendSql(){
      this.dialogVisible = false;
      let sql = this.editor.getDoc().getValue() || "";
      if (sql){
        if (sql.lastIndexOf(";") !== sql.length){
          sql += ";";
        }
      }
      this.formModel.sqlStr = sql + this.currentSql;
      this.editor.getDoc().setValue(this.formModel.sqlStr);
    },
    addRelated(){
      this.formModel.fieldList.push({id:this.formModel.fieldList.length})
    },
    deleteRelated(id){
      this.formModel.fieldList.splice(id,1)
    },
    uniqueArrayByProperty(array, propName) {
      return array.reduce((accumulator, currentValue) => {
        const index = accumulator.findIndex((obj) => obj[propName] === currentValue[propName]);
        if (index === -1) {
          accumulator.push(currentValue);
        }
        return accumulator;
      }, []);
    },
    getMainTableOptions(id) {
      return [this.formModel.mainTable, ...this.formModel.fieldList.filter(p=>p.id<id).map(p=>p.subTable)].map(p=>({
        id: p,
        tableName: this.allTables.find(t=>t.id===p)?.tableName
      }));
    }
  }
};
</script>

<style lang="scss" scoped>
.base-info {
  .form-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .full {
    //width: 100%;
    display: flex;
    ::v-deep .el-form-item__content {
      flex: 1 1 auto;
    }
  }
  .dialog-footer{
    margin-top: 10px;
  }
  ::v-deep .el-dialog__body {
    padding: 10px;
    .el-table__body tr.current-row>td.el-table__cell {
        background-color: #c6e2ff;
    }
  }
}
.sql-dialog{
  width: 100%;
  height: 500px;
  display: flex;
  flex-direction: row;
  gap:6px;
  .module-tree{
    flex: 0 0 auto;
    width:200px;
    height: 100%;
    overflow:auto;
  }
  .sql-table{
    flex:1 1 auto;
    width: calc(100% - 200px);
    height: 100%;
  }
}
</style>
