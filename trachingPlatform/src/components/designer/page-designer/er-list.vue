
<template>
  <base-dialog
    :visible.sync="dialogVisible"
    :hasFull="false"
    width="900px"
    title="ER模型列表"
    className="menu-dialog">
    <div class="dlg-content">
      <table2
        v-loading="loading"
        highlight-current-row
        :height="500"
        :columns="tableColumns"
        :data="tableData"
        :total="total"
        :searchParams="searchParams"
        @handleSearch="handleSearch"
        @current-change="currentChange">
        <template #operateTop>
          <el-input
            v-model="conditionCode"
            clearable
            :maxlength="40"
            placeholder="请输入编码"
            class="search-input" />
          <el-input
            v-model="conditionName"
            clearable
            :maxlength="40"
            placeholder="请输入名称"
            class="search-input" />
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="queryERModel">
            查询
          </el-button>
        </template>
      </table2>
    </div>
    <div
      slot="footer"
      class="dialog-footer flex-footer">
      <el-button
        type="primary"
        @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </base-dialog>
</template>

<script>
export default {
  name: "SceneListDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentRow: null,
      loading: true,
      conditionCode: "",
      conditionName: "",
      searchParams: {
        pageIndex: 1,
        pageSize: 10,
        moduleId: 0
      },
      tableColumns: [
        { width: 80, label: "序号", type: "index" },
        { prop: "modelName", label: "ER模型名称" },
        { prop: "des", label: "ER模型标识" },
        { prop: "moduleName", label: "所属模块" }
      ],
      tableData: [],
      total: 0
    };
  },
  computed: {
    dialogVisible: {
      set: function (val) {
        this.$emit("update:visible", false);
      },
      get: function () {
        return this.visible;
      }
    }
  },
  methods: {
    async handleSearch(params) {
      const { data, code, recordCount } = await this.$api.GetERModelPageList(
        params
      );
      if (code === 200) {
        this.tableData = data;
        this.total = recordCount;
      }
      this.loading = false;
    },
    queryERModel() {
      this.searchParams = {
        ...this.searchParams,
        name: this.conditionName,
        code: this.conditionCode,
        pageIndex: 1
      };
    },
    currentChange(val) {
      this.currentRow = val;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("confirm", this.currentRow);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px;
  .el-table__body tr.current-row > td.el-table__cell {
    background-color: #c6e2ff;
  }
}
</style>
