

<template>
  <div class="design-column-info" v-if="tableData.length">
    <el-alert
        title="重新“解析SQL”才能使用字段显示控制功能。"
        type="warning"
        show-icon
        :closable="false"
        v-if="tableData[0].addType === undefined">
    </el-alert>
    <el-switch
        class="column-switch"
        v-model="showSysColumns" 
        active-text="显示系统字段"
        :disabled="tableData[0].addType === undefined"
        @change="v => $emit('update:showSysColumns', v)">
    </el-switch>
    <el-tabs>
        <el-tab-pane
          v-for="(value, key) in groupedColumns"
          :key="key"
          :label="`${value[0]?.tableDisplayName || value[0]?.tableName} (${key})`"
          >
          <table2
            :row-class-name="v => ((!showSysColumns && v.row.addType > 0) ? 'sys-column-row-hide' : '')"
            :cell-class-name="getCellClass"
            :header-cell-class-name="getCellClass"
            :pagination="false"
            row-key="prop"
            :data="value"
            :columns="columns" />
        </el-tab-pane>
      </el-tabs>
      <column-search-config-dialog
        :searchConfigDialogVisible.sync="searchConfigDialogVisible"
        :searchConfigRow="searchConfigRow"
        @submit="handleSearchConfig">
      </column-search-config-dialog>
  </div>
</template>

<script>
import {DataType} from "@/constants/common.js"
import groupBy from "lodash/groupBy";
import ColumnSearchConfig from "./column-search-config.vue";
import ColumnSearchConfigDialog from "./column-search-config-dialog.vue";
const DATA_TYPE = DataType.concat([
  {
    Code: 'BIGINT',
    Name: '长整数',
    Length: 20,
    Remark: '长整数'
  },
  {
    Code: 'SMALLINT',
    Name: '短整数',
    Length: 6,
    Remark: '长整数'
  }
]);
import dispatchBtn from "./dispatch-btn.vue";
export default {
  name: "FusionFrontColumnInfo",
  props: {
    displayConfig: {
      type: Array,
      default: () => []
    },
    originalTableName: {},
    dataId: {},
    showSysColumns: true
  },
  components: { ColumnSearchConfigDialog },
  data() {
    return {
      searchConfigDialogVisible: false,
      originalRow: {},
      searchConfigRow: {},
      groupedColumns: {},
      columns: [
        {
          prop: "index",
          type: "index",
          label: "序号",
          align: "center",
          width: 60
        },
        { prop: "tableName", label: "表名" },
        // { prop: "tableAlias", label: "表别名" },
        { prop: "code", label: "字段名" },
        { prop: "prop", label: "字段别名" },
        { prop: "columnName", label: "字段显示名称" },
        {
          prop: "valueType",
          label: "字段类型",
          formatter(row, column, cellValue) {
            const btn = DATA_TYPE.find(item => item.Code === cellValue);
            return btn?.Name || cellValue;
          }
        },
        {
          prop: "isShow",
          label: "显示列",
          type: "el-switch",
          activeText: "是",
          activeValue: 1,
          inactiveValue: 0,
          inactiveText: "否",
          width: 120
        },
        {
          prop: "label",
          label: "显示名称",
          type: "el-input",
          maxlength: 50,
          size: "small",
          width: 130
        },
        {
          prop: "order",
          label: "排序",
          type: "el-input-number",
          width: 90,
          size: "small",
          controls: false,
          min: 1,
          max: 500,
          step: 1
        },
        {
          prop: "columnWidth",
          label: "列宽度",
          title:
            "最小宽度120px\n不设置时，所有未设置的列均分剩余宽度\n所有显示列都设置了宽度，列宽度不自动撑开",
          type: "el-input-number",
          size: "small",
          controls: false,
          min: 1,
          max: 500,
          step: 1,
          width: 90
        },
        {
          prop: "format",
          label: "显示格式",
          type: "el-select",
          width: 130,
          options:[
            {label:'默认',value:''},
            {label:'百分比',value:'percent'},
            {label:'数字/金额',value:'number'}
          ]
        },
        {
          prop: "decimalPlaces",
          label: "保留小数位数",
          type: "el-select",
          width: 110,
          options:[
            {label:'0',value:0},
            {label:'1',value:1},
            {label:'2',value:2},
            {label:'3',value:3},
            {label:'4',value:4}
          ]
        },
        {
          prop: "fixed",
          label: "是否固定",
          type: "el-switch",
          activeText: "是",
          activeValue: 1,
          inactiveValue: 0,
          inactiveText: "否",
          size: "small",
          width: 120
        },
        {
          prop: "isQuery",
          label: "查询设置",
          type: "components",
          components: ColumnSearchConfig,
          width: 240
        },
        // {
        //   prop: "queryName",
        //   label: "查询别名",
        //   type: "el-input",
        //   maxlength: 20,
        //   size: "small",
        //   width: 130
        // },
        // {
        //   prop: "queryDefaultValue",
        //   label: "查询默认值",
        //   type: "el-input",
        //   maxlength: 20,
        //   size: "small",
        //   width: 150
        // }
        // { prop: 'dataConfig', label: '数据转换配置' },
      ]
    };
  },
  computed: {
    tableData: {
      get: function () {
        this.groupedColumns = groupBy(this.displayConfig, "tableAlias");
        return this.displayConfig;
      },
      set: function (val) {
        this.groupedColumns = groupBy(val, "tableAlias");
        this.$emit("update:displayConfig", val);
      }
    }
  },

  mounted() {
    this.$eventBus.$on("configColumnSearch", data => {
      this.originalRow = data;
      this.searchConfigRow = { ...data };
      this.searchConfigDialogVisible = true;
    });
    if (this.dataId) {
      this.columns = [
        ...this.columns,
        {
          prop: "isDispatch",
          label: "是否调度",
          type: "el-switch",
          activeText: "是",
          activeValue: 1,
          inactiveValue: 0,
          inactiveText: "否",
          size: "small",
          width: 120
        },
        {
          prop: "setDispatch",
          label: "设置调度",
          type: "components",
          width: 150,
          components: dispatchBtn,
          componentsProps: {
            dataId: this.dataId,
            originalTableName: this.originalTableName,
            onConfirm: (dispatchData, triggerData) => {
              // 添加了调度
              if (dispatchData.length) {
                this.tableData = this.tableData.map(td => {
                  if (td.id === triggerData.id) {
                    return {
                      ...td,
                      isDispatch: 1,
                      isCompleteDispatch: true
                    };
                  }
                  return td;
                });
              } else {
                this.tableData = this.tableData.map(td => {
                  if (td.id === triggerData.id) {
                    return {
                      ...td,
                      isCompleteDispatch: false
                    };
                  }
                  return td;
                });
              }
              this.tableData;
            }
          }
        }
      ];
    } else {
      // 新增同步基础表的调度信息（isDispatch）
      this.columns = [
        ...this.columns,
        {
          prop: "isDispatch",
          label: "是否调度",
          type: "el-switch",
          activeText: "是",
          activeValue: 1,
          inactiveValue: 0,
          inactiveText: "否",
          size: "small",
          width: 120
        }
      ];
    }
  },
  methods: {
    getCellClass({column}) {
      return ['isShow','isQuery','isDispatch'].includes(column.property) ? 'split-cell' : '';
    },
    handleSearchConfig() {
      this.searchConfigDialogVisible = false;
      Object.assign(this.originalRow, this.searchConfigRow);
    }
  },
  beforeDestroy() {
    this.$eventBus.$off("configColumnSearch");
  }
};
</script>

<style lang="scss" scoped>
  .design-column-info {
    ::v-deep .el-table .sys-column-row-hide {
      display: none;
    }
    ::v-deep .el-table .split-cell {
      border-left: 3px solid #ebeef5;
    }
    .column-switch {
      margin: 8px 0;
    }
  }
</style>
