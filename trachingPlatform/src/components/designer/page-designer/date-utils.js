const none = "none";
const yesterday = "yesterday";
const today = "today";
const tomorrow = "tomorrow";
const startOfThisMonth = "startOfThisMonth";
const endOfThisMonth = "endOfThisMonth";
const startOfNextMonth = "startOfNextMonth";
const startOfThisYear = "startOfThisYear";
const endOfThisYear = "endOfThisYear";
const startOfNextYear = "startOfNextYear";

const datetimeDefaultValueOptions = [
  {
    value: none,
    label: "无"
  },
  {
    value: yesterday,
    label: "昨天"
  },
  {
    value: today,
    label: "今天"
  },
  {
    value: tomorrow,
    label: "明天"
  },
  {
    value: startOfThisMonth,
    label: "本月初"
  },
  {
    value: endOfThisMonth,
    label: "本月末"
  },
  {
    value: startOfNextMonth,
    label: "下月初"
  },
  {
    value: startOfThisYear,
    label: "今年初"
  },
  {
    value: endOfThisYear,
    label: "今年末"
  },
  {
    value: startOfNextYear,
    label: "明年初"
  }
];

const getDate = (type, isEndTime = false) => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();
  const day = now.getDate();
  let date;
  switch (type) {
    case yesterday:
      date = new Date(now.setDate(day - 1));
      break;
    case today:
      date = now;
      break;
    case tomorrow:
      date = new Date(now.setDate(day + 1));
      break;
    case startOfThisMonth:
      date = new Date(year, month, 1);
      break;
    case endOfThisMonth:
      date = new Date(year, month + 1, 0);
      break;
    case startOfNextMonth:
      date = new Date(year, month + 1, 1);
      break;
    case startOfThisYear:
      date = new Date(year, 0, 1);
      break;
    case endOfThisYear:
      date = new Date(year, 11, 31);
      break;
    case startOfNextYear:
      date = new Date(year + 1, 0, 1);
      break;
    default:
      break;
  }
  if (date) {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${
      isEndTime ? "23:59:59" : "00:00:00"
    }`;
  }
  return null;
};

const getDateArray = value => {
  if (typeof value !== "string") {
    return value;
  }
  const dateArray = JSON.parse(value);
  if (dateArray instanceof Array) {
    return dateArray.map((v, i) => getDate(v, i === 1));
  }
  return value;
};

const DateUtils = {
  datetimeDefaultValueOptions,
  getDateArray
};

export default DateUtils;
