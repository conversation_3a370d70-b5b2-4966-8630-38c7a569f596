<template>
  <div class="column-search-config">
    <el-switch
      v-model="rowData.isQuery"
      active-text="开启查询"
      :active-value="1"
      :inactive-value="0"></el-switch>
    <el-button
      :disabled="!rowData.isQuery"
      size="small"
      @click="configColumnSearch"
      >查询设置...</el-button
    >
  </div>
</template>

<script>
export default {
  name: "ColumnSearchConfig",
  props: {
    rowData: {
      type: Object,
      default: () => {}
    },
    columnInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    configColumnSearch() {
      this.$eventBus.$emit("configColumnSearch", this.rowData);
    }
  }
};
</script>

<style lang="scss" scoped>
.column-search-config {
  .el-switch {
    margin-right: 10px;
  }
}
</style>
