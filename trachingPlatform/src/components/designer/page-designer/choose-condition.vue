<template>
  <div v-if="rowData && rowData.btnPosition === 'in'" class="choose-condition">
    <el-button type="text" v-if="rowData.showCondition && rowData.showCondition.length">已设置条件</el-button>
    <el-button size="small" @click="showSetModal()">设置条件</el-button>
    <base-dialog :visible.sync="dialogVisible"
                 title="设置条件"
                 width="700px"
                 @submit="confirm">
      <div v-if="dialogVisible">
        <div v-for="(item, index) in conditionList" :key="'con' + index"
             style="margin-bottom: 12px; display: flex; align-items: center;">
          <el-select v-model="item.columnProp" placeholder="请选条件字段" style="width: 350px;">
            <el-option v-for="field in fieldList" :key="field.id"
                       :label="field.label" :value="field.prop"
                       :disabled="selectedProps.includes(field.prop)">
            </el-option>
          </el-select>
          <el-select v-model="item.op" placeholder="请选择" style="margin: 0 12px;">
            <el-option label="等于" value="eq"></el-option>
          </el-select>
          <el-input v-model="item.value" placeholder="请输入条件值"></el-input>
          <i class="el-icon-remove-outline"
             @click="conditionList.splice(index, 1)"
             style="color: #F56C6C; margin-left: 12px; cursor: pointer;"></i>
        </div>
        <el-button type="primary" @click="addCondition()">+ 添加条件</el-button>
      </div>
    </base-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    rowData: {
      type: Object,
      default: () => {}
    },
    columnInfo: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      dialogVisible: false,
      fieldList: [],
      conditionList: [],
      formValue: {}
    }
  },
  computed: {
    selectedProps: function () {
      return this.conditionList.map(v => v.columnProp)
    }
  },
  created() {
  },
  methods: {
    showSetModal () {
      this.conditionList = JSON.parse(JSON.stringify(this.rowData.showCondition || []))
      this.fieldList = this.columnInfo && this.columnInfo.componentsProps && this.columnInfo.componentsProps.columnList
        ? this.columnInfo.componentsProps.columnList : []
      this.dialogVisible = true
    },
    addCondition () {
      this.conditionList.push({
        columnProp: '',
        value: '',
        op: 'eq'
      })
    },
    confirm () {
      const list = JSON.parse(JSON.stringify(this.conditionList)).filter(v => v.columnProp && v.value)
      this.$set(this.rowData, 'showCondition', list)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.choose-condition {
  display: flex;
  justify-content:space-around;
  align-items: center;
}
</style>