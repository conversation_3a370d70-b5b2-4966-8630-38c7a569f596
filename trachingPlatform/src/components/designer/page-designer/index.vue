

<template>
  <div class="page-designer">
    <div class="main-toolbar">
      <div
        class="bill-header-left"
        @click="handleReturn">
        <i class="el-icon-arrow-left"></i>
        列表{{ formData.id?'编辑':'新增' }}
      </div>
      <div>
        <el-button
          icon="el-icon-check"
          type="primary"
          @click="handleSave"
          >保存</el-button
        >
        <el-button
          icon="el-icon-back"
          @click="$emit('close')"
          >返回</el-button
        >
      </div>
    </div>
    <div class="tab-container">
      <el-tabs v-model="activeName">
        <el-tab-pane
          label="基本信息"
          name="first">
          <baseInfo
            ref="baseInfo"
            :formData.sync="formData" />
        </el-tab-pane>
        <el-tab-pane
          label="列表信息"
          name="second">
          <columnInfo
            :displayConfig.sync="formData.displayConfig"
            :dataId="formData.id"
            :originalTableName="formData.pageName"
            :showSysColumns.sync="formData.pageConfig.showSysColumns" />
        </el-tab-pane>
        <el-tab-pane
          label="操作配置"
          name="fourth">
          <actionInfo :btnConfig.sync="formData.btnConfig" :displayConfig="formData.displayConfig" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import actionInfo from "./actionInfo.vue";
import baseInfo from "./baseInfo.vue";
import columnInfo from "./columnInfo.vue";
export default {
  name: "PageDesigner",
  components: {
    actionInfo,
    baseInfo,
    columnInfo
  },
  props: ["formData"],
  data() {
    return {
      activeName: "first"
    };
  },
  methods: {
    // 返回
    handleReturn() {
      this.$emit("close");
    },
    // 保存数据源
    handleSave() {
      this.$refs.baseInfo.$refs.baseInfoForm.validate(async valid => {
        if (valid) {
          if(this.formData.activeName === 1){
            this.formData.sqlStr = this.formData.sqls
            this.formData.type = 1
            this.formData.tableJoinConfig = JSON.stringify({mainTable:this.formData.mainTable,mainTableOptions:this.formData.mainTableOptions, AffiliateTables:this.formData.fieldList})
          }else{
            this.formData.type = 0
          }
          const { displayConfig, btnConfig, pageConfig, pageName } = this.formData;
          // 校验按钮配置
          let btnError = false;
          try {
            btnConfig.forEach(btn=>{
              if(['plus','edit',"detail"].includes(btn.id)&&btn.enable===1){
                // 必须关联表单
                if(!btn.relationForm){
                  this.$message.error("按钮【"+btn.name+"】必须关联表单！");
                  btnError = true;
                  throw new Error('er')
                  return false;
                }
              }
              if(['delete'].includes(btn.id)&&btn.enable===1){
                // 必须关联表单
                if(!btn.relationErName){
                  this.$message.error("按钮【"+btn.name+"】必须关联ER模型！");
                  btnError = true;
                  throw new Error('er')
                  return false;
                }
              }
            })
          } catch (e){
            if(e.message !== 'er') throw e
          }

          if(btnError)return;
          const searchConfig = [];
          const dialogTitle = pageConfig.title;
          pageConfig.title = dialogTitle ? dialogTitle : pageName;
          displayConfig.forEach(column => {
            const {isQuery, code, prop, queryDefaultValue, ...rest} = column;
            if (isQuery === 1) {
              searchConfig.push({
                ...rest,
                columnName: code,
                alias: prop,
                isQuery: true,
                defaultValue: queryDefaultValue,
                operator: 1,
                displayType: 0,
              });
            }
          });
          if (displayConfig.length === 0) {
            this.$message.error("请解析正确的SQL");
            return;
          }
          // 按 order 排序
          displayConfig.sort((a, b) => {
            if(!a.order&&!b.order)return 0
            else if(a.order&&!b.order)return -1
            else if(!a.order&&b.order)return 1
            else if(a.order&&b.order){
              return a.order - b.order;
            }
          })
          delete this.formData.sqls
          delete this.formData.fieldList
          delete this.formData.activeName
          delete this.formData.mainTableOptions
          const { code, data } = await this.$api.SaveDataSourceConfig({
            ...this.formData,
            displayConfig: JSON.stringify(displayConfig),
            btnConfig: JSON.stringify(btnConfig),
            pageConfig: JSON.stringify(pageConfig),
            searchConfig: JSON.stringify(searchConfig)
          });
          if (code === 200) {
            this.$message.success("保存成功");
            this.formData.id = data;
            this.$emit("close");
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.page-designer {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1990;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  .main-toolbar {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
    height: 60px;
    min-height: 60px;
    background: #ffffff;
    box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.11);
    .bill-header-left {
      cursor: pointer;
      font-size: 18px;
      font-weight: 500;
      color: #1a1b1f;
    }
  }
  .tab-container {
    flex: 1 1 auto;
    padding: 20px;
    min-height: 0;
    ::v-deep .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      .el-tabs__content {
        flex: 1 1 auto;
        min-height: 0;
        overflow-y: auto;
      }
      .el-tab-pane {
        max-height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
