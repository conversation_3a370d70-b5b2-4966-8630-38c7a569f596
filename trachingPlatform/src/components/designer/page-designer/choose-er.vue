

<template>
  <div v-if="rowData.id&&rowData.id==='delete'" class="choose-btn">
    <el-tag v-if="!!rowData.relationErName">{{ rowData.relationErName }}</el-tag>
    <el-button size="small" @click="visible=true">
      关联ER模型
    </el-button>
    <scene-list :visible.sync="visible" @confirm="handleConfirm" />
  </div>
</template>

<script>
import SceneList from "./er-list.vue";
export default {
  name: "ChooseBtn",
  components: { SceneList },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false
    };
  },
  methods: {
    handleConfirm(data) {
      // todo 实际应该关联 data.id，但是目前只做主表的删除
      this.rowData.relationEr = data.mainTableId;
      this.rowData.relationErName = data.modelName;
    }
  }
};
</script>

<style lang="scss" scoped>
.choose-btn{
  display: flex;
  justify-content:space-around;
  align-items: center;
}</style>
