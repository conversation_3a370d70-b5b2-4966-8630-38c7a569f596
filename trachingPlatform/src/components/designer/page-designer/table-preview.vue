
<template>
  <div class="table-preview">
    <table2 @selectionChange="selectionChange" :notShowSearch="notShowSearch" v-if="forceUpdate" ref="tablePreview" @selection-change="handleSelectionChange" :selectable="selectable" :data="tableData" :columns="columns" :queryFormConfig="queryConfig" :total="total"
      :pagination="pagination" :max-height="height" :paginationLayout="paginationLayout" :firstLoad="firstLoad" :searchParams="searchParams" @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #actions v-if="actionBtns.length">
        <div style="display: flex;margin-bottom: 14px">
          <div style="margin-right: 14px" v-for="but in actionBtns" :key="but.id">
            <el-button  @click="handlePlus(but)">{{ but?.name || "新增" }}</el-button>
          </div>
        </div>
      </template>
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>
  </div>
</template>

<script>
import empty from "@/components/base/empty.vue";
// import dispatchLink from "./dispatch-link.vue";
import { isEmpty, cloneDeep } from "lodash";
import { getSceneJumpPathFromRelationForm } from "@/utils/sceneJump";
import formatterFunction from "@/components/base/table2/formatter-function";
import { handleUniqueArray, exportExcel } from "@/utils/common.js"
import DateUtils from "./date-utils";
export default {
  components: { empty },
  name: "TablePreview",
  props: {
    withIndex: {
      type: Boolean,
      default: false
    },
    notShowSearch: {
      type: Boolean,
      default: false
    },
    paginationLayout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper"
    },
    selectable: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => { }
    },
    openType: {
      type: String,
      default: ""
    },
    formatBtns: {
      type: Function
    },
    prepareQueryParams: {
      // 外部扩展查询参数
    },
    height:{
      type: [Number, String],
      default: 'auto'
    }
  },
  data() {
    return {
      activeName: "first",
      queryConfig: { formItemList: [] },
      columns: [],
      loading: true,
      tableData: [],
      selectionList: [],
      pagination: false,
      firstLoad: true,
      total: 0,
      tableParams: {},
      searchParams: null,
      searchRequestData: null, // 最终的搜索参数
      deleteErId: null, // 删除按钮的关联的ER
      btnList: {},
      operateBtns: [], // 操作
      actionBtns: [], // 顶部
      forceUpdate: true,
      selectedData: [], //选中的数据列表
      localSelectedData:[],// 缓存到本地得选中数据
    };
  },
  watch: {
    formData: {
      handler(newVal) {
        if (!isEmpty(newVal)) {
          this.forceUpdate = false;
          this.$nextTick(() => {
            this.forceUpdate = true;
            this.getConfig(newVal);
          })
        }
      },
      immediate: true
    },
    // localSelectedData:{
    //   handler(newVal){
    //     console.log("localSelectedData---------",newVal);
    //   },
    //   deep:true,
    //   immediate: true,
    // }
  },
  mounted(){
    // sessionStorage.setItem('tablePageIndex',1);
  },
  methods: {
    // table的刷新方法暴露给ref
    refresh() {
      this.$refs.tablePreview?.refresh()
    },
    //
    async getConfig(val) {
      let queryParams = { dataSourceID: val.id, queryDefaultGroupList: [] };
      const { id: dataId, displayConfig, pageConfig, btnConfig } = val;
      // 字段，查询条件
      this.columns = this.withIndex  ? [{
        prop: "index",
        label: "序号",
        width: 60,
        align: "center",
        type: "index",
      }] : [];
      this.queryConfig = { formItemList: [] };
      const selectTypes = [];
      let itemList = []
      displayConfig.forEach(column => {
        const { fixed, isQuery, isShow, label, isDispatch, tableAlias, tableName, prop, code, columnWidth, queryName, valueType, queryDefaultValue, format, decimalPlaces } = column;
        if (isShow === 1) {
          if (isDispatch) {
            // this.columns.push({
            //   fixed: fixed === 1 ? "left" : false,
            //   type: "components",
            //   components: dispatchLink,
            //   componentsProps: {
            //     dataId: dataId,
            //     fieldId: column.id,
            //     btnConfig
            //   },
            //   label,
            //   prop,
            //   width: columnWidth ? columnWidth : 0
            // });
          } else {
            this.columns.push({
              fixed: fixed === 1 ? "left" : false,
              label,
              prop,
              formatter: formatterFunction(valueType, format, decimalPlaces),
              formatters: formatterFunction(valueType),
              width: columnWidth ? columnWidth : 0
            });
          }
        }
        if (isQuery === 1) {
          const queryProp = `${tableName}----${tableAlias}----${code}----${prop}----${valueType}`;
          if(valueType === "SELECT"){
            const cfg = JSON.parse(column.controlConfig)
            selectTypes.push(cfg.selectType)
            itemList.push({
              ...cfg,
              prop: queryProp,
              label: queryName,
              type: "el-select",
              dataType: valueType,
              value: queryDefaultValue,
              clearable: true
            });
          }else if(valueType === "RELATION"){
            const cfg = JSON.parse(column.controlConfig)
            itemList.push({
              ...cfg,
              prop: queryProp,
              label: queryName,
              type: "relationComps-select",
              dataType: valueType,
              clearable: true
            });
          }else{
            itemList.push({
              prop: queryProp,
              label: queryName,
              type: valueType === "DATETIME" ? "el-date-picker" : valueType === "INT" ? "el-input-number" : "el-input",
              dataType: valueType,
              value: queryDefaultValue,
              clearable: true
            });
            if (typeof queryDefaultValue !== "undefined") {
              if (valueType === "DATETIME") {
                queryParams[queryProp] = DateUtils.getDateArray(queryDefaultValue);
              } else {
                queryParams[queryProp] = queryDefaultValue;
              }
            }
          }
        }
      });
      if(selectTypes.length > 0){
        // 查询数据
        let selectOptions = null
        const res = await this.$api.GetSelecsByTypes(selectTypes)
        if(res.code===200){
          for (const key in res.data) {
            if (Object.hasOwnProperty.call(res.data, key)) {
              selectOptions = res.data;
            }
          }
        }
        itemList.forEach(item=>{
          if(item.dataType === "SELECT"){
            item.options = selectOptions[item.selectType]
          }
        })
      }
      this.queryConfig.formItemList = itemList;
      // 分页 =
      const { firstLoad, pagination, operateWidth = 160 } = pageConfig;
      this.pagination = pagination === 1;
      this.firstLoad = firstLoad === 1;
      this.operateWidth = operateWidth;
      if (pagination === 1) {
        queryParams = { ...queryParams, pageSize: 10, pageIndex: 1 };
      } else {
        queryParams = { ...queryParams };
      }
      this.operateBtns = [];

      const btns = {};
      // 按钮
      btnConfig.forEach(btn => {
        const { id, name, enable, operationType, relationForm, relationFormTypeId, relationEr, openType, ...rest } = btn;
        if(enable === 1 && btn.btnPosition === 'in'){
          this.operateBtns.push(btn)
        }
        if(enable === 1 && btn.btnPosition === 'top'){
          this.actionBtns.push(btn)
        }
        btns[operationType] = {
          name,
          enable,
          relationFormTypeId,
          relationForm,
          openType: this.openType ? this.openType : openType,
          ...rest
        };
        if (id === "delete") {
          this.deleteErId = relationEr;
        }
      });
      // 提供扩展
      const btnList = this.formatBtns ? this.formatBtns(btns) : btns;

      const { ...rest } = btnList;
      // console.log('btnList',btnList)
      // Object.keys(btnList).forEach(val=>{
      //   if(btnList[val].btnPosition === 'top'){
      //     this.actionBtns.push(btnList[val])
      //   }
      // })
      this.btnList = rest;

      // 流程选择
      if(this.btnList['0']){
        console.log("this.operateBtns-------------",this.operateBtns)
        this.operateBtns = [this.btnList['0']];
      }
      // if (plus) {
      //   // 目前顶部按钮 只有新增
      //   this.actionBtns = plus;
      // }
      // for (const key in rest) {
      //   if(rest[key]?.btnPosition === 'in'){
      //     if (Object.hasOwnProperty.call(rest, key)) {
      //       const element = rest[key];
      //       if (element.enable) {
      //         element.btnType = key;
      //         this.operateBtns.push(element);
      //       }
      //     }
      //   }
      // }
      // 查询参数初始化
      this.searchParams = queryParams;
    },
    // 条件改变回调
    async handleSearch(t) {
      this.loading = true;
      if (isEmpty(t)) return this.loading = false;
      const params = cloneDeep(t);
      const searchs = [];
      for (const key in params) {
        if (Object.hasOwnProperty.call(params, key)) {
          const val = params[key];
          if (typeof val !== "undefined" && key.indexOf("----") > 0) {
            if(typeof val==="null" ||val ===''){
              // 空值不查询
            }else{
              const [tableName, tableAlias, code, prop,valueType] = key.split("----");
              let operator = 1
              let opValue = val;
              let isPush = true;
              if(val instanceof Array){
                const is0 = typeof val[0]!=="undefined"&&typeof val[0]!=="null";
                const is1 = typeof val[1]!=="undefined"&&typeof val[1]!=="null";
                if(is0&&is1){
                  operator = 8
                }else if(is0&&!is1){
                  operator = 5
                  opValue = val[0]
                }else if(!is0&&is1){
                  operator = 2
                  opValue = val[1]
                }else {
                  isPush = false
                }
              }
              if(isPush){
                if(operator===8){
                  searchs.push({
                    // ColCode: key.replace('----', ''), //表别名+字段
                    operator: 5, // 操作（运算）符 1:等于 2:小于 5: 大于等于 8:之间
                    queryValue: opValue[0], // 条件值
                    tableName: tableName, // 表名
                    tableAlias: tableAlias, // 表别名
                    columnAlias: prop, // 字段别名
                    columnName: code // 字段名
                  });
                  searchs.push({
                    // ColCode: key.replace('----', ''), //表别名+字段
                    operator: 2, // 操作（运算）符 1:等于 2:小于 5: 大于等于 8:之间
                    queryValue: opValue[1], // 条件值
                    tableName: tableName, // 表名
                    tableAlias: tableAlias, // 表别名
                    columnAlias: prop, // 字段别名
                    columnName: code // 字段名
                  });
                }else{
                   // 搜索场景把字符串改为7（模糊匹配）
                  searchs.push({
                    // ColCode: key.replace('----', ''), //表别名+字段
                    operator: valueType==='VARCHAR'?7:operator, // 操作（运算）符 1:等于 2:小于 5: 大于等于 8:之间
                    queryValue: opValue, // 条件值
                    tableName: tableName, // 表名
                    tableAlias: tableAlias, // 表别名
                    columnAlias: prop, // 字段别名
                    columnName: code // 字段名
                  });
                }
              }
            }
            if (key.indexOf("----") > -1) {
              delete params[key];
            }
          }
        }
      }
      params.queryValueList = searchs;
      const req = this.prepareQueryParams ? this.prepareQueryParams(params, t) : params
      // 把搜索参数暴露出去
      this.searchRequestData = req;
      const { code, data, recordCount } = await this.$api.GetDynamicSqlData(req);
      this.loading = false;
      if (code === 200) {
        this.tableData = data;
        this.total = recordCount;
      }
    },
    // route跳转
    openRoute({ openType, operate, path, name, id, ...rest }) {
      if (!path) {
        return;
      }
      if (openType === "self") {
        this.$router.push({
          path,
          query: operate === "plus" ? { operate } : { id, operate }
        });
      } else if (openType === "dialog") {
        this.$emit("openPathDialog", {
          openType,
          operate,
          path,
          name,
          id,
          ...rest
        });
      } else {
        // 新标签
        const origin = window.location.origin;
        if (window.location.href.indexOf(origin + "/#/") > -1) {
          // 地址包涵 #
          window.open(`${origin}/#${path}?id=${id || ""}&operate=${operate}`);
        } else {
          window.open(`${origin}/${path}?id=${id || ""}&operate=${operate}`);
        }
      }
    },
    // 场景选择
    handleSelectionChange(val) {
      // console.log("val", val);
      this.selectedData = val;
      // this.localSelectedData中包含的数据 当前页如果有 则移除掉对应的数据
      let tablePageIndex = sessionStorage.getItem('tablePageIndex')
      // console.log("--------selection",this.$refs.tablePreview.$refs.table2.selection)
      // console.log("--------val",val)
      console.log("--------tablePageIndex",tablePageIndex)
      // ||(val.length==0&&tablePageIndex==this.$refs.tablePreview.search.pageIndex)
      // if(val.length!=0){
      this.tableData.forEach(row =>{
        this.localSelectedData.forEach(v=>{
          if(v.id==row.id){
            this.localSelectedData.splice(this.localSelectedData.indexOf(v),1)
          }
        })
      });
      // }
      this.showSelectedData(val)
      // this.$emit('changeSelection',val)
    },
    pageCurrentChange(val){
      sessionStorage.setItem('tablePageIndex',val)
      this.showSelectedData([]);
    },
    // 选中数据回显
    showSelectedData(val) {
      this.localSelectedData = handleUniqueArray([...this.localSelectedData,...val],'id')// 合并选中数据
      this.$emit('changeSelection',this.localSelectedData)
      // const table = this.$refs.tablePreview.$refs.table2;
      const rowsToSelect = this.tableData.filter(row => this.localSelectedData.find(v=>v.id==row.id));
      this.$refs.tablePreview.toggleRowSelection(rowsToSelect)
    },
    selectionChange(e){
      this.selectionList = e
    },
    // 新增按钮
    async handlePlus(but) {
      if(but.openType === 'externalLink'){
        window.open(but.relationFormName)
      }else if(but.openType === 'message'){
        this.$message.success(but.relationFormName)
      }else if(but.openType === 'logic'){
        if(this.selectionList.length){
          let selectionList = []
          this.selectionList.forEach(val=>{
            selectionList.push(val.id)
          })
          const {code, data, msg} = await this.$api.DataFlowRun({ids:selectionList},but.relationFormId)
          if(code === 200){
            this.$message.success('操作成功！')
          }else if(code === 1){
            this.$message.warning(msg)
          }
        }else{
          this.$message.warning('请选择数据！')
        }

      }else if(but.id === 'export') {
        exportExcel("/dfs/DynamicForms/ExportDataSourceData", {dataSourceID: this.formData.id}, this.formData.pageName);
      }else{
        const btn = but || {};
        const path = getSceneJumpPathFromRelationForm(btn);
        this.openRoute({ path, operate: "plus", ...btn });
      }
    },
    btnClick(row, btn) {
      switch (btn.operationType) {
        case 'edit':
          this.handleEdit(row, btn)
          break;
        case 'detail':
          this.handleDetail(row, btn)
          break;
        case 'delete':
          this.handleDelete(row)
          break;
        default:
          if(btn.operationType === "customizable"){
            this.handleCustomizable(row, btn)
          }else{
            btn?.click(row)
          }
          break;
      }
    },
    // 编辑按钮
    handleEdit(item, btn) {
      const { id } = item;
      const path = getSceneJumpPathFromRelationForm(btn);
      this.openRoute({ path, operate: "edit", ...btn, item, id});
    },
    // 明细按钮
    handleDetail(item, btn) {
      const { id } = item;
      const path = getSceneJumpPathFromRelationForm(btn);
      this.openRoute({ path, operate: "detail", ...btn, item, id});
    },
    // 自定义弹窗
    async handleCustomizable(item, btn) {
      if(btn.openType === 'dialog'){
        const { id } = item;
        const path = getSceneJumpPathFromRelationForm(btn);
        this.openRoute({ path, operate: "customizable", id, ...btn, item });
      }else{
        const {code, data, msg} = await this.$api.DataFlowRun({ids:item.id},btn.relationFormId)
        if(code === 200){
          this.$message.success('操作成功！')
        }else if(code === 1){
          this.$message.warning(msg)
        }
      }

    },
    // 删除按钮
    handleDelete({ id }) {
      this.$confirm("是否确认删除数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
      .then(() => {
        this.deleteApi(id);
      })
    },
    deleteApi(id){
      this.$api
        .removeCustomTableData({
          TableId: this.deleteErId,
          postData: [
            {
              id
            }
          ]
        })
        .then(res => {
          if (res.code === 200) {
            this.$refs.tablePreview.refresh();
            this.$message({
              type: "success",
              message: "删除成功"
            });
          }
        });
    },
    // 通过条件判断是否显示按钮
    isShowOperateBtn ({ row }, btn) {
      let show = true
      if (btn.showCondition && btn.showCondition.length) {
        btn.showCondition.forEach(v => {
          switch (v.op) {
            // 等于时
            case 'eq':
              if (row[v.columnProp].toString() !== v.value.toString()) {
                show = false
              }
              break;
          }
        })
      }
      return show
    }
  }
};
</script>

<style lang="scss" scoped>
.table-preview {
  height: 100%;
  ::v-deep .el-table__header-wrapper{
    height: 44px;
  }
}
</style>
