<template>
  <base-dialog
    v-dialogDrag
    :modal="true"
    :visible.sync="searchConfigDialogVisible"
    :close-on-click-modal="false"
    width="450px"
    className="search-config-dialog"
    title="设置查询字段"
    :before-close="beforeClose"
    @submit="submit">
    <el-form
      :model="searchConfigRow"
      label-position="left"
      label-width="90px">
      <el-form-item label="字段"
        >{{ searchConfigRow.columnName }} ({{
          searchConfigRow.prop
        }})</el-form-item
      >
      <el-form-item
        label="查询别名"
        prop="queryName">
        <el-input
          :maxlength="20"
          v-model="searchConfigRow.queryName"
          placeholder="查询别名"></el-input>
      </el-form-item>
      <el-form-item
        label="查询默认值"
        prop="queryDefaultValue"
        v-if="searchConfigRow.valueType === 'DATETIME'">
        <el-select
          v-model="datetimeDefaultValue[0]"
          placeholder="开始日期"
          class="datetime-select">
          <el-option
            v-for="item in datetimeDefaultValueOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <span class="separator">至</span>
        <el-select
          v-model="datetimeDefaultValue[1]"
          placeholder="结束日期"
          class="datetime-select">
          <el-option
            v-for="item in datetimeDefaultValueOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="查询默认值"
        prop="queryDefaultValue"
        v-else>
        <el-input
          :maxlength="20"
          v-model="searchConfigRow.queryDefaultValue"
          placeholder="查询默认值"></el-input>
      </el-form-item>
    </el-form>
  </base-dialog>
</template>

<script>
import DateUtils from "./date-utils";
export default {
  name: "ColumnSearchConfigDialog",
  props: {
    searchConfigDialogVisible: {
      type: Boolean,
      default: false
    },
    searchConfigRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      datetimeDefaultValueOptions: DateUtils.datetimeDefaultValueOptions,
      datetimeDefaultValue: []
    };
  },
  watch: {
    searchConfigRow(data) {
      if (data) {
        this.datetimeDefaultValue = [];
        if (
          data.valueType === "DATETIME" &&
          data.queryDefaultValue?.includes("[")
        ) {
          this.datetimeDefaultValue = JSON.parse(data.queryDefaultValue);
        }
      }
    }
  },
  methods: {
    submit() {
      if (this.datetimeDefaultValue.length) {
        this.searchConfigRow.queryDefaultValue = JSON.stringify(
          this.datetimeDefaultValue
        );
      }
      this.$emit("submit");
    },
    beforeClose() {
      this.$emit("update:searchConfigDialogVisible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-config-dialog {
  .datetime-select {
    width: 46%;
  }
  .separator {
    display: inline-block;
    width: 8%;
    text-align: center;
  }
}
</style>
