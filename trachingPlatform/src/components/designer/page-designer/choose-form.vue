

<template>
  <div v-if="rowData.id && rowData.id !== 'delete' && rowData.id !== 'export'">
    <div
        class="choose-btn"
        v-if="rowData.openType === 'new' || rowData.openType === 'self' || rowData.openType === 'dialog'">
      <el-tag
          closable
          @close="handleClose"
          v-if="!!rowData.relationFormName"
      >{{ rowData.relationFormName }}</el-tag
      >
      <el-button
          size="small"
          @click="visible = true">
        关联表单
      </el-button>
      <scene-list
          :visible.sync="visible"
          @confirm="handleConfirm" />
    </div>
    <div class="choose-btn" v-else-if="rowData.openType === 'externalLink'">
      <el-tag
          closable
          @close="handleClose"
          v-if="!!rowData.relationFormName"
      >{{ rowData.relationFormName }}</el-tag
      >
      <el-button
          size="small"
          @click="visible = true">
        外部链接配置
      </el-button>
      <base-dialog
          :visible.sync="visible"
          :hasFull="false"
          width="900px"
          title="外部链接内容"
          className="menu-dialog">
        <el-input v-model="rowData.relationFormName"></el-input>
      </base-dialog>
    </div>
    <div class="choose-btn" v-else-if="rowData.openType === 'message'">
      <el-tag
          closable
          @close="handleClose"
          v-if="!!rowData.relationFormName"
      >{{ rowData.relationFormName }}</el-tag
      >
      <el-button
          size="small"
          @click="visible = true">
        消息提醒配置
      </el-button>
      <base-dialog
          :visible.sync="visible"
          :hasFull="false"
          width="900px"
          title="消息提醒内容"
          className="menu-dialog">
        <el-input v-model="rowData.relationFormName"></el-input>
      </base-dialog>
    </div>
    <div class="choose-btn" v-else-if="rowData.openType === 'logic'">
      <el-tag
          closable
          @close="handleClose"
          v-if="!!rowData.relationFormName"
      >{{ rowData.relationFormName }}</el-tag
      >
      <el-button
          size="small"
          @click="visible = true">
        逻辑配置
      </el-button>
      <base-dialog
          :visible.sync="visible"
          :hasFull="false"
          width="900px"
          title="调用逻辑"
          className="menu-dialog">
        <el-select filterable @change="logicChange" v-model="rowData.relationFormId">
          <el-option v-for="i in logicList"
                     :key="i.name"
                     :label="i.displayName"
                     :value="i.name"></el-option>
        </el-select>
      </base-dialog>
    </div>
  </div>

</template>

<script>
import SceneList from "./scene-list.vue";
export default {
  name: "ChooseBtn",
  components: { SceneList },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    },
    columnInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false
    };
  },
  computed: {
    logicList() {
      return this.columnInfo.componentsProps.getLogicList();
    }
  },
  methods: {
    logicChange(e){
      const logic = this.logicList.find(v=>v.name === e)
      this.rowData.relationFormName = logic?.displayName
    },
    handleClose() {
      this.$confirm("确定删除该关联表单？")
        .then(_ => {
          this.columnInfo.componentsProps?.onConfirm({}, this.rowData);
          this.rowData.relationForm = "";
          this.rowData.relationFormTypeId = "";
          this.rowData.relationFormErId = "";
          this.rowData.dlgTitle = "";
          this.rowData.relationFormName = "";
        })
        .catch(_ => {});
    },
    handleConfirm(data) {
      this.columnInfo.componentsProps?.onConfirm(data, this.rowData);
      this.rowData.relationForm = data.id;
      this.rowData.relationFormTypeId = data.typeId;
      this.rowData.relationFormErId = data.erId;
      this.rowData.dlgTitle = data.formName;
      this.rowData.relationFormName = data.formName;
    }
  }
};
</script>

<style lang="scss" scoped>
.choose-btn {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
</style>
