
<template>
  <div>
    <el-button @click="addButton">新增</el-button>
    <table2
        :pagination="false"
        :data="tableData"
        :columns="columns"
        :cell-class-name="cellClassName" />
  </div>

</template>

<script>
const Buttons = [
  { label: "新增", value: "plus" },
  { label: "编辑", value: "edit" },
  { label: "删除", value: "delete" },
  { label: "查询", value: "detail" },
  { label: "导入", value: "import" },
  { label: "导出", value: "export" },
  { label: "自定义", value: "customizable" },
];
import chooseForm from "./choose-form.vue";
import chooseER from "./choose-er.vue";
import chooseCondition from "./choose-condition.vue";
export default {
  name: "ActionInfo",
  props: {
    btnConfig: {
      type: Array,
      default: () => []
    },
    displayConfig: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      logicList: [],
      columns: [
        { prop: "index", type: "index", label: "序号" },
        {
          prop: "operationType",
          width: 120,
          label: "操作类型",
          formatter(row, column, cellValue) {
            const btn = Buttons.find(item => item.value === cellValue);
            return btn?.label || cellValue;
          }
        },
        { prop: "name", width: 150, label: "按钮名称", type: "el-input", maxlength:10 },
        {
          prop: "enable",
          width: 120,
          label: "启用",
          type: "el-switch",
          activeText: "是",
          activeValue: 1,
          inactiveValue: 0,
          inactiveText: "否"
        },
        {
          prop: "btnPosition",
          width: 130,
          label: "按钮位置",
          type: "el-select",
          options: [
            { label: "数据行内", value: "in" },
            { label: "顶部", value: "top" },
          ]
          // formatter(row, column, cellValue) {
          //   return cellValue === "in"
          //     ? "数据行内"
          //     : cellValue === "top"
          //       ? "顶部"
          //       : "";
          // }
        },
        {
          prop: "btnCondition",
          width: 200,
          label: "显示条件",
          type: "components",
          components: chooseCondition,
          componentsProps: {
            columnList: this.displayConfig
          }
        },
        // { prop: 'btnStyle', label: '按钮样式' },
        // { prop: 'icon', label: '按钮图标' },
        {
          prop: "openType",
          width: 130,
          label: "打开方式",
          type: "el-select",
          options: [
            { label: "新窗口", value: "new" },
            { label: "当前窗口", value: "self" },
            { label: "弹窗", value: "dialog" },
            { label: "外部链接", value: "externalLink" },
            { label: "消息提醒", value: "message" },
            { label: "调用逻辑", value: "logic" },
          ]
        },
        { prop: "dlgTitle", width: 180, label: "弹出框标题", type: "el-input", maxlength:20 },
        { prop: "dlgWidth", width: 120, label: "弹出框宽度", type: "el-input", placeholder: '默认' },
        {
          prop: "relationForm",
          label:  "关联关系",
          type: "components",
          hied: false,
          components: chooseForm,
          componentsProps: {
            getLogicList: () => this.logicList,
            onConfirm: (data, rowData) => {
              this.$emit("update:btnConfig", this.tableData.map(d => {
                if (d.id === rowData.id) {
                  d.dlgTitle = data.formName;
                }
                return d;
              }));
            }
          }
        },
        {
          prop: "relationEr",
          label: "关联ER模型",
          type: "components",
          components: chooseER
        }
        // { prop: 'erModel', label: 'ER模型' },
        // { prop: 'outConfig', label: '出参配置' },
        // { prop: 'condition', label: '显示条件' },
        // { prop: 'layout', label: '按钮平铺' },
      ],
      buttons: [
        {
          id: "plus",
          operationType: "plus",
          name: "新增",
          btnPosition: "top",
          icon: "el-icon-plus",
          openType: "dialog",
          relationForm: "",
          relationEr: ""
        },
        {
          id: "edit",
          operationType: "edit",
          name: "编辑",
          btnPosition: "in",
          icon: "el-icon-edit",
          openType: "dialog",
          relationForm: "",
          relationEr: ""
        },
        {
          id: "delete",
          operationType: "delete",
          name: "删除",
          btnPosition: "in",
          icon: "el-icon-delete",
          openType: "",
          relationForm: "",
          relationEr: ""
        },
        {
          id: "detail",
          operationType: "detail",
          name: "详情",
          btnPosition: "in",
          icon: "el-icon-search",
          openType: "dialog",
          relationForm: "",
          relationEr: ""
        },
        {
          id: "export",
          operationType: "export",
          name: "导出",
          btnPosition: "top",
          icon: "el-icon-download",
          openType: "dialog",
          relationForm: "",
          relationEr: ""
        }
      ]
    };
  },
  async created() {
    let {code ,data} = await this.$api.GetLogicArrangeList({pageIndex: 1,pageSize: 9999})
    if(code === 200){
      this.logicList = data
    }
  },
  computed: {

    tableData: {
      get: function () {
        if (this.btnConfig.length === 0) {
          this.$emit("update:btnConfig", this.buttons);
          return this.buttons;
        } else {
          const exportBtn = this.btnConfig.find(p=>p.id==='export');
          if(!exportBtn){
            this.btnConfig.splice(4, 0, this.buttons.find(p=>p.id==='export'));
          }
          return this.btnConfig;
        }
      },
      set: function (val) {
        this.$emit("update:btnConfig", val);
      }
    }
  },
  methods: {
    // 添加自定义按钮
    addButton(){
      const newButton = {
        id: this.buttons.length,
        operationType: "customizable",
        name: "自定义按钮",
        btnPosition: "",
        icon: "",
        openType: "dialog",
        relationForm: "",
        relationEr: ""
      }
      this.tableData.push(newButton)
    },
    cellClassName({ row, column }){
      if (row.id === 'delete' && ['openType','dlgTitle','dlgWidth'].includes(column.property)){
        return 'hide-cell';
      }
      if (row.id === 'export' && ['btnPosition','openType','dlgTitle','dlgWidth','relationForm','relationEr'].includes(column.property)){
        return 'hide-cell';
      }
    }
  }
};
</script>

<style scoped>
::v-deep .hide-cell .cell{
  display: none;
}</style>
