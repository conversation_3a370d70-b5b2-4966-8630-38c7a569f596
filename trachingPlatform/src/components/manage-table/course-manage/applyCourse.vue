<template>
  <div class="apply-box">
    <div class="apply-header">
      <div class="apply-header-left">申请示范课程</div>
      <div class="apply-header-right">
        <div class="cancel-btn" @click="cancelClick">取消</div>
        <div class="staging-btn" @click="save()">暂存</div>
        <div class="submit-btn" @click="submitClick()">提交</div>
      </div>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="管理详情" name="first">
        <div class="apply-content">
          <div class="apply-form-item">
            <label class="apply-form-label">课程名称</label>
            <div class="apply-form-value">{{ courseInfo.courseName }}</div>
          </div>
          <div class="apply-form-item">
            <label class="apply-form-label"><span class="red-required">*</span>教师团队</label>
            <div class="apply-form-value-teach">
              <div class="apply-teach" v-for="( item, index ) in teachList " :key="index">
                <i class="el-icon-error" @click="deleteClick(index)"></i>
                <span :title="item.teacherName">{{ item.teacherName }}</span>
              </div>
              <el-popover placement="right" width="400" trigger="click" @show="teachShowClick" v-model="isPopup">
                <el-input style="width:260px;" v-model.trim="teachName" placeholder="请输入教师姓名" maxlength="10"></el-input>
                <el-button @click="addTeach">确定</el-button>
                <i class="el-icon-circle-plus-outline" slot="reference" v-show="teachList.length < 3"></i>
              </el-popover>

            </div>
          </div>
          <div class="apply-form-item">
            <label class="apply-form-label"><span class="red-required">*</span>所属学校</label>
            <div class="apply-form-value-teach">
              <div class="apply-teach" v-for="( item, index ) in schoolList " :key="index">
                <i class="el-icon-error" @click="deleteSchool(index)"></i>
                <span :title="item.schoolName">{{ item.schoolName }}</span>
              </div>
              <el-popover placement="right" width="400" trigger="click" @show="schoolShowClick" v-model="isSchool">
                <el-input style="width:260px;" v-model.trim="schoolNewName" placeholder="请输入学校名称"
                  maxlength="15"></el-input>
                <el-button @click="addSchool">确定</el-button>
                <i class="el-icon-circle-plus-outline" slot="reference"></i>
              </el-popover>
            </div>
          </div>
          <div class="apply-form-item">
            <label class="apply-form-label"><span class="red-required">*</span>面向专业</label>
            <div class="apply-form-value">
              <el-cascader ref="majorCascader" v-model="majorVaule" :options="majorList" :props="majorProps"
                @change="handleChange" style="width: 100%;" clearable filterable></el-cascader>
            </div>
          </div>
          <div class="apply-form-item">
            <label class="apply-form-label"><span class="red-required">*</span>课程简介</label>
            <div class="apply-form-value">
              <wangEditor :value="modelCourseInfo.introduction" @input="handleEditorChange" :height="300"></wangEditor>
            </div>

          </div>
          <div class="apply-form-item" v-for="( item, remarkIndex ) in remarkList ">
            <i v-if="remarkIndex > 0" class="el-icon-error icon-close1" @click="deleteRemark(remarkIndex)"></i>
            <label class="apply-form-label">
              <el-input style="width: 91px" v-model.trim="item.title"></el-input>
            </label>
            <div class="apply-form-value">
              <wangEditor :ref="`tinyModule${remarkIndex}`" :value="item.introduction"
                @input="handleRemarkChange($event, remarkIndex)" :height="300"></wangEditor>
            </div>
          </div>
          <i class="el-icon-circle-plus-outline" style="margin-bottom: 10px;" @click="addRemark"></i>
          <div class="apply-form-item" v-for="( item, index ) in teamList " :key="index">
            <i v-if="index > 0" class="el-icon-error icon-close1" @click="deleteTeam(index)"></i>
            <label class="apply-form-label"><span class="red-required">*</span>产教团队</label>
            <div class="apply-form-value-team">
              <div class="apply-form-team">
                <div class="apply-form-team-item" style="margin-bottom: 20px;">
                  <div style="margin-right: 10px;">
                    <label>姓名</label>
                    <el-input v-model.trim="item.name" placeholder="请输入内容"></el-input>
                  </div>
                  <div>
                    <label>职务职称</label>
                    <el-input v-model.trim="item.duty" placeholder="请输入内容"></el-input>
                  </div>
                </div>
                <div class="apply-form-team-item">
                  <div>
                    <label>简介</label>
                    <el-input type="textarea" v-model.trim="item.introduction" placeholder="请输入内容"></el-input>
                  </div>
                </div>
              </div>
            </div>

          </div>
          <i class="el-icon-circle-plus-outline" @click="addTeam" v-show="teamList.length < 3"></i>
        </div>
      </el-tab-pane>
      <el-tab-pane label="课程设计" name="second">
        <div class="no-empty" v-if="copyState == 1">
          <img src="@/assets/course/no-data-empty.png" alt="">
          <span>课程数据还在准备中…</span>
        </div>
        <prepareLessons v-else style="height: calc(100vh - 190px);"></prepareLessons>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import wangEditor from '@/components/base/wang-editor.vue'
import prepareLessons from '../../../views/teaching-page/prepare-lessons-edit.vue'
export default {
  name: 'FusionFrontApplycourse',
  components: {
    prepareLessons,
    wangEditor
  },
  data() {
    return {
      activeName: "first",
      teachList: [],
      isPopup: false,
      teachName: "",
      majorVaule: [],
      majorList: [],
      majorProps: { multiple: true, value: 'id', label: 'name', children: 'subordinateNodes' },
      modelCourseInfo: {
        introduction: ""
      },
      remarkList: [{
        title: "课程背景",
        introduction: ""
      }],
      teamList: [{
        name: "",
        duty: "",
        introduction: ""
      }],
      copyState: null,
      schoolList: [],
      isSchool: false,
      schoolNewName: '',
      modelCouseId: 0
    };
  },
  computed: {
    ...mapGetters({
      courseInfo: ['getCourseInfo']
    })
  },
  mounted() {
    this.MajorTree();
    if (this.$route.query.courseStatus == 0) {
      this.ModelCourseDetail();
    } else if (this.$route.query.courseStatus == 3 || this.$route.query.courseStatus == 2) {
      this.ModelCourseDetailById();
    }

    if (this.$route.query.courseStatus == 0) {
      this.modelCouseId = this.modelCourseInfo.id;
    }
    let self = this;//滚动隐藏弹窗
    this.$nextTick(() => {
      const scrollContainer = this.$el.querySelector('.apply-content');
      const cascader = this.$el.querySelector('.el-cascader__dropdown');

      scrollContainer.addEventListener('scroll', function () {
        // 获取滚动位置;
        self.isPopup = false;
        self.isSchool = false;
        cascader.style.display = 'none';
      });
    })

  },

  methods: {
    handleEditorChange(val) {
      this.modelCourseInfo.introduction = val;
    },
    handleRemarkChange(val, index) {
      if (val == '<p><br></p>') {
        val = '';
      }
      this.remarkList[index].introduction = val;
    },
    async GetCourseCloneStatus() {
      let { code, data } = await this.$api.GetCourseCloneStatus({ newCourseId: this.$route.query.id || this.modelCourseInfo.courseId });
      if (code == 200) {
        this.copyState = data;
      }
    },
    addTeam() {
      this.teamList.push({ input: "" })
    },
    addRemark() {
      this.remarkList.push({ title: "", introduction: "" })
    },
    handleChange(value) {
      console.log(value);
    },
    async MajorTree() {
      const { code, data } = await this.$api.MajorTree({});
      if (code === 200) {
        data.forEach(item => {
          item = this.isMajorCompare(item)
        })
        this.majorList = data;
      }
    },
    isMajorCompare(tree) {//对专业类型子类为空  和末级是专业进行处理
      if (tree.subordinateNodes?.length > 0) {
        tree.subordinateNodes.forEach(treeItem => {
          if (treeItem.isMajor && treeItem.subordinateNodes.length == 0) {
            delete treeItem.subordinateNodes
          } else if (!treeItem.isMajor && treeItem.subordinateNodes.length == 0) {
            treeItem.disabled = true;
          }
          treeItem = this.isMajorCompare(treeItem)
        });
      }
      return tree; // 返回修改后的节点  
    },
    //暂存时调用查询
    async ModelCourseDetail() {
      let param = {
        courseId: this.$route.query.id
      };
      const res = await this.$api.ModelCourseDetail(param);
      if (res.code == 200) {
        if (res.data) {
          if (res.data.auditStatus == 0) {
            this.modelCouseId = res.data.id;
          }
          let majorItemList = [];
          this.remarkList = [];
          this.teamList = [];
          this.schoolList = [];
          this.modelCourseInfo.introduction = res.data.introduction;
          this.modelCourseInfo.id = res.data.id;
          this.modelCourseInfo.courseId = res.data.courseId;
          res.data.teacherList?.forEach(item => {
            this.teachList.push({
              teacherName: item.teacherName,
              // teacherId: item.teacherId
            })
          })
          res.data.schoolList?.forEach(listItem => {
            this.schoolList.push({ schoolName: listItem.schoolName })
          })
          res.data.majorList?.forEach(majorItem => {
            let majorIdsArr = majorItem.majorIds.split(',').map(Number)
            majorItemList.push(majorIdsArr)
          })
          this.majorVaule = majorItemList;
          res.data.teamMemberList?.forEach(({ name, duty, introduction }) => {
            this.teamList.push({
              name, duty, introduction
            })
          })
          res.data.introductionList?.forEach(({ title, introduction }) => {
            this.remarkList.push({
              title,
              introduction
            })
          })
        }
      }
    },
    async ModelCourseDetailById() {
      let param = {
        id: this.$route.query.courseRecordId
      };
      const res = await this.$api.ModelCourseDetailById(param);
      if (res.code == 200) {
        if (res.data) {
          let majorItemList = [];
          this.remarkList = [];
          this.teamList = [];
          this.schoolList = [];
          this.modelCourseInfo.introduction = res.data.introduction;
          this.modelCourseInfo.id = res.data.id;
          this.modelCourseInfo.courseId = res.data.courseId;
          res.data.teacherList?.forEach(item => {
            this.teachList.push({
              teacherName: item.teacherName,
              // teacherId: item.teacherId
            })
          })
          res.data.schoolList?.forEach(listItem => {
            this.schoolList.push({ schoolName: listItem.schoolName })
          })
          res.data.majorList?.forEach(majorItem => {
            let majorIdsArr = majorItem.majorIds.split(',').map(Number)
            majorItemList.push(majorIdsArr)
          })
          this.majorVaule = majorItemList;
          res.data.teamMemberList?.forEach(({ name, duty, introduction }) => {
            this.teamList.push({
              name, duty, introduction
            })
          })
          res.data.introductionList?.forEach(({ title, introduction }) => {
            this.remarkList.push({
              title,
              introduction
            })
          })
        }
      }
    },
    teachShowClick() {
      this.teachName = "";
      this.isPopup = true;
    },
    schoolShowClick() {
      this.schoolNewName = "";
      this.isSchool = true;
    },
    addSchool() {
      if (!this.schoolNewName) {
        this.$message.error("请输入学校名称！")
        return
      }
      this.schoolList.push({
        schoolName: this.schoolNewName,
      });
      this.isSchool = false;
    },
    addTeach() {
      if (!this.teachName) {
        this.$message.error("请输入教师姓名！")
        return
      }
      this.teachList.push({
        teacherName: this.teachName,
      });
      this.isPopup = false;
    },
    deleteClick(index) {
      this.teachList.splice(index, 1)
    },
    deleteSchool(index) {
      this.schoolList.splice(index, 1)
    },
    deleteRemark(index) {
      this.remarkList.splice(index, 1)
    },
    deleteTeam(index) {
      this.teamList.splice(index, 1)
    },
    handleClick(tab, event) {
      if (this.activeName == "second") {
        this.GetCourseCloneStatus()
      }
      if (this.copyState == 1) {
        setTimeout(() => {
          this.GetCourseCloneStatus()
        }, 10000);
      }

    },

    // 暂存
    async save() {
      let param = this.generateRequestParam(0);

      let $this = this;

      await this.apply(param, function () {
        $this.$message.success('暂存成功！');
      });
    },

    // 提交校验
    submitValidate() {
      if (!this.teachList.length > 0) {
        this.$message.error("教师团队未添加！");
        return false;
      }
      if (!this.schoolList.length > 0) {
        this.$message.error("所属学校未添加！");
        return false;
      }
      if (!this.majorVaule.length > 0) {
        this.$message.error("面向专业未添加！");
        return false;
      }
      if (!this.modelCourseInfo.introduction) {
        this.$message.error("请输入课程简介！");
        return false;
      }

      let errorItem = this.remarkList.find(item => !item.introduction);
      if (errorItem) {
        this.$message.error(`${errorItem.title} 未填写！！`);
        return false;
      }

      let isApiData = true;
      this.teamList.some((listItem, ny) => {
        if (!listItem.name) {
          this.$message.error(`第${ny + 1}产教团队姓名未填写！！`);
          isApiData = false;
          return true; //退出循环
        }
        if (!listItem.duty) {
          this.$message.error(`第${ny + 1}产教团队职务职称未填写！！`);
          isApiData = false;
          return true;
        }
        if (!listItem.introduction) {
          this.$message.error(`第${ny + 1}产教团队简介未填写！！`);
          isApiData = false;
          return true;
        }
      })

      return isApiData;
    },

    // 生成请求参数
    generateRequestParam(auditStatus) {
      let param = {
        id: this.modelCouseId,
        courseId: this.$route.query.id || this.modelCourseInfo.courseId,
        introduction: this.modelCourseInfo.introduction,
        auditStatus: auditStatus,
        teacherList: null,
        schoolList: null,
        majorList: null,
        introductionList: null,
        teamMemberList: null
      };

      //教师团队
      if (this.teachList?.length > 0) {
        param.teacherList = this.teachList.map(({ teacherId, teacherName }) => ({
          id: 0,
          modelCourseId: this.modelCourseInfo.id || 0,//示范课程id
          // teacherId,
          teacherName
        }))
      }
      //所属学校
      if (this.schoolList.length > 0) {
        param.schoolList = this.schoolList.map(item => ({
          id: 0,
          modelCourseId: this.modelCourseInfo.id || 0,//示范课程id
          schoolName: item.schoolName
        }))
      }
      //面向专业
      if (this.majorVaule.length > 0) {
        param.majorList = this.majorVaule.map(item => ({
          id: 0,
          modelCourseId: this.modelCourseInfo.id || 0,
          majorId: item[item.length - 1],
          majorIds: item.join(',')
        }))
      }
      //产教团队
      if (this.teamList.length > 0) {
        param.teamMemberList = this.teamList.map(item => ({
          id: 0,
          modelCourseId: this.modelCourseInfo.id || 0,
          name: item.name,
          duty: item.duty,
          headPortrait: "",
          introduction: item.introduction
        }))
      }
      //描叙列表
      if (this.remarkList.length > 0) {
        param.introductionList = this.remarkList.map(item => ({
          id: 0,
          modelCourseId: this.modelCourseInfo.id || 0,
          title: item.title,
          introduction: item.introduction
        }))
      }

      return param;
    },

    // 请求
    async apply(param, callback) {
      const { code, data } = await this.$api.ModelCourseSave(param);
      if (code === 200) {
        this.modelCouseId = data;
        if (typeof callback === 'function') {
          callback();
        }
      }
    },

    // 提交
    async submitClick() {
      if (!this.submitValidate()) {
        return;
      }

      let param = this.generateRequestParam(1);

      let $this = this;

      await this.apply(param, function () {
        $this.$message.success('操作成功！');

        let query = {
          id: $this.$route.query.originalCourseId,
          courseName: $this.courseInfo.courseName,
          // tabVale: 'course'
        };
        $this.$router.push({
          path: "/teachingWork",
          query
        })
      });
    },
    cancelClick() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.apply-box {
  //  min-width: 500px;
  height: 100%;
  // margin: 0 auto;
  background-color: #fff;

  .apply-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 63px;
    margin: 0 25px;
    border-bottom: 2px dashed #e3e3e3;

    .apply-header-left {
      flex: 55%;
      line-height: 63px;
      text-align: right;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }

    .apply-header-right {
      flex-basis: 45%;
      line-height: 63px;
      display: flex;
      justify-content: flex-end;

      >div {
        width: 80px;
        height: 34px;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        cursor: pointer;
        border-radius: 4px;
      }

      .cancel-btn {
        background: #FFFFFF;
        border: 1px solid #DDE2E9;
        color: #333333;
        margin-right: 18px;
      }

      .staging-btn {
        background: #FFFFFF;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        margin-right: 18px;
      }

      .submit-btn {
        background: #2B66FF;
        color: #FFFFFF;
        margin-right: 31px;
      }

    }
  }

  .apply-content {
    margin-top: 20px;
    height: calc(100vh - 210px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 20px;

    .apply-form-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      .icon-close1 {
        display: inline-block;
        position: absolute;
        top: -11px;
        right: -6px;
        z-index: 100;
        cursor: pointer;

        &:before {
          font-size: 16px;
          color: #2B66FA;
        }
      }

      .apply-form-label {
        font-size: 14px;
        color: #787D83;
        margin-right: 10px;
        line-height: 36px;
        width: 91px;

        .red-required {
          color: red;
        }
      }

      .apply-form-value,
      .apply-form-value-team {
        // width: 645px;
        width: 900px;
        line-height: 36px;
      }

      .apply-form-value-teach {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        // width: 645px;
        width: 900px;
      }


      .apply-teach {
        width: 94px;
        height: 36px;
        background: #FBFBFB;
        border-radius: 3px;
        border: 1px solid #CCD4E0;
        font-size: 14px;
        color: #333333;
        text-align: center;
        margin-right: 20px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        cursor: pointer;

        &:hover {
          border: 1px solid #2B66FA;
        }

        >span {
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }


        .el-icon-error {
          display: inline-block;
          position: absolute;
          top: -11px;
          right: -6px;

          &:before {
            font-size: 16px;
            color: #2B66FA;
          }
        }
      }

      .apply-form-team {
        height: 181px;
        background: #FFFFFF;
        border-radius: 3px;
        border: 1px solid #CCD4E0;
        padding: 20px;

        .apply-form-team-item {
          display: flex;

          >div {
            display: flex;
            width: 100%;

            label {
              min-width: 60px;
              font-size: 14px;
              color: #333333;
            }
          }
        }
      }


      .tox-tinymce {
        height: 200px !important;
      }
    }
  }

  .el-tabs--card>.el-tabs__header .el-tabs__nav,
  .el-tabs--card>.el-tabs__header .el-tabs__item,
  .el-tabs--card>.el-tabs__header {
    border: none;
  }

  .el-tabs {
    margin: 21px 25px 0;

    .el-tabs__header {
      margin-bottom: 0;

      .el-tabs__nav-scroll {
        display: flex;
        justify-content: center;

        .el-tabs__item {
          background: #F7F7F7;
          width: 118px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          margin-right: 14px;
        }

        .is-active {
          background: #4572FA;
          color: #fff;
        }
      }
    }

    .el-tabs__content {
      border: 1px solid #DEDEDE;
      margin-top: 1px;

      .el-tabs .el-tabs__header .el-tabs__nav-scroll {
        display: inline-block;
      }

      .no-empty {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 570px;

        span {
          font-size: 16px;
          color: #333333;
          margin-top: 31px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>