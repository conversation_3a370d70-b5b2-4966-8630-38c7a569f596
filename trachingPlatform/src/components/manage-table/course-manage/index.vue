<template>
  <div class="course-box">
    <div class="course-manage-box">
      <div v-premission="'courseDemonstrate'" class="modelCourse-staust" v-if="modelCourseAuditStatus == 2">
        <span>示范教材</span>
        <i class="icon-bianjis" @click="applyClick"></i>
      </div>
      <div v-premission="'courseDemonstrate'" class="not-pass_box" v-else-if="modelCourseAuditStatus == 3">
        <div class="not-pass">
          <span>未通过</span>
          <el-popover placement="bottom-start" title="提示原因" width="200" trigger="hover"
            :content="modelCourseAuditRemark">
            <div class="circle" slot="reference">!</div>
          </el-popover>
        </div>
        <div class="cx_apply" @click="applyClick">重新申请</div>
      </div>
      <div v-premission="'courseDemonstrate'" class="inReview" v-else-if="modelCourseAuditStatus == 1">审核中</div>
      <div class="history">
        <el-button plain @click="historyDialog">历史记录</el-button>
      </div>
      <div class="course-manage-content">
        <div class="left-img">
          <img :src="courseInfo.images" alt="课程封面">

          <el-upload v-premission="'courseCover'" class="upload-demo" :action="uploadUrl" multiple
            :on-success="uploadSuccess" :before-upload="beforeAvatarUpload" :show-file-list="false">
            <i class="icon iconfont icon-xiugai edit-btn" style="fontSize:20px"></i>修改封面
          </el-upload>
          <!-- <div class="apply-course" v-if="!modelCourseAuditStatus" @click="applyClick">申请示范</div> -->
          <el-button v-premission="'courseDemonstrate'" plain class="apply-course" v-if="!modelCourseAuditStatus"
            @click="applyClick">申请示范</el-button>
          <el-button class="open-course" plain @click="openClick">开放课程</el-button>
        </div>
        <div class="course-right-content">
          <el-row type="flex" align="middle">
            <el-col :span="3">课程名称:</el-col>
            <el-col :span="21">
              <div class="courseName" v-if="courseNameInputStatus == 'box'" @dblclick="changeCourseNamesa">
                {{ courseInfo?.courseName }}</div>
              <el-input :autofocus="true" v-model.trim="courseObject.courseName" v-else placeholder="请输入课程名称"
                @blur="chanegCourseName"></el-input>
            </el-col>
          </el-row>
          <el-row type="flex" align="middle">
            <el-col :span="3">绑定案例:</el-col>
            <el-col :span="21">
              <div class="cases">{{ courseInfo.cases[0]?.caseName }}</div>
            </el-col>
          </el-row>
          <el-row type="flex">
            <el-col :span="3"></el-col>
            <el-col :span="21">
              <span style="font-size:12px;color:#999">如课程已使用该案列业务场景及资源，则不可解绑，可增加绑定的案例数。</span>
            </el-col>
          </el-row>
          <div class="prompt">支持jpg/png格式建议单张图片不超过2M,课程封面图最佳尺寸:230*300</div>
        </div>
      </div>
    </div>
    <baseDialog :noFooter="true" :showToScreen="false" :visible.sync="dialogVisible" title="历史记录"
      className="history-dialog">
      <el-table :data="HistoryData" max-height="250" border stripe
        :header-cell-style="{ background: '#F2F3F5', color: '#222222' }" style="width: 100%">
        <template slot="empty">
          <zdDefaultPage size="middle" msg="暂无数据" type="noResult"></zdDefaultPage>
        </template>
        <el-table-column label="编辑次数" width="180">
          <template slot-scope="scope">
            <span>{{ HistoryData.length - HistoryData.lastIndexOf(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="applyTime" label="申请时间" width="180">
        </el-table-column>
        <el-table-column prop="status" label="状态">
        </el-table-column>
        <el-table-column prop="shelfTime" label="上架时间">
        </el-table-column>
        <el-table-column prop="address" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="14" @click="viewHistory(scope.row.id)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </baseDialog>
  </div>

</template>

<script>
import { mapGetters } from 'vuex';
import prepareLessons from '../../../views/teaching-page/prepare-lessons-edit.vue'
export default {
  props: {
    getCourseInfo: {
      type: Function,
      default: () => { }
    }
  },
  components: {
    prepareLessons,
    baseDialog: () => import("@/components/base/dialog.vue"),
  },
  data() {
    return {
      applyCourseDialog: false,
      courseNameInputStatus: 'box',
      courseObject: {},
      uploadUrl: window.FILEIP,
      value: "",
      content: '',
      searchform: {
        pageIndex: 1,
        pageSize: 20
      },
      selectList: [],
      modelCourseAuditStatus: "",
      modelCourseAuditRemark: "",
      modelCourseId: '',
      modelCourseOriginalCourseId: '',
      newCourseId: '',
      dialogVisible: false,
      HistoryData: [
        {
          "id": 1746764685623747,
          "applyTime": "2024-09-26 10:28:57",
          "status": "已上架",
          "shelfTime": "2024-09-26"
        },
        {
          "id": 1746764220055980,
          "applyTime": "2024-09-26 10:27:24",
          "status": "已通过",
          "shelfTime": "--"
        }
      ]
    }
  },

  computed: {
    ...mapGetters({
      'courseInfo': ['getCourseInfo'],
    }),
  },

  watch: {
    'courseInfo.id': function (oldVar, newVar) {
      this.CourseFindByID();
    }
  },

  mounted() {
    this.CourseFindByID();
  },
  methods: {
    async CourseFindByID() {
      const { data, code } = await this.$api.CourseFindByID({ ID: this.courseInfo.id });
      if (code == 200) {
        this.modelCourseAuditStatus = data.modelCourseAuditStatus;
        this.modelCourseAuditRemark = data.modelCourseAuditRemark;
        this.modelCourseId = data.modelCourseId;
        this.modelCourseOriginalCourseId = data.modelCourseOriginalCourseId;
      }
    },
    viewHistory(id) {
      this.$router.push({ path: 'courseModal/courseDtails', query: { id, history: true } })
    },
    historyDialog() {
      this.HistoryList()
      this.dialogVisible = true
    },
    async HistoryList() {
      let { code, data } = await this.$api.HistoryList({ courseId: this.$route.query.id })
      if (code == 200) {
        this.HistoryData = data
      }
    },
    async SearchTeachers(val) {
      let param = {
        params: {},
        pageIndex: this.searchform.pageIndex,
        pageSize: this.searchform.pageSize
      };
      if (val) {
        param.params.key = val;
      }
      const res = await this.$api.SearchTeachers(param);
      if (res.code == 200) {
        return res.data;
      }

    },
    obtainName(list, id) {
      let name = "";
      list.forEach(item => {
        if (item.id == id) {
          name = item.name;
        }
      })
      return name
    },
    async applyClick() {
      let query = {//申请示范课程传递id
        id: null,
        courseName: this.$route.query.courseName,
        courseStatus: this.modelCourseAuditStatus ? this.modelCourseAuditStatus : 0,//课程审核转态
        originalCourseId: this.$route.query.id
      }
      // 未审核（未提交）状态
      if (!this.modelCourseAuditStatus) {
        if (!this.modelCourseId) {//暂存
          await this.ModelCopyCourse();
          query.id = this.newCourseId;
        } else {
          query.id = this.modelCourseOriginalCourseId;
        }
      } else if (this.modelCourseAuditStatus == 3 || this.modelCourseAuditStatus == 2) {//重新申请  拒绝
        await this.ModelCopyCourse();
        query.id = this.newCourseId;
        query.courseRecordId = this.modelCourseId;
      }


      //modelCourseId  暂存后的新课程id  自带id
      // modelCourseOriginalCourseId  暂存课程记录id 课程id
      this.$router.push({
        path: "/applyingCourses",
        query
      })
    },
    openClick() {
      let query = {//申请示范课程传递id
        courseName: this.$route.query.courseName,
        originalCourseId: this.$route.query.id
      }
      this.$router.push({
        path: "/openCourseIndex",
        query
      })
    },
    async ModelCopyCourse() {
      let { code, data } = await this.$api.ModelCopyCourse(this.$route.query.id)
      if (code == 200) {
        this.newCourseId = data;
      }
    },
    //修改课程名称
    chanegCourseName() {

      if (!this.courseObject.courseName) {
        this.$message.error('课程名称不可为空')
        this.courseNameInputStatus = 'box'
        return
      }
      if (this.courseObject.courseName.length > 20) {
        this.$message.error('课程名称最大字符长度为20')
        return
      }

      let data = this.courseInfo
      data.courseName = this.courseObject.courseName
      this.EditCourseInfo(data)
    },


    changeCourseNamesa() {
      // this.courseObject.courseName = this.courseInfo?.courseName
      this.courseNameInputStatus = 'input'
    },

    //编辑课程信息
    async EditCourseInfo(data) {
      let res = await this.$api.CourseSave(data)
      if (res.code == 200 && res.data) {
        this.$message.success('修改成功')
        this.getCourseInfo()
      }
      this.courseNameInputStatus = 'box'
      console.log(this.courseInfo);
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      console.log('response', response);
      console.log('file', file);

      if (response.code == 200 && !response.msg) {
        let data = this.courseInfo
        data.images = response.data
        this.EditCourseInfo(data)
      }
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
        this.$message.error("课程封面仅支持 png、jpeg、jpg格式");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
        return false;
      }
    },
  },

}
</script>

<style lang="scss" scoped>
.course-box {
  height: 100%;

  .history-dialog {
    width: 500px;
  }

  .course-manage-box {
    width: 100%;
    height: 100%;
    background: white;
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;

    .modelCourse-staust {
      background: url('../../../assets/course/sf_course_bg.png') no-repeat center/cover;
      width: 109px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      position: absolute;
      top: -10px;
      left: 20px;
      cursor: pointer;

      span {
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
      }

      .icon-bianjis {
        background: url('../../../assets/course/sf_bianji.png') no-repeat center/cover;
        display: inline-block;
        width: 12px;
        height: 12px;
        font-size: 12px;
        margin-left: 9px;
      }
    }

    .not-pass_box {
      display: flex;
      align-items: center;
      position: absolute;
      left: 0;
      top: 20px;

      .not-pass {
        width: 100px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background: radial-gradient(0% 111% at 78% 85%, #F02B32 0%, #F76A62 99%, #F76A62 100%);
        border-radius: 0px 4px 4px 0px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        cursor: pointer;

        .icon-wenhao {
          background: url('../../../assets/course/wenhao.png') no-repeat center/cover;
          display: inline-block;
          width: 13px;
          height: 13px;
          margin-left: 9px;
        }

        .circle {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          border: 1px solid #fff;
          width: 16px;
          height: 16px;
        }
      }

      .cx_apply {
        font-size: 14px;
        color: #666666;
        margin-left: 24px;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .history {
      position: absolute;
      right: 20px;
      top: 20px;
    }

    .inReview {
      width: 100px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      background: radial-gradient(0% 122% at 63% 83%, #EE8A21 0%, #F4A862 100%);
      border-radius: 0px 4px 4px 0px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      position: absolute;
      left: 0;
      top: 20px;
    }

    .course-manage-content {
      width: 800px;
      height: 300px;
      margin: auto;
      display: flex;


      .left-img {
        width: 230px;
        position: relative;
        background: #F8F7FC;
        box-shadow: 1px 1px 6px 0px rgba(0, 0, 0, 0.1);

        .upload-demo {

          ::v-deep .el-upload {
            width: 130px;
            height: 28px;
            // border: 1px solid #2B66FF;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            margin-left: 50%;
            transform: translateX(-65px) translateY(-20px);
            background: #F3F8FF;
            color: #2B66FF;

            &:hover {
              border: 1px solid #2B66FF;

            }
          }
        }

        img {
          width: 100%;
          height: 100%;
        }

        .edit-btn {
          // position: absolute;
          // top: 6px;
          // right: 8px;
          // border-radius: 20px;
          // background: #F6F8FA;
          font-size: 26px;
          color: #2B66FF;
          cursor: pointer;
        }

        .apply-course {
          font-size: 13px;
          position: absolute;
          margin-left: 20%;
          transform: translateX(-50%);
          cursor: pointer;
          top: -50px;
        }

        .open-course {
          font-size: 13px;
          position: absolute;
          margin-left: 65%;
          transform: translateX(-50%);
          cursor: pointer;
          top: -50px;
        }
      }

      .course-right-content {
        flex: 1;
        // background: yellowgreen;
        box-sizing: border-box;
        padding-left: 40px;
        color: #666;
        font-size: 14px;
        position: relative;

        ::v-deep .el-input__inner {
          background: #F6F8FA;
          border-radius: 8px;
          height: 30px;
          border: none;
        }

        ::v-deep .el-row {
          margin-bottom: 20px;
        }

        ::v-deep .el-input__icon {
          height: 40px; // 设置高度，icon元素恢复原来的高度，这时arror才能垂直居中
          position: relative;
          top: -5px; // 元素整体上移，需要自己调整，让箭头也居中
        }

        .prompt {
          position: absolute;
          bottom: 0;
        }

        .courseName,
        .cases {
          background: #F6F8FA;
          height: 36px;
          display: flex;
          align-items: center;
          padding-left: 15px;
          font-size: 14px;
          color: #333333;
          caret-color: transparent;
        }

        .demo-course-class {
          margin-top: 50px;
          display: flex;
          align-items: center;
        }


      }
    }
  }

  .historical-records {}
}
</style>