<template>
  <div class="boxoo">
    <el-table
        :data="classLists"
        :border="true"
        stripe
        max-height="564px"
        tooltip-effect="dark"
        ref="multipleTable"
        style="width: 100%"
        :cell-style="{'text-align':'center',color:'#222'}"
        :header-cell-style="{'text-align':'center',background:'#F2F3F5',color:'#222'}"
        >
        <el-table-column
          label="班级名称"
          width="180">
          <template slot-scope="scope">
            <!-- <input  v-if="rename == scope.row.id" type="text"> -->
            <el-input style="width:100%;height:100%;color:#2B66FF" v-model.trim="renameValue" autofocus v-if="rename == scope.row.id" @blur="carriageReturn(scope.row.id)"></el-input>
            <span v-else class="className" @click="switchClass(scope.row.name)">{{scope.row.name}}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="studentNo"
          label="学生人数">
        </el-table-column>
        <el-table-column
          prop="isDuring"
          label="运行状态">
          <template slot-scope="scope">
            <el-switch
              @change="switchClassStatus(scope.row.id)"
              v-model="scope.row.isDuring"
              :active-value="0"
              :inactive-value="1"
              active-text="授课中"
              inactive-text="已结课">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间">
        </el-table-column>

        <el-table-column
          prop="address"
          width="200"
          label="操作">
          <template slot-scope="scope">
            <div class="operate">
              <span class="hove" @click="classRename(scope.row.id,scope.row)">重命名</span>
              <!-- <span class="hove" @click="classSetUp('classSetUp')">设置</span> -->
              <!-- <span class="hove">邀请入班</span> -->
              <span v-premission="'deleteClass'" class="del" @click="deleteClass(scope.row.id)">删除</span>
            </div>
          </template>
        </el-table-column>
    </el-table>
  </div>

</template>

<script>
export default {
  props:{
    classLists: {
      type: Array,
      default: () => { }
    },
    // GetClassListBy: {
    //   type: Function,
    //   default: () => { }
    // },
    classSetUp:{
      type:Function,
      default: () => { }
    },
    createClass:{
      type:Function,
      default:()=>{}
    },
    switchClass:{
      type:Function,
      default:()=>{}
    }
  },
  data() {
    return {
      rename:'',
      renameValue:'',
      renameClassInfo:{}
    }
  },
  methods:{
    rowClass({ row, rowIndex}) {
      return 'background:#F9F9F9'
    },
    async switchClassStatus(id){
      let res = await this.$api.ClassSetIsDuring({id})
    },
    // //新增班级/修改班级信息
    // async createClass(classId,name){
    //   let data = {
    //     name: name?name:"新添加班级",
    //     id:classId,
    //     courseId:this.$route.query.id
    //   }

    //   let res = await this.$api.AddClass(data)
    //   console.log(res,'修改');
    //   if(res.code === 200){
    //     this.GetClassListBy()
    //   }else{
    //     console.log('失败');
    //   }
    // },
    // //获取班级列表
    // async GetClassListBy(){
    //   let queryData = {
    //     params: {
    //       courseId:this.$route.query.id
    //     },
    //   }
    //   let res = await this.$api.GetClassList(queryData)
    //   if(res.code === 200){
    //     console.log('所有班级',res.data);
    //     this.classList = res.data
    //   }else{
    //     console.log('失败00');
    //   }
    // },

    //删除班级
    async deleteClass(data){
      this.$confirm('此操作将永久删除该班级, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delClass',data)
      }).catch(() => {});
    },
    //班级重命名触发
    classRename(data,className){
      this.rename = data
      this.renameValue = className.name
      this.renameClassInfo = className
    },
    //班级重命名完成
    carriageReturn(id){
      if (!this.renameValue) {
        this.$message.error('班级名称不能为空')
        return
      }
      this.rename = ''
      this.renameClassInfo.name = this.renameValue
      this.createClass(id,this.renameClassInfo)

    }

  },
  created(){

  },
  mounted(){

  }

}
</script>

<style lang="scss" scoped>
  ::v-deep .el-table{
    border: 1px solid #E7E7E7;
    // max-height: 665px;
    .el-table__cell{
      border: none;
    }
  }
  .boxoo{
    .operate{
      width: 100%;
      display: flex;
      justify-content: space-evenly;

      span{
        cursor: pointer;
        color: #787D83 ;
        font-size: 14px;
      }
      .del{
        color: #FF0000;
        &:hover{
          text-decoration-line: underline;
        }
      }
      .hove{
        color: #2B66FF;
        &:hover{
          text-decoration-line: underline;
        }
      }
    }
    .className{
      cursor: pointer;
      &:hover{
        color: #2B66FF;
      }
    }
  }


  // ::v-deep .el-table__cell{
  //   border: none;
  // }



</style>
