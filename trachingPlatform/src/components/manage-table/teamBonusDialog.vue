<template>
  <el-dialog class="team-score" title="得分详情" :visible.sync="dialogVisible" width="750px" @close="handleClose">

    <div class="team-score-content">
      <div class="searchBoxs">
        <el-input v-model="searchData.params.userName" placeholder="学生姓名" clearable @clear="init" @keyup.enter.native="init"></el-input>
        <el-select v-model="searchData.params.teamId" placeholder="小组名称" clearable @clear="init" @change="init">
          <el-option v-for="item in groupsList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="searchData.params.relatedType" placeholder="得分类型" clearable @clear="init" @change="init">
          <el-option v-for="item in scoreTypeList" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <el-row>
          <el-button type="primary" @click="search">筛选</el-button>
          <el-button type="button" @click="Reset">重置</el-button>
        </el-row>
      </div>

      <el-table ref="multipleTable" :data="bonusDetailsList" height="352px" style="width: 100%">
        <el-table-column label="姓名" prop="userName">
        </el-table-column>
        <!-- <el-table-column
          prop="teamId"
          label="小组ID">
        </el-table-column> -->
        <el-table-column prop="teamName" label="小组名称">
        </el-table-column>
        <el-table-column prop="score" label="得分">
        </el-table-column>
        <el-table-column prop="relatedType" label="得分类型">
          <template slot-scope="scope">{{ scope.row.relatedType | relatedType }}</template>
        </el-table-column>
        <el-table-column prop="createTime" label="时间" width="180">
        </el-table-column>
      </el-table>

      <el-pagination
        background
        layout="prev, pager, next"
        @current-change="handleCurrentChange"
        :hide-on-single-page="true"
        page-size="7"
        :total="recordCount">
      </el-pagination>

    </div>
  </el-dialog>


</template>

<script>
export default {
  props: {
    showFlag: {
      type: Boolean,
      default: false
    },
    groupingList: {
      type: Array,
      default: []
    },
    currentGroupId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      dialogVisible: false,
      searchData: {
        params: {
          target: 1,
          userName: '',
          relatedType: '',
          teamId: '',
          courseId:this.$route.query.id
        },
        pageIndex: 1,
        pageSize: 7
      },
      bonusDetailsList: [],
      scoreTypeList: [
        { label: '随机点名加分', value: 1 },
        { label: '讨论加分', value: 2 },
        { label: '直接点名加分', value: 3 },
      ],
      groupsList: [],
      recordCount:0
    }
  },
  watch: {
    showFlag: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          if(this.currentGroupId){
            this.searchData.params.teamId = this.currentGroupId
          }
          this.init()
          this.groupListFilter(this.groupingList)
        }
      }
    }
  },
  filters: {
    relatedType(val) {
      if (val == 1) {
        return '随机点名加分'
      } else if (val == 2) {
        return '讨论加分'
      } else if (val == 3) {
        return '直接点名加分'
      }
    }
  },
  methods: {
    search(){
      this.searchData.pageIndex = 1
      this.init()
    },
    handleClose() {
      this.$emit('closeChildDialog')
      this.searchData = {
        params: {
          target: 1,
          userName: '',
          relatedType: '',
          teamId: '',
          courseId:this.$route.query.id
        },
        pageIndex: 1,
        pageSize: 7
      }
    },
    handleCurrentChange(val){
      this.searchData.pageIndex = val
      this.init()
    },
    Reset() {
      this.searchData =
      {
        params: {
          target: 1,
          userName: '',
          relatedType: '',
          teamId: '',
          courseId:this.$route.query.id
        },
        pageIndex: 1,
        pageSize: 7
      },
        this.init()
    },
    async init() {
      let data = JSON.parse(JSON.stringify(this.searchData))
      // console.log(Boolean(this.searchData.params.teamId),'000000000');
      if(!this.searchData.params.teamId){
        let arr = this.groupsList.map(e=>{
          return e.value
        })
        data.params.teamId = arr
        // console.log(data,this.searchData,'dsfsdfsd');
      }
      let res = await this.$api.TeachCourseBonusPointSearch(data)
      if (res.code == 200) {
        this.bonusDetailsList = res.data
        this.recordCount = res.recordCount
      }
    },
    groupListFilter(val) {
      this.groupsList = val?.map(e => {
        if (e.id != 0) {
          return { label: e.teamName, value: e.id }
        } else {
          return []
        }
      }).flat()
    }
  }


}
</script>

<style lang="scss" scoped>
.team-score {
  ::v-deep .el-dialog {
    height: 560px;

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
  }

  .team-score-content {
    width: 90%;
    .el-pagination{
      margin: 20px 0;
      text-align: center;
    }
    .searchBoxs {
      display: flex;
      margin: 20px 0;
      justify-content: space-between;

      .el-input {
        width: 160px;
      }

      .el-select {
        width: 150px;
      }
    }
  }
}
</style>