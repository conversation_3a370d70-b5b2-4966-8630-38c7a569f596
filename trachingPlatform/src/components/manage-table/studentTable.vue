<template>
  <div class="studentbox">
    <el-table ref="multipleTable" :data="sdutentList" stripe :border="true" max-height="510px" tooltip-effect="dark"
      style="width: 100%" :cell-style="{ 'text-align': 'center' }" @sort-change="sortChange"
      :header-cell-style="{ 'text-align': 'center', background: '#F9F9F9' }" @selection-change="handleSelectionChange">
      <template slot="empty">
        <el-empty description="暂无数据"></el-empty>
      </template>
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column label="姓名">
        <template slot-scope="scope"><span>{{ scope.row.userName }}</span></template>
      </el-table-column>
      <el-table-column prop="studentNo" label="学号/工号" sortable="custom">
      </el-table-column>
      <el-table-column prop="mobilePhone" label="联系方式" show-overflow-tooltip>
      </el-table-column>

      <!-- <el-table-column prop="tenantId" label="学校">
      </el-table-column>
      <el-table-column prop="name" label="院系">
      </el-table-column>
      <el-table-column prop="name" label="专业">
      </el-table-column> -->

      <el-table-column prop="className" label="班级">
      </el-table-column>
      <el-table-column prop="createTime" label="加入时间" sortable="custom">
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <!-- <el-dropdown @command="studentSetUp" trigger="click" placement="right-start">
            <span class="el-dropdown-link">
              <i class="el-icon-more" style="font-size:17px;color:#D9D9D9;transform: rotate(90deg);"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="commandData('resetting', scope.row)">重置密码</el-dropdown-item>
              <el-dropdown-item :command="commandData('del', scope.row)">移除</el-dropdown-item>
              <el-dropdown-item :command="commandData('ShiftAdjustment', scope.row)">调班</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-button type="text" v-premission="'editStudent'" @click="studentSetUp('modify',scope.row)">修改</el-button>
          <el-button type="text" v-premission="'resetPasswordStudent'" @click="studentSetUp('resetting',scope.row)">重置密码</el-button>
          <el-button type="text" v-premission="'deleteStudent'" @click="studentSetUp('del',scope.row)">移除</el-button>
          <el-button type="text" v-premission="'transferStudent'" @click="studentSetUp('ShiftAdjustment',scope.row)">调班</el-button>

          <!-- <el-button type="text" v-premission="'transferStudent'" @click="changeRole(scope.row)">角色切换</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" :visible.sync="dialogTableVisible" v-if="dialogTableVisible" :class="{'modifyDialog':status == 'modify','resettingDialog':status == 'resetting','delDialog':status == 'del'}">
      <div v-if="status == 'del'" style="width: 100%;">
          <div class="del" v-if="status == 'del'">
            <img src="@/assets/teaching/remove-student.png" alt="">
            <span style="margin: 10px 0;color: #2b66ff;">{{this.currentStudent.userName}}</span>
            <div class="text">移除学生后，不再接收班级相关的信息。确认移除？</div>
          </div>

          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogTableVisible = false">取 消</el-button>
            <el-button type="primary" @click="submit">确 定</el-button>
          </div>
        </div>

      <div class="resetting" v-else-if="status == 'resetting'">
        <el-form :model="resetting" ref="resetting" label-width="96px" :rules="rules">
          <el-form-item label="修改密码" prop="password">
            <el-input v-model.trim="resetting.password" autocomplete="off" show-password></el-input>
          </el-form-item>

          <el-form-item label="确认修改密码" prop="newPassword">
            <el-input v-model.trim="resetting.newPassword" autocomplete="off" show-password></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogTableVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit('resetting')">确 定</el-button>
        </div>
      </div>

      <div class="shiftAdjustment" v-else-if="status == 'ShiftAdjustment'">
        <div class="content">
          <div class="item">

            <div class="top">
              <div class="title">姓名</div>
              <div class="title">班级</div>
            </div>
            <div class="bottom">
              <div class="content">{{ userName }}</div>
              <div class="content">{{ currentClass.name }}</div>
            </div>

          </div>
          <span>调班至</span>
          <div class="item">
            <div class="top">
              <div class="title">姓名</div>
              <div class="title">班级</div>
            </div>
            <div class="bottom">
              <div class="content">{{ userName }}</div>
              <div class="content">
                <el-select v-model="value" placeholder="请选择">
                  <el-option v-for="item in classList" :key="item.value" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </div>
            </div>

          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogTableVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </div>

      <div class="modify" v-else-if="status == 'modify'">
        <el-form label-position="right" label-width="72px" :model="modifyStudentInfo">
          <el-form-item label="姓名">
            <el-input v-model.trim="modifyStudentInfo.userName" maxlength="20" onKeyUp="value=value.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g,'')"></el-input>
          </el-form-item>
          <el-form-item label="学号/工号">
            <el-input v-model.trim="modifyStudentInfo.studentNo" maxlength="20" onKeyUp="value=value.replace(/[\W]/g,'')"></el-input>
          </el-form-item>
          <el-form-item label="联系方式">
            <el-input v-model.trim="modifyStudentInfo.mobilePhone" maxlength="11" onKeyUp="value=value.replace(/[^\d.]/g,'')"></el-input>
          </el-form-item>
          <el-form-item label="班级">
            <el-input v-model="modifyStudentInfo.className" disabled></el-input>
          </el-form-item>
          <el-form-item label="加入时间">
            <el-input v-model="modifyStudentInfo.createTime" disabled></el-input>
          </el-form-item>
          <div class="submit">
            <el-button @click="dialogTableVisible = false">取消</el-button>
            <el-button type="primary" @click="editStudentInfo">确认</el-button>
          </div>
        </el-form>

      </div>

    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from "vuex"
import doTokenSuccess from "@/utils/doTokenSuccess";
export default {
  props: {
    sdutentList: {
      type: Array,
      default: () => { }
    },
    QueryClassStudent: {
      type: Function,
      default: () => { }
    },
    GetAllstudentList: {
      type: Function,
      default: () => { }
    },
    AddEditingStudents: {
      type: Function,
      default: () => { }
    },
    classId: {
      type: Number,
      default: () => { }
    },
  },
  data() {
    let validatePass = (rule, value, callback) => {
      if (this.resetting.newPassword === '') {
        callback(new Error('请再次输入密码'));
      } else if (this.resetting.newPassword != this.resetting.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    }
    let validatePass2 = (rule, value, callback) => {
      console.log(value.length);
      let regLet = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,12}$/
      if (!value) {
        callback(new Error('请输入密码'));
      } else if (value.length < 6 || value.length > 12 || !regLet.test(value)) {
        callback(new Error('密码格式为6~12位的数字和字母'));
      } else {
        callback();
      }
    };
    return {
      multipleSelection: [],
      dialogTableVisible: false,
      title: '',
      status: '',
      stutentId: '',
      resetting: {
        password: '',
        newPassword: ''
      },
      rules: {
        newPassword: [
          { validator: validatePass, trigger: 'blur' },
        ],
        password: [
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      value: '',
      userName: '',
      currentClass: {},
      classList: [],
      currentStudent: {},
      modifyStudentInfo:{}
    }
  },
  watch:{
    dialogTableVisible:{
      handler(val){
        if(!val){
          if(this.status == 'resetting'){
            this.resetting.password = ''
            this.resetting.newPassword = ''
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"]
    })
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
      // console.log('11111',this.multipleSelection);
    },
    async BatchDeletion(){
      if(this.multipleSelection.length == 0){
        this.$message('请选择要删除的数据!')
        return
      }
      let data = this.multipleSelection.map(val => val.id)
      console.log(data);
      let res = await this.$api.DeleteByStudentIds(data)
      if(res.code == 200){
        this.QueryClassStudent(this.classId)
        this.$message.success('批量删除成功')
      }
    },
    sortChange(val){
      this.$emit('sortChange',val)
    },
    //学生操作
    studentSetUp(status,command) {
      console.log('班级设置参数', command);
      this.dialogTableVisible = true;
      this.currentStudent = command
      this.status = status;
      this.currentStudent = command
      if (status == 'del') {
        this.stutentId = command.id
        this.title = '移除'
      } else if (status == 'resetting') {
        this.stutentId = command.userId
        this.title = '重置密码'
      } else if (status == 'ShiftAdjustment') {
        this.title = '调班'
        this.value = ''
        this.stutentId = command.id
        this.FindClassInfo(command.classId)
        this.userName = command.userName
        // this.currentStudent = command
      }else if (status == 'modify') {
        this.title = '修改'
        this.modifyStudentInfo = command
      }

    },
    // 教师切换到学生
    async changeRole(row){
      // 通过学生id 拿到token 并记录教师的用户id 用于切换回教师角色
      sessionStorage.setItem('changeUserType',this.userInfo.id)
      let { data } = await this.$api.GetToken({id:row.userId,loginType:0})
      if(!data.accessToken) return this.$message.warning('该学生未近期未登录,无法切换！')
      // 模拟学生登录
      await doTokenSuccess(data.accessToken, this.router);
      this.$router.replace({
        path:'/studentHome',
      })
      
    },
    async editStudentInfo(){
      // console.log(this.modifyStudentInfo);
      let reg = /^1[3456789]\d{9}$/;
      if(!reg.test(this.modifyStudentInfo.mobilePhone)){
          this.$message('手机号格式错误!')
          return
        }
      let querydata = {
        id: this.modifyStudentInfo.userId,
        userName: this.modifyStudentInfo.account,
        realName: this.modifyStudentInfo.userName,
        nikeName: this.modifyStudentInfo.nikeName,
        studentNo: this.modifyStudentInfo.studentNo,
        mobilePhone: this.modifyStudentInfo.mobilePhone,
        sex: this.modifyStudentInfo.sex,
        classId: this.classId
      }
      let {data,code} = await this.$api.editStudent(querydata)
      console.log(data,code);
      if(code == 200 && data == true){
        this.$message.success('修改成功')
        this.dialogTableVisible = false
      }
      this.QueryClassStudent()
    },
    //删除单个学生
    async removeStudent() {
      let data = {
        id: this.stutentId
      }
      let res = await this.$api.DeleteStudnet(data)
      console.log(res);
      if (res.code === 200) {
        // console.log('userid', this.classId);

        this.classId ? this.QueryClassStudent(this.classId) : this.GetAllstudentList()

        this.$message({
          message: '移除成功',
          type: 'success'
        });
      } else {
        this.$message.error('移除失败');
      }
    },
    //修改学生密码
    async ChangePassword() {
      let data = {
        userId: this.stutentId,
        newPassword: this.resetting.newPassword
      }
      let res = await this.$api.DesetStudentPassword(data)
      console.log(res);
      if (res.code === 200) {
        if (res.code === 200) {
          this.QueryClassStudent()
          this.$message({
            message: '修改成功',
            type: 'success'
          });
        } else {
          this.$message.error('修改失败');
        }
      }
    },
    //查找班级信息
    async FindClassInfo(classId) {
      let data = {
        id: classId
      }
      let res = await this.$api.FindIndividualData(data)
      if (res.code === 200) {
        this.currentClass = res.data
        this.GetClassLists()
      }
    },
    //获取班级列表
    async GetClassLists() {
      let data = {
        params: {
          courseId: this.$route.query.id
        }
      }
      let res = await this.$api.GetClassList(data)
      if (res.code === 200) {
        this.classList = res.data.filter(e => e.id !== this.currentClass.id)
      }

    },
    //确认
    submit(resetting) {

      if (this.status == 'del') {
        this.removeStudent()
        this.dialogTableVisible = false
      } else if (this.status == 'resetting') {

        this.$refs[resetting].validate((valid) => {
          if (valid) {

            this.ChangePassword()
            this.dialogTableVisible = false
          } else {
            return false;
          }
        });

      } else if (this.status == 'ShiftAdjustment') {
        this.currentStudent.classId = this.value
        if (!this.value) {
          this.$message('请选择班级!')
          return
        }
        this.AddEditingStudents(this.currentStudent)
        this.dialogTableVisible = false
      }

    },
    commandData(sta, row) {
      return {
        status: sta,
        id: row.id,
        classId: row.classId,
        userId: row.userId,
        userName: row.userName,
        row: row
      }
    },

  },
  created() {

  },


}
</script>

<style lang='scss' scoped>
::v-deep .el-table {
  border: 1px solid #E7E7E7;
  font-size: 14px;
  color: #222222;

  .el-table__cell {
    border: none;
  }
}
.studentbox{
  ::v-deep .el-dialog{
    border-radius: 10px;
    .el-dialog__header{
      // background: #F7F7F7;
      border-radius: 10px 10px 0 0;
    }
    .el-dialog__body {
      box-sizing: border-box;
      padding: 0;
      display: flex;
      justify-content: center;
      padding: 0 20px;
    }
    .submit{
      .el-button{
        width:100px;
        height: 40px;
      }
    }
  }
}

.del {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .cion {
    width: 85px;
    height: 85px;
    background: #ca6666;
    margin-bottom: 18px
  }

  .text {
    width: 252px;
    font-size: 14px;
    color: #333333;
    text-align: center;
  }
}
.modifyDialog{
  ::v-deep .el-dialog{
    width: 500px !important;
    // height: 500px !important;
    .el-dialog__header{
      padding:20px 28px;
      .el-dialog__headerbtn{
        right: 28px;
      }
    }
    .el-dialog__body{
      padding: 0 28px;
    }
  }
}
.resettingDialog{
  ::v-deep .el-dialog{
    width: 500px !important;
  }
}
.delDialog{
  ::v-deep .el-dialog{
    width: 500px !important;
  }
}
.resetting {
  width:100%;
  ::v-deep .el-form {
    font-size: 14px;
    // margin-top: 55px;

    .el-input__inner {
      // width: 272px;
      height: 34px;
    }
  }
  .dialog-footer {
    margin: 40px 0 20px 0;
    text-align: right;
  }
}

.shiftAdjustment {
  >.content {
    display: flex;
    align-items: center;

    >span {
      margin: 0 34px;
      color: #333333;
      font-size: 16px;
    }

    .item {
      width: 286px;
      height: 120px;
      border: 1px solid #E7E7E7;
      display: flex;
      flex-direction: column;

      >.top {
        width: 100%;
        height: 43px;
        display: flex;
        background: #F9F9F9;

        .title {
          font-size: 14px;
          color: #222222;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          &:nth-child(1) {
            width: 30%;
          }

          &:nth-child(2) {
            width: 70%;

          }
        }
      }

      .bottom {
        flex: 1;
        color: #222222;
        font-size: 14px;
      }


      .bottom {
        width: 100%;
        display: flex;

        .content {
          &:nth-child(1) {
            width: 30%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          &:nth-child(2) {
            width: 70%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          ::v-deep .el-input__inner {
            width: 180px;
            height: 34px;

          }

          ::v-deep .el-input__icon {
            height: 40px;
            position: relative;
            top: -2px;
          }
        }
      }
    }
  }
}

.modify{
  // margin-top: 20px;
  width: 100%;
  ::v-deep .el-input__inner{
    // width: 400px;
  }
  .submit{
    text-align: right;
    margin: 40px 0 20px 0;
  }
}
.dialog-footer {
  width: 100%;
  text-align: right;
  margin: 40px 0 20px 0;
}

// ::v-deep {
//   .el-dialog__wrapper {
//     .el-dialog {
//       width: 800px;
//       height: 366px;
//     }
//   }

// }
</style>
