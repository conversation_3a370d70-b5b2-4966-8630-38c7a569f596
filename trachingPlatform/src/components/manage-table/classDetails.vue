
<template>
  <div class="classDelaitsbox">
    <!-- <span class="class-name">{{ className }}</span> -->
    <!-- <i class="icon iconfont icon-erweima"></i> -->
    <div class="classmain">
      <div class="classmain-l">
        <span class="class-name">{{ className }}</span>
      </div>
    </div>

    <div class="classDelaitstop">
      <div class="classDelflex">
      <div class="groupingTabs">
        <span :class="{ groupActive: status == 'student' }" @click="switchTab('student')">学生</span>
        <span :class="{ groupActive: status == 'grouping' }" @click="switchTab('grouping')">分组</span>
      </div>

      <div v-if="status == 'student'" class="classDelaits-search">
        <el-input v-model="searchValue" placeholder="姓名、学号/工号、联系方式" size="mini" :clearable="true" style="width: 220px"
          @clear="search1">
          <i slot="suffix" style="cursor: pointer" class="el-input__icon el-icon-search" @click="search1"></i>
        </el-input>
        <span class="cdecounts">
          共<span style="color: #2b66ff"> {{ sdutentList.length }} </span>名学生
        </span>
      </div>
      <!-- <div v-else style="font-size: 14px">
        分组 (<span style="color: #2b66fa">{{ this.groupList.length }}</span>)
      </div> -->
    </div>
      <div v-if="status == 'student'">
        <div class="btnlistflex">
          <div class="classmain-r" @click="opencode">
             <i class="icon iconfont icon-erweima iconfize"></i>
             <span class="inviteclass">邀请进班</span>
           </div>
          <el-button size="small" v-premission="'deleteAllStudent'" @click="delsDialogVisible = true">批量删除</el-button>
          <el-button size="small" v-premission="'addStudent'" :autofocus="true" @click="addStudents">添加学生</el-button>
          <el-button size="small" v-premission="'excelImportStudent'" @click="BatchImport">EXCEL批量导入</el-button>
          <el-button size="small" v-premission="'importStudent'" @click="exportStudentDialog">导出学生</el-button>
          </div>
      </div>

      <div v-else>
        <el-row>
          <a href="/template/班级分组导入模版.xlsx" download
            style="display: inline-block; height: 100%; margin-right: 10px"><el-button size="small">模板下载</el-button></a>
          <el-upload class="grouping-uploader" :on-success="GroupUploadSuccess" :action="groupUrl" accept=".xls, .xlsx"
            ref="uploads" :data="groupData" :headers="headers" :limit="1" :show-file-list="false">
            <el-button size="small">导入</el-button>
          </el-upload>
          <el-button size="small" @click="seUpGrouping">设置分组</el-button>
          <el-button size="small" @click="deleteAllGrouping">删除分组</el-button>
        </el-row>
      </div>
    </div>
    <div class="class-delaits-content">
      <StudentTableVue v-if="status == 'student'" ref="StudentTableVue" @sortChange="sortChange" :AddEditingStudents="AddEditingStudents" :classId="classId"
        :QueryClassStudent="QueryClassStudent" :sdutentList="sdutentList"></StudentTableVue>
      <div v-else style="height: 100%" class="SortableBox">
        <div v-for="(item) in groupList" :key="item.id" class="grouping-item">
          <div class="groupTop">
            <div class="grouping-item-top" v-if="item.id != 0">
              <el-input v-if="groupNameStatus == item.id" maxlength="20" v-model.trim="currentGroup.teamName"
                @blur="ModifyGroupName"></el-input>
              <span v-else>{{ item.teamName }} (<span style="color: #2b66fa">{{
          item.students.length
        }}</span>)
              </span>
              <div>
                <i class="el-icon-edit" @click="groupRename(item)"></i>
                <i class="el-icon-delete" @click="deleteOneGroup(item)"></i>
              </div>
            </div>
            <div class="grouping-item-top" v-if="item.id == 0">
              <span>{{ item.teamName }} (<span style="color: #2b66fa">{{
          item.students.length
        }}</span>)
              </span>
            </div>
            <span v-if="item.id != 0">团队得分: {{ item.teamBonusPoint }}</span>
            <el-button class="dtailsBtn" v-if="item.id != 0" type="primary"
              @click="viewGroupScores(item.id)">查看得分详情</el-button>
          </div>
          <div class="grouping-item-content">
            <draggable v-model="item.students" forceFallback="true" group="people" animation="500" @start="onStart"
              @add="onAdd">
              <transition-group>
                <div v-for="e of item.students" :key="e.userId" class="student-item" draggable="true">
                  <div class="student-info">
                    <span>{{ e.realName }}</span>
                    <span>{{ e.studentNo }}</span>
                  </div>
                  <div class="student-image">
                    <img v-if="e.photo" :src="e.photo" alt="" />
                    <section v-else>
                      <i class="iconfont icon-shuyi_xuesheng"></i>
                    </section>
                    <div v-if="e.isLeader">组长</div>
                  </div>
                  <el-dropdown @command="GroupHandleCommand" @visible-change="visibleChange($event, e)">
                    <i class="el-icon-arrow-down el-icon-more" style="
                        transform: rotate(90deg);
                        color: #bcbcbc;
                        font-size: 19px;
                      "></i>
                    <el-dropdown-menu slot="dropdown" style="position: relative" class="grouping-el-dropdown-menu">
                      <el-dropdown-item :command="{ key: 'teamLeader', val: e }"
                        v-if="item.id != 0">设为组长</el-dropdown-item>
                      <div class="mobiletab">
                        移动至
                        <i class="el-icon-arrow-right" style="position: absolute; right: 5px"></i>
                        <div class="mobile-grouping">
                          <div v-for="group in groupNameList" :key="group.id" @click="EditGroupingPersonnel(e, group)">
                            {{ group.teamName }}
                          </div>
                        </div>
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
        <div class="noData" v-if="groupList.length == 0">
          <img src="@/assets/images/no-data.png" alt="" />
          <span>暂无数据</span>
        </div>
      </div>
    </div>
    <!-- 添加学生弹窗 -->
    <el-dialog title="添加学生" :visible.sync="dialogVisible" class="addstudentDialog">
      <AddStudent v-if="dialogVisible" @selectStutent="getAddStudents" :close="addDialogClose" :AddEditingStudentss="AddEditingStudentss" :classId="classId" @single="addStatus"  @classStudentArr="getClassAllStudent"
        @selectClassStudent="getclasslStudent"></AddStudent>

      <span slot="footer" class="dialog-footer" v-if="!isSingle">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addStudentSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 批量导入 -->
    <el-dialog title="批量导入" :visible.sync="dialogVisibledao" class="batchImport">
      <div class="contents">
        <el-upload class="avatar-uploader" :on-success="uploadSuccess" :on-progress="upLoadProgress" :action="actionUrl" accept=".xls, .xlsx"
          ref="GroupUpload" :data="paramsData" :headers="headers" :limit="1" :on-error="upLoadError"
          :show-file-list="false">
          <i class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <div class="info">
          <a href="/template/学生导入模板.xls" download>模板下载</a>
          <span class="format">支持上传xlsx、xls格式</span>
          <span>{{ file.name }}</span>
          <el-progress v-if="progressFlag" style="width: 250px;margin-top: 20px;" :text-inside="true" :stroke-width="14" :percentage="loafProgress"></el-progress>
        </div>
      </div>
      <div class="import-error">
        <div v-for="item in importError" :key="item.line">
          <span>错误信息:第{{ item.line }}行</span>
          <span>{{ item.msg }}</span>
        </div>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibledao = false">取 消</el-button>
        <el-button type="primary" @click="BatchImportStudent">确 定</el-button>
      </span> -->
    </el-dialog>
    <!-- 分组dialog -->
    <el-dialog class="groupingDialog" :title="groupTitle" :visible.sync="groupDialogVisible" @close="groupDialogClose">
      <div v-if="groupDialog == 'grouping'" class="groupCount">
        <el-input-number v-model="groupNum" controls-position="right" placeholder="限数字整数" :min="1" :max="10" :step="1"
          :step-strictly="true"></el-input-number>
        <span>分组后，系统自动分配成员，支持移动成员</span>
        <el-checkbox v-model="reorganizeChecked">重新分组</el-checkbox>
      </div>
      <div v-else-if="groupDialog == 'DeleteOne'" class="DeleteOneGroup">
        <img src="@/assets/teaching/folder-delete.png" alt="" />
        <span>确定删除该分组？</span>
        <span>删除后组员将被解散</span>
      </div>
      <div v-else-if="groupDialog == 'DeleteAll'" class="DeleteOneGroup">
        <img src="@/assets/teaching/folder-delete.png" alt="" />
        <span>该操作将删除全部分组</span>
        <span>删除后数据无法恢复,确认删除?</span>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="groupDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmGrouping">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 批量删除 -->
    <el-dialog title="批量删除" :visible.sync="delsDialogVisible" class="delsDialog">
      <div class="dels">
        <img src="@/assets/teaching/remove-student.png" alt="">
        <span style="margin: 10px 0;color: #2b66ff;"></span>
        <div class="text">移除学生后，不再接收班级相关的信息。确认移除？</div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="delsDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteStudents">确 定</el-button>
        </span>
      </div>
    </el-dialog>
    <!-- 导出学生 -->
    <el-dialog title="导出学生" :visible.sync="exportDialogVisible" class="exportDialogs">
      <div class="export">
        <div class="text">确认导出{{className}}班的学生信息？</div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="exportStudent">确 定</el-button>
        </div>
      </div>
    </el-dialog>

    <transition name="el-fade-in-linear">
      <div class="task-publish-dialog" v-show="dialogcodeVisible">
        <div class="task-publish">
          <span class="close" @click="close"><i class="el-icon-close icons"></i></span>
          <div class="qrcode">
            <div class="qrcodecon" ref="qrCodeDivc">
              <div class="codecontainer">
                <div class="codecontainerflex">
                  <div class="qrCodes-l">
                    <img :src="this.courseInfo?.images" v-if="this.courseInfo?.images" />
                    <img src="@/assets/course/cover.png" v-else />
                  </div>
                  <div class="qrCodes-r">
                    <div class="coursetitle">
                      {{ this.courseInfo?.courseName }}
                    </div>
                    <div class="courseclass">班级:{{ classMessage.name }}</div>
                    <div class="coursecode">
                      <div class="coursecodel">
                        班级码:{{ classMessage.verifycode }}
                      </div>
                      <div class="coursecoder" v-clipboard:copy="classMessage.verifycode" v-clipboard:success="onCopy"
                        v-clipboard:error="onError">
                        复制
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div id="qrcode" class="code" ref="qrCodeDiv"></div>
            </div>
          </div>
          <div class="btnflex">
            <div class="btncontianer">
              <div class="btnl" @click="downloadcode">
                保存图片
              </div>
              <el-tooltip class="scan-tooltip item" popper-class="arrowItem" effect="dark" content="点击复制链接，发送给学生"
                placement="top-start">
                <div class="btnr" v-clipboard:copy="httpurl" v-clipboard:error="onError" v-clipboard:success="onCopylink">
                  邀请加入
                </div>
              </el-tooltip>
            </div>
          </div>
          <!-- <div class="qr_foot" ><span>下载二维码</span></div> -->
        </div>
      </div>
    </transition>

    <teamBonusDialog :showFlag="showFlag" :currentGroupId="currentGroupId" :groupingList="groupList"
      @closeChildDialog="closeChildDialog"></teamBonusDialog>
  </div>
</template>

<script>
import html2canvas from "html2canvas";
import QRCode from "qrcodejs2";
import AddStudent from "@/views/teaching-page/dialog/addStudent.vue";
import teamBonusDialog from './teamBonusDialog.vue'
import StudentTableVue from "./studentTable.vue";
import { mapGetters } from "vuex";
import * as XLSX from "xlsx";
import { getToken } from "@/utils/token.js";
import draggable from "vuedraggable";

export default {
  components: {
    StudentTableVue,
    AddStudent,
    draggable,
    teamBonusDialog
  },
  props: {
    // addStudents:{
    //   type:Function,
    //   default:()=>{}
    // },
    classId: {
      type: Number,
      default: () => { }
    },
    className: {
      type: String,
      default: () => { }
    },
    classObj: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      httpurl: "",
      classcode: "jk889",
      status: "student",
      sdutentList: [],
      searchValue: "",
      dialogcodeVisible: false,
      dialogVisible: false,
      dialogVisibledao: false,
      delsDialogVisible:false,
      AddStudents: "",
      fileData: "",
      file: "",
      actionUrl: `${window.PUBLICHOSTA}/jxz/ClassStudents/ImportClassStudents`,
      groupUrl: `${window.PUBLICHOSTA}/jxz/Team/ImportTeamStudents`,
      addClassStudentList: [],
      selectStudentList: [],
      headers: {
        token: getToken()
      },
      paramsData: {
        courseId: this.$route.query.id,
        classId: this.classId
      }, // 上传额外参数
      fileList: [],
      classMessage: {},
      isshow: true,
      importError: [],
      groupTitle: "设置分组",
      groupDialog: "grouping",
      groupDialogVisible: false,
      groupNum: 1,
      groupList: [],
      currentGroup: {},
      groupNameStatus: "",
      reorganizeChecked: false,
      groupData: {
        classId: this.classId,
        courseId: this.$route.query.id
      },
      groupNameList: [],
      showFlag: false,
      currentGroupId: '',
      loafProgress:0,
      progressFlag:false,
      exportDialogVisible:false,
      isSingle:false
    };
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"],
      courseInfo: ["getCourseInfo"],
      isToggleTheme: ["isToggleTheme"]
    })
  },
  watch: {
    classId: {
      handler(newVal, val) {
        this.paramsData.classId = newVal;
        this.groupData.classId = newVal;
        this.status = "student";
        this.searchValue = "";
        this.QueryClassStudent();
      },
      immediate: true
    },
    status: {
      handler(newVal, val) {
        if (newVal === "grouping") {
          this.GetAllGroups();
        }
      }
    },
    classObj: {
      handler(val) {
       console.log(val, "12ssf------");
        this.classMessage = val;
      },
      immediate: true
    }
  },
  created() { },
  mounted() { },
  methods: {
    viewGroupScores(groupId) {
      this.currentGroupId = groupId
      this.showFlag = true
    },
    addStatus(val){
      this.isSingle = val
    },
    upLoadProgress(event, file, fileList){
      this.progressFlag = true;
      this.loafProgress = parseInt(event.percent)// 动态获取文件上传进度
      // console.log(this.loafProgress);
      if (this.loafProgress >= 100) {
        this.progressFlag = false;
        this.loafProgress = 0
      }
    },
    sortChange(val){
      // console.log('---------',val);
      if(val.order){
        let a = ''
        if(val.order == 'descending'){
          a = val.prop + ' desc'
        }else if(val.order == "ascending"){
          a = val.prop + ' asc'
        } 
        this.QueryClassStudent(a)
      }else{
        this.QueryClassStudent()
      }
            // this.GetStudentsTask('CreateTime asc')
      //   }else if(val.order == 'descending'){
      //     this.GetStudentsTask('CreateTime desc')
    },
    closeChildDialog() {
      this.showFlag = false
    },
    deleteStudents(){
      this.$refs.StudentTableVue.BatchDeletion()
      this.delsDialogVisible = false
    },
    visibleChange(val, item) {
      if (val) {
        // console.log(item.teamId);
        this.groupNameList = this.groupList.map(e => {
          if (e.id != item.teamId && e.teamName != '未分组') {
            return e
          } else {
            return []
          }
        }).flat()
      }
    },
    groupDialogClose() {
      if (this.groupDialog == "grouping") {
        this.groupNum = 1;
        this.reorganizeChecked = false;
      }
    },
    GroupHandleCommand(val) {
      if (val.key == "teamLeader") {
        if (val.val.isLeader) {
          this.$message("该成员已是组长");
          return;
        }
        this.SetTeamLeader(val.val);
      }
    },
    async onAdd(e) {
      const item = e.item._underlying_vm_
      let data = {
        currentTeamId: item.teamId,
        userId: item.userId
      };
      this.groupList.forEach(e => {
        e.students?.forEach(element => {
          if (element.userId == item.userId) {
            data.teamId = e.id;
          }
        });
      });

      const res = await this.$api.AddAndEditGroups(data);
      if (res.code == 200) {
        this.$message.success("移动成功");
      }
      this.GetAllGroups();
    },
    async SetTeamLeader(val) {
      const data = {
        userId: val.userId,
        teamId: val.teamId
      };
      const res = await this.$api.SetLeader(data);
      if (res.code == 200) {
        this.$message.success("设置成功");
        this.GetAllGroups();
      }
    },
    //修改分组信息
    ModifyGroupName() {
      this.groupNameStatus = "";
      this.ModifyGroupingInfo();
    },
    async ModifyGroupingInfo() {
      let data = {
        id: this.currentGroup.id,
        teamName: this.currentGroup.teamName
      };
      const res = await this.$api.EditGrouping(data);
      if (res.code == 200) {
        this.$message.success("修改成功");
      }
      this.GetAllGroups();
    },
    //编辑分组人员
    async EditGroupingPersonnel(user, newRow) {
      // console.log('00000000',row,newRow);
      let data = {
        currentTeamId: user.teamId,
        userId: user.userId,
        teamId: newRow.id
      };
      const res = await this.$api.AddAndEditGroups(data);
      if (res.code == 200) {
        this.$message.success("移动成功");
        this.GetAllGroups();
      }
    },
    seUpGrouping() {
      this.groupTitle = "设置分组";
      this.groupDialog = "grouping";
      this.groupDialogVisible = true;
    },
    //设置分组
    confirmGrouping() {
      if (this.groupDialog == "grouping") {
        if (!this.groupNum) {
          this.$message("数值不能为空!");
          return;
        }
        this.GroupingCount();
      } else if (this.groupDialog == "DeleteOne") {
        this.DeleteGrouping();
      } else if (this.groupDialog == "DeleteAll") {
        this.deleteAllGroupRequest();
      }
    },
    async GroupingCount() {
      let data = {
        automatic: this.reorganizeChecked,
        type: 0,
        configId: 0,
        classId: this.classId,
        number: this.groupNum,
        courseId: this.$route.query.id
      };
      const res = await this.$api.AutomaticGrouping(data);
      if (res.code == 200) {
        this.$message.success(res.msg);
        this.GetAllGroups();
        this.groupDialogVisible = false;
      }
    },
    //删除一个组
    deleteOneGroup(val) {
      this.currentGroup = val;
      this.groupTitle = "提示";
      this.groupDialog = "DeleteOne";
      this.groupDialogVisible = true;
    },
    async DeleteGrouping() {
      const res = await this.$api.DeleteGroup({ id: this.currentGroup.id });
      if (res.code == 200) {
        this.$message.success("删除成功");
        this.groupDialogVisible = false;
        this.GetAllGroups();
      }
    },
    //删除全部分组
    deleteAllGrouping() {
      this.groupTitle = "提示";
      this.groupDialog = "DeleteAll";
      this.groupDialogVisible = true;
    },
    async deleteAllGroupRequest() {
      let data = [];
      this.groupList.forEach(e => {
        data.push(e.id);
      });
      const res = await this.$api.DeleteByIds(data);
      if (res.code == 200 || res.data) {
        this.$message.success("删除成功");
        this.GetAllGroups();
        this.groupDialogVisible = false;
      }
    },
    //分组重命名
    async groupRename(group) {
      this.currentGroup = group;
      this.groupNameStatus = group.id;
    },
    // 导出学生
    exportStudentDialog(){
      this.exportDialogVisible = true
    },
    exportStudent() {
      // console.log("jkdsfhksak", this.sdutentList);
      if (this.sdutentList.length == 0) {
        this.$message("无可导出数据!");
        return;
      }
      this.exportDialogVisible = false
      const exl_data = [["姓名", "学号/工号", "联系方式", "班级", "加入时间"]];
      this.sdutentList.forEach(val => {
        val.studentNo = val.studentNo !== null ? val.studentNo : "";
        val.createTime = val.createTime !== null ? val.createTime : "";
        exl_data.push([
          val.userName,
          val.studentNo,
          val.mobilePhone,
          val.className,
          val.createTime
        ]);
      });
      // this.$confirm(`确认导出${this.className}班的学生信息`, "提示")
      //   .then(() => {
          var sheet = XLSX.utils.aoa_to_sheet(exl_data);
          sheet["!cols"] = [
            {
              wpx: 70
            },
            {
              wpx: 100
            },
            {
              wpx: 100
            },
            {
              wpx: 120
            },
            {
              wpx: 140
            }
          ];
          this.openDownloadDialog(
            this.sheet2blob(sheet),
            `${this.className}班学生信息.xlsx`
          );
        // })
        // .catch(() => { });
    },
    openDownloadDialog(url, saveName) {
      if (typeof url == "object" && url instanceof Blob) {
        url = URL.createObjectURL(url); // 创建blob地址
      }
      var aLink = document.createElement("a");
      aLink.href = url;
      aLink.download = saveName || ""; // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
      var event;
      if (window.MouseEvent) event = new MouseEvent("click");
      else {
        event = document.createEvent("MouseEvents");
        event.initMouseEvent(
          "click",
          true,
          false,
          window,
          0,
          0,
          0,
          0,
          0,
          false,
          false,
          false,
          false,
          0,
          null
        );
      }
      aLink.dispatchEvent(event);
    },
    sheet2blob(sheet, sheetName) {
      sheetName = sheetName || "sheet1";
      var workbook = {
        SheetNames: [sheetName],
        Sheets: {}
      };
      workbook.Sheets[sheetName] = sheet;
      // 生成excel的配置项
      var wopts = {
        bookType: "xlsx", // 要生成的文件类型
        bookSST: false, // 是否生成Shared String Table，官方解释是，如果开启生成速度会下降，但在低版本IOS设备上有更好的兼容性
        type: "binary"
      };
      var wbout = XLSX.write(workbook, wopts);
      var blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });
      // 字符串转ArrayBuffer
      function s2ab(s) {
        var buf = new ArrayBuffer(s.length);
        var view = new Uint8Array(buf);
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
        return buf;
      }
      return blob;
    },
    addDialogClose(){
      this.dialogVisible = false
      this.QueryClassStudent()
    },
    close() {
      this.dialogcodeVisible = false;
    },
    opencode() {
      //console.log(this.courseInfo,'动态数据')
      const codeHtml = document.getElementById("qrcode");
      codeHtml.innerHTML = "";
      this.qrcode();
      let canvasID = this.$refs.qrCodeDiv;
      this.dialogcodeVisible = true;
    },
    onCopy: function (e) {
      this.$message({
          message: '班级码复制成功',
          type: 'success'
        });
    },
    onCopylink: function (e) {
      this.$message({
          message: '链接复制成功',
          type: 'success'
        });
    },
    // 复制失败
    onError: function (e) {
      // console.log("复制失败");
    },
    qrcode() {
      let url =
        window.location.href.split("#")[0] +
        "#/invitation?classid=" +
        this.classMessage.id +
        "&courseId=" +
        this.$route.query.id +
        "&invitationId=" +
        this.classMessage.createBy +
        "&classname=" +
        this.classMessage.name +
        "&teachername=" +
        this.userInfo.realName +
        "&schoolName=" +
        this.userInfo.schoolName +
        "&classcode=" +
        this.classMessage.verifycode;

      //let url = `http://192.168.1.130:9000/` + '#/invitation'
      //let url = `window.location.href.split("#")[0]'
      this.httpurl = url;
      this.qRCodeData = new QRCode(this.$refs.qrCodeDiv, {
        correctLevel:QRCode.CorrectLevel.L,
        width: 275,
        height: 246,
        text: url, // 二维码地址
        colorDark: "#000", // 二维码颜色
        colorLight: "#fff" // 二维码背景色
      });
    },
    downloadcode() {
      let canvasID = this.$refs.qrCodeDivc;
      let that = this;
      let a = document.createElement("a");
      html2canvas(canvasID, {
        useCORS: true,
        scrollY: 0,
        removeContainer: false
      }).then(canvas => {
        let dom = document.body.appendChild(canvas);
        dom.style.display = "none";
        a.style.display = "none";
        document.body.removeChild(dom);
        let blob = that.dataURLToBlob(dom.toDataURL("image/png"));
        a.setAttribute("href", URL.createObjectURL(blob));
        a.setAttribute("download", "二维码.png");
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(blob);
        document.body.removeChild(a);
      });
    },
    dataURLToBlob(dataurl) {
      //ie 图片转格式
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], {
        type: mime
      });
    },
    GetInviteStudent() {
      // console.log("赋值链接");
    },
    switchTab(e) {
      this.status = e;
    },
    BatchImport() {
      this.dialogVisibledao = true;
    },
    //新增或编辑学生信息(批量)
    async AddEditingStudentss(val) {
      // console.log(val);
      const data = {
        params: {
          classId: this.classId,
          courseId: this.$route.query.id
        },
        dtos: []
      };

      val &&
        val.forEach(e => {
          const obj = {};
          obj.id = 0;
          obj.courseId = this.$route.query.id;
          obj.userId = e.userId;
          obj.userName = e.userName;
          obj.tenantId = this.userInfo.tenantId;
          obj.mobilePhone = e.mobilePhone;
          obj.classId = this.classId;
          obj.studentNo = e.studentNo;
          data.dtos.push(obj);
        });
      // console.log("批量新增学生", data);
      const res = await this.$api.AddEditingStudents(data);
      // console.log("新增编辑学生", res);
      if (res.code === 200) {
        this.$message({ message: '添加成功', type: 'success' });
        this.QueryClassStudent();
        this.dialogVisible = false;
      }
    },
    //添加学生(班级单位)
    async addClassStudent() {
      const data = {
        classId: this.classId,
        courseId: this.$route.query.id,
        classIds: this.addClassStudentList,
        studentInfo: this.selectStudentList
      };
      const res = await this.$api.BatchAddClassStudents(data);
      if (res.code === 200) {
        this.$message({ message: '添加成功', type: 'success' });
        this.QueryClassStudent();
        this.dialogVisible = false;
      }
    },
    //新增或编辑学生信息(单个)
    async AddEditingStudents(data) {
      const res = await this.$api.AddEditingStudent(data);
      if (res.code === 200) {
        this.QueryClassStudent();
      }
    },
    //查询学生列表
    async QueryClassStudent(orderBy) {
      const data = {
        params: {
          courseId: this.$route.query.id,
          classId: this.classId
        }
      };
      if(orderBy){
        data.orderBy = orderBy
      }
      const res = await this.$api.QueryClassStudentList(data);
      if (res.code === 200) {
        this.sdutentList = res.data;
      }
    },
    //根据条件进行学生检索
    async search1() {
      // console.log(this.searchValue);
      const data = {
        params: {
          key: this.searchValue,
          classId: this.classId
        }
      };
      if (this.searchValue) {
        const res = await this.$api.searchStudentList(data);
        if (res.code === 200) {
          this.sdutentList = res.data;
        }
      } else {
        this.QueryClassStudent();
      }
    },
    addStudents() {
      this.dialogVisible = true;
    },
    getAddStudents(data) {
      this.AddStudents = data;
    },
    getClassAllStudent(val) {
      this.addClassStudentList = val;
    },
    getclasslStudent(val) {
      this.selectStudentList = val;
    },
    //添加学生
    addStudentSubmit() {
      // console.log('88888',this.AddStudents);
      if (this.AddStudents) {
        this.AddEditingStudentss(this.AddStudents);
      }
      if (this.addClassStudentList.length>0 || this.selectStudentList.length>0) {
        this.addClassStudent();
      }
    },
    //获取分组和学生信息
    async GetAllGroups() {
      const data = {
        classId: this.classId,
        courseId: this.$route.query.id
      };
      const res = await this.$api.GetAllGroupsAndStudents(data);
      if (res.code === 200) {
        this.groupList = res.data;
      }
    },
    // 上传失败
    upLoadError: function (err, file, fileList) {
      this.$message.error("上传失败，请检查服务器");
      this.progressFlag = false
      this.loafProgress = 0 // 上传进度
    },
    uploadSuccess(response, file, fileList) {
      // this.fileList = [];
      // this.file = file
      this.$refs.GroupUpload.clearFiles();
      // console.log('444444444444', response, file, fileList);
      if (response.code == 200 && response.data.length == 0) {
        this.$message({ message: "上传成功!", type: "success" });
        this.dialogVisibledao = false;
        this.progressFlag = false;
        this.QueryClassStudent();
      } else if (response.data.length != 0) {
        this.progressFlag = false;
        this.$message({ message: "Excel信息错误", type: "error" });
        this.importError = response.data;
      }else{
        this.$message({ message: response.msg, type: "error" });
      }
    },
    //分组上传成功
    GroupUploadSuccess(response, file, fileList) {
      // console.log("sfsdf", response, file, fileList);
      this.$refs.uploads.clearFiles();
      if (response.code == 200) {
        this.GetAllGroups();
      }
    },
    async BatchImportStudent() {
      if (!this.file) {
        this.$message("请上传文件!");
        return;
      }
      let data = {
        courseId: this.$route.query.id,
        classId: this.classId,
        fileName: "",
        name: ""
      };
      let res = await this.$api.ImportClassStudents(data);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog {
    // width: 800px;
    // height: 749px;
    background: #FFFFFF;
    border-radius: 10px 10px 10px 10px;

    .el-dialog__header {
      height: 60px;
      // background: #F7F7F7;
      border-radius: 10px 10px 0 0;
    }

    .el-dialog__body {
      box-sizing: border-box;
      padding: 0;
      display: flex;
      justify-content: center;
    }

    .el-dialog__footer {
      display: flex;
      justify-content: flex-end;
      // position: absolute;
      // bottom: 0px;
      // width: 100%;
      // padding-right: 20px;
      padding: 40px 20px 20px 20px;

      // button {
      //   width: 80px;
      //   height: 38px;
      //   border-radius: 4px;
      //   font-size: 16px;
      //   margin-left: 10px;
      //   cursor: pointer;

      //   &:nth-child(1) {
      //     border: 1px solid #DDE2E9;
      //     background: #F6F8FA;
      //     color: #222222;
      //   }

      //   &:nth-child(2) {
      //     background: #2B66FF;
      //     color: #FFFFFF;
      //     border: none;
      //   }
      // }
    }
  }
}
.grouping-el-dropdown-menu {
  .mobiletab {
    width: 90px;
    height: 30px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #606266;
    cursor: pointer;

    &:hover {
      background: #f6f7fb;
      color: #2b66fa;
    }

    &:hover .mobile-grouping {
      display: block !important;
    }

    .mobile-grouping {
      width: 120px;
      min-height: 72px;
      background-color: white;
      position: absolute;
      left: 90px;
      top: -36px;
      border-radius: 0 4px 4px 4px;
      display: none !important;
      display: flex;
      flex-direction: column;
      box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.16);

      >div {
        width: 120px;
        padding: 0 10px;
        height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
        line-height: 40px;
        font-size: 14px;
        color: #333333;
        cursor: pointer;

        &:hover {
          background: #f6f7fb;
          color: #2b66fa;
        }
      }
    }
  }
}

.classDelaitsbox {
  height: 100%;
  display: flex;
  flex-direction: column;
  .classmain {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 10px;
    &::before{
      content: '';
      width:4px;
      height: 16px;
      background: #2b66ff;
      position: absolute;
      left: 0;
      border-radius: 2px;
      top: 4px;
    }
  }
  .addstudentDialog{
    ::v-deep .el-dialog{
      width: 720px;
      // height:674px;
    }
  }

  .batchImport {
    ::v-deep .el-dialog {
      width: 500px;
      height: 300px;

      .el-dialog__body {
        display: flex;
        flex-direction: column;
      }

      .contents {
        width: 80%;
        display: flex;
        margin: auto;
        margin-top: 20px;

        .info {
          display: flex;
          flex-direction: column;
          margin-left: 10px;

          a {
            color: #2b66ff;
          }

          .format {
            margin: 10px 0;
            font-size: 12px;
          }
        }
      }

      .import-error {
        padding: 0 20px;
        margin-top: 20px;
        max-height: 90px;
        overflow-y: auto;

        >div {
          display: flex;
          flex-direction: column;
        }
      }

      .avatar-uploader .el-upload {
        width: 140px;
        height: 140px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .avatar-uploader .el-upload:hover {
        border-color: #2B66FF;
      }

      .avatar-uploader-icon {
        font-size: 20px;
        color: #8c939d;
        width: 120px;
        height: 120px;
        line-height: 120px;
        text-align: center;
      }
    }
  }

  .class-name {
    font-size: 14px;
    color: #333333;
    margin: 5px 0 10px 0;
    font-weight: bold;
  }

  .classDelaitstop {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding-bottom: 12px;
    margin-bottom: 17px;
    border-bottom: 1px solid #e7e7e7;

    .classDelflex{
      display: flex;
      align-items: center;
    }



    .btnlistflex{
      display: flex;
      align-items: center;
      .classmain-r {
      padding-right:10px;
      cursor: pointer;
      .iconfize {
        font-size: 26px;
        padding-right: 3px;
      }
      .inviteclass{
        font-size: 14px;
        color: #7c7c7c;
        position: relative;
        top:-5px
      }
    }
    }
    .grouping-uploader {
      display: inline-block;
      margin-right: 10px;
    }

    .groupingTabs {
      width: 156px;
      height: 100%;
      padding: 0 33px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #333333;

      >span {
        cursor: pointer;
      }

      .groupActive {
        position: relative;
        color: #2b66fa;

        &::before {
          content: "";
          width: 40px;
          height: 3px;
          background: #3080f4;
          border-radius: 3px;
          position: absolute;
          top: 33px;
          left: -6px;
        }
      }
    }

    .tabs {
      display: flex;
      align-items: center;
    }

    .classDelaits-search {
      display: flex;
      align-items: center;
      padding-left: 12px;
      .cdecounts {
        font-size: 14px;
        color: #7c7c7c;
        margin-left: 20px;
      }

      ::v-deep .el-input__inner {
        background: #f7f7f7;
        border: none;
        padding-right: 57px;
      }
    }

    ::v-deep .el-row {
      display: flex;
    }
  }

  .class-delaits-content {
    flex: 1;
    min-height: 0;
    overflow-y: auto;

    .noData {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      >span {
        font-size: 14px;
        color: #606a78;
        text-align: center;
      }

      >img {
        width: 221px;
        height: 180px;
        margin-bottom: 20px;
        align-items: center;
      }
    }

    .grouping-item {
      width: 100%;
      // height: 160px;
      // border: 2px solid rgb(244, 244, 244);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      margin-bottom: 35px;

      .groupTop {
        display: flex;
        align-items: center;

        >span {
          margin-left: 20px;
        }

        .dtailsBtn {
          margin-left: 20px;
        }
      }

      .grouping-item-top {
        width: 400px;
        height: 45px;
        background: #f9f9f9;
        box-sizing: border-box;
        padding: 0 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        color: #222222;
        border-radius: 2px;

        >span {
          max-width: 77%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .el-input {
          width: 240px;
        }

        .el-icon-edit,
        .el-icon-delete {
          font-size: 17px;
          margin-left: 20px;
          color: #787d83;
          cursor: pointer;
          display: none;
        }

        &:hover {

          .el-icon-edit,
          .el-icon-delete {
            display: inline-block;
          }
        }
      }

      .grouping-item-content {
        display: flex;
        // align-items: center;
        // background-color: aqua;
        box-sizing: border-box;
        padding-top: 17px;
        flex: 1;
        flex-wrap: wrap;

        >div {
          width: 100%;

          >span {
            display: flex;
            flex-wrap: wrap;
          }
        }

        .student-item {
          width: 194px;
          height: 88px;
          border-radius: 10px;
          background: #f2faff;
          box-sizing: border-box;
          padding: 20px;
          display: flex;
          margin-right: 24px;
          margin-bottom: 17px;
          justify-content: space-between;
          position: relative;

          .el-dropdown {
            position: absolute;
            top: 10px;
            right: 1px;

            .mobiletab {
              position: relative;
              display: flex;
              justify-content: center;
              align-items: center;

              .mobile-grouping {
                width: 100px;
                height: 200px;
                background-color: #2b66fa;
                position: absolute;
                left: 100px;
                top: 0px;
              }
            }
          }

          &:nth-child(6n) {
            margin: 0;
          }

          // .mobiletab:hover .mobile-grouping{
          //   display: block;
          // }

          .student-info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            >span:nth-child(1) {
              font-size: 16px;
              color: #222222;
            }

            >span:nth-child(2) {
              font-size: 14px;
              color: #787d83;
            }
          }

          .student-image {
            height: 46px;
            width: 46px;
            position: relative;

            >section {
              width: 100%;
              height: 100%;
              border-radius: 5px;
              background-color: white;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #c6d6fe;

              i {
                font-size: 27px;
              }
            }

            img {
              width: 100%;
              height: 100%;
            }

            >div {
              background-color: #2b66fa;
              width: 40px;
              height: 18px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              top: 37px;
              left: 3px;
              color: #ffffff;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  .groupingDialog {
    ::v-deep .el-dialog {
      width: 600px;
      height: 366px;
      border-radius: 10px 10px 10px 10px;
    }

    .el-dialog__footer {
      margin-bottom: 27px;
    }

    .groupCount {
      display: flex;
      flex-direction: column;

      .el-input-number {
        width: 262px;
        margin: 45px 0 18px 0;
      }

      .el-input__inner {
        background: #f7f7f7;
      }

      >span {
        color: #909090;
        font-size: 14px;
      }

      .el-checkbox {
        text-align: center;
        margin-top: 20px;

        .el-checkbox__label {
          font-size: 12px;
        }
      }
    }

    .DeleteOneGroup {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #606a78;
      font-size: 14px;

      >img {
        margin-top: 24px;
      }

      >span:nth-child(2) {
        margin: 13px 0;
      }
    }
  }
  .delsDialog{
    ::v-deep .el-dialog {
      width: 500px;
      // height: 330px;
      border-radius: 10px;
      .el-dialog__body{
        padding: 0 20px;
      }
    }
    .dialog-footer{
      width: 100%;
      text-align: right;
    }
    .dels {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .dialog-footer {
        margin: 40px 0 20px 0;
        text-align: right;
      }
      .cion {
        width: 85px;
        height: 85px;
        background: #ca6666;
        margin: 23px 0 18px 0;
      }

      .text {
        width: 252px;
        height: 46px;
        font-size: 14px;
        color: #333333;
        text-align: center;
      }
    }
  }
  .exportDialogs{
    ::v-deep .el-dialog {
      width: 500px;
      // height: 330px;
      border-radius: 10px;
      .el-dialog__body{
        padding: 0 20px;
      }
    }
    .export{
      width:100%;
    }
    ::v-deep .dialog-footer{
      width: 100% ;
      text-align: right;
      margin: 40px 0 20px 0
    }
  }

  .codecontent {
    width: 100%;

    .codecenter {
      width: 60%;
      height: 300px;
      margin: 0 auto;
    }
  }

  .task-publish-dialog {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.3);
    top: 0;
    left: 0;

    .close {
      position: absolute;
      top: 8px;
      right: 10px;
      width: 24px;
      height: 24px;
      cursor: pointer;

      .icons {
        color: #979797;
        font-size: 25px;
      }
    }

    .task-publish {
      width: 424px;
      height: auto;
      background: #ffffff;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #fff;
      padding-bottom: 37px;

      .qrcode {
        .code {
          width: 342px;
          height: 290px;
          background: #ffffff;
          margin: 0 auto;
          border: 1px solid #e7e7e7;
          padding: 20px 35px;
          border-radius: 20px;
        }

        .qrcodecon {
          padding-top: 38px;
          padding-bottom: 35px;

          .codecontainer {
            width: 83%;
            display: flex;
            padding-bottom: 10px;
            background: #f7f7f7;
            border-radius: 10px;
            padding: 18px;
            justify-content: center;
            margin: 0 auto 18px auto;

            .codecontainerflex {
              display: flex;

              .qrCodes-l {
                img {
                  width: 90px;
                  height: 108px;
                }
              }

              .qrCodes-r {
                padding-left: 14px;

                .coursetitle {
                  font-size: 16px;
                  line-height: 21px;
                  padding-bottom: 16px;
                  color: #333333;
                }

                .courseclass {
                  font-size: 14px;
                  color: #787d83;
                  padding-bottom: 14px;
                }

                .coursecode {
                  display: flex;
                  align-items: center;

                  .coursecodel {
                    font-size: 14px;
                    color: #787d83;
                  }

                  .coursecoder {
                    margin-left: 12px;
                    border: 1px solid #e3e3e3;
                    border-radius: 5px;
                    padding: 2px 9px;
                    font-size: 14px;
                    color: #979797;
                    cursor: pointer;
                    background: #fff;
                  }
                }
              }
            }
          }
        }

        .scan {
          color: #666;
          font-size: 13px;
          text-align: center;
          margin-top: 10px;
          margin-bottom: 24px;

          img {
            margin-right: 6px;
          }
        }
      }

      .btnflex {
        display: flex;
        justify-content: center;

        .btncontianer {
          display: flex;

          .btnl {
            width: 105px;
            height: 40px;
            border: 1px solid #2b66ff;
            // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            margin: 0 auto;
            line-height: 38px;
            text-align: center;
            color: #2b66ff;
            font-size: 15px;
            cursor: pointer;
            margin-right: 23px;
          }

          .btnr {
            width: 105px;
            height: 40px;
            background: linear-gradient(310deg, #2b66ff 0%, #4a7bf7 100%);
            // box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            margin: 0 auto;
            line-height: 40px;
            text-align: center;
            color: #fff;
            font-size: 15px;
            cursor: pointer;
          }
        }
      }

      // .qr_foot {
      // 	position: absolute;
      // 	bottom: 0;
      // 	width: 100%;
      // 	height: 50px;
      // 	line-height: 50px;
      // 	background: #efefef;
      // 	text-align: center;

      // 	span {
      // 		text-decoration: underline;
      // 		cursor: pointer;
      // 	}
      // }
    }
  }
}

.arrowItem {
  .popper__arrow:after {
    border-top-color: #303133 !important;
    border-bottom-color: #303133 !important;
  }
}
</style>
