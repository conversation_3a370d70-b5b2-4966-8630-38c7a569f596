
<template>
  <div class="course-weight-config-panel">
    <div class="config-title">
      <span>课程权重占比配置</span>

      <div class="title-btn">
        <!-- <el-checkbox v-model="checked">应用全部课程</el-checkbox> -->
        <el-button @click="restoreClick">恢复默认</el-button>
      </div>
    </div>
    <div class="config-content">
      <div class="config-content-item">
        <div class="item-label-box">
          <span class="label-text">考试</span>
          <el-input v-model="examObj.weight" style="width: 70px;" @input="handleInput($event, 'examObj')"></el-input>
          <span class="symbol">%</span>
        </div>
        <div class="item-score-rules">
          计分规则：
          <el-select v-model="examObj.ruleType" clearable placeholder="请选择">
            <el-option label="按得分率计分" :value="1"></el-option>
            <!-- <el-option label="按得分等级计分" :value="2"></el-option> -->
          </el-select>
          得分率=考试得分/考试总分*100%;
          <el-button style="margin-left: 5px;" @click="scoreClick">得分配置</el-button>
        </div>
      </div>
      <div class="config-content-item">
        <div class="item-label-box">
          <span class="label-text">签到（出勤）</span>
          <el-input v-model="attendanceObj.weight" @input="handleInput($event, 'attendanceObj')"
            style="width: 70px;"></el-input>
          <span class="symbol">%</span>
        </div>
        <div class="item-score-rules">计分规则：<span>按签到计分</span>
          签到率=签到次数/签到总数，若出勤率低于
          <el-input v-model="attendanceObj.attendanceRate" style="width: 70px;"></el-input>
          %，签到权重成绩计为0分;
        </div>
      </div>
      <div class="config-content-item">
        <div class="item-label-box">
          <span class="label-text">作业</span>
          <el-input v-model="workObj.weight" @input="handleInput($event, 'workObj')" style="width: 70px;"></el-input>
          <span class="symbol">%</span>
        </div>
        <div class="item-score-rules">计分规则：
          <!-- 分班作业 <el-input v-model="input" style="width: 70px;"></el-input>% -->
          <el-select v-model="workObj.ruleType" clearable placeholder="请选择">
            <el-option label="按作业得分率计分" :value="1"></el-option>
            <el-option label="按完成得分率计分" :value="2"></el-option>
          </el-select>
          作业完成率=完成作业数/全部作业数*100%
        </div>
      </div>
      <div class="config-content-item">
        <div class="item-label-box">
          <span class="label-text">讨论</span>
          <el-input v-model="discussObj.weight" @input="handleInput($event, 'discussObj')"
            style="width: 70px;"></el-input>
          <span class="symbol">%</span>
        </div>
        <div class="item-score-rules">
          计分规则：
          <el-select v-model="discussObj.ruleType" clearable placeholder="请选择">
            <el-option label="按讨论得分计分" :value="1"></el-option>
            <el-option label="按参与率计分" :value="2"></el-option>
          </el-select>
          <!-- 参与讨论即默认得1分，同一讨论默认分仅可得1次; 另教师可根据讨论言论进行额外加1-3分，同一讨论额外分仅可得一次; -->
          计分分值最高加{{ discussObj.weight }}分或扣{{
            discussObj.weight }}分
        </div>
      </div>
      <div class="config-content-item">
        <div class="item-label-box">
          <span class="label-text">点名</span>
          <el-input v-model="rollCallObj.weight" @input="handleInput($event, 'rollCallObj')"
            style="width: 70px;"></el-input>
          <span class="symbol">%</span>
        </div>
        <div class="item-score-rules">
          计分规则：<span>按得点名得分计分</span>
          <!-- 教师可根据点名情况进行扣1-3分或加1-3分， -->
          计分分值最高加{{ rollCallObj.weight }}分或扣{{ rollCallObj.weight }}分
        </div>
      </div>
    </div>
    <div class="content-tip">修改权重后，由教师直接给分的学生或已计算分值的综合成绩按权重占比重新进行计算</div>
    <div class="config-btn-box">
      <el-button class="save-btn" type="primary" @click="configSaveClick">保存</el-button>
    </div>
    <el-dialog title="考试得分等级权重计分规则" v-if="scoreConfigDialog" :visible.sync="scoreConfigDialog" custom-class="scoreConfigDialog" width="700px">
      <div class="dialog-header-config">
        <div class="header-title">
          考试得分等级权重计分规则
          <el-popover placement="right-start" title="等级计分规则说明:" width="320" trigger="hover">
            <i slot="reference" class="iconfont icon-24gf-questionCircle"></i>
            <div>
              实例:若小明参与考试A，考试总分为200分，小明考试得分为150.
              <br />
              1.考试等级规则一计分:(150/200)*100=75(划分等级为良好)
              <br />
              考试等级占比分为:100*80%=80分
              <br />
              最终考试占课程总分为:100*80%*60%=48分
              <br />
              2.考试等级规则二计分:(150/200)*100=75(划分等级为合格)
              <br />
              考试等级占比分为:100*100%=100分
              <br />
              最终考试占课程总分为:100*100%*60%=60分
            </div>
          </el-popover>
        </div>
        <i class="icon iconfont icon-guanbi" @click="handleClose"></i>
      </div>
      <div class="dialog-body-content">
        <div class="flex-box" style="padding-right: 20px;">
          <el-radio-group class="select-raido" v-model="isCollapse"  @input="isApiSearch = false;" style="margin: 15px 0;">
            <el-radio-button :label="5">选项一</el-radio-button>
            <el-radio-button :label="3">选项二</el-radio-button>
          </el-radio-group>
          <div v-if="nowTablelength > 0" class="text">当前应用：{{ nowTablelength == 3 ? '选项二' : nowTablelength == 5 ? '选项一'
            : '' }}
          </div>
        </div>
        <el-table :data="tableData" :header-cell-style="{ background: '#F7F7F7', color: '#222222' }"
          style="width: 100%">
          <el-table-column prop="levelName" label="得分等级">
            <template slot-scope="{row}">
              <div style="display: flex;align-items: center;justify-content: space-between;">
                <el-input v-if="!row.isLevel" v-model="row.levelName" style="width: 90px;"></el-input>
                <span v-else>{{ row.levelName }}</span>
                <!-- <i class="icon iconfont icon-bianji" @click="row.isLevel = !row.isLevel"></i> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="startRate" label="等级划分区间" width="300">
            <template slot-scope="{row}">
              <div style="display: flex;align-items: center;gap: 5px;">
                <el-input v-model="row.startRate" disabled style="width: 60px;"></el-input>
                <el-select v-model="row.startSymbol" style="width: 65px;" disabled placeholder="">
                  <el-option label=">" value=">"></el-option>
                  <el-option label="<" value="<"></el-option>
                  <el-option label=">=" value=">="></el-option>
                  <el-option label="<=" value="<="></el-option>
                </el-select>
                X
                <el-select v-model="row.endSymbol" style="width: 65px;" disabled placeholder="">
                  <el-option label=">" value=">"></el-option>
                  <el-option label="<" value="<"></el-option>
                  <el-option label=">=" value=">="></el-option>
                  <el-option label="<=" value="<="></el-option>
                </el-select>
                <el-input v-model="row.endRate" disabled style="width: 60px;"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="proportion" label="等级占比">
            <template slot-scope="scope">
              <div class="flex-box">
                <div v-if="!scope.row.isProportion">
                  <el-input v-model="scope.row.proportion" style="width: 90px;"
                    @input="handleInput($event, 'tableData', scope.$index.toString())"></el-input>
                  <span>%</span>
                </div>
                <span v-else>{{ scope.row.proportion }}%</span>
                <div>
                  <i class="icon iconfont icon-bianji" style="margin-left: 10px;"
                    @click="scope.row.isProportion = !scope.row.isProportion"></i>
                  <!-- <i class="icon iconfont icon-shanchu" style="margin-left: 12px;"
                    @click="deleteTableClick(scope.row, scope.$index)"></i> -->
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div style="margin-top: 10px">
          <el-button v-if="this.tableData.length <= 5" icon="el-icon-plus" @click="addSelectClick">添加选项</el-button>
        </div> -->
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button class="score-btn cancel-btn" @click="handleClose">取 消</el-button>
        <el-button class="score-btn save-btn" type="primary" @click="scoreConfigClick">应 用</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { checkObjectFields } from "@/utils/objValidate.js";//对象验证
export default {
  name: 'FusionFrontCourseWeightConfig',

  data() {
    return {
      checked: false,
      scoreConfigDialog: false,
      tableData: [],
      examObj: {//考试对象
        type: 1,//1考试 2作业 3签到 4点名 5讨论
        ruleType: 1,//计分规则
        weight: "60",//权重
      },
      attendanceObj: {//签到（出勤）
        type: 3,
        attendanceRate: "20",//出勤率
        weight: "10",//权重
      },
      workObj: {//作业
        type: 2,
        ruleType: 2,//计分规则
        weight: "10",//权重
      },
      discussObj: {//讨论
        type: 5,
        ruleType: 1,//计分规则
        weight: "10",//权重
      },
      rollCallObj: {//点名
        type: 4,
        weight: "10",//权重
      },
      isCollapse: "", 
      isApiSearch: true,//是否从接口拿数据
      nowTablelength: null,
    };
  },

  computed: {
    ...mapGetters({
      courseInfo: ['getCourseInfo']
    })
  },

  watch: {
    isCollapse: {
      handler(val) {
        if (val && !this.isApiSearch) {
          this.tableData = [];
          let selectOne = [{
            isLevel: true,
            isProportion: true,
            levelName: "优秀",
            startRate: 90,
            endRate: 100,
            proportion: 100,
            startSymbol: "<=",
            endSymbol: "<="
          },
          {
            isLevel: true,
            isProportion: true,
            levelName: "良好",
            startRate: 70,
            endRate: 90,
            proportion: 80,
            startSymbol: "<=",
            endSymbol: "<"
          }, {
            isLevel: true,
            isProportion: true,
            levelName: "合格",
            startRate: 60,
            endRate: 70,
            proportion: 65,
            startSymbol: "<=",
            endSymbol: "<"
          }, {
            isLevel: true,
            isProportion: true,
            levelName: "不合格",
            startRate: 0,
            endRate: 60,
            proportion: 50,
            startSymbol: "<=",
            endSymbol: "<"
          }, {
            isLevel: true,
            isProportion: true,
            levelName: "未作答(无作答记录)",
            startRate: 0,
            endRate: 0,
            proportion: 0,
            startSymbol: "=",
            endSymbol: "="
          }]
          let selectTwo = [{
            isLevel: true,
            isProportion: true,
            levelName: "合格",
            startRate: 60,
            endRate: 100,
            proportion: 100,
            startSymbol: "<=",
            endSymbol: "<="
          },
          {
            isLevel: true,
            isProportion: true,
            levelName: "不合格",
            startRate: 0,
            endRate: 60,
            proportion: 80,
            startSymbol: "<=",
            endSymbol: "<"
          }, {
            isLevel: true,
            isProportion: true,
            levelName: "未作答(无作答记录)",
            startRate: 0,
            endRate: 0,
            proportion: 65,
            startSymbol: "=",
            endSymbol: "="
          }]
          this.tableData = val == 3 ? [...selectTwo] : [...selectOne]
        }
      },
      immediate: true,
      deep:true,
    }
  },
  mounted() {
    this.init();
  },

  methods: {
    handleInput(val, keyVal, index) {
      let regex = /^(?:0|[1-9]\d?|100)$/;
      if (!regex.test(val)) {
        this.$message.error("只能输入0-100整数");
        if (index) {
          this[keyVal][index].proportion = "";
          return
        }
        this[keyVal].weight = "";
        return
      }
    },
    async init() {
      let paramData = {
        params: {
          courseId: this.courseInfo.id || this.$route.query.id,
        }
      }
      const res = await this.$api.weightConfigSearchBy(paramData);
      if (res.code == 200) {
        //定义一个映射表，将 type 与目标对象关联
        let objMap = {
          1: 'examObj',//考试
          2: 'workObj',//作业
          3: 'attendanceObj',//签到（出勤）
          4: 'rollCallObj',//点名
          5: 'discussObj',//讨论
        }
        if (res.data.length > 0) {
          res.data.forEach(item => {
            const mapKey = objMap[item.type];
            if (this[mapKey]) {
              this[mapKey] = {
                id: item.id,
                type: item.type,
                ruleType: item.ruleType,
                attendanceRate: item.attendanceRate,
                weight: item.weight,
              }
            }
          });
        }
      }
    },
    async ruleSearch() {
      let paramData = {
        params: {
          courseId: this.courseInfo.id || this.$route.query.id,
        }
      };
      const res = await this.$api.courseScoringRulesSearchBy(paramData);
      if (res.code == 200) {
        if (res.data.length > 0) {
          this.tableData = res.data.map(item => ({ ...item, isLevel: true, isProportion: true }))
          this.nowTablelength = res.data.length;
          this.isCollapse = res.data.length == 5 ? 5 : 3;
          this.isApiSearch = true;
        }
      }
    },
    //添加选项
    addSelectClick() {
      this.tableData.push({
        isLevel: false,
        isProportion: false,
        levelName: "",
        startRate: "",
        endRate: "",
        proportion: ""
      })
    },
    //恢复默认点击事件
    restoreClick() {
      const defaultValues = {
        examObj: { type: 1, ruleType: 1, weight: "60" },
        attendanceObj: { type: 3, attendanceRate: "20", weight: "10" },
        workObj: { type: 2, ruleType: 2, weight: "10" },
        discussObj: { type: 5, ruleType: 1, weight: "10" },
        rollCallObj: { type: 4, weight: "10" },
      };
      Object.keys(defaultValues).forEach(key => {
        this[key] = Object.assign({}, this[key], defaultValues[key]);//合并已修改的对象
        console.log(this[key]);
      });
    },
    scoreClick() {
      this.scoreConfigDialog = true;
      this.ruleSearch()
    },
    handleClose() {
      this.scoreConfigDialog = false;
    },
    //规则配置接口
    async scoreConfigClick() {
      if (this.tableData.length == 0) {
        this.$message.error("未添加权重计分规则！请添加！");
        return
      }
      let convertObj = {
        levelName: "得分等级",
        startRate: "得分区间开始数值",
        endRate: "得分区间结束数值",
        proportion: "课程权重占比得分",
      }
      //验证是否填写对应字段
      for (let index = 0; index < this.tableData.length; index++) {
        const item = this.tableData[index];
        if (!checkObjectFields(item, `表格第${index + 1}行`, convertObj, this)) {
          return false;
        }
      }
      let paramData = {
        params: {
          courseId: this.courseInfo.id || this.$route.query.id,
        },
        dtos: []
      };
      if (this.tableData.length > 0) {
        paramData.dtos = this.tableData.map((item, index) => ({
          id: item.id || 0,
          courseId: this.courseInfo.id || this.$route.query.id,
          sort: index + 1,
          ruleType: 2,
          startRate: item.startRate,
          endRate: item.endRate,
          levelName: item.levelName,
          proportion: item.proportion,
          startSymbol: item.startSymbol,
          endSymbol: item.endSymbol
        }))
      }
      const res = await this.$api.courseScoringRulesSaveList(paramData);
      if (res.code == 200) {
        this.$message.success("规则配置成功！");
        this.scoreConfigDialog = false;
      }
    },
    //保存接口
    async configSaveClick() {
      let paramData = {
        params: {},
        dtos: []
      };
      // 定义各部分对象的配置
      const sections = [
        { obj: this.examObj, message: "考试部分" },
        { obj: this.attendanceObj, message: "签到（出勤）部分" },
        { obj: this.workObj, message: "作业部分" },
        { obj: this.discussObj, message: "讨论部分" },
        { obj: this.rollCallObj, message: "点名部分" }
      ];
      let convertObj = {
        ruleType: "计分规则",
        weight: "权重",
        attendanceRate: "出勤率",
      }
      // 遍历各部分，检查字段并填充参数数据
      for (let section of sections) {
        if (checkObjectFields(section.obj, section.message, convertObj, this)) {
          let { id, type, ruleType, attendanceRate, weight } = section.obj;
          paramData.dtos.push({
            id: id || 0,
            courseId: this.courseInfo.id || this.$route.query.id,
            type,
            ruleType,
            attendanceRate,
            weight,
          });
        } else {
          return; // 如果某部分为空，终止保存
        }
      }
      const total = paramData.dtos.reduce((accumulator, item) => accumulator + Number(item.weight), 0)
      if (total != 100) {
        this.$message.error("当前占比配置总和不足（超出）100%，请调整！");
        return
      }
      let res = await this.$api.weightConfigSaveList(paramData);
      if (res.code == 200) {
        this.$message.success('保存成功！');
      }
    },
    //规则删除
    async deleteTableClick(row, index) {
      if (!row.id) {
        this.tableData.splice(index, 1)
        return
      }
      const res = await this.$api.deleteCourseScoringRules({ id: row.id });
      if (res.code == 200) {
        this.tableData.splice(index, 1)
      }
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .scoreConfigDialog {
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.16);
  border: 1px solid #D1D1D1;
  border-radius: 10px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .dialog-header-config {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    background: #F7F7F7;
    border-radius: 10px 10px 0px 0px;
    padding: 0 20px;

    .header-title {
      font-size: 16px;
      color: #333333;

      &::before {
        content: " ";
        display: inline-block;
        width: 3px;
        height: 12px;
        border-radius: 3px;
        background: #3886FF;
        margin-right: 12px;
      }

      .icon-24gf-questionCircle {
        color: #80AFFF;
        cursor: pointer;

        &:hover {
          color: rgba(43, 102, 250, 1);
        }
      }
    }
    .icon-guanbi{
      cursor: pointer;
    }
  }

  .dialog-body-content {
    margin: 0 20px 25px 20px;

    .flex-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        color: #222222;
      }
    }

    .select-raido {
      .el-radio-button {
        &:last-child {
          margin-left: 15px;
        }

        .el-radio-button__inner {
          background: #F6F8FA;
          border-radius: 3px 3px 3px 3px;
          border: 1px solid #DDE2E9;
        }
      }

      .is-active {
        .el-radio-button__inner {
          color: #2B66FA;
          background: #FFFFFF;
          border-radius: 3px;
          border: 1px solid #2B66FA;
          box-shadow: none;
        }
      }

    }

    .el-button {
      color: #2B66FA;
      border: 1px solid #2B66FA;
      margin-left: 27px;
      margin-bottom: 31px;
      width: 92px;
      height: 24px;
      padding: 0;
    }

    .el-table {
      border: 1px solid #D4D4D4;
    }
  }

  .score-btn {
    width: 100px;
    height: 40px;
    padding: 0;
    line-height: 40px;
  }

  .cancel-btn {
    background: #F6F8FA;
    border: 1px solid #DDE2E9;
  }

  .save-btn {
    background: #2B66FF;

    &:hover,
    &:focus,
    &:active {
      color: #fff;
    }
  }
}

.course-weight-config-panel {
  padding: 0 39px 0 37px;

  .config-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 25px;

    >span {
      font-size: 16px;
      color: #333333;

      &::before {
        content: " ";
        display: inline-block;
        width: 3px;
        height: 12px;
        border-radius: 3px;
        background: #3886FF;
        margin-right: 10px;
      }
    }

  }

  .config-content {
    font-size: 14px;
    color: #333333;
    border: 1px solid #D9D9D9;
    margin-top: 20px;
    padding: 20px 25px;

    .config-content-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;

      .item-label-box {
        display: flex;
        align-items: center;

        .label-text {
          min-width: 84px;
          font-size: 14px;
          color: #787D83;
          margin-right: 10px;
        }

        .symbol {
          margin-left: 5px;
        }
      }

      .item-score-rules {
        >span {
          font-size: 14px;
          color: #222222;
          font-weight: bold;
          margin: 0 20px;
        }

        .el-select {
          margin: 0 12px;
        }

        .el-input {
          margin: 0 10px;
        }
      }
    }
  }

  .content-tip {
    text-align: right;
    margin-top: 20px;
    font-weight: 400;
    font-size: 14px;
    color: #BBBBBB;
  }

  .config-btn-box {
    text-align: right;
    margin-top: 40px;

    .save-btn {
      width: 100px;
      height: 40px;
      padding: 0;
      line-height: 40px;
      background: #2B66FA;

      &:hover,
      &:focus,
      &:active {
        color: #fff;
      }
    }
  }
}
</style>