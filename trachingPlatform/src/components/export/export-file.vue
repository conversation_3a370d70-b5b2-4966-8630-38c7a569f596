<template>
  <el-button
    class="export-file-button"
    title="导出"
    v-if="checkExportPermission()"
    v-preventReClick
    v-bind="$attrs"
    v-loading.fullscreen.lock="fullscreenLoading"
    @click="handleClick">
    <i class="action-icon iconfont icon-daoru"></i>
  </el-button>
</template>
<script>
import { checkExportPermission } from "@/utils/check-role";
import eventBus from "@/utils/eventBus";
export default {
  name: "ToMobileButton",
  components: {},
  mixins: [],
  props: {
    getMobileDynamicConfig: {
      type: Function
    },
    isIcon:{
      type: Boolean,
      default: false
    },
    item: {
      // 具体数据 对应外部的mobileData
      type: Object
    },
    contentType: {
      // 数据类型 1 动态表单| 2 场景模板 | 
      type: String,
      default: "1"
    },
    typeName: {
      type: String
    }
  },
  data() {
    return {
      fullscreenLoading: false
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    checkExportPermission,
    async handleClick(e) {
      // 点击按钮，导出对应的文件数据
      e.preventDefault();
      e.stopPropagation();
      this.$confirm(
        "确认将该资源导出？"
      )
        .then(async _ => {
          // 注册一个eventBus 触发对应的事件，导出文件
          
          eventBus.$emit("exportFile", {
            contentType: this.contentType,
          });
         
          // 数据源列表，通过调用接口，将查询参数存放到后台，返回一个新的访问url
          // const dynamicConfig = this.getMobileDynamicConfig?.();
          // const menuId = sessionStorage.getItem("currentMenuId");
          // const url = `${window.CURRENT_HOST}/#/single${this.item.url}`;
          // // 0: 其他教学资源, 1: 拓展资源, 2: 案例资源
          // const belongingType = this.contentType === "resource" ? 0 : 2;
          // this.fullscreenLoading = true;
          // let type = "";
          // switch (this.contentType) {
          //   case "resource":
          //     type = "resource";
          //     break;
          //   case "flow":
          //     type = "流程";
          //     break;
          // }
          // type = this.typeName || type;
          // if (menuId) {
          //   await this.$api.SetToMobileLink({
          //     resourceName: this.item.title,
          //     belongingType,
          //     type,
          //     url,
          //     menuId,
          //     config: JSON.stringify(dynamicConfig)
          //   });
          //   this.$message.success("设置成功");
          //   this.fullscreenLoading = false;
          // } else {
          //   await this.$api.SetToMobileLink({
          //     resourceName: this.item.title,
          //     belongingType,
          //     type,
          //     url,
          //     config: JSON.stringify(dynamicConfig)
          //   });
          // }
          // this.fullscreenLoading = false;
          // this.$message.success("设置成功");
        })
        .catch(_ => {
          console.log("error", _);
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.export-file-button {
  cursor: pointer;
}
</style>
