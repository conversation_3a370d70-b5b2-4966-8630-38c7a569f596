<template>
  <div class="user-com">
    <el-dropdown class="avatar-container" trigger="click" @command="handleCommand">
        <div class="avatar-wrapper">
          <img :src="userInfo.user.profilePhoto||avatar" class="user-avatar">
          <div class="user-info">
            <p class="user-name">{{userInfo.user.name}}</p>
            <p class="user-role">{{userInfo.schools[0].name}} </p>
          </div>  
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown"  >
            <el-dropdown-item command="userInfo">个人资料</el-dropdown-item>
            <el-dropdown-item command="editPassWord">修改密码</el-dropdown-item>
            <el-dropdown-item command="logout" divided @click.native="logout">
                <span style="display:block;">退出系统</span>
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>

    <el-dialog
      :title="dialogTitle"
      append-to-body
      :visible.sync="dialogVisible"
      custom-class="user-dialog"
      width="500px">
        <div class="user-info" v-show="type==1">
          <div class="user-img">
            <div class="block"><el-avatar :size="82" :src="avatar"></el-avatar></div>
            <el-upload
              class="upload-user-img"
              :action="actionUrl"
              :data="uploadData"
              :headers="uploadHeaders"
              :on-success="handleAvatarSuccess"
              :show-file-list="false">
              <el-button class="upload-btn" size="small" type="primary">修改头像</el-button>
            </el-upload>
          </div>
          <el-form class="info-form" ref="form" label-width="80px">
            <el-form-item label="姓名">
              <el-input readonly v-model="userInfo.user.name" placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input :disabled="true" readonly class="w-280" v-model="userInfo.user.phone" placeholder="请输入手机号" />
              <el-button type="text" size="small" @click="type=3;dialogTitle='更换手机号';">更换手机号</el-button>
            </el-form-item>
            <el-form-item label="密码">
              <el-input :disabled="true" readonly class="w-280" v-model="userInfo.user.email" placeholder="请输入密码" />
              <el-button   type="text" size="small" @click="type=2;">更换密码</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="phone-info" v-show="type==3">
          <p class="tip">请输入您要绑定的新手机号</p>
          <el-form class="phone-form" :model="phoneForm" :rules="phoneRules" ref="phoneForm" label-width="0">
            <el-form-item label="" prop="phone">
              <el-input class="phone-ipt" v-model="phoneForm.phone" placeholder="请输入绑定的新手机号"></el-input>
            </el-form-item>
            <el-form-item label="" prop="code">
              <div>
                <el-input class="code-ipt" v-model="phoneForm.smsCode" placeholder="请输入验证码"></el-input>
                <el-button class="code-btn" @click="sendMsg('ChangePhone')" :disabled="isSendingCode">{{ isSendingCode ? `${countdown}秒后重新发送` : '短信验证码' }}</el-button>
              </div>
            </el-form-item>
            <el-form-item class="footer-btn">
              <el-button class="cancle-btn" @click="type=1;">取消</el-button>
              <el-button class="save-btn" @click="savePhone">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="pass-info" v-show="type==2">
          <el-form class="pass-form" :model="passForm" ref="form" :rules="rules" label-width="80px">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="passForm.phone" placeholder="请输入手机号" />
            </el-form-item>
             <el-form-item label="验证码" prop="smsCode">
              <div style="display: flex;align-items: center;">
                <el-input class="code-ipt" v-model="passForm.smsCode" placeholder="请输入验证码"></el-input>
                <el-button style="margin-left:16px;" class="code-btn" @click="sendMsg('ResetPassword')" :disabled="isSendingCode">{{ isSendingCode ? `${countdown}秒后重新发送` : '短信验证码' }}</el-button>

              </div>
            </el-form-item>
            <el-form-item label="新密码" prop="password">
              <el-input v-model="passForm.password" placeholder="请输入新密码" />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="passForm.confirmPassword" placeholder="请输入确认密码" />
            </el-form-item>
            <el-form-item class="footer-btn">
              <el-button class="cancle-btn" @click="type=1;">取消</el-button>
              <el-button class="save-btn" @click="savePass">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import token from "@/utils/token.js";
// 定义手机号校验规则
const validatePhone = (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入有效的手机号码'));
  } else {
    callback();
  }
};
export default {
  name: "User",
  data() {
    return {
      avatar: require('@/assets/public/user.png'), // 替换为你的头像URL或本地路径，或者使用默认头像
      dialogTitle: '个人资料',
      dialogVisible:false,
      actionUrl: window.FILEIP, // 上传图片地址
      type:1,// 1 个人资料 2 修改密码 3 更换手机号码
      countdown: 0, // 验证码倒计时
      isSendingCode: false, // 是否正在发送验证码
      passForm:{
        phone:'',//手机号码
        pass:'',// 原密码
        smsCode:'', // 验证码
        password:'',// 新密码
        confirmPassword:''// 确认密码
      },
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      rules:{
        smsCode:[{ required: true, message: '请输入验证码', trigger: 'blur' }],
        password:[{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword:[{ required: true, message: '请输入确认密码', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (value !== this.passForm.password) {
              callback(new Error('两次输入密码不一致'));
            } else {
              callback();
            }
          }, trigger: 'blur' }] 
      },
      phoneRules:{
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        smsCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' }  
        ]
      },
      phoneForm:{
        phone:'',// 手机号
        smsCode:''// 验证码
      }
    }; 
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  mounted() {
    this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
  },
  methods: {
    logout() {
      // 在这里添加退出系统的逻辑，例如清除用户信息、发送退出请求等
      this.$router.push('/login') // 跳转到登录页面，或者其他操作，例如跳转到首页或显示登录表单等
    },
    // 倒计时方法
    startCountdown() {
      this.isSendingCode = true;
      this.countdown = 60; // 设置倒计时为60秒
      const timer = setInterval(() => {
        this.countdown--; // 每次间隔1秒将倒计时数量减少1
        if (this.countdown <= 0) {
          clearInterval(timer); // 当倒计时结束后清除定时器
          this.isSendingCode = false;
        }
      }, 1000);
    },
    handleAvatarSuccess(file) {
      // 预览图片
      this.avatar = file.data; // 图片地址
    },
    handleCommand(command) {
      this.dialogVisible = true; // 显示对话框
      switch (command) {
        case 'userInfo':
          this.type=1;
          this.dialogTitle='个人资料';
          break;
        case 'editPassWord':
          this.type=2;
          this.dialogTitle='修改密码';
          break; 
      }
    },
    savePass(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 在这里处理表单提交逻辑，例如发送请求保存用户信息
          this.$api.ResetPassword(this.passForm).then(res=>{
            if(res.errCode==0){
              this.$message({
                message: '重置成功',
                type: 'success'
              });
              this.dialogVisible=false;
            }

          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      }); 
    },
    savePhone(){
      this.$refs.phoneForm.validate((valid) => {
        if (valid) {
          // 在这里处理表单提交逻辑，例如发送请求保存用户信息
          this.$api.ChangePhone(this.phoneForm).then(res=>{
            if(res.errCode==0){
              this.$message({
                message: '重置成功',
                type: 'success'
              });
              this.dialogVisible=false;
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      }); 

    },
    sendMsg(type){
      if(type=="ChangePhone"){
        if(this.phoneForm.phone){
          this.$api.SendPhoneVerifyCode({phone:this.phoneForm.phone,type}).then(res=>{
            if(res.errCode==0){
              this.$message({
                message: '验证码发送成功',
                type: 'success'
              });
              this.startCountdown(); // 开始倒计时
            }
          })
        }
      }else if(type=="ResetPassword"){
        if(this.passForm.phone){
          // 在这里处理表单提交逻辑，例如发送请求保存用户信息
          this.$api.SendPhoneVerifyCode({phone:this.passForm.phone,type}).then(res=>{
            if(res.errCode==0){
              this.$message({
                message: '验证码发送成功',
                type: 'success'
              });
              this.startCountdown(); // 开始倒计时
            }
          })
        }else{
          this.$message({
            message: '请输入手机号',
            type: 'error'
          });
        }

      }
    },
  },
}
</script>

<style lang="scss" scoped>
.user-com{
  height: 60px; 
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.avatar-wrapper{
  display: flex;
  align-items: center;
  .user-avatar{
    vertical-align: middle;
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
  .user-info{
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-right: 10px;
    .user-name{
      font-size: 14px;
      line-height: 20px;
      color:#5C6075;
    }
    .user-role{
      font-size: 10px;
      line-height: 14px;
      color:#9FA5B3;
    }
  }
}


</style> 
<style lang="scss">
  .user-dialog{
    background: linear-gradient( 180deg, #DAFFF5 0%, #FFFFFF 10%);
    .el-dialog__body{
      padding-top: 0;
      padding-bottom: 0;
    }
    .user-info{
      .el-form-item{
        margin-bottom:15px!important;
      }
      .user-img{
        text-align: center;
        .block{
          .el-avatar{}
        }
        .upload-user-img{
          margin-top: 10px;
          .upload-btn{
            width: 80px;
            height: 30px;
            color: #0070FC;
            background: #FFFFFF;
            border-radius: 15px;
            border: 1px solid #0070FC;
          }
        }
      }
      .info-form{
        margin-top: 28px;
        padding-bottom: 12px;
        .el-form-item__label{
          line-height: 36px;
          color: #333333;
        }
        .w-280{
          width: 280px;
          height: 38px;
          background: #F9FAFC;
          border-radius: 4px;
        }
        .el-button{
          height: 30px;
          color: #333333; 
          margin-left: 10px;
        }
      }
    }

   .phone-info{
      text-align: center;
      .phone-form{
        margin: 0 38px;
      }
      .tip{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        margin-bottom: 50px;
        text-align: left;
      }
      .phone-ipt{
        width: 380px;
        height: 38px;
        background: #FFFFFF;
        border-radius: 4px;
        margin:0 auto 20px;
      }
      .code-ipt{
        width: 280px;
        height: 38px;
        background: #FFFFFF;
        border-radius: 4px;
      }
      .el-input__inner{
        height: 38px;
        line-height: 38px; 
      }
      .code-btn{
        width: 90px;
        height: 38px;
        padding: 0;
        margin-left: 10px;
        color: #0070FC;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #0070FC;

      }
    }
    .footer-btn{
      border-top: 1px solid #F2F3F5;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 39px;
      height: 58px;
      .save-btn{
        width: 80px;
        color: #FFFFFF;
        height: 38px;
        background: #0070FC;
        border-radius: 4px;
      }
      .cancle-btn{
        width: 80px;
        height: 38px;
        color: #333;
        background: #FFFFFF;
        border-radius: 4px;
      }
    }
    .pass-info{
      .pass-form{
        margin-top: 12px;
        .el-form-item__label{
          line-height: 36px;
          color: #333333;
        }
      }
    }

}
</style>