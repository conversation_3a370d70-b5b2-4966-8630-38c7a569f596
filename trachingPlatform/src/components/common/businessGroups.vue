<template>
  <!-- 业务下拉组件  -->
  <el-cascader v-model="optionsValues" :options="businessOptions" :props="selectProps" placeholder="请选择所属业务场景"
    filterable clearable @change="handleChangeBusiness" :style="searchform.style">
    <template slot-scope="{ data }">
      <span v-if="data.sort">({{ data.sort }})</span>
      <span>{{ data.name }}</span>
    </template>
  </el-cascader>
</template>
<script>
export default {
  props:
    [
      'searchform',
      'multiple',
    ],
  data() {
    return {
      dialogLable: false, // 设置标签的弹窗
      businessOptions: [], // 业务场景，
      optionsValues: [],
      selectProps: {
        label: 'name',
        value: 'id',
        children: 'children',
        emitPath: false,
        expandTrigger: 'hover',
        multiple: this.multiple,
      },
      isModel: true,
    }
  },
  created() {
    // 初始化业务场景选择
    this.initBusines(this.searchform)
    if (!this.searchform.optionsValues) {
      this.optionsValues = this.searchform.optionsValues;
    }
    // console.log('optionsValues', this.optionsValues);
  },
  methods: {
    // 初始化业务场景选择
    async initBusines() {
      if (this.$route.query.questionType == 20) {
        let businessGroup = await this.$Api.GetBusinessModuleFindTreeAll({ caseId: this.$route.query.id });
        this.businessOptions = businessGroup.data;
      } else {
        // let res = await this.$Api.GetGroupTypesTree({
        //   isTools: this.searchform.isTools,
        //   isModel: this.isModel
        // })
        // this.businessOptions = res.data
      }

    },
    handleChangeBusiness(val) {
      this.$emit('handleChangeBusiness', val)
    },
    // change(){
    //     this.$emit('change', this.businessOptions) 
    // }
  },
  watch: {
    'searchform': {
      handler(val) {
        this.optionsValues = val.optionsValues;
        if (val.hasOwnProperty('isModel')) {
          this.isModel = val.isModel;
        }
      },
      immediate: true,
      deep: true
    },
    // 'searchform.name': {
    //     handler(val) {
    //         this.searchform.name = val;
    //         this.initBusines(this.searchform);
    //     },
    //     deep: true,
    // }
  }
}
</script>


<style>
.input_w {
  /* width: 350px; */
}
</style>
