<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    :title="id ? '修改网页链接' : '粘贴网页链接'" 
    width="30%" 
    class="iframe-dialog"
  >
    <div class="form-item">
      <el-input v-model="src" placeholder="请输入http或https链接"></el-input>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">{{ id ? '更新' : '确认' }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'IframeDialog',
  props: {
    id: {
      type: String,
      default: null
    },
    oldData: {
      type: Object,
      default: () => ({
        src: ''
      })
    }
  },
  data() {
    return {
      dialogVisible: false,
      src: ''
    }
  },
  mounted() {
    this.open()
  },
  methods: {
    open() {
      this.dialogVisible = true
      this.src = this.oldData.src || ''
    },
    
    submitForm() {
      if (!this.src.trim()) {
        this.$message.warning('请输入链接地址')
        return
      }
      // 添加校验是否为http或https链接
      if (!/^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[a-zA-Z0-9#?&%=~_\-./]*)?$/.test(this.src)) {
        this.$message.warning('请输入正确的链接地址')
        return
      }

      // 触发提交事件，将数据传递给父组件
      this.$emit('submit', {
        src: this.src
      })
      
      // 关闭对话框
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.iframe-dialog .el-dialog__body {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
</style>

.form-item label {
  width: 80px !important;
  flex-shrink: 0 !important;
  line-height: 32px !important;
}

.form-item input {
  flex: 1 !important;
  padding: 8px 12px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}
</style>
