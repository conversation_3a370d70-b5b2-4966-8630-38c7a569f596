import Vue from 'vue'
import IframeDialog from './IframeDialog.vue'

export default {
  methods: {
    /**
     * 初始化H5卡片功能
     */
    initIframeFeature() {
      if (!this.editor) return

      // 初始化已存在的H5卡片
      setTimeout(() => {
        this.initExistingIframes()
      }, 1000)

    },

    /**
     * 注册H5卡片相关按钮
     */
    registerIframeButtons() {
      // 注册插入H5卡片按钮
      this.editor.ui.registry.addButton('insertIframe', {
        icon: 'browse',
        tooltip: '插入H5卡片',
        onAction: () => {
          this.openIframeDialog()
        }
      })
    },

    /**
     * 初始化已存在的H5卡片
     */
    initExistingIframes() {
      if (!this.editor || !this.editor.getBody()) return

      // 查找所有带有type-iframe类的H5卡片容器，无论是否已初始化
      const containers = this.editor.getBody().querySelectorAll('.type-iframe')
      
      if (!containers.length) return
      
      // 重新初始化H5卡片JS
      containers.forEach(container => {
        this.initIframeJs(container)
      })
    },

    /**
     * 初始化H5卡片的JS
     * @param {HTMLElement} container H5卡片容器DOM元素
     */
    initIframeJs(container) {
      if (!container) return

      // 获取H5卡片元素
      const src = container.getAttribute('data-iframe-data') || ''
      const overlay = container.querySelector('.iframe-overlay')
      const iframe = container.querySelector('iframe')
      const id = container.getAttribute('data-id')
      if (!overlay || !iframe) return

      // 定义事件处理函数
      const editBtnClickHandler = (e) => {
        e.stopPropagation()
        this.openIframeDialog(id, {src})
      }

      const deleteBtnClickHandler = (e) => {
        e.stopPropagation()
        container.remove()
        this.myValue = this.editor.getContent()
      }

      // const previewBtnClickHandler = (e) => {
      //   e.stopPropagation()
      //   if (container.classList.contains('preview-mode')) {
      //     // 退出预览模式
      //     container.classList.remove('preview-mode')
      //     iframe.style.width = '100%'
      //     iframe.style.height = '400px'
      //     e.target.textContent = '预览'
      //   } else {
      //     // 进入预览模式
      //     container.classList.add('preview-mode')
      //     iframe.style.width = '100%'
      //     iframe.style.height = '100vh'
      //     e.target.textContent = '缩放'
      //   }
      // }

      const newWindowBtnClickHandler = (e) => {
        e.stopPropagation()
        window.open(src, '_blank')
      }

      // 移除旧事件监听器并绑定新事件
      function removeAllEventListeners(element) {
        const clone = element.cloneNode(true)  // 创建元素的克隆
        element.parentNode.replaceChild(clone, element)  // 替换元素，这会移除所有事件监听器
        return clone
      }

      // 编辑按钮事件
      const editBtn = overlay.querySelector('.edit-iframe-btn')
      if (editBtn) {
        const newEditBtn = removeAllEventListeners(editBtn)
        newEditBtn.addEventListener('click', editBtnClickHandler)
      }

      // 删除按钮事件
      const deleteBtn = overlay.querySelector('.delete-iframe-btn')
      if (deleteBtn) {
        const newDeleteBtn = removeAllEventListeners(deleteBtn)
        newDeleteBtn.addEventListener('click', deleteBtnClickHandler)
      }

      // 预览按钮事件
      // const previewBtn = overlay.querySelector('.preview-iframe-btn')
      // if (previewBtn) {
      //   const newPreviewBtn = removeAllEventListeners(previewBtn)
      //   newPreviewBtn.addEventListener('click', previewBtnClickHandler)
      // }

      // 新窗口按钮事件
      const newWindowBtn = overlay.querySelector('.new-window-iframe-btn')
      if (newWindowBtn) {
        const newNewWindowBtn = removeAllEventListeners(newWindowBtn)
        newNewWindowBtn.addEventListener('click', newWindowBtnClickHandler)
      }
    },

    /**
     * 生成H5卡片HTML
     * @param {String} src H5卡片链接地址
     * @returns {String} H5卡片HTML
     */
    generateIframeHtml({src}) {
      const id = Date.now()
      return `
        <div class="iframe-wrapper type-iframe"  data-iframe="true" data-id="${id}" data-iframe-data='${src}'>
          <div class="iframe-container">
            <iframe src="${src}" width="100%" height="400" frameborder="0"></iframe>
          </div>
          <div class="iframe-overlay" >
            <button class="edit-iframe-btn">编辑</button>
            <button class="delete-iframe-btn">删除</button>
            <button class="new-window-iframe-btn">新窗口</button>
          </div>
        </div>
      `
      // 预览暂时去掉，打开新窗口即可
      // <button class="preview-iframe-btn">预览</button>
    },

    /**
     * 打开H5卡片弹窗
     * @param {String} id H5卡片ID，如果有则表示编辑现有卡片
     * @param {Object} oldData 旧的H5卡片数据
     */
    openIframeDialog(id = null, oldData = {}) {
      // 创建H5卡片弹窗实例
      const IframeDialogConstructor = Vue.extend(IframeDialog)
      const iframeDialogInstance = new IframeDialogConstructor({
        propsData: {
          id,
          oldData
        }
      })
      
      const container = document.createElement('div')
      document.body.appendChild(container)
      iframeDialogInstance.$mount(container)
      
      // 监听抽屉关闭事件
      iframeDialogInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })

      // 监听提交事件
      iframeDialogInstance.$on('submit', (data) => {
        const html = this.generateIframeHtml(data)

        if (id) {
          // 更新现有H5卡片
          const container = this.editor.dom.select(`[data-id="${id}"]`)[0]
          if (container) {
            this.editor.dom.setOuterHTML(container, html)
          }
        } else {
          // 插入新的H5卡片
          this.editor.execCommand('mceInsertContent', false, html)
        }

        // 更新编辑器内容
        this.myValue = this.editor.getContent()
        this.initExistingIframes()
      })
    }
  }
}