import { upload } from '../../util/upload'

export default {
  methods: {
    /**
     * 初始化已存在的图片
     */
    initExistingImage() {
      console.log('尝试初始化图片...', this.editor);
      // 检查编辑器body是否存在
      const editorBody = this.editor.getBody();
      
      if (!editorBody) {
        console.log('编辑器body未准备好，将延迟初始化图片');
        return;
      }
      
      // 查找所有带有type-image类的图片
      const imageWrappers = editorBody.querySelectorAll('.type-image');
      console.log('找到图片数量:', imageWrappers.length);

      function showBar(event) {
        event.stopPropagation();
        event.preventDefault();

        // 展示图片操作栏，包含删除图标，点击删除后，将图片容器整个结构删除
        const img = event.target;
        const imageWrapper = img.closest('.image-wrapper');
        
        // 检查是否已存在操作栏，如果存在则移除
        const existingBar = imageWrapper.querySelector('.image-operation-bar');
        if (existingBar) {
          existingBar.remove();
          return;
        }
        
        // 创建操作栏 - 浮动在图片上方，纯白背景带阴影
        const operationBar = document.createElement('div');
        operationBar.className = 'image-operation-bar';
        operationBar.style.cssText = 'position: absolute; top: -40px; left: 50%; transform: translateX(-50%); background-color: #ffffff; border-radius: 4px; padding: 5px; display: flex; z-index: 1000; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);';
        
        // 创建删除按钮 - 使用简洁风格的删除图标
        const deleteBtn = document.createElement('div');
        deleteBtn.className = 'image-delete-btn';
        deleteBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M832 256h-192V160c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v96H192c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM416 192h192v64H416v-64z m-16 728c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z" fill="#666666"></path></svg>';
        deleteBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';
        
        // 添加鼠标悬停效果
        deleteBtn.addEventListener('mouseover', function() {
          this.style.backgroundColor = '#f0f0f0';
        });
        deleteBtn.addEventListener('mouseout', function() {
          this.style.backgroundColor = 'transparent';
        });
        
        // 添加删除按钮点击事件
        deleteBtn.addEventListener('click', function(e) {
          e.stopPropagation();
          e.preventDefault();
          // 移除点击事件
          img.removeEventListener('click', showBar);
          // 删除整个图片容器
          if (imageWrapper) {
            imageWrapper.remove();
          }
        });
        
        // 创建预览大图按钮
        const previewBtn = document.createElement('div');
        previewBtn.className = 'image-preview-btn';
        previewBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z" fill="#666666"></path></svg>';
        previewBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';
        
        // 添加鼠标悬停效果
        previewBtn.addEventListener('mouseover', function() {
          this.style.backgroundColor = '#f0f0f0';
        });
        previewBtn.addEventListener('mouseout', function() {
          this.style.backgroundColor = 'transparent';
        });
        
        // 添加预览大图按钮点击事件
        previewBtn.addEventListener('click', function(e) {
          e.stopPropagation();
          e.preventDefault();
          // 获取当前图片
          const img = imageWrapper.querySelector('img');
          if (img) {
            // 创建预览层
            const previewOverlay = document.createElement('div');
            previewOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 9999;';
            
            // 创建预览图片
            const previewImg = document.createElement('img');
            previewImg.src = img.src;
            previewImg.style.cssText = 'max-width: 90%; max-height: 90%; object-fit: contain;';
            
            // 添加点击事件关闭预览
            previewOverlay.addEventListener('click', function() {
              document.body.removeChild(previewOverlay);
            });
            
            // 将预览图片添加到预览层
            previewOverlay.appendChild(previewImg);
            
            // 将预览层添加到body
            document.body.appendChild(previewOverlay);
          }
        });
        
        // 将所有按钮添加到操作栏
        operationBar.appendChild(previewBtn);
        operationBar.appendChild(deleteBtn);
        
        // 将操作栏添加到图片容器
        imageWrapper.appendChild(operationBar);
      }

      // 为每个图片添加点击事件
      imageWrappers.forEach(img => {
        // console.log(img);
        img.removeEventListener('click', showBar);
        img.addEventListener('click', showBar);
        
        // 阻止图片的默认点击和鼠标按下事件
        img.addEventListener('mousedown', function(e) {
          e.stopPropagation();
          e.preventDefault();
        });
      });
    },

    /**
     * 生成图片HTML
     * @param {String} uploadedUrl 上传的图片URL
     * @returns {String} 图片HTML
     */
    generateimageHtml(uploadedUrl) {
      return `
        <div class="image-wrapper" style="position: relative; width: 100%; margin: 10px auto;display: flex;justify-content: center;">
            <img class="type-image" style="width:auto;max-width:100%;height:300px; cursor:pointer;" src="${uploadedUrl}" onclick="event.stopPropagation(); event.preventDefault();" onmousedown="event.stopPropagation(); event.preventDefault();">
            </img>
        </div>
      `;
    },

    /**
     * 打开文件选择器并上传图片
     */
    openImageDialog() {
      // 创建一个隐藏的文件输入框
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = 'image/*';
      fileInput.style.display = 'none';
      document.body.appendChild(fileInput);
      
      // 监听文件选择事件
      fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        console.log(file);
        
        if (file) {
          this.editor.focus();
          setTimeout(() => {
          let savedBookmark = this.editor.selection.getBookmark()
          // 上传文件
          upload('cu', 'image', file, url => {
            const html = this.generateimageHtml(url);

            this.editor.focus();
            this.editor.selection.moveToBookmark(savedBookmark);

            this.editor.execCommand('mceInsertContent', false, html);
            this.myValue = this.editor.getContent();
            // 初始化新插入的图片，添加事件监听器
            // this.initExistingImage();
          },error => {
            console.error('图片上传失败:', error);
           })
          }, 50);
        }
        // 移除文件输入框
        document.body.removeChild(fileInput);
      });
      
      // 触发文件选择框
      fileInput.click();
    }
  }
}