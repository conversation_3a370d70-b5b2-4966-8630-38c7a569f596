.website-wrapper {
  position: relative;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.website {
  width: 100%;
  display: flex;
  background: #fff;
  transition: transform 0.3s;
  cursor: pointer;
}

.website:hover {
  transform: translateY(-2px);
}

.website-image {
  flex: 0 0 200px;
  height: 150px;
  overflow: hidden;
}

.website-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.website-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.website-title {
  font-size: 18px;
  color: #333;
  margin: 0 0 10px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.website-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.website-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  z-index: 1;
}

.website-wrapper:hover .website-overlay {
  opacity: 1 !important;
  pointer-events: auto !important;
}

.edit-website-btn {
  padding: 6px 12px;
  font-size: 14px;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.delete-website-btn {
  margin-left: 10px;
  padding: 6px 12px;
  font-size: 14px;
  background-color: #ffffff;
  border: none;
  border-radius: 4px;
  color: #000000;
  cursor: pointer;
}
