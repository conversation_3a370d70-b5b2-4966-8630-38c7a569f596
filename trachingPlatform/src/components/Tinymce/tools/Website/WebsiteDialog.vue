<template>
  <div class="website-dialog">
    <div class="website-form">
      <div class="left-section">
        <div class="image-upload">
          <div 
            class="upload-area" 
            :class="{'has-image': imageUrl}"
            @click="triggerUpload"
          >
            <img v-if="imageUrl" :src="imageUrl" class="preview-image">
            <div v-else class="upload-placeholder">
              <span class="upload-icon">+</span>
            </div>
          </div>
          <input 
            type="file" 
            ref="fileInput" 
            accept="image/*" 
            style="display: none"
            @change="handleFileChange"
          >
          <p class="upload-tip">点击上传缩略图</p>
        </div>
      </div>
      <div class="right-section">
        <div class="form-item">
          <label>网站地址：</label>
          <input 
            type="url" 
            v-model="url" 
            placeholder="请输入网站地址（以http://或https://开头）"
          >
        </div>
        <div class="form-item">
          <label>标题：</label>
          <input type="text" v-model="title" placeholder="请输入标题">
        </div>
        <div class="form-item description-item">
          <label>描述：</label>
          <textarea v-model="description" placeholder="请输入描述" rows="8"></textarea>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import config from '../../util/glob.js'
import { upload } from '../../util/upload'

export default {
  name: 'WebsiteDialog',
  props: {
    editor: {
      type: Object,
      required: true
    },
    target: {
      type: [Object, HTMLElement],
      default: null
    },
    oldData: {
      type: Object,
      default: () => ({
        imageUrl: '',
        title: '',
        description: '',
        url: ''
      })
    }
  },
  data() {
    return {
      imageUrl: this.oldData.imageUrl || '',
      title: this.oldData.title || '',
      description: this.oldData.description || '',
      url: this.oldData.url || '',
      uniqueClass: 'website-' + Date.now()
    }
  },
  methods: {
    triggerUpload() {
      this.$refs.fileInput.click()
    },
    async handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return
      
      try {
        const success = (url) => {
          this.imageUrl = url
        }
        const failure = (error) => {
          this.editor.notificationManager.open({
            text: '图片上传失败',
            type: 'error'
          })
          console.error('上传失败', error)
        }
        
        await upload('cu', 'Image', file, success, failure)

      } catch (error) {
        console.error('上传失败', error)
      }
    },
    generateWebsiteHtml() {
      return `
        <div class="website-wrapper" contenteditable="false" data-website="true" data-info='${JSON.stringify({
          imageUrl: this.imageUrl,
          title: this.title,
          description: this.description,
          url: this.url
        })}'>
          <div class="website ${this.uniqueClass}">
            <div class="website-image">
              <img src="${this.imageUrl}" alt="${this.title}">
            </div>
            <div class="website-content">
              <h3 class="website-title">${this.title}</h3>
              <p class="website-description">${this.description}</p>
            </div>
          </div>
          <div class="website-overlay" contenteditable="false">
            <button class="edit-website-btn">编辑</button>
            <button class="delete-website-btn">删除</button>
          </div>
        </div>
      `
    },
    openDialog() {
      this.editor.windowManager.open({
        title: this.target ? '修改H5网站' : '插入H5网站',
        size: 'medium',
        sandbox: 'allow-popups',
        body: {
          type: 'panel',
          items: [
            {
              type: 'htmlpanel',
              html: '<div id="website-dialog-container"></div>'
            }
          ]
        },
        buttons: [
          { type: 'cancel', text: '取消' },
          { type: 'submit', text: this.target ? '更新' : '插入' }
        ],
        onSubmit: (api) => {
          if (!this.imageUrl) {
            this.editor.notificationManager.open({
              text: '请上传图片',
              type: 'warning'
            })
            return
          }

          if (!this.url) {
            this.editor.notificationManager.open({
              text: '请输入网站地址',
              type: 'warning'
            })
            return
          }

          if (!this.url.startsWith('http://') && !this.url.startsWith('https://')) {
            this.editor.notificationManager.open({
              text: '网站地址必须以http://或https://开头',
              type: 'warning'
            })
            return
          }

          if (!this.title.trim()) {
            this.editor.notificationManager.open({
              text: '请输入标题',
              type: 'warning'
            })
            return
          }

          if (!this.description.trim()) {
            this.editor.notificationManager.open({
              text: '请输入描述',
              type: 'warning'
            })
            return
          }

          const html = this.generateWebsiteHtml()

          if (this.target) {
            this.editor.dom.setOuterHTML(this.target, html)
          } else {
            this.editor.insertContent(html)
          }

          api.close()
        }
      })

      this.$nextTick(() => {
        const container = document.getElementById('website-dialog-container')
        if (container) {
          this.$mount(container)
        }
      })
    }
  }
}
</script>

<style>
.website-dialog {
  padding: 20px !important;
  min-width: 500px !important;
}

.website-form {
  display: flex !important;
  gap: 30px !important;
  width: 100% !important;
}

.left-section {
  flex: 0 0 160px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.right-section {
  flex: 1 !important;
}

.image-upload {
  width: 100% !important;
  text-align: center !important;
}

.upload-area {
  width: 160px !important;
  height: 160px !important;
  border: 2px dashed #ddd !important;
  border-radius: 8px;
  cursor: pointer !important;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 8px !important;
}

.upload-area:hover {
  border-color: #007bff !important;
}

.upload-area.has-image {
  border-style: solid !important;
}

.preview-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.upload-placeholder {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

.upload-icon {
  font-size: 40px !important;
  color: #ddd !important;
  transition: color 0.3s !important;
}

.upload-area:hover .upload-icon {
  color: #007bff !important;
}

.upload-tip {
  font-size: 12px !important;
  color: #999 !important;
  margin: 0 !important;
  text-align: center !important;
}

.form-item {
  margin-bottom: 20px !important;
}

.form-item label {
  display: block;
  margin-bottom: 8px !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.form-item input,
.form-item textarea {
  width: 100% !important;
  padding: 8px 12px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  transition: border-color 0.3s !important;
}

.form-item input:focus,
.form-item textarea:focus {
  border-color: #007bff !important;
  outline: none !important;
}

.description-item {
  margin-top: 30px !important;
}

.description-item textarea {
  resize: vertical;
  min-height: 120px !important;
}
</style>
