import WebsiteDialog from './WebsiteDialog.vue'
import Vue from 'vue'

// 插入H5网站功能菜单
export const websiteEl = (editor) => {
  editor.ui.registry.addButton('insertWebsite', {
    icon: 'browse',
    tooltip: '插入H5网站',
    onAction: () => {
      WEBSITE_FUNC.openWebsiteDialog(editor)
    }
  })
}

// 初始化现有的H5网站
export const websiteFirstInit = (editor) => {
  editor
    .getBody()
    .querySelectorAll('.website-wrapper')
    .forEach((websiteEl) => initWebsiteJs(editor, websiteEl))
}

// 初始化新插入的H5网站
export const websiteOtherInit = (editor) => {
  editor
    .getBody()
    .querySelectorAll('.website-wrapper:not([data-initialized])')
    .forEach((websiteEl) => {
      websiteEl.setAttribute('data-initialized', 'true')
      initWebsiteJs(editor, websiteEl)
    })
}

// 初始化H5网站的JS
export const initWebsiteJs = (editor, websiteEl) => {
  if (!websiteEl) return

  const website = websiteEl.querySelector('.website')

  // 点击整个区域跳转（这里可以根据需要添加跳转逻辑）
  if (website) {
    website.addEventListener('click', () => {
      const wrapper = website.closest('.website-wrapper')
      const data = JSON.parse(wrapper.getAttribute('data-info') || '{}')
      if (data.url) {
        console.log('处于安全考虑，富文本内预览不允许执行js通过iframe打开新窗口')
        window.open(data.url, '_blank')
      }
    })
  }

  // 编辑按钮事件
  const editBtn = websiteEl.querySelector('.edit-website-btn')
  if (editBtn) {
    editBtn.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡
      const wrapper = editBtn.closest('.website-wrapper')
      const data = JSON.parse(wrapper.getAttribute('data-info') || '{}')
      WEBSITE_FUNC.openWebsiteDialog(editor, wrapper, data)
    }
  }

  // 删除按钮事件
  const deleteBtn = websiteEl.querySelector('.delete-website-btn')
  if (deleteBtn) {
    deleteBtn.onclick = (e) => {
      e.stopPropagation() // 阻止事件冒泡

      // 移除点击事件
      if (website) {
        website.removeEventListener('click', () => {
          console.log('Website clicked')
        })
      }

      // 移除编辑按钮事件
      if (editBtn) {
        editBtn.onclick = null
      }

      // 移除删除按钮事件
      deleteBtn.onclick = null

      // 移除初始化标记
      websiteEl.removeAttribute('data-initialized')

      // 删除DOM元素
      websiteEl.remove()
    }
  }
}

// 预览弹窗iframe注入H5网站样式和脚本
export const websiteInitIframe = () => {
  setTimeout(() => {
    const iframe = document.querySelector('.tox-dialog iframe')
    if (iframe && iframe.contentDocument) {
      // 注入样式
      const style = iframe.contentDocument.createElement('style')
      style.textContent = `
        .website-overlay {
          display: none;
        }
      `
      iframe.contentDocument.head.appendChild(style)

      // 插入脚本
      const script = iframe.contentDocument.createElement('script')
      script.textContent = `
        (function() {
          function initWebsite(websiteEl) {
            if (!websiteEl) return;
            
            const website = websiteEl.querySelector('.website');
            
            if (website) {
              website.addEventListener('click', () => {
                const wrapper = website.closest('.website-wrapper');
                const data = JSON.parse(wrapper.getAttribute('data-info') || '{}');
                if (data.url) {
                  window.open(data.url, '_blank');
                }
              });
            }
          }
          
          document.querySelectorAll('.website-wrapper').forEach(initWebsite);
        })();
      `
      iframe.contentDocument.body.appendChild(script)
    }
  }, 300)
}

// H5网站功能菜单
export const WEBSITE_FUNC = {
  openWebsiteDialog(editor, target, oldData = {}) {
    const WebsiteDialogConstructor = Vue.extend(WebsiteDialog)
    const websiteDialogInstance = new WebsiteDialogConstructor({
      propsData: {
        editor,
        target: target instanceof HTMLElement ? target : null,
        oldData
      }
    })

    websiteDialogInstance.openDialog()
  }
}
