.fold-panel-container {
  position: relative;
  width: 100%;
  margin: 10px 0;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.fold-panel {
  width: 100%;
}

.fold-panel-header {
  padding: 12px 16px;
  background: #fafafa;
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
  position: relative;
}

.fold-panel-title {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}

.fold-panel-icon {
  font-size: 12px;
  color: #666;
  transition: transform 0.3s;
}

.fold-panel-content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s;
}

.fold-panel-expanded .fold-panel-content {
  padding: 16px;
  max-height: 1000px;
}

.fold-panel-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.fold-panel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  z-index: 1;
}

.fold-panel-container:hover .fold-panel-overlay,
.fold-panel-container.type-panel:hover .fold-panel-overlay {
  opacity: 1 !important;
  pointer-events: auto !important;
}

.edit-fold-panel-btn {
  padding: 6px 12px;
  font-size: 14px;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.delete-fold-panel-btn {
  margin-left: 10px;
  padding: 6px 12px;
  font-size: 14px;
  background-color: #ffffff;
  border: none;
  border-radius: 4px;
  color: #000000;
  cursor: pointer;
}
