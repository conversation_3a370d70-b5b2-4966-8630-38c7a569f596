import Vue from 'vue'
import FoldPanelDialog from './FoldPanelDialog.vue'

export default {
  methods: {
    /**
     * 初始化折叠面板功能
     */
    initFoldPanelFeature() {
      if (!this.editor) return

      // 注册按钮
      // this.registerFoldPanelButtons()
      setTimeout(() => {
        this.initExistingFoldPanels()
      }, 1000); 
      // 初始化已存在的折叠面板

      // 监听编辑器初始化
      // this.editor.on('init', () => {
      //   // 初始化已存在的折叠面板
      //   this.initExistingFoldPanels()
      // })

      // // 监听内容变化，初始化新插入的折叠面板
      // this.editor.on('NodeChange', () => {
      //   this.initExistingFoldPanels()
      // })

    },

    /**
     * 注册折叠面板相关按钮
     */
    registerFoldPanelButtons() {
      // 注册插入折叠面板按钮
      this.editor.ui.registry.addButton('insertFoldPanel', {
        icon: 'copy',
        tooltip: '插入折叠面板',
        onAction: () => {
          this.openFoldPanelDialog()
        }
      })
    },

    /**
     * 初始化已存在的折叠面板
     */
    initExistingFoldPanels() {
      if (!this.editor || !this.editor.getBody()) return

      // 查找所有带有type-panel类的折叠面板容器，无论是否已初始化
      const containers = this.editor.getBody().querySelectorAll('.type-panel')
      
      if (!containers.length) return
      
      // 重新初始化折叠面板JS
      containers.forEach(container => {
        this.initFoldPanelJs(container)
      })
    },

    /**
     * 初始化折叠面板的JS
     * @param {HTMLElement} container 折叠面板容器DOM元素
     */
    initFoldPanelJs(container) {
      if (!container ) return

      // 获取折叠面板元素
      const panel = container.querySelector('.fold-panel')
      const header = container.querySelector('.fold-panel-header')
      const content = container.querySelector('.fold-panel-content')
      const editBtn = container.querySelector('.edit-fold-panel-btn')
      const deleteBtn = container.querySelector('.delete-fold-panel-btn')
      // console.log(panel ,header ,content);
      
      if (!panel || !header || !content) return

      function removeAllEventListeners(element) {
        const clone = element.cloneNode(true);  // 创建元素的克隆
        element.parentNode.replaceChild(clone, element);  // 替换元素，这会移除所有事件监听器
        return clone;
      }

      if (header) {
        // 定义事件处理函数
        const headerClickHandler = (e) => {
          // 切换折叠面板的展开/收起状态
          panel.classList.toggle('fold-panel-expanded')
          
          // 更新图标
          const icon = header.querySelector('.fold-panel-icon')
          if (icon) {
            if (panel.classList.contains('fold-panel-expanded')) {
              icon.innerHTML = '&#9660;' // 向下箭头
            } else {
              icon.innerHTML = '&#9654;' // 向右箭头
            }
          }
        }
        const newHeader = removeAllEventListeners(header);
        newHeader.addEventListener('click', headerClickHandler);
        // header.removeEventListener('click', headerClickHandler)
        // header.addEventListener('click', headerClickHandler)
      }

      // 绑定编辑按钮事件
      if (editBtn) {
        // 定义编辑按钮事件处理函数
        const editBtnClickHandler = (e) => {
          e.stopPropagation()
          const id = container.getAttribute('data-id')
          const title = container.querySelector('.fold-panel-title').innerText
          const content = container.querySelector('.fold-panel-text').innerText
          console.log(id, title, content);
          this.openFoldPanelDialog(id, { title, content })
        }
        // 清除所有事件监听器
        
        const newEditBtn = removeAllEventListeners(editBtn);
        newEditBtn.addEventListener('click', editBtnClickHandler);
        // editBtn.removeEventListener('click', editBtnClickHandler)
        // editBtn.addEventListener('click', editBtnClickHandler)
      }

      // 绑定删除按钮事件
      if (deleteBtn) {
        // 定义删除按钮事件处理函数
        const deleteBtnClickHandler = (e) => {
          e.stopPropagation()
          container.remove()
          this.myValue = this.editor.getContent()
        }
        const newDeleteBtn = removeAllEventListeners(deleteBtn);
        newDeleteBtn.addEventListener('click', deleteBtnClickHandler);
        // deleteBtn.removeEventListener('click', deleteBtnClickHandler)
        // deleteBtn.addEventListener('click', deleteBtnClickHandler)
      }
    },

    /**
     * 生成折叠面板HTML
     * @param {String} title 折叠面板标题
     * @param {String} content 折叠面板内容
     * @returns {String} 折叠面板HTML
     */
    generateFoldPanelHtml(title, content) {
      const id = new Date().getTime()
      return `
        <div class="fold-panel-container type-panel" data-id="${id}" data-fold-panel="true">
          <div class="fold-panel">
            <div class="fold-panel-header">
              <span class="fold-panel-title">${title}</span>
              <span class="fold-panel-icon">&#9654;</span>
            </div>
            <div class="fold-panel-content">
              <div class="fold-panel-text">${content}</div>
            </div>
          </div>
          <div class="fold-panel-overlay" >
            <button class="edit-fold-panel-btn">编辑面板</button>
            <button class="delete-fold-panel-btn">删除面板</button>
          </div>
        </div>
      `
    },

    /**
     * 打开折叠面板弹窗
     * @param {String} id 折叠面板ID，如果有则表示编辑现有面板
     * @param {Object} oldData 旧的折叠面板数据
     */
    openFoldPanelDialog(id = null, oldData = {}) {
        // 创建折叠面板弹窗实例
        const FoldPanelDialogConstructor = Vue.extend(FoldPanelDialog)
        const foldPanelDialogInstance = new FoldPanelDialogConstructor({
          propsData: {
            id,
            oldData
          }
        })
        
        const container = document.createElement('div')
      document.body.appendChild(container)
      foldPanelDialogInstance.$mount(container)
      
      // 监听抽屉关闭事件
      foldPanelDialogInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })
        // 监听提交事件
      foldPanelDialogInstance.$on('submit', (data) => {
          const { title, content } = data
          const html = this.generateFoldPanelHtml(title, content)

          if (id) {
            // 更新现有折叠面板
            const container = this.editor.dom.select(`[data-id="${id}"]`)[0]
            if (container) {
              this.editor.dom.setOuterHTML(container, html)
            }
          } else {
            // 插入新的折叠面板
            this.editor.execCommand('mceInsertContent', false, html)
          }

          // 更新编辑器内容
          this.myValue = this.editor.getContent()
          this.initExistingFoldPanels()
        })

    }
  }
}