<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    :title="id ? '修改折叠面板' : '插入折叠面板'" 
    width="40%" 
    class="fold-panel-dialog"
  >
    <div class="form-item">
      <label>标题：</label>
      <el-input v-model="title" placeholder="请输入标题"></el-input>
    </div>
    <div class="form-item">
      <label>内容：</label>
      <el-input type="textarea" v-model="content" placeholder="请输入内容" :rows="6"></el-input>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">{{ id ? '更新' : '插入' }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'FoldPanelDialog',
  props: {
    id: {
      type: String,
      default: null
    },
    oldData: {
      type: Object,
      default: () => ({
        title: '',
        content: ''
      })
    }
  },
  data() {
    return {
      dialogVisible: false,
      title: '',
      content: '',
    }
  },
  mounted() {
    this.open()
  },
  methods: {
    open() {
      this.dialogVisible = true
      this.title = this.oldData.title || ''
      this.content = this.oldData.content || ''
    },
    
    submitForm() {
      if (!this.title.trim()) {
        this.$message.warning('请输入标题')
        return
      }

      if (!this.content.trim()) {
        this.$message.warning('请输入内容')
        return
      }

      // 触发提交事件，将数据传递给父组件
      this.$emit('submit', {
        title: this.title,
        content: this.content,
      })
      
      // 关闭对话框
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.fold-panel-dialog .el-dialog__body {
  padding: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
</style>
