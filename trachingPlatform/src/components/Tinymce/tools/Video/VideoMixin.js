import { upload } from '../../util/upload'

export default {
    data() {
        return {
            loadingInstance: null, // 用于存储 loading 实例
            videoObserver: null, // 用于存储 IntersectionObserver 实例
            currentlyPlayingVideo: null // 用于存储当前正在播放的视频
        };
    },
    mounted() {
        // 初始化 IntersectionObserver
        this.videoObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const video = entry.target;
                if (entry.isIntersecting) {
                    // 视频在视口中，继续播放
                    // if (!video.paused) return;

                    if (this.currentlyPlayingVideo && this.currentlyPlayingVideo !== video) {
                        this.currentlyPlayingVideo.pause();
                    }
                    this.currentlyPlayingVideo = video;
                    // video.play().catch(error => {
                    //     console.error('视频播放失败:', error);
                    // });
                    //
                } else {
                    // 视频超出视口，暂停播放
                    if (video.paused) return;
                    video.pause();
                    if (this.currentlyPlayingVideo === video) {
                        this.currentlyPlayingVideo = null;
                    }
                }
            });
        });
    },
    beforeDestroy() {
        // 组件销毁时断开观察
        if (this.videoObserver) {
            this.videoObserver.disconnect();
        }
    },
    methods: {
        /**
         * 初始化已存在的视频
         */
        initExistingVideo() {
            console.log('尝试初始化视频...', this.editor);
            // 检查编辑器body是否存在
            const editorBody = this.editor.getBody();

            if (!editorBody) {
                console.log('编辑器body未准备好，将延迟初始化视频');
                return;
            }

            // 查找所有带有type-video类的视频
            const videoWrappers = editorBody.querySelectorAll('.type-video');
            // console.log('找到视频数量:', videoWrappers.length);

            function showBar(event) {
                event.stopPropagation();
                event.preventDefault();

                // 展示视频操作栏，包含删除图标，点击删除后，将视频容器整个结构删除
                const img = event.target;
                const videoWrapper = img.closest('.video-wrapper');

                // 检查是否已存在操作栏，如果存在则移除
                const existingBar = videoWrapper.querySelector('.video-operation-bar');
                if (existingBar) {
                    existingBar.remove();
                    return;
                }

                // 创建操作栏 - 浮动在视频上方，纯白背景带阴影
                const operationBar = document.createElement('div');
                operationBar.className = 'video-operation-bar';
                operationBar.style.cssText = 'position: absolute; top: -40px; left: 50%; transform: translateX(-50%); background-color: #ffffff; border-radius: 4px; padding: 5px; display: flex; z-index: 1000; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);';

                // 创建删除按钮 - 使用简洁风格的删除图标
                const deleteBtn = document.createElement('div');
                deleteBtn.className = 'video-delete-btn';
                deleteBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M832 256h-192V160c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v96H192c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM416 192h192v64H416v-64z m-16 728c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z" fill="#666666"></path></svg>';
                deleteBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';

                // 添加鼠标悬停效果
                deleteBtn.addEventListener('mouseover', function() {
                    this.style.backgroundColor = '#f0f0f0';
                });
                deleteBtn.addEventListener('mouseout', function() {
                    this.style.backgroundColor = 'transparent';
                });

                // 添加删除按钮点击事件
                deleteBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    // 移除点击事件
                    img.removeEventListener('click', showBar);
                    // 删除整个视频容器
                    if (videoWrapper) {
                        videoWrapper.remove();
                    }
                });

                // 创建预览大图按钮
                const previewBtn = document.createElement('div');
                previewBtn.className = 'video-preview-btn';
                previewBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z" fill="#666666"></path></svg>';
                previewBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';

                // 添加鼠标悬停效果
                previewBtn.addEventListener('mouseover', function() {
                    this.style.backgroundColor = '#f0f0f0';
                });
                previewBtn.addEventListener('mouseout', function() {
                    this.style.backgroundColor = 'transparent';
                });

                // 添加预览大图按钮点击事件
                previewBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                    // 获取当前视频
                    const video = videoWrapper.querySelector('video');
                    if (video) {
                        // 创建预览层
                        const previewOverlay = document.createElement('div');
                        previewOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 9999;';

                        // 创建预览视频
                        const previewVideo = document.createElement('video');
                        console.log(video);

                        // 获取视频源地址 - 从source标签获取
                        const sourceElement = video.querySelector('source');
                        if (sourceElement && sourceElement.src) {
                            previewVideo.src = sourceElement.src;
                        } else {
                            // 如果没有source标签，尝试直接获取video的src
                            previewVideo.src = video.src;
                        }
                        previewVideo.style.cssText = 'max-width: 90%; max-height: 90%; object-fit: contain;';
                        previewVideo.controls = true;

                        // 添加点击事件关闭预览
                        previewOverlay.addEventListener('click', function() {
                            document.body.removeChild(previewOverlay);
                        });

                        // 将预览视频添加到预览层
                        previewOverlay.appendChild(previewVideo);

                        // 将预览层添加到body
                        document.body.appendChild(previewOverlay);
                    }
                });

                // 将所有按钮添加到操作栏
                operationBar.appendChild(previewBtn);
                operationBar.appendChild(deleteBtn);

                // 将操作栏添加到视频容器
                videoWrapper.appendChild(operationBar);
            }

            // 为每个视频添加点击事件
            videoWrappers.forEach(video => {
                // console.log(img);
                video.removeEventListener('click', showBar);
                video.addEventListener('click', showBar);

                // 阻止视频的默认点击和鼠标按下事件
                video.addEventListener('mousedown', function(e) {
                    e.stopPropagation();
                    e.preventDefault();
                });
                // 让 IntersectionObserver 开始观察视频元素
                this.videoObserver.observe(video);

                // 监听视频播放事件
                video.addEventListener('play', () => {
                    console.log("video", video)
                    if (this.currentlyPlayingVideo && this.currentlyPlayingVideo !== video) {
                        this.currentlyPlayingVideo.pause();
                    }
                    this.currentlyPlayingVideo = video;
                });
            });
        },

        /**
         * 生成视频HTML
         * @param {String} uploadedUrl 上传的视频URL
         * @returns {String} 视频HTML
         */
        generatevideoHtml(uploadedUrl) {
            return `
        <div class="video-wrapper" style="position: relative; width: 100%; margin: 10px auto;display: flex;justify-content: center;">
             <video  class="type-video" controls  style="width:auto;max-width:100%;height:300px; cursor:pointer;"  onclick="event.stopPropagation(); event.preventDefault();" onmousedown="event.stopPropagation(); event.preventDefault();">
              <source src="${uploadedUrl}" type="video/mp4">
              您的浏览器不支持视频标签。
            </video>
        </div>
      `;
        },

        /**
         * 打开文件选择器并上传视频
         */
        openVideoDialog() {
            // 创建一个隐藏的文件输入框
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'video/*';
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);



            // 监听文件选择事件
            fileInput.addEventListener('change', (event) => {
                const file = event.target.files[0];
                console.log(file);
                if (file) {
                    // 显示 loading
                    this.loadingInstance = this.$loading({
                        lock: true,
                        text: '视频上传中，请稍候...',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    this.editor.focus();
                    setTimeout(() => {
                        let savedBookmark = this.editor.selection.getBookmark()

                        // 上传文件
                        upload('cu', 'video', file, url => {
                            const html = this.generatevideoHtml(url);
                            // console.log(savedBookmark);

                            this.editor.focus();
                            this.editor.selection.moveToBookmark(savedBookmark);

                            this.editor.execCommand('mceInsertContent', false, html);
                            this.myValue = this.editor.getContent();
                            // 初始化新插入的视频，添加事件监听器
                            // this.initExistingVideo();
                            // 隐藏 loading
                            if (this.loadingInstance) {
                                this.loadingInstance.close();
                            }
                        }, error => {
                            // 隐藏
                            this.$message({
                                message: '视频上传失败',
                                type: 'error'
                            });
                            if (this.loadingInstance) {
                                this.loadingInstance.close();
                            }
                            console.error('视频上传失败:', error);
                        })
                    }, 50);
                }
                // 移除文件输入框
                document.body.removeChild(fileInput);
            });

            // 触发文件选择框
            fileInput.click();
        }
    }
}