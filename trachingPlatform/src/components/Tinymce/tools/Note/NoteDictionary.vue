<template>
  <el-drawer title="重点词词典" :visible.sync="drawerVisible" direction="rtl" size="50%">
    <div class="note-list">
      <el-dialog :visible.sync="dialogVisible" title="编辑重点词" width="40%" :modal="false" class="note-add-dialog">
        <div class="dialog-item">
          <span class="label">文本内容:</span>
          <!-- <el-input type="text" placeholder="请输入文本" v-model="addForm.word"></el-input> -->
          {{ addForm.word }}
        </div>
        <div class="dialog-item">
          <span class="label">别名:</span>
          <el-input type="text" placeholder="请输入别名" v-model="addForm.alias"></el-input>
        </div>
        <div class="dialog-item">
          <span class="label">释义:</span>
          <el-input type="textarea" :rows="4" placeholder="请输入释义" v-model="addForm.paraphrase"></el-input>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </el-dialog>

      <div v-for="(item, index) in localNoteList" :key="index" class="note-item p-x-20 p-t-10">
        <div class="note-title"><strong>{{ item.word || '未命名重点词' }}</strong></div>
        <div class="note-content">
          <div v-if="item.alias">别名: {{ item.alias }}</div>
          <div v-if="item.paraphrase">释义: {{ item.paraphrase }}</div>
        </div>
        <div class="note-actions">
          <el-button type="text" size="small" @click="editNote(item)" title="编辑">
            编辑
          </el-button>
          <el-button type="text" size="small" @click="deleteNote(item)" title="删除">
            删除
          </el-button>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'NoteDictionary',
  props: {
    nowSection: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      drawerVisible: true,
      dialogVisible: false,
      addForm: {
        word: '',
        alias: '',
        paraphrase: ''
      },
      localNoteList: []
    }
  },
  mounted() {
    this.getNoteList();
  },
  methods: {
    addNote() {
      this.dialogVisible = true;
      this.addForm = {
        word: '',
        alias: '',
        paraphrase: ''
      };
    },
    editNote(item) {
      this.dialogVisible = true;
      this.addForm = {
        id: item.id,
        word: item.word,
        alias: item.alias,
        paraphrase: item.paraphrase
      };
    },
    async deleteNote(item) {
      try {
        await this.$confirm('确认删除该重点词吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await this.$api.DeleteTextbookDict({ id: item.id });
        this.$message.success('删除成功');
        this.$emit('deleteNote', item.id);
        this.getNoteList(); // 刷新本地列表
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(error.message || '删除失败');
        }
      }
    },
    async submitForm() {
      if (!this.addForm.word) {
        this.$message.error('请输入文本内容');
        return;
      }

      try {
        const params = {
          textbookId: this.nowSection.textbookId,
          sectionId: this.nowSection.sectionId,
          word: this.addForm.word,
          alias: this.addForm.alias,
          paraphrase: this.addForm.paraphrase
        };

        if (this.addForm.id) {
          // 编辑逻辑
          await this.$api.UpdateTextbookDict({
            id: this.addForm.id,
            ...params
          });
          this.getNoteList();
        } else {
          // 新增逻辑
          await this.$api.CreateTextbookDict(params);
          this.getNoteList();
        }
        
        this.dialogVisible = false;
        this.$emit('refresh');
      } catch (error) {
        this.$message.error(error.message || '操作失败');
      }
    },
    async getNoteList() {
      try {
        const res = await this.$api.GetTextbookDict({
          textbookId: this.nowSection.textbookId,
          sectionId: this.nowSection.sectionId,
          PageIndex: 1,
          PageSize: 1000
        });
        
        this.localNoteList = res.data?.items || [];
      } catch (error) {
        console.error('获取重点词列表失败:', error);
        this.$message.error(error.message || '获取重点词列表失败');
      }
    },
    openDialog() {
      this.drawerVisible = true;
      this.getNoteList();
    }
  }
}
</script>

<style scoped>

.note-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.note-title {
  font-size: 14px;
}

.note-content {
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.note-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.note-add-dialog .el-message-box__content {
  padding: 20px;
}

.dialog-item {
  margin-bottom: 15px;
}

.dialog-item .label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.dialog-item .el-input {
  width: 100%;
}

.dialog-item .el-textarea {
  width: 100%;
}
</style>