.note-mark {
  background-color: #EDF2FF !important;
  border-radius: 2px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  position: relative;
}

.note-mark:hover {
  background-color: #ffe7ba !important;
}

.note-tooltip {
  position: absolute !important;
  z-index: 1002 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: #fff !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  min-width: 400px !important;
  max-width: 600px !important;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  pointer-events: none !important; /* 修改为none，使鼠标事件穿透tooltip */
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  white-space-collapse: collapse;
}
.note-tooltip[data-placement="top"]::before {
  content: '' !important;
  position: absolute !important;
  bottom: -6px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border-width: 6px 6px 0 !important;
  border-style: solid !important;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent !important;
}

.note-tooltip[data-placement="bottom"]::before {
  content: '' !important;
  position: absolute !important;
  top: -6px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border-width: 0 6px 6px !important;
  border-style: solid !important;
  border-color: transparent transparent rgba(0, 0, 0, 0.8) !important;
}

.note-tooltip[data-placement="left"]::before {
  content: '' !important;
  position: absolute !important;
  right: -6px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-width: 6px 0 6px 6px !important;
  border-style: solid !important;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.8) !important;
}

.note-tooltip[data-placement="right"]::before {
  content: '' !important;
  position: absolute !important;
  left: -6px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-width: 6px 6px 6px 0 !important;
  border-style: solid !important;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent !important;
}

.note-tooltip-content {
  margin-bottom: 8px !important;
}

.note-tooltip-item {
  display: flex !important;
  margin-bottom: 4px !important;
}

.note-tooltip-item:last-child {
  margin-bottom: 0 !important;
}

.note-tooltip-text {
  font-weight: bold !important;
  color: #409eff !important;
  margin-bottom: 8px !important;
  display: block !important;
}

.note-tooltip-label {
  color: #909399 !important;
  margin-right: 8px !important;
  flex-shrink: 0 !important;
}

.note-tooltip-value {
  flex: 1 !important;
  word-break: break-all !important;
}

.note-tooltip-actions {
  display: flex !important;
  justify-content: flex-end !important;
  margin-top: 8px !important;
  padding-top: 8px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.edit-note-btn {
  padding: 4px 12px !important;
  border: none !important;
  border-radius: 4px !important;
  background-color: #409eff !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
}

.edit-note-btn:hover {
  background-color: #66b1ff !important;
}
