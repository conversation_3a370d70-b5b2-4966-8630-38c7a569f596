<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    title="标记重点词" 
    width="40%" 
    :modal="false" 
    class="note-add-dialog"
  >
    <div class="dialog-item">
      <span class="label">文本内容:</span>
      {{ noteData.word }}
    </div>
    <div class="dialog-item">
      <span class="label">别名:</span>
      <el-input type="text" placeholder="请输入别名" v-model="noteData.alias"></el-input>
    </div>
    <div class="dialog-item">
      <span class="label">释义:</span>
      <el-input type="textarea" :rows="4" placeholder="请输入释义" v-model="noteData.paraphrase"></el-input>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'NoteDialog',
  props: {
    selectedText: {
      type: String,
      required: true
    },
    id: {
      type: String,
      default: ''
    },
    nowSection: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      noteData: {
        word: this.selectedText,
        id: this.id,
        alias: '',
        paraphrase: ''
      }
    }
  },
  mounted() {
    this.open();
  },
  methods: {
    async submitForm() {
      if (!this.noteData.word) {
        this.$message.error('请输入文本内容');
        return;
      }

      try {
        const params = {
          textbookId: this.nowSection.textbookId,
          sectionId: this.nowSection.sectionId,
          word: this.noteData.word,
          alias: this.noteData.alias,
          paraphrase: this.noteData.paraphrase
        };

        if (this.noteData.id) {
          // 编辑逻辑
          await this.$api.UpdateTextbookDict({
            id: this.noteData.id,
            ...params
          });
          this.$message.success('编辑成功');
        } else {
          // 新增逻辑
          const result = await this.$api.CreateTextbookDict(params);
          this.$message.success('添加成功');
          this.$emit('refresh');
        }
        
        this.dialogVisible = false;
      } catch (error) {
        this.$message.error(error.message || '操作失败');
      }
    },
    
    open() {
      this.dialogVisible = true;
      this.noteData = {
        word: this.selectedText,
        id: this.id,
        alias: '',
        paraphrase: ''
      };
    }
  }
}
</script>

<style scoped>
.note-add-dialog .el-message-box__content {
  padding: 20px;
}

.dialog-item {
  margin-bottom: 15px;
}

.dialog-item .label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.dialog-item .el-input {
  width: 100%;
}

.dialog-item .el-textarea {
  width: 100%;
}
</style>
