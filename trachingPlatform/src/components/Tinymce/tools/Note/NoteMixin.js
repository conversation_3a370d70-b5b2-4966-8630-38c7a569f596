import NoteDialog from './NoteDialog.vue'
import NoteDictionary from './NoteDictionary.vue'
import api from "@/api"
 /**
* 获取重点词列表
* @param {Object} editor TinyMCE编辑器实例
* @returns {Promise<Array>} 重点词列表
*/
const getNoteList = function(data) {
return new Promise((resolve, reject) => {
  api.GetTextbookDict(data).then(res => {
   if(res.errCode == 0) {
     resolve(res.data.items)
   } else {
     resolve([])
   }
 }).catch(err => {
   reject(err)
 })
})
}
/**
* 获取重点词详情
* @param {Object} editor TinyMCE编辑器实例
* @returns {Promise<Array>} 重点词列表
*/
const getNoteDetails = function(id) {
  return new Promise((resolve, reject) => {
  api.GetTextbookDictDetail({id}).then(res => {
    if(res.errCode == 0) {
      resolve(res.data)
    } else {
      resolve([])
    }
  }).catch(err => {
    reject(err)
  })
  })
}
export default {
  data() {
    return {
      noteList: [] // 重点词数据列表
    }
  },
  methods: {
    /**
     * 初始化重点词功能
     * @param {Object} editor TinyMCE编辑器实例
     */
    initNoteFeature() {
      // 初始化没有重点词的情况，不用渲染
      const content = this.value
      // console.log('content',content);
      
      if(!content || !content.includes('note-mark')) {
        console.log('没有重点词，不渲染');
        return
      }
      // 初始化重点词数据
      this.editor.on('init', ()=> {
        this.updateNoteMarks()
      });
    },
    updateData() {
      this.updateNoteMarks()
    },
    /**
     * 注册重点词相关按钮
     * @param {Object} editor TinyMCE编辑器实例
     */
    registerNoteButtons() {
      // 注册收录词典按钮
      this.editor.ui.registry.addButton('insertNote', {
        icon: 'paste-text',
        tooltip: '收录词典',
        onAction: () => {
          const selectedText = this.editor.selection.getContent({ format: 'text' })
          if (!selectedText) return
          console.log('选中收录词典', this.nowSection);
          this.openNoteDialog(selectedText)
          
        }
      })

      // 注册词典按钮到工具栏
      // this.editor.ui.registry.addButton('noteDictionary', {
      //   icon: 'paste-text',
      //   tooltip: '重点词词典',
      //   onAction: () => {
      //     console.log('打开收录词典列表', this.nowSection);
      //     this.openNoteDictionary()
      //   }
      // })
    },

    /**
     * 更新所有重点词标记
     * @param {Object} editor TinyMCE编辑器实例
     */
    async updateNoteMarks() {
      console.log('this.editor', this.editor);
      let editor = null
      if(this.editor) {
        editor = this.editor
      }
      if(!editor) return
      try {
        const res = await getNoteList({
          textbookId: this.nowSection?.textbookId || '',
          sectionId: this.nowSection?.sectionId || this.nowSection?.id || '',
          PageIndex: 1,
          PageSize: 1000
        })
        this.noteList = res
      } catch (error) {
        this.noteList = []
      }
      if(!this.noteList.length) return

      let newContent = this.value
      console.log(this.noteList);
      
      // 遍历重点词列表，为每个词添加标记
      this.noteList.forEach(note => {
        // 创建一个正则表达式来匹配未被标记的词
        // const regex = new RegExp(note.word, 'g')
        
        // 创建一个正则表达式来匹配已被标记的词
        // const alreadyMarkedRegex = new RegExp(`<span class="note-mark"[^>]*>${note.word}</span>`, 'g')
        // console.log(regex, alreadyMarkedRegex);
        
        // 使用一个更复杂的替换逻辑，只替换那些未被标记的词
        newContent = newContent.replace(new RegExp(note.word, 'g'), (match, offset) => {
          // 检查这个位置前后是否已经有note-mark标签
          const beforeText = newContent.substring(0, offset);
          const afterText = newContent.substring(offset + match.length);
          
          // 检查这个词是否已经在note-mark标签内
          const isInsideNoteMarkBefore = beforeText.lastIndexOf('<span class="note-mark"') > beforeText.lastIndexOf('</span>');
          const isInsideNoteMarkAfter = afterText.indexOf('</span>') < afterText.indexOf('<span class="note-mark"') || afterText.indexOf('<span class="note-mark"') === -1;
          
          if (isInsideNoteMarkBefore && isInsideNoteMarkAfter) {
            // 已经在note-mark标签内，不需要替换
            return match;
          } else {
            // 不在note-mark标签内，需要添加标记
            return `<span class="note-mark" data-note='${note.id}'>${match}</span>`;
          }
        });
      })

      if (this.value !== newContent) {
        // this.editor.setContent(newContent)
        this.myValue = newContent
      }
      // console.log(this.value);
      
      editor.getBody().querySelectorAll('.note-mark').forEach(noteEl => {
        this.initNoteJs(noteEl)
      })
    },

    /**
     * 移除特定ID的标记
     * @param {String} noteId 要移除标记的ID
     */
    removeNoteMarks(noteId) {
      if (!noteId) return

      // 先移除事件监听器
      const noteElements = this.editor.getBody().querySelectorAll(`.note-mark[data-note='${noteId}']`)
      noteElements.forEach(noteEl => {
        // 移除事件监听器
        if (noteEl._handleMouseEnter) {
          noteEl.removeEventListener('mouseenter', noteEl._handleMouseEnter)
          noteEl._handleMouseEnter = null
        }
        if (noteEl._handleMouseLeave) {
          noteEl.removeEventListener('mouseleave', noteEl._handleMouseLeave)
          noteEl._handleMouseLeave = null
        }
        
        // 移除tooltip
        const tooltip = noteEl.querySelector('.note-tooltip')
        if (tooltip) {
          tooltip.remove()
        }
      })

      let newContent = this.value
      
      // 创建一个正则表达式来匹配带有特定ID的note-mark标签
      const noteMarkRegex = new RegExp(`<span class="note-mark"[^>]*data-note=['"](${noteId})['"][^>]*>([^<]*)</span>`, 'g')
      
      // 将带有特定ID的note-mark标签替换为纯文本内容
      newContent = newContent.replace(noteMarkRegex, '$2')

      if (this.value !== newContent) {
        // this.editor.setContent(newContent)
        this.myValue = newContent
      }
    },

    /**
     * 初始化重点词的JS
     * @param {Object} editor TinyMCE编辑器实例
     * @param {HTMLElement} noteEl 重点词DOM元素
     */
    initNoteJs(noteEl) {
      // console.log('初始化重点词的JS', noteEl);
      
      // 移除旧的事件监听器（如果存在）
      if (noteEl._handleMouseEnter) {
        noteEl.removeEventListener('mouseenter', noteEl._handleMouseEnter)
      }
      if (noteEl._handleMouseLeave) {
        noteEl.removeEventListener('mouseleave', noteEl._handleMouseLeave)
      }

      let tooltipTimeout;

      const handleMouseEnter = async (e) => {
        const target = e.currentTarget;
        
        // 检查当前元素是否为tooltip或包含tooltip，如果是则直接返回
        if (target.classList.contains('note-tooltip') || target.closest('.note-tooltip')) {
          return;
        }
        
        const id = JSON.parse(target.getAttribute('data-note'))
        // console.log(id);
        const noteData = await getNoteDetails(id)
        // console.log(noteData);
        
        // 移除已存在的tooltip
        const existingTooltip = target.querySelector('.note-tooltip')
        if (existingTooltip) {
          existingTooltip.remove()
        }
        
        // 创建tooltip
        const tooltip = document.createElement('div')
        tooltip.className = 'note-tooltip'
        tooltip.innerHTML = `
          <div class="note-tooltip-content">
            <div class="note-tooltip-item note-tooltip-text">
              ${noteData.word || ''}
            </div>
            ${noteData.alias ? `
              <div class="note-tooltip-item">
                <div class="note-tooltip-label">别名:</div>
                <div class="note-tooltip-value">${noteData.alias}</div>
              </div>
            ` : ''}
            ${noteData.paraphrase ? `
              <div class="note-tooltip-item">
                <div class="note-tooltip-label">释义:</div>
                <div class="note-tooltip-value">${noteData.paraphrase}</div>
              </div>
            ` : ''}
          </div>
          
        `
        
        target.appendChild(tooltip)
        // console.log(tooltip,noteEl);
        
        // 定位tooltip
        const rect = target.getBoundingClientRect()
        const tooltipRect = tooltip.getBoundingClientRect()
        
        tooltip.style.left = `${0}px`
        tooltip.style.top = `${40}px`
        
      }

      const handleMouseLeave = (e) => {
        const target = e.currentTarget;

        // 如果上一次移除的操作还未完成，清除延时器
        if (tooltipTimeout) {
          clearTimeout(tooltipTimeout);
        }

        tooltipTimeout = setTimeout(() => {
          const tooltip = target.querySelector('.note-tooltip');
          if (tooltip) {
            tooltip.remove();
          }
        }, 200);  // 延时200ms后再移除tooltip

      }
      
      // 绑定事件
     // 保存新的事件处理函数引用
      noteEl._handleMouseEnter = handleMouseEnter;
      noteEl._handleMouseLeave = handleMouseLeave; 
      console.log('绑定事件',noteEl);
      
      noteEl.addEventListener('mouseenter', handleMouseEnter)
      noteEl.addEventListener('mouseleave', handleMouseLeave)
    },

    /**
     * 打开标记重点词弹窗
     * @param {Object} editor TinyMCE编辑器实例
     * @param {String} selectedText 选中的文本
     * @param {String} id 重点词ID（编辑时使用）
     */
    openNoteDialog(selectedText, id = '') {
      const NoteDialogConstructor = Vue.extend(NoteDialog)
      const noteDialogInstance = new NoteDialogConstructor({
        propsData: {
          selectedText,
          id,
          nowSection: this.nowSection
        },
      })

      noteDialogInstance.$on('refresh', () => {
        console.log('刷新列表');
        // 更新所有重点词标记
        this.updateNoteMarks()
      })

      const container = document.createElement('div')
      document.body.appendChild(container)
      noteDialogInstance.$mount(container)
      
      // 监听抽屉关闭事件
      noteDialogInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })
    },

    /**
     * 打开重点词词典
     * @param {Object} editor TinyMCE编辑器实例
     */
    openNoteDictionary() {
      const NoteDictionaryConstructor = Vue.extend(NoteDictionary)
      const noteDictionaryInstance = new NoteDictionaryConstructor({
        propsData: {
          nowSection: this.nowSection
        },
      })

      const container = document.createElement('div')
      document.body.appendChild(container)
      noteDictionaryInstance.$mount(container)
      
      // 监听抽屉关闭事件
      noteDictionaryInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })

      noteDictionaryInstance.$on('refresh', () => {
        this.updateNoteMarks()
      })
      noteDictionaryInstance.$on('deleteNote', (deletedId) => {
        // 删除词条后更新富文本中的标记
        if (deletedId) {
          // 移除特定ID的标记
          this.removeNoteMarks(deletedId)
        } else {
          // 如果没有提供ID，则更新所有标记
          this.updateNoteMarks()
        }
      })
    }
  },
  
}