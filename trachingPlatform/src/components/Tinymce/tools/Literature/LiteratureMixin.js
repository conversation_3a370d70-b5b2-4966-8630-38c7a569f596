import Vue from 'vue'
import LiteratureDialog from './LiteratureDialog.vue'

export default {
  methods: {
    /**
     * 初始化参考文献功能
     */
    initLiteratureFeature() {
      if (!this.editor) return
      
      setTimeout(() => {
        this.initExistingLiteratures()
      }, 1000);
    },

    /**
     * 注册参考文献相关按钮
     */
    registerLiteratureButtons() {
      // 注册插入参考文献按钮
      this.editor.ui.registry.addButton('insertLiterature', {
        icon: 'new-document',
        tooltip: '插入参考文献',
        onAction: () => {
          this.openLiteratureDialog()
        }
      })
    },

    /**
     * 初始化已存在的参考文献
     */
    initExistingLiteratures() {
      if (!this.editor || !this.editor.getBody()) return

      // 查找所有参考文献元素，无论是否已初始化
      const literatureEls = this.editor.getBody().querySelectorAll('.type-literature')
      
      if (!literatureEls.length) return
      
      // 重新初始化参考文献JS
      literatureEls.forEach(literatureEl => {
        this.initLiteratureJs(literatureEl)
      })
    },

    /**
     * 初始化参考文献的JS
     * @param {HTMLElement} literatureEl 参考文献DOM元素
     */
    initLiteratureJs(literatureEl) {
      if (!literatureEl) return

      const deleteBtn = literatureEl.querySelector('.delete-literature-btn')
      
      function removeAllEventListeners(element) {
        const clone = element.cloneNode(true);  // 创建元素的克隆
        element.parentNode.replaceChild(clone, element);  // 替换元素，这会移除所有事件监听器
        return clone;
      }

      // 绑定删除按钮事件
      if (deleteBtn) {
        // 定义删除按钮事件处理函数
        const deleteBtnClickHandler = (e) => {
          e.stopPropagation()
          literatureEl.remove()
          this.myValue = this.editor.getContent()
        }
        const newDeleteBtn = removeAllEventListeners(deleteBtn);
        newDeleteBtn.addEventListener('click', deleteBtnClickHandler);
      }
    },

    /**
     * 生成参考文献HTML
     * @param {Object} item 参考文献数据
     * @returns {String} 参考文献HTML
     */
    generateLiteratureHtml(item) {
      return `
        <p class="literature-reference type-literature" data-id="${item.id}" data-literature="true">
          <span class="literature-number">【${item.title}】</span>
          <span class="literature-content">${item.content}</span>
          <span class="literature-actions">
              <button class="delete-literature-btn">删除</button>
          </span>
        </p>
      `
    },

    /**
     * 打开参考文献弹窗
     */
    openLiteratureDialog() {
      // 创建参考文献弹窗实例
      const LiteratureDialogConstructor = Vue.extend(LiteratureDialog)
      const literatureDialogInstance = new LiteratureDialogConstructor({
        propsData: {
          nowSection: this.nowSection
        }
      })
      
      const container = document.createElement('div')
      document.body.appendChild(container)
      literatureDialogInstance.$mount(container)
      literatureDialogInstance.$once('insert', (data) => {
        const html = this.generateLiteratureHtml(data)
        this.editor.execCommand('mceInsertContent', false, html)
        this.myValue = this.editor.getContent()
      })
      // 监听抽屉关闭事件
      literatureDialogInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })
    }
  }
}