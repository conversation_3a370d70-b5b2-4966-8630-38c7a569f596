<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    title="参考文献管理" 
    width="50%" 
    class="literature-dialog" 
  >
    <div class="literature-list">
      <div class="text-right m-r-20">
        <el-button type="text" icon="el-icon-plus" @click="addLiterature">
          添加文献
        </el-button>
      </div>

      <el-dialog :visible.sync="addDialogVisible" title="新增文献" width="40%" :modal="false" class="literature-add-dialog" append-to-body>
        <div class="dialog-item">
          <span class="label">参考资料类型:</span>
          <el-select v-model="addForm.title" placeholder="请选择" clearable>
            <el-option v-for="item in typeOptions" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </div>
        <div class="dialog-item">
          <span class="label">资料内容:</span>
          <el-input type="textarea" :rows="10" placeholder="请输入内容" v-model="addForm.content" maxlength="500" show-word-limit>
          </el-input>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAddForm">确认</el-button>
        </span>
      </el-dialog>
      <div v-for="(item, index) in literatureList" :key="index" class="literature-item p-x-20 p-t-10">
        <div class="literature-title"><strong>{{ item.title || '未命名参考文献' }}</strong></div>
        <div class="literature-content">
          <div>{{ item.content }}</div>
        </div>
        <div class="literature-actions">
          <el-button type="text" size="small" @click="citeLiterature(item)" :disabled="!item.content" title="插入">
            插入
          </el-button>
          <el-button type="text" size="small" @click="editLiterature(item)" title="编辑">
            编辑
          </el-button>
          <el-button type="text" size="small" @click="deleteLiterature(item)" title="删除">
            删除
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LiteratureDialog',
  props: {
    editor: {
      type: Object,
      required: true
    },
    nowSection: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      literatureList: [],
      dialogVisible: true,
      addDialogVisible: false,
      addForm: {
        title: '',
        content: ''
      },
      typeOptions: ['网络资料', '著作资料', '其他资料']
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    editLiterature(item) {
      this.addDialogVisible = true;
      this.addForm = {
        id: item.id,
        title: item.title,
        content: item.content,
        ...this.nowSection
      };
    },
    deleteLiterature(item) {
      this.$confirm('确认删除该参考文献吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.DeleteTextbookLiterature({id:item.id}).then(res => {
          this.getList();
        }).catch(err => {
          this.$message.error('删除失败');
        });
      }).catch(() => {
      });
    },
    addLiterature() {
      this.addDialogVisible = true;
      this.addForm = {
        type: '',
        content: '',
        ...this.nowSection
      };
    },

    async submitAddForm() {
      if (!this.addForm.content) {
        this.$message.error('请输入资料内容');
        return;
      }

      try {
        const apiCall = await this.addForm.id
          ? this.$api.UpdateTextbookLiterature(this.addForm)
          : this.$api.CreateTextbookLiterature(this.addForm);
        apiCall.then(res => {
          console.log(res);
          this.addDialogVisible = false;
          if (res.errCode !== 0) {
            this.$message.error(res.errMsg);
            return;
          }
          this.getList();
        })
      } catch (error) {
        this.addDialogVisible = false;
      }
    },
    citeLiterature(item) {
      if (!item.content) return

      this.$emit('insert', {
        id: item.id,
        content: item.content,
        title: item.title
      })
      // 关闭对话框
      this.dialogVisible = false
    },
    getList() {
      this.$api.GetTextbookLiterature(this.nowSection).then(res => {
        if (res.errCode === 0) {
          this.literatureList = res.data.items || [];
        } else {
          this.$message.error(res.errMsg)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.literature-dialog {
  padding: 20px !important;
  min-width: 600px !important;
}

.literature-add-dialog {
  .el-message-box__content {
    padding: 20px;
  }

  .dialog-item {
    margin-bottom: 15px;

    .label {
      display: block;
      margin-bottom: 5px;
      font-size: 14px;
      color: #606266;
    }

    .el-select {
      width: 100%;
    }

    .el-textarea {
      width: 100%;
    }
  }
}

.literature-list {
  max-height: 400px !important;
  overflow-y: auto !important;
}

.literature-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.literature-title {
  font-size: 14px;
}

.literature-content {
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.literature-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.literature-index {
  flex: 0 0 50px !important;
  text-align: center !important;
  font-weight: bold !important;
}

.action-btn {
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  transition: all 0.3s !important;
}

.add-btn {
  background-color: #28a745 !important;
  color: white !important;
}

.delete-btn {
  background-color: #dc3545 !important;
  color: white !important;
}

.cite-btn {
  background-color: #007bff !important;
  color: white !important;
}

.action-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

.add-literature-btn {
  padding: 8px 16px !important;
  background-color: #28a745 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
}

.add-literature-btn:hover {
  background-color: #218838 !important;
}

.text-right {
  text-align: right;
}
</style>