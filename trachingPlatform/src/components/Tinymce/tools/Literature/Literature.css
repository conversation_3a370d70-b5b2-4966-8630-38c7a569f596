.literature-reference {
  position: relative !important;
  display: inline-block !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  margin: 0 !important;
}

.literature-reference:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.literature-number {
  color: #333 !important;
  font-size: inherit !important;
  font-weight: normal !important;
}

.literature-content {
  display: none !important;
  color: #333 !important;
  font-size: inherit !important;
  margin-left: 4px !important;
}

.literature-reference:hover .literature-content {
  display: inline !important;
  padding-right: 90px !important;
}

.literature-actions {
  display: none ;
  position: absolute !important;
  right: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  gap: 4px !important;
  white-space: nowrap !important;
}

.literature-reference:hover .literature-actions {
  display: flex;
}

.edit-literature-btn,
.delete-literature-btn {
  padding: 2px 6px !important;
  font-size: 12px !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
}

.edit-literature-btn:hover {
  background-color: #fff !important;
}

.delete-literature-btn:hover {
  background-color: #fff !important;
  color: #dc3545 !important;
}
