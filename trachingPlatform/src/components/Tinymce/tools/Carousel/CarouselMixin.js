import CarouselDialog from './CarouselDialog.vue'
import Vue from 'vue'

export default {
  methods: {
    /**
     * 初始化轮播图的JS
     */
    initExistingCarousels() {
      console.log('尝试初始化轮播图...', this.editor);
      const editorBody = this.editor.getBody();

      if (!editorBody) {
        console.log('编辑器body未准备好，将延迟初始化轮播图片');
        return;
      }
      const carouselWrappers = editorBody.querySelectorAll('.type-carousel');

      // 为每个轮播图添加点击事件和自动轮播
      carouselWrappers.forEach(carouselEl => {
        // console.log(carouselEl);
        
        // 创建事件处理函数
        const handleClick = (event) => {
          event.stopPropagation();
          event.preventDefault();
          this.showBar(carouselEl);
        };
        carouselEl.removeEventListener('click', handleClick);
        // 保存事件处理函数引用
        carouselEl.addEventListener('click', handleClick);
        
        // 初始化轮播图导航点点击事件
        this.initCarouselDots(carouselEl);
        
        // 初始化自动轮播
        this.initAutoSlide(carouselEl);
      });
    },
    
    /**
     * 初始化轮播图导航点点击事件
     * @param {HTMLElement} carouselEl 轮播图元素
     */
    initCarouselDots(carouselEl) {
      const dots = carouselEl.querySelectorAll('.carousel-dot');
      const slides = carouselEl.querySelectorAll('.carousel-slide');
      
      dots.forEach((dot, index) => {
        // 移除旧的事件监听器
        if (dot._dotClickHandler) {
          dot.removeEventListener('click', dot._dotClickHandler);
        }
        
        // 创建点击事件处理函数
        const handleDotClick = (e) => {
          e.stopPropagation();
          e.preventDefault();
          
          // 移除所有幻灯片和导航点的active类
          slides.forEach(slide => slide.classList.remove('active'));
          dots.forEach(dot => dot.classList.remove('active'));
          
          // 为当前幻灯片和导航点添加active类
          slides[index].classList.add('active');
          dot.classList.add('active');
          
          // 更新当前幻灯片索引
          carouselEl._currentSlideIndex = index;
        };
        
        // 保存事件处理函数引用
        dot._dotClickHandler = handleDotClick;
        dot.addEventListener('click', handleDotClick);
      });
    },
     
     /**
      * 初始化自动轮播
      * @param {HTMLElement} carouselEl 轮播图元素
      */
     initAutoSlide(carouselEl) {
       const slides = carouselEl.querySelectorAll('.carousel-slide');
       const dots = carouselEl.querySelectorAll('.carousel-dot');
       
       // 如果已经有定时器，先清除
       if (carouselEl._autoSlideTimer) {
         clearInterval(carouselEl._autoSlideTimer);
       }
       
       // 初始化当前幻灯片索引
       carouselEl._currentSlideIndex = 0;
       
       // 设置自动轮播定时器，每3秒切换一次
       carouselEl._autoSlideTimer = setInterval(() => {
         // 计算下一张幻灯片的索引
         const nextIndex = (carouselEl._currentSlideIndex + 1) % slides.length;
         
         // 移除所有幻灯片和导航点的active类
         slides.forEach(slide => slide.classList.remove('active'));
         dots.forEach(dot => dot.classList.remove('active'));
         
         // 为下一张幻灯片和对应的导航点添加active类
         slides[nextIndex].classList.add('active');
         dots[nextIndex].classList.add('active');
         
         // 更新当前幻灯片索引
         carouselEl._currentSlideIndex = nextIndex;
       }, 3000);
     },
    
    /**
     * 显示轮播图操作栏
     * @param {HTMLElement} carouselEl 轮播图元素
     */
    showBar(carouselEl) {
      console.log('显示操作栏');
      
      // 检查是否已存在操作栏，如果存在则移除
      const existingBar = carouselEl.querySelector('.carousel-operation-bar');
      if (existingBar) {
        existingBar.remove();
        return;
      }
      
      // 获取轮播图数据
      const carouselId = carouselEl.getAttribute('data-id');
      const urlsJson = carouselEl.getAttribute('data-urls');
      const urls = JSON.parse(urlsJson || '[]');
      
      // 创建操作栏 - 浮动在轮播图上方，纯白背景带阴影
      const operationBar = document.createElement('div');
      operationBar.className = 'carousel-operation-bar';
      operationBar.style.cssText = 'position: absolute; top: -40px; left: 50%; transform: translateX(-50%); background-color: #ffffff; border-radius: 4px; padding: 5px; display: flex; z-index: 1000; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);';
      
      // 创建编辑按钮
      const editBtn = document.createElement('div');
      editBtn.className = 'carousel-edit-btn';
      editBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32zm-622.3-84c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9c3.9-3.9 3.9-10.2 0-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2c-1.9 11.1 1.5 22.5 9.4 30.4 4.6 4.6 10.3 7.8 16.8 9.3zM664.1 159l67.9 67.9-393.5 393.5-67.9-67.9 393.5-393.5z" fill="#666666"></path></svg>';
      editBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';
      
      // 添加鼠标悬停效果
      editBtn.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f0f0f0';
      });
      editBtn.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
      });
      
      // 添加编辑按钮点击事件
      editBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        // 打开轮播图编辑弹窗
        this.openCarouselDialog(carouselId, urls);
      });
      
      // 创建预览按钮
      const previewBtn = document.createElement('div');
      previewBtn.className = 'carousel-preview-btn';
      previewBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M396.8 172.8C211.2 172.8 57.6 326.4 57.6 512S211.2 851.2 396.8 851.2 736 697.6 736 512 582.4 172.8 396.8 172.8z m0 614.4c-153.6 0-275.2-121.6-275.2-275.2S243.2 236.8 396.8 236.8 672 358.4 672 512 550.4 787.2 396.8 787.2z" fill="#666666"></path><path d="M985.6 945.6l-169.6-169.6c32-38.4 57.6-83.2 70.4-134.4 6.4-25.6-12.8-51.2-38.4-57.6-25.6-6.4-51.2 12.8-57.6 38.4-25.6 102.4-115.2 179.2-224 179.2-57.6 0-115.2-25.6-160-64l-51.2 51.2c57.6 51.2 134.4 83.2 211.2 83.2 83.2 0 160-32 217.6-83.2l166.4 166.4c6.4 6.4 12.8 6.4 19.2 6.4 6.4 0 12.8 0 19.2-6.4 6.4-12.8 6.4-25.6-3.2-9.6z" fill="#666666"></path></svg>';
      previewBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';
      
      // 添加鼠标悬停效果
      previewBtn.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f0f0f0';
      });
      previewBtn.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
      });
      
      // 添加预览按钮点击事件
      previewBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        
        // 创建预览层
        const previewOverlay = document.createElement('div');
        previewOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 9999;';
        
        // 创建预览容器
        const previewContainer = document.createElement('div');
        previewContainer.style.cssText = 'position: relative; width: 80%; height: 80%; display: flex; flex-direction: column; align-items: center;';
        
        // 创建预览图片容器
        const previewImgContainer = document.createElement('div');
        previewImgContainer.style.cssText = 'width: 100%; height: 90%; display: flex; justify-content: center; align-items: center; position: relative;';
        
        // 创建左右箭头
        const prevBtn = document.createElement('div');
        prevBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="32" height="32"><path d="M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8c-16.4 12.8-16.4 37.5 0 50.3l450.8 352.1c5.3 4.1 12.9 0.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z" fill="#ffffff"></path></svg>';
        prevBtn.style.cssText = 'position: absolute; left: 20px; cursor: pointer; z-index: 10000;';
        
        const nextBtn = document.createElement('div');
        nextBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="32" height="32"><path d="M765.7 486.8L314.9 134.7c-5.3-4.1-12.9-0.4-12.9 6.3v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1c16.4-12.8 16.4-37.6 0-50.4z" fill="#ffffff"></path></svg>';
        nextBtn.style.cssText = 'position: absolute; right: 20px; cursor: pointer; z-index: 10000;';
        
        // 创建预览图片
        const previewImg = document.createElement('img');
        previewImg.style.cssText = 'max-width: 100%; max-height: 100%; object-fit: contain;';
        
        // 创建关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; color: white; font-size: 30px; cursor: pointer; width: 40px; height: 40px; line-height: 40px; text-align: center; background-color: rgba(0, 0, 0, 0.5); border-radius: 50%; z-index: 10001;';
        
        

        // 创建指示器容器
        const indicatorContainer = document.createElement('div');
        indicatorContainer.style.cssText = 'display: flex; justify-content: center; margin-top: 20px;';
        
        // 当前显示的图片索引
        let currentPreviewIndex = 0;
        
        // 显示指定索引的图片
        const showPreviewImage = (index) => {
          if (index < 0) index = urls.length - 1;
          if (index >= urls.length) index = 0;
          currentPreviewIndex = index;
          previewImg.src = urls[index];
          
          // 更新指示器状态
          const indicators = indicatorContainer.querySelectorAll('.preview-indicator');
          indicators.forEach((indicator, i) => {
            if (i === index) {
              indicator.style.backgroundColor = '#ffffff';
            } else {
              indicator.style.backgroundColor = 'rgba(255, 255, 255, 0.5)';
            }
          });
        };
        
        // 添加左右箭头点击事件
        prevBtn.addEventListener('click', () => {
          showPreviewImage(currentPreviewIndex - 1);
        });
        
        nextBtn.addEventListener('click', () => {
          showPreviewImage(currentPreviewIndex + 1);
        });
        
        // 创建指示器
        urls.forEach((_, index) => {
          const indicator = document.createElement('div');
          indicator.className = 'preview-indicator';
          indicator.style.cssText = `width: 10px; height: 10px; border-radius: 50%; background-color: ${index === 0 ? '#ffffff' : 'rgba(255, 255, 255, 0.5)'}; margin: 0 5px; cursor: pointer;`;
          
          indicator.addEventListener('click', () => {
            showPreviewImage(index);
          });
          
          indicatorContainer.appendChild(indicator);
        });
        
        // 显示第一张图片
        showPreviewImage(0);
        
        // 组装预览界面
        previewImgContainer.appendChild(prevBtn);
        previewImgContainer.appendChild(previewImg);
        previewImgContainer.appendChild(nextBtn);
        previewContainer.appendChild(previewImgContainer);
        previewContainer.appendChild(indicatorContainer);
        previewContainer.appendChild(closeBtn);
        previewOverlay.appendChild(previewContainer);
        
        // 添加关闭按钮点击事件
        closeBtn.addEventListener('click', function(e) {
          e.stopPropagation();
          document.body.removeChild(previewOverlay);
      });

        // 添加点击事件关闭预览
        previewOverlay.addEventListener('click', function(e) {
          if (e.target === previewOverlay) {
            document.body.removeChild(previewOverlay);
          }
        });
        
        // 将预览层添加到body
        document.body.appendChild(previewOverlay);
      });
      
      // 创建删除按钮
      const deleteBtn = document.createElement('div');
      deleteBtn.className = 'carousel-delete-btn';
      deleteBtn.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M832 256h-192V160c0-17.7-14.3-32-32-32H416c-17.7 0-32 14.3-32 32v96H192c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM416 192h192v64H416v-64z m-16 728c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z m144 0c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V368c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v552z" fill="#666666"></path></svg>';
      deleteBtn.style.cssText = 'cursor: pointer; padding: 2px; display: flex; justify-content: center; align-items: center; margin: 0 2px; border-radius: 4px; transition: background-color 0.3s;';
      
      // 添加鼠标悬停效果
      deleteBtn.addEventListener('mouseover', function() {
        this.style.backgroundColor = '#f0f0f0';
      });
      deleteBtn.addEventListener('mouseout', function() {
        this.style.backgroundColor = 'transparent';
      });
      
      // 添加删除按钮点击事件
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        e.preventDefault();
        
        // 移除事件监听器
        if (carouselEl._carouselClickHandler) {
          carouselEl.removeEventListener('click', carouselEl._carouselClickHandler);
        }
        
        // 删除整个轮播图容器
        carouselEl.remove();
      });
      
      // 将所有按钮添加到操作栏
      operationBar.appendChild(editBtn);
      operationBar.appendChild(previewBtn);
      operationBar.appendChild(deleteBtn);
      // console.log(carouselEl, operationBar);
      
      // 将操作栏添加到轮播图容器
      carouselEl.appendChild(operationBar);
    },

    /**
     * 生成轮播图HTML
     * @param {Array} uploadedUrls 上传的图片URL数组
     * @returns {String} 轮播图HTML
     */
    generateCarouselHtml(uploadedUrls, id = null) {
      const carouselId = id || new Date().getTime()
      return `
        <div class="carousel-wrapper type-carousel" data-id="${carouselId}" data-carousel="true" data-urls='${JSON.stringify(uploadedUrls)}' style="position: relative; width: 100%; height: 300px; margin: 10px auto;">
          <div class="carousel-container">
            <div class="carousel-slides">
              ${uploadedUrls.map((url, index) => `
                <div class="carousel-slide ${index === 0 ? 'active' : ''}">
                  <img src="${url}" style="max-width:100%;max-height:100%;object-fit:contain" onclick="event.stopPropagation(); event.preventDefault();" onmousedown="event.stopPropagation(); event.preventDefault();"/>
                </div>
              `).join('')}
            </div>
            <div class="carousel-dots">
              ${uploadedUrls.map((_, index) => `
                <div class="carousel-dot ${index === 0 ? 'active' : ''}" data-index="${index}"></div>
              `).join('')}
            </div>
          </div>
        </div>
      `
    },

    /**
     * 打开轮播图弹窗
     * @param {String} id 轮播图ID（编辑时使用）
     * @param {Array} oldUrls 已有的图片URL数组（编辑时使用）
     */
    openCarouselDialog(id = null, oldUrls = []) {
      const CarouselDialogConstructor = Vue.extend(CarouselDialog)
      const carouselDialogInstance = new CarouselDialogConstructor({
        propsData: {
          id,
          oldUrls
        }
      })
      // 监听提交事件
      carouselDialogInstance.$on('submit', (uploadedUrls) => {
        // 生成轮播图HTML，如果是编辑模式，传入原有ID
        const html = this.generateCarouselHtml(uploadedUrls, id)
        console.log('id---', id);
      this.editor.focus();
      const savedBookmark = this.editor.selection.getBookmark()
        if (id) {
          // 查找data-id属性等于id的轮播图元素
          const carouselElements = this.editor.getBody().querySelectorAll(`[data-id="${id}"]`);
          console.log(carouselElements);
          
          if (carouselElements && carouselElements.length > 0) {
            // 找到元素后，使用dom.setOuterHTML替换它
            // this.editor.dom.setOuterHTML(carouselElements[0], html);
            carouselElements[0].remove()
             this.editor.execCommand('mceInsertContent', false, html);
            this.myValue = this.editor.getContent();
          }
        } else {
      this.editor.focus();
      this.editor.selection.moveToBookmark(savedBookmark);
            this.editor.execCommand('mceInsertContent', false, html);
            this.myValue = this.editor.getContent();
        }
      })

      const container = document.createElement('div')
      document.body.appendChild(container)
      carouselDialogInstance.$mount(container)

      // 监听抽屉关闭事件
      carouselDialogInstance.$once('hook:destroyed', () => {
        document.body.removeChild(container)
      })
    }
  }
}