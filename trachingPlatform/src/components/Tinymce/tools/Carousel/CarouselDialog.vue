<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    :title="id ? '修改轮播图' : '插入轮播图'" 
    width="720px" 
    :modal="false" 
    class="carousel-dialog"
  >
    <div class="upload-container">
      <!-- <el-upload
        action="#"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        :multiple="true"
        accept="image/*"
      >
        <el-button size="small" type="primary">选择图片</el-button>
      </el-upload> -->
<el-upload
  class="avatar-uploader"
        action="#"
        :auto-upload="false"
  :show-file-list="false"
  :on-change="handleFileChange"
  :multiple="true"
        accept="image/*"
  >
  <img v-if="imageUrl" :src="imageUrl" class="avatar">
  <div v-else>
    <i  class="el-icon-plus avatar-uploader-icon"></i>
  </div>
</el-upload>
      <!-- <p style="font-size:12px;color:gray;margin-top:5px;">提示：最多上传10张</p> -->
    </div>
    
    <div  v-if="selectedFiles.length > 0">
    <section class="preview-container">
      <div 
        v-for="(file, index) in selectedFiles" 
        :key="index" 
        class="preview-item"
      >
        <img :src="file.preview" class="preview-image" />
        <p class="preview-text">图{{index + 1}}</p>
        <div class="btn-group">
          <el-button 
            size="mini" 
            class="move-btn move-up-btn"
            @click="moveImageUp(index)"
            v-show="index !== 0"
          >
            前移
          </el-button>
          <el-button 
            size="mini" 
            class="move-btn move-down-btn"
            @click="moveImageDown(index)"
            v-show="index !== selectedFiles.length - 1"
          >
            后移
          </el-button>
          <el-button 
            type="danger" 
            size="mini" 
            circle 
            class="delete-btn"
            @click="removeImage(index)"
            title="删除"
          >
          <i class="el-icon-delete"></i>
          </el-button>
        </div>
      </div>
      </section>
      <p style="font-size:12px;color:gray;margin-top:5px;">提示：鼠标移入图片有删除、排序功能</p>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">{{ id ? '更新' : '插入' }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
// 引入上传图片的函数
import { upload } from '../../util/upload'
export default {
  name: 'CarouselDialog',
  props: {
    id: {
      type:String,
      default: ''
    },
    oldUrls: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedFiles: [],
      uniqueClass: 'carousel-' + Date.now()
    }
  },
  mounted() {
    this.open()
    if (this.oldUrls.length > 0) {
      this.oldUrls.forEach(url => {
        this.selectedFiles.push({ raw: null, preview: url, fromOld: true })
      })
    }
  },
  methods: {
    open() {
      this.dialogVisible = true
    },
    
    handleFileChange(file, fileList) {
      if (this.selectedFiles.length + 1 > 10) {
        this.$message.error('最多只能上传10张图片')
        return
      }
      
      const reader = new FileReader()
      reader.onload = () => {
        this.selectedFiles.push({ 
          raw: file.raw, 
          preview: reader.result 
        })
      }
      reader.readAsDataURL(file.raw)
    },
    
    removeImage(index) {
      this.selectedFiles.splice(index, 1)
    },
    
    moveImageUp(index) {
      if (index > 0) {
        // 交换当前图片与上一个图片的位置
        const temp = this.selectedFiles[index];
        this.$set(this.selectedFiles, index, this.selectedFiles[index - 1]);
        this.$set(this.selectedFiles, index - 1, temp);
      }
    },
    
    moveImageDown(index) {
      if (index < this.selectedFiles.length - 1) {
        // 交换当前图片与下一个图片的位置
        const temp = this.selectedFiles[index];
        this.$set(this.selectedFiles, index, this.selectedFiles[index + 1]);
        this.$set(this.selectedFiles, index + 1, temp);
      }
    },
    
    async uploadImages() {
      const uploadedUrls = []
      for (const file of this.selectedFiles) {
        if (file.fromOld) {
          uploadedUrls.push(file.preview)
        } else {
          try {
            await upload('cu', 'Image', file.raw,
              res => {
                uploadedUrls.push(res)
              },
              () => {
                this.$message.error('上传失败')
              }
            )
          } catch (error) {
            this.$message.error('上传失败: ' + error.message)
          }
        }
      }
      return uploadedUrls
    },
    
    async submitForm() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning('请先上传图片')
        return
      }

      try {
        const uploadedUrls = await this.uploadImages()
        if (uploadedUrls.length === 0) {
          this.$message.warning('请先上传图片')
          return
        }

        // 触发提交事件，将上传的URL传递给父组件
        this.$emit('submit', uploadedUrls)
        
        // 关闭对话框
        this.dialogVisible = false
      } catch (error) {
        this.$message.error('操作失败: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.carousel-dialog .el-dialog__body {
  padding: 20px;
}

.upload-container {
  margin-bottom: 15px;
}

.preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 20px;
}

.preview-item {
  position: relative;
  width: 120px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.preview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 120px;
  height: 90px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.preview-item:hover .preview-image {
  border-color: #409EFF;
}

.preview-text {
  text-align: center;
  font-size: 12px;
  margin-top: 5px;
  color: #606266;
  font-weight: 500;
}

.btn-group {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.3s ease;
}

.preview-item:hover .btn-group {
  opacity: 1;
  transform: translateY(0);
}

.move-btn,
.delete-btn {
  padding: 3px;
  font-size: 11px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.move-btn i {
  font-size: 9px;
}

.move-up-btn,
.move-down-btn {
  background-color: #409EFF;
  border-color: #409EFF;
  color: #ffffff;
}

.move-up-btn:hover:not([disabled]),
.move-down-btn:hover:not([disabled]) {
  background-color: #66b1ff;
  border-color: #66b1ff;
  transform: scale(1);
  opacity: 1;
}

.delete-btn {
  background-color: #F56C6C;
  border-color: #F56C6C;
  opacity: 0.85;
}

.delete-btn:hover {
  background-color: #f78989;
  border-color: #f78989;
  transform: scale(1);
  opacity: 1;
}
</style>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>
