.carousel-container {
  width: 100%;
  height: 300px;
  margin: 10px 0;
  position: relative;
  overflow: hidden;
}
.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
}
.carousel-slide.active {
  opacity: 1;
}
.carousel-slide img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.carousel-nav {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}
.carousel-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 4px;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s;
}
.carousel-dot.active {
  background: #fff;
}

.carousel-slides {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  justify-content: center;
  align-items: center;
}

.carousel-slide.active {
  display: flex;
}

.carousel-slide img {
  max-width: 100%;
  max-height: 100%;
  height: 100%;
  width: auto;
  object-fit: contain;
}

.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}