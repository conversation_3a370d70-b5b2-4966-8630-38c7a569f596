import token from "@/utils/token.js";
import store from "@/store/index.js";
import config from "./glob";
export const upload = async(type, fileType, blobInfo, success, failure) => {
  // console.log('blobInfo, success', blobInfo)
  const userInfo = store.getters.userInfo;
  const formData = new FormData()
  if(type == 'tinymce') {
    formData.append('file', blobInfo.blob(), blobInfo.filename())
  } else {
    formData.append('file', blobInfo, blobInfo.name)
  }
  formData.append('schoolId', userInfo?.schools[0]?.id )
  formData.append('uploadFileType', fileType)
  formData.append('withPreSigned', false)

  try {
    const response = await fetch(config.baseURL + '?uploadFileType=' + fileType, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: token.getToken(),
        schoolId: userInfo?.schools[0]?.id
      }
    })

    if (!response.ok) {
      throw new Error('上传失败: ' + response.statusText)
      return failure(error.message || '上传失败')
    }

    const data = await response.json()
    // console.log(data, data.data)
    if (data.errCode === 0) {
      success(data.data) // 返回图片URL
    } else {
      failure(data.msg || '上传失败：未知错误')
    }
  } catch (error) {
    console.error('上传错误:', error)
    failure(error.msg || '上传失败：未知错误')
  }
}
