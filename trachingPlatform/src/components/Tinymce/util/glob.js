export default {
  baseURL: window.FILEIP,
  Plugins: "quickbars",
  Toolbar: 'undo redo formatpainter removeformat | ' +
    'fontselect fontsizeselect formatselect Bold Italic Underline Strikethrough Subscript Superscript forecolor backcolor |' +
    'numlist bullist outdent indent lineheight alignleft aligncenter alignright alignjustify alignnone |' +
    'insertImage insertVideo insertCarousel insertFoldPanel insertIframe noteDictionary insertLiterature',
  quickbars_selection_toolbar: 'bold italic underline strikethrough blockquote | insertNote ',
  quickbars_insert_toolbar: 'insertfile insertImage insertVideo insertCarousel insertFoldPanel insertIframe insertLiterature',
  font_formats: `楷体=KaiTi_GB2312,serif;
         华文行楷=STXingkai,serif
         Helvetica=helvetica;
        微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;
        苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;
        黑体=SimHei,serif;
        隶书=LiSu,serif;
        宋体=STSong,serif;
        仿宋=FangSong,serif;
        新宋体=NSimSun,serif;
        幼圆=YouYuan,serif;
        华文隶书=STLiti,serif;
        华文新魏=STXinwei,serif;
                    `,
  fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 28pt 30pt 32pt 36pt 48pt 72pt",
  block_formats: '正文=p;标题 1=h1;标题 2=h2;标题 3=h3;标题 4=h4;标题 5=h5;标题 6=h6;',
  setCuIcon(editor, _self) {
    const buttons = [
      { name: 'undo', icon: 'undo', text: '撤销', cmd: 'Undo' },
      { name: 'redo', icon: 'redo', text: '重做', cmd: 'Redo' },
      { name: 'formatpainter', icon: 'format-painter', text: '', cmd: 'mceToggleFormat' },
      { name: 'removeformat', icon: 'remove-formatting', text: '清除格式', cmd: 'RemoveFormat' },

      { name: 'fontselect', icon: 'fontselect', text: '字体', cmd: 'fontselect' },
      { name: 'fontsizeselect', icon: 'font-size', text: '字号', cmd: 'FontSizeSelect' },
      { name: 'formatselect', icon: 'font-size', text: '段落', cmd: 'formatselect' },
      { name: 'Bold', icon: 'bold', text: '加粗', cmd: 'Bold' },
      { name: 'Italic', icon: 'italic', text: '斜体', cmd: 'Italic' },
      { name: 'Underline', icon: 'underline', text: '下划线', cmd: 'Underline' },
      { name: 'Strikethrough', icon: 'strikethrough', text: '删除线', cmd: 'Strikethrough' },
      { name: 'Subscript', icon: 'subscript', text: '下标', cmd: 'Subscript' },
      { name: 'Superscript', icon: 'superscript', text: '上标', cmd: 'Superscript' },
      { name: 'forecolor', icon: 'forecolor', text: '字体颜色', cmd: 'forecolor' },
      { name: 'backcolor', icon: 'backcolor', text: '背景颜色', cmd: 'backcolor' },

      { name: 'numlist', icon: 'ordered-list', text: '有序列表', cmd: 'InsertOrderedList' },
      { name: 'bullist', icon: 'unordered-list', text: '无序列表', cmd: 'InsertUnorderedList' },
      { name: 'lineheight', icon: 'line-height', text: '行高', cmd: 'lineheight' },

      { name: 'outdent', icon: 'outdent', text: '减少缩进量', cmd: 'Outdent' },
      { name: 'indent', icon: 'indent', text: '增加缩进量', cmd: 'Indent' },
      { name: 'alignleft', icon: 'align-left', text: '左对齐', cmd: 'AlignLeft' },
      { name: 'aligncenter', icon: 'align-center', text: '居中对齐', cmd: 'AlignCenter' },
      { name: 'alignright', icon: 'align-right', text: '右对齐', cmd: 'AlignRight' },
      { name: 'alignjustify', icon: 'align-justify', text: '两端对齐', cmd: 'AlignJustify' },
      { name: 'alignnone', icon: 'align-none', text: '无对齐', cmd: 'AlignNone' },

      // { name: 'table', icon: 'table', text: '表格', cmd: 'mceInsertTable' },
      { name: 'insertImage', icon: 'image', text: '图片', cmd: 'mceImage' },
      { name: 'insertVideo', icon: 'embed', text: '视频', cmd: 'mceinsertVideo' },
      { name: 'insertCarousel', icon: 'gallery', text: '轮播图', cmd: 'mceInsertCarousel' },
      { name: 'insertFoldPanel', icon: 'copy', text: '折叠面板', cmd: 'mceInsertFoldPanel' },
      { name: 'insertIframe', icon: 'browse', text: 'H5网站', cmd: 'insertIframe' },
      { name: 'noteDictionary', icon: 'paste-text', text: '重点词词典', cmd: 'mceInsertIframe' },
      { name: 'insertLiterature', icon: 'new-document', text: '文献', cmd: 'mceInsertLiterature' },

    ];
    buttons.forEach(btn => {
      if (btn.name == 'insertImage') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openImageDialog()
          },
        });
        return false
      }
      if (btn.name == 'insertVideo') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openVideoDialog()
          },
        });
        return false
      }
      if (btn.name == 'insertCarousel') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openCarouselDialog()
          },
        });
        return false
      }
      if (btn.name == 'insertFoldPanel') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openFoldPanelDialog()
          },
        });
        return false
      }
      if (btn.name == 'insertIframe') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openIframeDialog()
          },
        });
        return false
      }
      if (btn.name == 'noteDictionary') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openNoteDictionary()
          },
        });
        return false
      }
      if (btn.name == 'insertLiterature') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            _self.openLiteratureDialog()
          },
        });
        return false
      }
      if (btn.name != 'fontselect' && btn.name != 'fontsizeselect' && btn.name != 'formatselect' && btn.name != 'table') {
        editor.ui.registry.addButton(btn.name, {
          icon: btn.icon,
          tooltip: btn.text,
          text: btn.text,
          onAction: () => {
            editor.execCommand(btn.cmd);
          },
        });
      }
    });
  }
}
