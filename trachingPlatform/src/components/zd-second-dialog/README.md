~~# 二次确认弹窗组件
## 组件说明
> 基于el-dialog组件二次封装，作用于二次确认弹窗统一风格

## 组件使用
```js
import ZdSecondDialog from "@/components/zd-second-dialog/index.vue";
```
```vue
<zd-delete-dialog :dialogVisible="dialogVisible" :secondDialogConfig="secondDialogConfig" @handleSecondDialog="handleDialog"></zd-delete-dialog>
```
# Attributes

| 参数                  | 类型      | 可选值                                                   | 默认值   | 说明                                                              | 备注                                                         |
|---------------------|---------|-------------------------------------------------------|-------|-----------------------------------------------------------------|------------------------------------------------------------|
| dialogVisible       | Boolean | ture,false                                            | false | 控制弹窗是否显示                                                        |  |
| secondDialogConfig  | Object  | 兼容el-dialog原有的所有属性，及定制化属性（contImg，contTitle，contDesc） | {}    |                                                                 |  |
| secondDialogConfig.contImg | String  | 图片名称（不要后缀）                                            | delete-dialog-icon | 如需新增icon，可在src/assets/components-icon文件夹下新增，图片类型为png，使用时传入图片名即可 |  |
| secondDialogConfig.contTitle | String  | 内容标题                                                  | 确定删除 |  |  |
| secondDialogConfig.contDesc | String  | 内容描述                                                  |  |  |  |

# Events


| 事件名           | 说明         | 回调参数       |
|---------------|------------|------------|
| handleSecondDialog | 点击确定按钮抛出状态 | ture |~~
