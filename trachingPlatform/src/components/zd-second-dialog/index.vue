<template>
  <el-dialog
      v-if="dialogVisible"
      class="second-dialog"
      @close="handleClose"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :title="secondDialogConfig.title"
      :width="secondDialogConfig.width"
      v-bind="$attrs.secondDialogConfig"
      :visible.sync="dialogVisible">
    <div class="dialog-content">
      <!--<div class="dialog-img" v-if="secondDialogConfig.contImg">-->
      <!--  <el-image :src="require(`../../assets/components-icon/${secondDialogConfig.contImg}.png`)" />-->
      <!--</div>-->
      <div class="dialog-title" v-if="secondDialogConfig.contTitle">{{secondDialogConfig.contTitle}}</div>
      <div v-if="secondDialogConfig.type === 'text' && secondDialogConfig.contDesc" class="dialog-desc">{{secondDialogConfig.contDesc}}</div>
      <div v-if="secondDialogConfig.type === 'html' && secondDialogConfig.contDesc" class="dialog-desc" v-html="secondDialogConfig.contDesc"></div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="footer-close" @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleDialogSubmit">确定</el-button>
    </span>
  </el-dialog>

</template>

<script>
export default {
  name: "zd-second-dialog",
  props:{
    secondDialogConfig: {
      type: Object,
      default: ()=>{
        return {}
      }
    }
  },
  data(){
    return {
      dialogVisible: false,
      isVisible: null,
      config:{
        width: '600px',
        title: '提示',
        contTitle: '确定删除',
        contDesc: '',
        contImg: 'delete-dialog-icon',
        type: 'text'
      }
    }
  },
  methods:{
    handleShow() {
      this.dialogVisible = true
      this.secondDialogConfig = {...this.config, ...this.secondDialogConfig}
      return new Promise((resolve, reject) => {
        this.isVisible = { resolve, reject }
      })
    },
    handleClose(){
      this.dialogVisible = false
      this.isVisible.reject()
    },
    handleDialogSubmit(){
      this.dialogVisible = false
      this.isVisible.resolve()
    }
  }

}
</script>

<style lang="scss" scoped>
.second-dialog{
  .dialog-content{
    //height: 170px;
    display: flex;
    flex-flow: column;
    .dialog-img{
      margin-top: 24px;
      width: 84px;
      height: 84px;
    }
    .dialog-title{
      margin: 17px 0 9px;
      height: 20px;
      line-height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
    .dialog-desc{
      height: 20px;
      line-height: 20px;
      font-weight: 400;
      font-size: 14px;
      color: #606A78;
    }
  }
  ::v-deep .el-dialog__header{
    height: 60px !important;
    background: #F7F7F7 !important;
    border-radius: 10px !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    color: #333333 !important;
  }
  .dialog-footer{
    display: flex;
    justify-content: center;
    padding-bottom: 20px !important;
    .footer-close{
      background: #F6F8FA;
      color: #222222;
    }
    ::v-deep .el-button--small {
      padding: 9px 15px;
      border-radius: 3px;
      width: 100px;
      height: 40px;
      font-weight: 400 !important;
      font-size: 16px !important;
    }
  }
}


</style>
