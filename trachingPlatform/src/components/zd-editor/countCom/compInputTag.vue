<template>
  <span class="editor-count">
    <div :class="courseInputContent.isCorrect === false ? 'editor-input1' : 'editor-input'">
    <!--    <span style="line-height: 12px;-->
    <!--color: red;-->
    <!--height: 14px;-->
    <!--width: 14px;-->
    <!--text-align: center;-->
    <!--border: 1px solid red;-->
    <!--border-radius: 16px;-->
    <!--font-size: 8px;-->
    <!--position: absolute;-->
    <!--top: 6px;-->
    <!--left: 0;-->
    <!--z-index: 2;">{{inputIndex +1}}</span>-->
        <el-input placeholder="请输入正确答案！" v-model="courseInputContent.answerCont" @input="onInputChange(courseInputContent)"></el-input>
        <div v-if="!courseInputContent.answerCont && editorState !=='view'" class="const-rules">请输入正确答案！</div>
    </div>
  </span>
</template>

<script>

export default {
  props:{
    defaultValue:{
      type: String,
      default: ''
    },
    updateValue:{
      type: Function,
      default: ()=>{}
    }
  },
  data() {
    return {
      courseInputContent: [],
      editorState: '',
      // inputIndex: 0
    }
  },
  created() {
    // 自定义元素数据绑定
    let courseContents = window.sessionStorage.getItem('courseContents')
    // 是否为点击按钮新增
    let course = window.sessionStorage.getItem('course')
    // 选取的数据
    let selectText = window.sessionStorage.getItem('selectText')
    // 编辑器状态
    this.editorState = window.sessionStorage.getItem('editorState')

    if(course){
      let timestamp = 'answerId_' + (new Date().getTime())
      this.updateValue(timestamp)

      this.courseInputContent = {
        answerId: timestamp, // 绑定id
        answerCont: selectText, // 答案
        isCorrect: true, // 答案
      }
      let conts = []
      if(courseContents) {
        let courseInputs = JSON.parse(courseContents)
        conts = [...courseInputs,this.courseInputContent]
      }else{
        conts = [this.courseInputContent]
      }
      window.sessionStorage.setItem('courseContents',JSON.stringify(conts))
      // this.inputIndex = JSON.parse(window.sessionStorage.getItem('courseContents')).length - 1
    }else{
      if(courseContents){
        let courseRe = JSON.parse(courseContents)
        courseRe.forEach((val,index)=>{
          if(val.answerId == this.defaultValue){
            this.courseInputContent = val
            // this.inputIndex = index
          }
        })
      }
    }
    this.$nextTick(()=>{
      window.sessionStorage.removeItem('course')
      window.sessionStorage.removeItem('selectText')
    })
  },
  methods: {
    onInputChange(e){
      let courseRe = JSON.parse(window.sessionStorage.getItem('courseContents'))
      courseRe?.forEach(val=>{
        if(e.answerId === val.answerId){
          val.answerCont = e.answerCont
        }
      })
      window.sessionStorage.setItem('courseContents',JSON.stringify(courseRe))
    },
  },
  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
.editor-count {
  cursor: pointer;
  ::v-deep .el-dialog {
    margin: 0 auto;
  }
  .editor-input{
    display: flex;
    ::v-deep .el-input__inner{
      border-radius: 0;
      border: unset;
      border-bottom: 1px solid #dcdfe6;
    }
  }
  .editor-input1{
    display: flex;
    ::v-deep .el-input__inner{
      border-radius: 0;
      border: unset;
      border-bottom: 1px solid #f60505;
    }
  }
  .const-rules{
    width: 120px;
    height: 12px;
    font-size: 10px;
    color: #f60505;
    position: absolute;
    top:25px;
    left: 3px;
    z-index: 9;
  }

}
</style>
