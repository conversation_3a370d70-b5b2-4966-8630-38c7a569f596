<template>
  <span class="editor-count">
    <div :class="courseSelectContent.isCorrect === false && 'editor-input'">
    <ZdSelectAll :courseSelectContent="courseSelectContent" :editorState="editorState" @selectOptions="selectOptions" @selectValue="selectValue"></ZdSelectAll>
      <!--    <el-select v-model="value" placeholder="请选择">-->
    <!--      <el-option-->
    <!--          v-for="item in options"-->
    <!--          :key="item.value"-->
    <!--          :label="item.label"-->
    <!--          :value="item.value">-->
    <!--      </el-option>-->
    <!--    </el-select>-->
    </div>
  </span>
</template>

<script>
import ZdSelectAll from "@/components/zd-select-all";
export default {
  components:{
    ZdSelectAll
  },
  props:{
    defaultValue:{
      type: String,
      default: ''
    },
    updateValue:{
      type: Function,
      default: ()=>{}
    }
  },
  data() {
    return {
      value: '',
      editorState: '',
      // inputIndex: 0,
      options: [],
      courseSelectContent: [],
    }
  },
  created() {
    // 自定义元素数据绑定
    let courseContents = window.sessionStorage.getItem('courseContents')
    this.editorState = window.sessionStorage.getItem('editorState')
    // 是否为点击按钮新增
    let course = window.sessionStorage.getItem('course')

    if(course){
      let timestamp = 'answerId_' + (new Date().getTime())
      this.updateValue(timestamp)

      this.courseSelectContent = {
        answerId: timestamp, // 绑定id
        isCorrect: true, // 答案
      }
      let conts = []
      if(courseContents) {
        let courseInputs = JSON.parse(courseContents)
        conts = [ ...courseInputs, this.courseSelectContent]
      }else{
        conts = [this.courseSelectContent]
      }
      window.sessionStorage.setItem('courseContents',JSON.stringify(conts))
      // this.inputIndex = JSON.parse(window.sessionStorage.getItem('courseContents')).length -1
    }else{
      if(courseContents){
        let courseRe = JSON.parse(courseContents)
        courseRe.forEach((val,index)=>{
          if(val.answerId == this.defaultValue){
            this.courseSelectContent = val
            // this.inputIndex = index
          }
        })
      }
    }
    this.$nextTick(()=>{
      window.sessionStorage.removeItem('course')
      window.sessionStorage.removeItem('selectText')
    })
  },
  methods: {
    // 选项
    selectOptions(e){
      let courseContents = JSON.parse(window.sessionStorage.getItem('courseContents'))
      let selects = JSON.parse(JSON.stringify(e))
      selects = selects.filter((e) => e.value);
      courseContents.forEach(val=>{
        if(val.answerId === this.courseSelectContent.answerId){
          val.selectOptions = selects
        }
      })
      window.sessionStorage.setItem('courseContents',JSON.stringify(courseContents))
    },
    // 答案
    selectValue(e){
      let courseContents = JSON.parse(window.sessionStorage.getItem('courseContents'))
      courseContents.forEach(val=>{
        if(val.answerId === this.courseSelectContent.answerId){
          val.answerCont = e
        }
      })
      window.sessionStorage.setItem('courseContents',JSON.stringify(courseContents))
    },
    onInputChange(e){
      let courseRe = JSON.parse(window.sessionStorage.getItem('courseContents'))
      courseRe.forEach(val=>{
        if(e.answerId === val.answerId){
          val.answerCont = e.answerCont
        }
      })
      window.sessionStorage.setItem('courseContents',JSON.stringify(courseRe))
    },
  },
  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
.editor-count {
  cursor: pointer;
  ::v-deep .el-dialog {
    margin: 0 auto;
  }
  .editor-input{
    display: flex;
    ::v-deep .el-input__inner{
      border: 1px solid #f60505;
    }
  }

}
</style>
