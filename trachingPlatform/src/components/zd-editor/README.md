# zd-editor 扩展封装
## 组件说明
>zdEditor基于wangEditor扩展功能(自定义菜单及对应新元素), 已包含(讨论,素材库,任务/检测)
>> 由于wangEditor本身api限制后续扩展新菜单和新元素只能调整组件源码,详情查看代码注释
> 
> 注:新元素本身尽量使用简洁模式不要嵌入负责业务组件,可以在新元素上再做扩展.
> >如果业务需要使用组件同级数据,可以临时存浏览器在新元素组件中使用完后清除.
## 组件使用
>注册引入组件(后续会优化组件自动全局注册)
```js
import ZdEditor from "@/components/zd-editor/index.vue";
```

```vue
<ZdEditor :compConfig="compConfig" @editorHtml="editorHtml" :disable="disable" :editorDefaultHtml="defaultHtml"></ZdEditor>
```

# Attributes

| 参数                          | 类型     | 可选值                                | 默认值 | 说明           | 备注                                                         |
|-----------------------------|--------|------------------------------------|-----|--------------|------------------------------------------------------------|
| compConfig                  | Object | excludeKeys,toolbarKeys,insertKeys | {}  | 配置方式于官方文档保持一致 | excludeKeys排除的菜单key,toolbarKeys只显示的菜单key,insertKeys自定义菜单配置 |
| compConfig.excludeKeys      | Array  | 编辑器的所有菜单key                        | []  | 需要排除的菜单集合 |                                                            |
| compConfig.toolbarKeys      | Array  | 编辑器的所有菜单key                        | []  | 显示的菜单集合        |                                                            |
| compConfig.insertKeys       | Object | index,keys                         | {}  | 自定义菜单配置        |                                                            |
| compConfig.insertKeys.index | Number |  | 0   | 自定义菜单配置        | 如自定义菜单需要放置最后index设置为32 |
| compConfig.insertKeys.keys  | Array | 'compDiscussTag','compResourceTag','compTaskTag' | [] | 自定义菜单配置        | 新添加菜单和元素请在可选值及时更新 |


# Events


| 事件名           | 说明       | 回调参数     |
|---------------|----------|----------|
| editorHtml | 组件HTML数据 |  |
