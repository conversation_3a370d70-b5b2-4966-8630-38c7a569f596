<template>
  <div style="display: inline-block" class="editor-count">
      <template v-for="(item,index) in courseResourceContents">
        <el-tag draggable="true" style="cursor: move" v-if="!item.viewComponent" @click="openDialog" >素材库</el-tag>
        <comp-teach-resource draggable="true" style="cursor: move"v-else :pIndex="index" :isEdit="true" :isPrepare="true" :list="item.content"/>
      </template>
    <el-dialog v-if="dialogConfig.isShow" class="insert-dialog" :title="dialogConfig.title" :show-close="false"
               :visible.sync="dialogConfig.isShow" :width="dialogConfig.width" :top="dialogConfig.top"
               :before-close="handleClose">
      <comp-resource-dialog ref="resourceRef" :isType="dialogConfig.type" :isShow="dialogConfig" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleDialogSubmit">插 入</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CompResourceDialog from "@/views/teaching-page/components/dialog/resource-dialog.vue";
import CompTeachResource from "@/views/teaching-page/components/teachResource.vue";

export default {
  props:{
    defaultValue:{
      type: String,
      default: ''
    },
    updateValue:{
      type: Function,
      default: ()=>{}
    }
  },
  components: {
    CompTeachResource,
    CompResourceDialog
  },
  data() {
    return {
      courseResourceContents: [],
      compResource: {},
      dialogConfig: {
        title: "",
        isShow: false,
        width: "",
        type: null,
        top: "15vh"
      }
    }
  },
  created() {
    if(window.sessionStorage.getItem('course')){
      this.courseResourceContents.push(
        {
          compCourseId: this.courseResourceContents.length,
          viewComponent: false
        }
      )
    }else{
      if(window.sessionStorage.getItem('eventKey') === 'true'){
        if(window.sessionStorage.getItem('courseContents') && this.defaultValue){
          let courseRe = JSON.parse(window.sessionStorage.getItem('courseContents'))
          courseRe.forEach(val=>{
            if(val.resourceId == this.defaultValue){
              val.content = JSON.parse(val.content)
              this.courseResourceContents = [val]
            }
          })
        }else{
          this.courseResourceContents = [{
            compCourseId: this.courseResourceContents.length,
            viewComponent: false
          }]
        }
      }else{
        if(window.sessionStorage.getItem('courseResourceContents')){
          this.courseResourceContents = JSON.parse(window.sessionStorage.getItem('courseResourceContents'))
        }else{
          this.CoursePrepareSearchBy()
        }
      }
    }
    this.$nextTick(()=>{
      window.sessionStorage.removeItem('course')
    })
  },
  methods: {
    add2(e){
      console.log(e)
      console.log(this.courseResourceContents)
    },
    openDialog() {
      this.dialogConfig.title = "素材库";
      this.dialogConfig.width = "750px";
      this.dialogConfig.type = 6;
      this.dialogConfig.isShow = true;
    },
    handleClose() {
      this.dialogConfig = {
        title: "",
        isShow: false,
        width: "",
        type: null
      };
      this.attachmentFiles = [];
    },
    handleDialogSubmit() {
      let resourceToolList = [];
      let coursePrepareType = null;
      // 素材
      const resourceList = [].concat(JSON.parse(JSON.stringify(this.$refs.resourceRef.checkList)));
      if (resourceList.length === 0) {
        return this.$message({
          message: "未选择数据!",
          type: "warning"
        });
      }
      coursePrepareType = this.$refs.resourceRef.coursePrepareType;
      resourceToolList = resourceList;
      for (let index = 0; index < resourceToolList.length; index++) {
        const item = resourceToolList[index];
        this.courseResourceContents.forEach(val=>{
          if(index === val.compCourseId){
            val.resourceName = item.name
            val.resourceId = item.id
            val.filesType = item.filesType
            val.url = item.file
            val.type = coursePrepareType ? coursePrepareType : this.dialogConfig.type
            val.content = item
            val.viewComponent = true
            this.updateValue(item.id)
          }
        })
        let curseContent = JSON.parse(JSON.stringify(this.courseResourceContents))
        curseContent.forEach(item=>{
          item.content = JSON.stringify(item.content)
        })
        if(window.sessionStorage.getItem('courseContents')){
          curseContent = [...JSON.parse(window.sessionStorage.getItem('courseContents')), ...curseContent]
        }
        curseContent = JSON.stringify(curseContent)
        window.sessionStorage.setItem('courseContents',curseContent)
      }
      this.dialogConfig.isShow = false;
    },
    CoursePrepareSearchBy() {
      this.courseResourceContents = [];
      this.currentTypeResources = [];
      const param = {
        params: {
          categoryId: window.sessionStorage.getItem('categoryId'),
        },
        orderBy: 'sort asc'
      };
      this.$api.CoursePrepareSearchBy(param).then(res => {
        if (res.code === 200) {
          let courseRe = []
          res.data?.forEach(val=>{
            val.viewComponent = true
            val.content = JSON.parse(val.content)
            if(val.type === 6 || val.type === 7){
              courseRe.push(val)
            }
          })
          this.courseResourceContents = [...courseRe]
          window.sessionStorage.setItem('courseResourceContents',JSON.stringify(this.courseResourceContents))
          if(!window.sessionStorage.getItem('courseContents')){
            window.sessionStorage.setItem('courseContents',JSON.stringify(res.data))
          }
        }
        this.lessonsLoading = false;
      }).catch(() => {
        this.lessonsLoading = false;
      });
    },
  },
  beforeDestroy() {}
}
</script>

<style scoped>
.editor-count{
  cursor: pointer;
  ::v-deep .el-dialog{
    margin: 0 auto;
  }
}

</style>
