import { DomEditor, SlateTransforms } from '@wangeditor/editor'
import { h } from 'snabbdom'
import compDiscussTag from './countCom/compDiscussTag.vue'
import compResourceTag from './countCom/compResourceTag.vue'
import compTaskTag from './countCom/compTaskTag.vue'
import compInputTag from './countCom/compInputTag.vue'
import compSelectTag from './countCom/compSelectTag.vue'

const elMap = new WeakMap()

export default {
  editorPlugin: function (editor) {
    const { isInline, isVoid } = editor
    const newEditor = editor
    newEditor.isInline = elem => {
      const type = DomEditor.getNodeType(elem)
      if (type === 'compDiscussTag' || type === 'compResourceTag' || type === 'compTaskTag' || type === 'compInputTag' || type === 'compSelectTag') return true // 针对 type: attachment ，设置为 inline
      return isInline(elem)
    }
    newEditor.isVoid = elem => {
      const type = DomEditor.getNodeType(elem)
      if (type === 'compDiscussTag' || type === 'compResourceTag' || type === 'compTaskTag' || type === 'compInputTag' || type === 'compSelectTag') return true // 针对 type: attachment ，设置为 void
      return isVoid(elem)
    }
    return newEditor // 返回 newEditor ，重要！！！
  }, // 插件
  renderElems: [
    {
      type: 'compDiscussTag', // 新元素 type ，重要！！！
      renderElem: function (elem, children, editor) {
        // const isDisabled = editor.isDisabled()
        // const selected = DomEditor.isNodeSelected(editor, elem)
        const { vueValue } = elem
        // 附件元素 vnode
        const attachVnode = h(
          // HTML tag
          'div',
          // HTML 属性、样式、事件
          {
            props: {
              contentEditable: false,
            }, // HTML 属性，驼峰式写法
            style: {
              marginLeft: '3px',
              marginRight: '3px',
            }, // style ，驼峰式写法
            dataset: {
              vueValue
            },
            hook: {
              insert(vnode) {
                const el = vnode.elm
                const instance = new Vue({
                  ...compDiscussTag,
                  propsData: {
                    disabled: editor.isDisabled(),
                    defaultValue: el.dataset.vueValue,
                    updateValue: value => {
                      // 更新slate node的值，为了删除撤回是，节点是删除时的数据，而不是初始数据
                      // const { elem } = elMap.get(vnode.elm)
                      const location = DomEditor.findPath(editor, elem)
                      // console.log(location)
                      SlateTransforms.setNodes(editor, {
                        vueValue: value
                      }, {
                        at: location
                      })
                    }
                  }
                }).$mount()
                el.innerHTML = ''
                el.appendChild(instance.$el)
                elMap.set(vnode.elm, instance)
              },
              destroy(vnode) {
                const instance = elMap.get(vnode.elm)
                if (instance) {
                  instance.$destroy()
                }
                elMap.delete(vnode.elm)
              },
            }
          },
        )
        return attachVnode
      },
    },
    {
      type: 'compResourceTag', // 新元素 type ，重要！！！
      renderElem: function (elem, children, editor) {
        // const isDisabled = editor.isDisabled()
        // const selected = DomEditor.isNodeSelected(editor, elem)
        const { vueValue } = elem
        // 附件元素 vnode
        const attachVnode = h(
            // HTML tag
            'div',
            // HTML 属性、样式、事件
            {
              props: {
                contentEditable: false,
              }, // HTML 属性，驼峰式写法
              style: {
                marginLeft: '3px',
                marginRight: '3px',
                // borderRadius: '4px'
              }, // style ，驼峰式写法
              dataset: {
                vueValue
              },
              hook: {
                insert(vnode) {
                  const el = vnode.elm
                  const instance = new Vue({
                    ...compResourceTag,
                    propsData: {
                      disabled: editor.isDisabled(),
                      defaultValue: el.dataset.vueValue,
                      updateValue: value => {
                        // 更新slate node的值，为了删除撤回是，节点是删除时的数据，而不是初始数据
                        // const { elem } = elMap.get(vnode.elm)
                        const location = DomEditor.findPath(editor, elem)
                        // console.log(location)
                        SlateTransforms.setNodes(editor, {
                          vueValue: value
                        }, {
                          at: location
                        })
                      }
                    }
                  }).$mount()
                  el.innerHTML = ''
                  el.appendChild(instance.$el)
                  elMap.set(vnode.elm, instance)
                },
                destroy(vnode) {
                  const instance = elMap.get(vnode.elm)
                  if (instance) {
                    instance.$destroy()
                  }
                  elMap.delete(vnode.elm)
                },
              }
            },
        )
        return attachVnode
      },
    },
    {
      type: 'compTaskTag', // 新元素 type ，重要！！！
      renderElem: function (elem, children, editor) {
        // const isDisabled = editor.isDisabled()
        // const selected = DomEditor.isNodeSelected(editor, elem)
        const { vueValue } = elem
        // 附件元素 vnode
        const attachVnode = h(
            // HTML tag
            'div',
            // HTML 属性、样式、事件
            {
              props: {
                contentEditable: false,
              }, // HTML 属性，驼峰式写法
              style: {
                marginLeft: '3px',
                marginRight: '3px',
                // borderRadius: '4px'
              }, // style ，驼峰式写法
              dataset: {
                vueValue
              },
              hook: {
                insert(vnode) {
                  const el = vnode.elm
                  const instance = new Vue({
                    ...compTaskTag,
                    propsData: {
                      disabled: editor.isDisabled(),
                      defaultValue: el.dataset.vueValue,
                      updateValue: value => {
                        // 更新slate node的值，为了删除撤回是，节点是删除时的数据，而不是初始数据
                        // const { elem } = elMap.get(vnode.elm)
                        const location = DomEditor.findPath(editor, elem)
                        SlateTransforms.setNodes(editor, {
                          defaultValue: value,
                          vueValue: value
                        }, {
                          at: location
                        })
                      }
                    }
                  }).$mount()
                  el.innerHTML = ''
                  el.appendChild(instance.$el)
                  elMap.set(vnode.elm, instance)
                },
                destroy(vnode) {
                  const instance = elMap.get(vnode.elm)
                  if (instance) {
                    instance.$destroy()
                  }
                  elMap.delete(vnode.elm)
                },
              }
            },
        )
        return attachVnode
      },
    },
    {
      type: 'compInputTag', // 新元素 type ，重要！！！
      renderElem: function (elem, children, editor) {
        // const isDisabled = editor.isDisabled()
        // const selected = DomEditor.isNodeSelected(editor, elem)
        const { vueValue } = elem
        // 附件元素 vnode
        const attachVnode = h(
            // HTML tag
            'span',
            // HTML 属性、样式、事件
            {
              props: {
                contentEditable: false,
              }, // HTML 属性，驼峰式写法
              style: {
                marginLeft: '3px',
                marginRight: '3px',
                display: 'inline-block',
                // borderRadius: '4px'
              }, // style ，驼峰式写法
              dataset: {
                vueValue
              },
              hook: {
                insert(vnode) {
                  const el = vnode.elm
                  const instance = new Vue({
                    ...compInputTag,
                    propsData: {
                      disabled: editor.isDisabled(),
                      defaultValue: el.dataset.vueValue,
                      updateValue: value => {
                        // 更新slate node的值，为了删除撤回是，节点是删除时的数据，而不是初始数据
                        // const { elem } = elMap.get(vnode.elm)
                        const location = DomEditor.findPath(editor, elem)
                        SlateTransforms.setNodes(editor, {
                          defaultValue: value,
                          vueValue: value
                        }, {
                          at: location
                        })
                      }
                    }
                  }).$mount()
                  el.innerHTML = ''
                  el.appendChild(instance.$el)
                  elMap.set(vnode.elm, instance)
                },
                destroy(vnode) {
                  const instance = elMap.get(vnode.elm)
                  if (instance) {
                    instance.$destroy()
                  }
                  elMap.delete(vnode.elm)
                },
              }
            },
        )
        return attachVnode
      },
    },
    {
      type: 'compSelectTag', // 新元素 type ，重要！！！
      renderElem: function (elem, children, editor) {
        // const isDisabled = editor.isDisabled()
        // const selected = DomEditor.isNodeSelected(editor, elem)
        const { vueValue } = elem
        // 附件元素 vnode
        const attachVnode = h(
            // HTML tag
            'span',
            // HTML 属性、样式、事件
            {
              props: {
                contentEditable: false,
              }, // HTML 属性，驼峰式写法
              style: {
                marginLeft: '3px',
                marginRight: '3px',
                display: 'inline-block',
                // borderRadius: '4px'
              }, // style ，驼峰式写法
              dataset: {
                vueValue
              },
              hook: {
                insert(vnode) {
                  const el = vnode.elm
                  const instance = new Vue({
                    ...compSelectTag,
                    propsData: {
                      disabled: editor.isDisabled(),
                      defaultValue: el.dataset.vueValue,
                      updateValue: value => {
                        // 更新slate node的值，为了删除撤回是，节点是删除时的数据，而不是初始数据
                        // const { elem } = elMap.get(vnode.elm)
                        const location = DomEditor.findPath(editor, elem)
                        SlateTransforms.setNodes(editor, {
                          defaultValue: value,
                          vueValue: value
                        }, {
                          at: location
                        })
                      }
                    }
                  }).$mount()
                  el.innerHTML = ''
                  el.appendChild(instance.$el)
                  elMap.set(vnode.elm, instance)
                },
                destroy(vnode) {
                  const instance = elMap.get(vnode.elm)
                  if (instance) {
                    instance.$destroy()
                  }
                  elMap.delete(vnode.elm)
                },
              }
            },
        )
        return attachVnode
      },
    },
  ],
  elemsToHtml: [
    {
      type: 'compDiscussTag', // 新元素的 type ，重要！！！
      elemToHtml: function (elem) {
        const vueValue = elem.vueValue
        // 生成 HTML 代码
        const html = `<span data-w-e-type="compDiscussTag" data-vue-value="${vueValue}"></span>`
        return html
      },
    },
    {
      type: 'compResourceTag', // 新元素的 type ，重要！！！
      elemToHtml: function (elem) {
        const vueValue = elem.vueValue
        // 生成 HTML 代码
        const html = `<span data-w-e-type="compResourceTag" data-vue-value="${vueValue}"></span>`
        return html
      },
    },
    {
      type: 'compTaskTag', // 新元素的 type ，重要！！！
      elemToHtml: function (elem) {
        const vueValue = elem.vueValue
        // 生成 HTML 代码
        const html = `<span data-w-e-type="compTaskTag" data-vue-value="${vueValue}"></span>`
        return html
      },
    },
    {
      type: 'compInputTag', // 新元素的 type ，重要！！！
      elemToHtml: function (elem) {
        const vueValue = elem.vueValue
        // 生成 HTML 代码
        const html = `<span data-w-e-type="compInputTag" data-vue-value="${vueValue}"></span>`
        return html
      },
    },
    {
      type: 'compSelectTag', // 新元素的 type ，重要！！！
      elemToHtml: function (elem) {
        const vueValue = elem.vueValue
        // 生成 HTML 代码
        const html = `<span data-w-e-type="compSelectTag" data-vue-value="${vueValue}"></span>`
        return html
      },
    },
  ],
  parseElemsHtml: [
    {
      selector: `span[data-w-e-type="compDiscussTag"]`, // CSS 选择器，匹配特定的 HTML 标签
      parseElemHtml: function (domElem,children,editor) {
        const vueValue = domElem.getAttribute('data-vue-value') || ''
        const myResume = {
          type: 'compDiscussTag',
          vueValue,
          children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
        }
        return myResume
      },
    },
    {
      selector: `span[data-w-e-type="compResourceTag"]`, // CSS 选择器，匹配特定的 HTML 标签
      parseElemHtml: function (domElem) {
        const vueValue = domElem.getAttribute('data-vue-value') || ''
        const myResume = {
          type: 'compResourceTag',
          vueValue,
          children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
        }
        return myResume
      },
    },
    {
      selector: `span[data-w-e-type="compTaskTag"]`, // CSS 选择器，匹配特定的 HTML 标签
      parseElemHtml: function (domElem) {
        const vueValue = domElem.getAttribute('data-vue-value') || ''
        const myResume = {
          type: 'compTaskTag',
          vueValue,
          children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
        }
        return myResume
      },
    },
    {
      selector: `span[data-w-e-type="compInputTag"]`, // CSS 选择器，匹配特定的 HTML 标签
      parseElemHtml: function (domElem) {
        const vueValue = domElem.getAttribute('data-vue-value') || ''
        const myResume = {
          type: 'compInputTag',
          vueValue,
          children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
        }
        return myResume
      },
    },
    {
      selector: `span[data-w-e-type="compSelectTag"]`, // CSS 选择器，匹配特定的 HTML 标签
      parseElemHtml: function (domElem) {
        const vueValue = domElem.getAttribute('data-vue-value') || ''
        const myResume = {
          type: 'compSelectTag',
          vueValue,
          children: [{ text: '' }], // void node 必须有 children ，其中有一个空字符串，重要！！！
        }
        return myResume
      },
    },
  ]
}
