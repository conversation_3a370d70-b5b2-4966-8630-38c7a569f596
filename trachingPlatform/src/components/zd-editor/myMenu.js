import countbtns from './countbtn'

import { Boot } from '@wangeditor/editor'

// 自定义菜单类
// 讨论
class MyButtonDiscussMenu {                       // JS 语法
  constructor() {
    this.title = '讨论' // 自定义菜单标题
    this.tag = 'button'
    this.iconSvg = '<svg t="1724932855479" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9294" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M889.2265625 811.61914063l-99.58007813-76.20117188H379.8125c-7.91015625-0.26367188-14.23828125-6.76757813-14.23828125-14.765625v-47.19726563h373.44726563c30.5859375-0.08789063 55.28320313-24.87304688 55.28320312-55.37109375v-245.21484374h80.59570313c7.91015625 0 14.4140625 6.41601563 14.41406249 14.23828124l-0.08789062 335.83007813v88.68164063z m-654.78515625-175.07812501L134.7734375 712.7421875V226.35546875c0-10.37109375 8.0859375-18.45703125 18.36914063-18.45703125h585.79101562c10.1953125 0 18.36914063 8.26171875 18.36914063 18.45703125v391.72851563c0 10.37109375-8.0859375 18.45703125-18.36914063 18.45703124h-504.4921875z m640.45898438-300.67382812h-80.59570313V226.35546875c0-30.5859375-24.78515625-55.37109375-55.37109375-55.37109375H153.14257812c-30.5859375 0-55.37109375 24.78515625-55.37109374 55.37109375v486.9140625c0 26.98242188 16.25976563 39.11132813 32.43164062 39.11132813 8.87695313 0 17.84179688-3.42773438 26.71875-10.1953125l89.82421875-68.73046876h81.82617188v47.19726563c0 28.4765625 22.93945313 51.6796875 51.15234374 51.6796875h397.44140626l93.16406249 71.27929688c8.0859375 6.15234375 16.61132813 9.31640625 24.78515625 9.31640624 4.48242188 0 8.96484375-1.0546875 13.0078125-2.98828125 11.42578125-5.625 18.01757813-18.01757813 18.01757813-34.1015625V387.10742187c0-28.30078125-22.93945313-51.15234375-51.24023438-51.24023437z" fill="#707070" p-id="9295"></path><path d="M259.2265625 479.65625h49.48242188v-55.37109375h-49.48242188v55.37109375z m329.765625 0h49.48242188v-55.37109375h-49.48242188v55.37109375z m-164.8828125 0h49.48242188v-55.37109375H424.109375v55.37109375z" fill="#707070" p-id="9296"></path></svg>'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue() {
    return false
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive() {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled() {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    window.sessionStorage.setItem('course', true)
    window.sessionStorage.removeItem('index')
    editor.insertNode({
      type: 'compDiscussTag',
      vueValue: value,
      children: [{text: ''}]  // void 元素必须有一个 children ，其中只有一个空字符串，重要！！！
    }) // value 即 this.value(editor) 的返回值
  }

}
// 素材
class MyButtonRescurceMenu {
  constructor() {
    this.title = '素材库' // 自定义菜单标题
    this.iconSvg = '<svg t="1724933296242" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9441" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M755.41 878.33H116.17V145.67h639.24v211.97h-28.33V174H144.5v676h582.58V590.63h28.33v287.7z" p-id="9442"></path><path d="M358.98 561.92l199.43-116.36 29.91 58.18 164.53-116.36 134.61 160" fill="#FFE100" p-id="9443"></path><path d="M879.5 357.52v190.62H304.88V357.52H879.5m28.33-28.33H276.55v247.28h631.28V329.19z" p-id="9444"></path><path d="M366.12 574.16l-14.28-24.47 212.33-123.9 29.29 56.97L755.3 368.3l143 169.96-21.67 18.23-126.24-150.04-167.21 118.27-30.54-59.4-186.52 108.84zM381 666v97.36H206.66V666H381m28.33-28.33h-231v154h231v-154zM664.92 666v97.36H490.55V666h174.37m28.32-28.33h-231v154h231v-154zM178.33 227.37H294.7v28.32H178.33zM316.52 227.37h29.09v28.32h-29.09z" p-id="9445"></path></svg>' // 可选
    this.tag = 'button'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue() {
    return ''
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive() {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled() {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    window.sessionStorage.setItem('course', true)
    editor.insertNode({
      type: 'compResourceTag',
      vueValue: value,
      children: [{text: ''}]  // void 元素必须有一个 children ，其中只有一个空字符串，重要！！！
    }) // value 即 this.value(editor) 的返回值
  }

}
// 任务/检测
class MyButtonTaskMenu {
  constructor() {
    this.title = '任务/测验' // 自定义菜单标题
    this.iconSvg = '<svg t="1724933379439" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9590" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M488.72954977 768.71247399h-173.94996947c-23.88849695 0-43.25050315-19.36793102-43.25050317-43.25050375V292.97471146c0-23.88849695 19.3620062-43.25050315 43.25050317-43.25050373h389.23675621c23.88849695 0 43.25050315 19.3620062 43.25050315 43.25050373v147.99966774c15.57018094 6.83120992 29.80137411 15.83086918 43.25050315 25.99769979V292.97471146c0-47.76514485-38.72993723-86.49508207-86.5010063-86.49508206H314.78550512c-47.77106967 0-86.50100689 38.72401239-86.5010063 86.49508206v432.48725878c0 47.77106967 38.72993723 86.50100689 86.49508146 86.5010069h217.41376346a218.01038329 218.01038329 0 0 1-43.46379397-43.25050315z m0 0" fill="#323333" p-id="9591"></path><path d="M670.77269601 490.98499494c-90.17433738 0-163.26768784 73.09927527-163.26768784 163.26768783s73.09927527 163.26768784 163.26768784 163.26768783 163.26768784-73.09927527 163.26768784-163.26768783-73.09927527-163.26768784-163.26768784-163.26768783z m0 285.63698851c-67.59520467 0-122.38707518-54.79779534-122.38707516-122.38707458s54.79779534-122.38707518 122.38707516-122.38707516S793.16569542 586.64562905 793.16569542 654.23490887c-0.00592482 67.58927984-54.79779534 122.38707518-122.39299941 122.38707458z m0 0" fill="#323333" p-id="9592"></path><path d="M739.42842633 639.39346861h-46.38468326v-23.18937922c0-12.8092584-10.38604565-23.19530406-23.19530466-23.19530405-12.8092584 0-23.19530406 10.38012082-23.19530405 23.19530405v46.38468387c0 12.8092584 10.38604565 23.19530406 23.19530405 23.19530405H739.42842633c12.8092584 0 23.18937922-10.38604565 23.18937983-23.19530405 0-12.8092584-10.38604565-23.19530406-23.18937983-23.19530465zM500.86931459 313.97194204a23.05903539 23.05903539 0 0 0-32.60969469 0L390.98341273 391.24814981l-30.93892191-30.93892193c-8.69749841-8.69749841-22.79834775-8.69749841-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272736 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58237076a22.31844448 22.31844448 0 0 0 15.81309529-6.54089756c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.7655688L500.86931459 346.5342387a23.0187473 23.0187473 0 0 0 6.75418836-16.28114833c0-6.10839289-2.42913757-11.96202297-6.75418836-16.28114833z m76.20975669 39.70751707c-12.8092584 0-23.19530406 10.38012082-23.19530407 23.18937923s10.38604565 23.19530406 23.19530407 23.19530407h139.15405101c12.8092584 0 23.18937922-10.38012082 23.18937922-23.19530407 0-12.8092584-10.38012082-23.18937922-23.18937921-23.18937923H577.07907128zM468.26554473 476.31537229L390.98341273 553.59750489l-30.93892191-30.93892195c-8.69749841-8.69157359-22.79834775-8.69157359-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272737 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58829557a22.31844448 22.31844448 0 0 0 15.81309529-6.54089814c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.75964338l91.60812062-91.56664802c7.93320878-9.15962721 7.43553161-22.89906803-1.14347205-31.46622202-8.57307942-8.56715461-22.31251966-9.04705727-31.46029781-1.10199945z m-1e-8 0" fill="#323333" p-id="9593"></path></svg>' // 可选
    this.tag = 'button'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue() {
    return ''
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive() {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled() {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    window.sessionStorage.setItem('course', true)
    editor.insertNode({
      type: 'compTaskTag',
      vueValue: value,
      children: [{text: ''}]  // void 元素必须有一个 children ，其中只有一个空字符串，重要！！！
    }) // value 即 this.value(editor) 的返回值
  }

}
// 输入框
class MyButtonInputMenu {
  constructor() {
    this.title = '插入输入框' // 自定义菜单标题
    this.iconSvg = '<svg t="1724933379439" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9590" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M488.72954977 768.71247399h-173.94996947c-23.88849695 0-43.25050315-19.36793102-43.25050317-43.25050375V292.97471146c0-23.88849695 19.3620062-43.25050315 43.25050317-43.25050373h389.23675621c23.88849695 0 43.25050315 19.3620062 43.25050315 43.25050373v147.99966774c15.57018094 6.83120992 29.80137411 15.83086918 43.25050315 25.99769979V292.97471146c0-47.76514485-38.72993723-86.49508207-86.5010063-86.49508206H314.78550512c-47.77106967 0-86.50100689 38.72401239-86.5010063 86.49508206v432.48725878c0 47.77106967 38.72993723 86.50100689 86.49508146 86.5010069h217.41376346a218.01038329 218.01038329 0 0 1-43.46379397-43.25050315z m0 0" fill="#323333" p-id="9591"></path><path d="M670.77269601 490.98499494c-90.17433738 0-163.26768784 73.09927527-163.26768784 163.26768783s73.09927527 163.26768784 163.26768784 163.26768783 163.26768784-73.09927527 163.26768784-163.26768783-73.09927527-163.26768784-163.26768784-163.26768783z m0 285.63698851c-67.59520467 0-122.38707518-54.79779534-122.38707516-122.38707458s54.79779534-122.38707518 122.38707516-122.38707516S793.16569542 586.64562905 793.16569542 654.23490887c-0.00592482 67.58927984-54.79779534 122.38707518-122.39299941 122.38707458z m0 0" fill="#323333" p-id="9592"></path><path d="M739.42842633 639.39346861h-46.38468326v-23.18937922c0-12.8092584-10.38604565-23.19530406-23.19530466-23.19530405-12.8092584 0-23.19530406 10.38012082-23.19530405 23.19530405v46.38468387c0 12.8092584 10.38604565 23.19530406 23.19530405 23.19530405H739.42842633c12.8092584 0 23.18937922-10.38604565 23.18937983-23.19530405 0-12.8092584-10.38604565-23.19530406-23.18937983-23.19530465zM500.86931459 313.97194204a23.05903539 23.05903539 0 0 0-32.60969469 0L390.98341273 391.24814981l-30.93892191-30.93892193c-8.69749841-8.69749841-22.79834775-8.69749841-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272736 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58237076a22.31844448 22.31844448 0 0 0 15.81309529-6.54089756c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.7655688L500.86931459 346.5342387a23.0187473 23.0187473 0 0 0 6.75418836-16.28114833c0-6.10839289-2.42913757-11.96202297-6.75418836-16.28114833z m76.20975669 39.70751707c-12.8092584 0-23.19530406 10.38012082-23.19530407 23.18937923s10.38604565 23.19530406 23.19530407 23.19530407h139.15405101c12.8092584 0 23.18937922-10.38012082 23.18937922-23.19530407 0-12.8092584-10.38012082-23.18937922-23.18937921-23.18937923H577.07907128zM468.26554473 476.31537229L390.98341273 553.59750489l-30.93892191-30.93892195c-8.69749841-8.69157359-22.79834775-8.69157359-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272737 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58829557a22.31844448 22.31844448 0 0 0 15.81309529-6.54089814c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.75964338l91.60812062-91.56664802c7.93320878-9.15962721 7.43553161-22.89906803-1.14347205-31.46622202-8.57307942-8.56715461-22.31251966-9.04705727-31.46029781-1.10199945z m-1e-8 0" fill="#323333" p-id="9593"></path></svg>' // 可选
    this.tag = 'button'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue() {
    return ''
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive() {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled() {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    window.sessionStorage.setItem('course', true)
    editor.insertNode({
      type: 'compInputTag',
      vueValue: value,
      children: [{text: ''}]  // void 元素必须有一个 children ，其中只有一个空字符串，重要！！！
    }) // value 即 this.value(editor) 的返回值
  }

}
// 下拉框
class MyButtonSelectMenu {
  constructor() {
    this.title = '插入单选下拉框' // 自定义菜单标题
    this.iconSvg = '<svg t="1724933379439" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9590" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M488.72954977 768.71247399h-173.94996947c-23.88849695 0-43.25050315-19.36793102-43.25050317-43.25050375V292.97471146c0-23.88849695 19.3620062-43.25050315 43.25050317-43.25050373h389.23675621c23.88849695 0 43.25050315 19.3620062 43.25050315 43.25050373v147.99966774c15.57018094 6.83120992 29.80137411 15.83086918 43.25050315 25.99769979V292.97471146c0-47.76514485-38.72993723-86.49508207-86.5010063-86.49508206H314.78550512c-47.77106967 0-86.50100689 38.72401239-86.5010063 86.49508206v432.48725878c0 47.77106967 38.72993723 86.50100689 86.49508146 86.5010069h217.41376346a218.01038329 218.01038329 0 0 1-43.46379397-43.25050315z m0 0" fill="#323333" p-id="9591"></path><path d="M670.77269601 490.98499494c-90.17433738 0-163.26768784 73.09927527-163.26768784 163.26768783s73.09927527 163.26768784 163.26768784 163.26768783 163.26768784-73.09927527 163.26768784-163.26768783-73.09927527-163.26768784-163.26768784-163.26768783z m0 285.63698851c-67.59520467 0-122.38707518-54.79779534-122.38707516-122.38707458s54.79779534-122.38707518 122.38707516-122.38707516S793.16569542 586.64562905 793.16569542 654.23490887c-0.00592482 67.58927984-54.79779534 122.38707518-122.39299941 122.38707458z m0 0" fill="#323333" p-id="9592"></path><path d="M739.42842633 639.39346861h-46.38468326v-23.18937922c0-12.8092584-10.38604565-23.19530406-23.19530466-23.19530405-12.8092584 0-23.19530406 10.38012082-23.19530405 23.19530405v46.38468387c0 12.8092584 10.38604565 23.19530406 23.19530405 23.19530405H739.42842633c12.8092584 0 23.18937922-10.38604565 23.18937983-23.19530405 0-12.8092584-10.38604565-23.19530406-23.18937983-23.19530465zM500.86931459 313.97194204a23.05903539 23.05903539 0 0 0-32.60969469 0L390.98341273 391.24814981l-30.93892191-30.93892193c-8.69749841-8.69749841-22.79834775-8.69749841-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272736 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58237076a22.31844448 22.31844448 0 0 0 15.81309529-6.54089756c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.7655688L500.86931459 346.5342387a23.0187473 23.0187473 0 0 0 6.75418836-16.28114833c0-6.10839289-2.42913757-11.96202297-6.75418836-16.28114833z m76.20975669 39.70751707c-12.8092584 0-23.19530406 10.38012082-23.19530407 23.18937923s10.38604565 23.19530406 23.19530407 23.19530407h139.15405101c12.8092584 0 23.18937922-10.38012082 23.18937922-23.19530407 0-12.8092584-10.38012082-23.18937922-23.18937921-23.18937923H577.07907128zM468.26554473 476.31537229L390.98341273 553.59750489l-30.93892191-30.93892195c-8.69749841-8.69157359-22.79834775-8.69157359-31.48992135 0-8.69749841 8.69749841-8.69749841 22.79834775 0 31.49584619l43.17940641 43.18533123c0.97757985 1.67077275 1.75964399 3.38894354 3.20527746 4.82272737 4.82272737 4.77532934 11.13256081 6.81936026 17.34759881 6.58829557a22.31844448 22.31844448 0 0 0 15.81309529-6.54089814c0.51545105-0.50952622 0.74059092-1.16124653 1.16124653-1.75964338l91.60812062-91.56664802c7.93320878-9.15962721 7.43553161-22.89906803-1.14347205-31.46622202-8.57307942-8.56715461-22.31251966-9.04705727-31.46029781-1.10199945z m-1e-8 0" fill="#323333" p-id="9593"></path></svg>' // 可选
    this.tag = 'button'
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue() {
    return ''
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive() {
    return false
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled() {
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    if (this.isDisabled(editor)) return
    window.sessionStorage.setItem('course', true)
    editor.insertNode({
      type: 'compSelectTag',
      vueValue: value,
      children: [{text: ''}]  // void 元素必须有一个 children ，其中只有一个空字符串，重要！！！
    }) // value 即 this.value(editor) 的返回值
  }

}

Boot.registerModule({
  menus: [{
    key: 'compDiscussTag', // 定义 menu key ：要保证唯一、不重复（重要）
    factory() {
      return new MyButtonDiscussMenu() // 把 `YourMenuClass` 替换为你菜单的 class
    },
  }, {
    key: 'compResourceTag', // 定义 menu key ：要保证唯一、不重复（重要）
    factory() {
      return new MyButtonRescurceMenu() // 把 `YourMenuClass` 替换为你菜单的 class
    },
  },{
    key: 'compTaskTag', // 定义 menu key ：要保证唯一、不重复（重要）
    factory() {
      return new MyButtonTaskMenu() // 把 `YourMenuClass` 替换为你菜单的 class
    }
  },{
    key: 'compInputTag', // 定义 menu key ：要保证唯一、不重复（重要）
    factory() {
      return new MyButtonInputMenu() // 把 `YourMenuClass` 替换为你菜单的 class
    }
  },{
    key: 'compSelectTag', // 定义 menu key ：要保证唯一、不重复（重要）
    factory() {
      return new MyButtonSelectMenu() // 把 `YourMenuClass` 替换为你菜单的 class
    }
  }
  ], // 菜单
})
Boot.registerModule(countbtns)
