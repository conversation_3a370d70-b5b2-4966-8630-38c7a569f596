<template>
  <div class="zd-editor">
    <div :class="disable ? 'editor-no-border':'editor-content'">
      <Toolbar
          :class="disable ? 'editor-no-border':'editor-toolbar'"
          :editor="editor"
          :defaultConfig="toolbarConfig"
          :mode="mode"
      />
      <Editor
          ref="editorContainer"
          :style="{height: defaultConfig.height, 'overflow-y': 'hidden', resize: defaultConfig.resize}"
          v-model="html"
          :defaultConfig="defaultConfig"
          :mode="mode"
          @onCreated="onCreated"
          @onChange="onChange"
      />
    </div>
  </div>
</template>

<script>
import '@wangeditor/editor/dist/css/style.css'
import './myMenu'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import {file_callback} from "@/utils/tinymceSettings";

// 处理wangeditor在新元素附近按键时本身的bug，
let eventKey = false
let isCopy = false
document.onkeydown = function(e) {
    eventKey = true
  if(e.code === 'MetaLeft' || e.code === 'ControlLeft'){
    this.MetaLeft = true
  }
  if(this.MetaLeft && e.code === 'KeyV'){
    isCopy = true
    this.MetaLeft = false
  }
  window.sessionStorage.setItem('eventKey', eventKey)
}

export default {
  name: "myEditor",
  components: { Editor, Toolbar },
  props:{
    editorState:{
      type: String,
      default: 'add'
    },
    compConfig:{
      type: Object,
      default: ()=>{
        return false
      },
    },
    editorConfig:{
      type: Object,
      default: ()=>{
        return {}
      },
    },
    editorDefaultHtml:{
      type: String,
      default: "",
    }
  },
  watch:{
    editorDefaultHtml:{
      handler(newVal, oldVal){
        if ((newVal && !oldVal) || (!newVal && oldVal)) {
          if(this.arrIncludes(this.compConfig?.insertKeys?.keys, ['compDiscussTag','compResourceTag','compTaskTag'])){
            this.html = '<p><draggable chosen-class="chosen" force-fallback="true" group="people" animation="300">'+newVal+'</draggable></p>'
          }else{
           this.html = newVal
          }
        }else{
          this.html = newVal
        }
      },
      immediate: true,
    }
  },
  data() {
    return {
      disable: false,
      MetaLeft: false,
      editor: null,
      html: '',
      getHtml: '<p></p>',
      toolbarConfig: {},
      defaultConfig: {
        placeholder: '请输入内容...',
        height: '200px',
        MENU_CONF: {
          uploadImage: {
            server: 'http://slrh.chinazdap.com:9992/api/v1/UploadFiles/PostFiles',
            customUpload: file_callback
          },
          uploadVideo: {
            server: 'http://slrh.chinazdap.com:9992/api/v1/UploadFiles/PostFiles',
            customUpload: file_callback
          },
        }
      },
      mode: 'default' // or 'simple'
    }
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错

      this.$emit('initEditor',this.editor)
      //只读模式
      this.disable = window.sessionStorage.getItem('editorState') === 'view'
      if(this.disable){
        this.defaultConfig.height = '100%'
        this.toolbarConfig.toolbarKeys = []
        this.toolbarConfig.insertKeys = []
        editor.disable()
      }
    },
    onChange(editor) {
      window.sessionStorage.setItem('selectText', editor.getSelectionText())
      let oldHtml = this.getHtml
      let newHtml = editor.getHtml()
      let courseCont = []
      // 删除自定义组件
      if(oldHtml.length > newHtml.length && editor.getHtml() !== '<p><br></p>'){
        if(oldHtml.length - newHtml.length >= 50){
          let html = this.difference(newHtml,oldHtml)
          let resourceId = this.extractValue(html)?this.extractValue(html)[0]:0
          let courseContents = JSON.parse(window.sessionStorage.getItem('courseContents') || '[]')
          courseContents?.forEach(val=>{
            let courseId
            if(val.resourceId){
              courseId = val.resourceId
            }
            if(val.answerId){
              courseId = val.answerId
            }
            if(courseId != resourceId){
              courseCont.push(val)
            }
          })
          window.sessionStorage.setItem('courseContents', JSON.stringify(courseCont))
        }
        //复制自定义组件
      }else if(oldHtml.length < newHtml.length && editor.getHtml() !== '<p><br></p>'){
        if(newHtml.length - oldHtml.length >= 50){
          let html = this.difference(oldHtml,newHtml, true)
          let resourceId = this.extractValue(html)
          let courseContents = JSON.parse(window.sessionStorage.getItem('courseContents') || '[]')
          if(isCopy){
            // 修改HTML answerId
            resourceId?.forEach(val=>{
              const timestamp = 'answerId_' + (new Date().getTime() + Math.round(100))
              const resourceIdReg = new RegExp("(.*)"+ val)
              const htmlNew = editor.getHtml().replace(resourceIdReg, '$1' + timestamp)
              editor.setHtml(htmlNew)
              // 添加数据
              courseContents.push({
                answerId: timestamp, // 绑定id
                answerCont: '', // 答案
                isCorrect: true, // 答案是否正确
              })
            })
            window.sessionStorage.setItem('courseContents', JSON.stringify(courseContents))
          }
        }
      }
      this.getHtml = (editor.getHtml() !== '<p><br></p>') && editor.getHtml()
      this.html = editor.getHtml()
      // console.log('editorHtml', editor.getHtml())
      this.$emit('editorHtml', editor.getHtml())
    },
    difference(str1, str2, type) {
      // 将字符串按标签分割
      const tags1 = str1.match(/<[^>]+>/g);
      const tags2 = str2.match(/<[^>]+>/g);
      // 遍历标签数组，找出差异部分
      let difference = '';
      if(type){
        for (let i = 0; i < tags2.length; i++) {
          for (let j = i + 1; j < tags2.length; j++) {
            if (tags2[i] === tags2[j] && tags2[j].length > 50) {
              difference += tags2[i];
            }
          }
        }
      }else{
        for (let i = 0; i < tags2.length; i++) {
          if (tags1 && tags2[i] && !tags1.includes(tags2[i])) {
            difference += tags2[i];
          }
        }
      }
      return difference;
    },
    arrIncludes(arr,info){
      let st = false
      arr && info.forEach((val)=>{
        if(arr.includes(val)){
          st = true
        }
      })
      return st
    },
    extractValue(str) {
      // 正则表达式匹配 data-vue-value 属性的值
      const regex = /data-vue-value="([^"]+)"/g;
      let match = str.match(regex);
      match = match?.map(val=>{
        return val.slice(16,38)
      })
      return match ? match : null;
    }
  },
  created() {
    this.defaultConfig = {...this.defaultConfig, ...this.editorConfig}
    this.toolbarConfig = this.compConfig || this.toolbarConfig
    if(this.editorState === 'add'){
      this.editorDefaultHtml = ''
    }
    if(this.arrIncludes(this.compConfig?.insertKeys?.keys, ['compDiscussTag','compResourceTag','compTaskTag'])){
      this.html = '<p><draggable chosen-class="chosen" force-fallback="true" group="people" animation="300">'+this.editorDefaultHtml+'</draggable></p>'
    }else{
      this.html = this.editorDefaultHtml
    }
    window.sessionStorage.setItem('editorState', this.editorState)
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
    window.sessionStorage.removeItem('eventKey')
    window.sessionStorage.removeItem('editorState')
  }
}
</script>

<style scoped lang="scss">
.zd-editor{
  width: 100%;
  height: 100%;
  .editor-content{
    border: 1px solid #ccc;
    height: 100%;
    .editor-toolbar{
      border-bottom: 1px solid #ccc;
    }
  }
  .editor-no-border{
    border-bottom: unset;
  }
}
</style>
<style>
.zd-editor a{
  color: #0b50fa;
  text-decoration: underline;
}
.zd-editor span{
  font-size: 14px;
}
.zd-editor span[data-slate-zero-width="n"] {
  padding: 0 1px !important;
}
.zd-editor .w-e-text-container [data-slate-editor] span{
  /* padding-top: 10px */
}
.zd-editor .w-e-text-container [data-slate-editor] p{
  /*display: flex !important;*/
  width: 100%;
/*  flex-flow: wrap;*/
}
</style>
