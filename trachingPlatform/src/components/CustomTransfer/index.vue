<template>
  <div class="custom-transfer">
    <!-- 左侧未选择列表 -->
    <div class="transfer-panel left-panel">
      <div class="panel-header">
        <span class="panel-title">{{ titles[0] }}</span>
        <div class="search-box" v-if="filterable">
          <el-input
            v-model="leftSearch"
            placeholder="请输入搜索内容"
            prefix-icon="el-icon-search"
            clearable
            @input="handleLeftSearch"
          />
        </div>
      </div>
      <div class="panel-body">
        <el-checkbox-group v-model="leftChecked" @change="handleLeftCheckChange">
          <div
            v-for="item in leftList"
            :key="item.key"
            class="transfer-item"
          >
            <el-checkbox :label="item.key">
              {{ item.label }}
            </el-checkbox>
          </div>
        </el-checkbox-group>
        <div v-if="leftList.length === 0" class="empty-text">暂无数据</div>
      </div>
    </div>

    <!-- 右侧已选择列表 -->
    <div class="transfer-panel right-panel">
      <div class="panel-header">
        <span class="panel-title">{{ titles[1] }}</span>
        <div class="search-box" v-if="filterable">
          <el-input
            v-model="rightSearch"
            placeholder="请输入搜索内容"
            prefix-icon="el-icon-search"
            clearable
            @input="handleRightSearch"
          />
        </div>
      </div>
      <div class="panel-body">
        <div
          v-for="item in rightList"
          :key="item.key"
          class="transfer-item"
        >
          <span class="item-label">{{ item.label }}</span>
          <i class="el-icon-delete delete-icon" @click="handleRemove(item)"></i>
        </div>
        <div v-if="rightList.length === 0" class="empty-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTransfer',
  props: {
    // 数据源
    data: {
      type: Array,
      required: true
    },
    // 已选择的值
    value: {
      type: Array,
      default: () => []
    },
    // 标题
    titles: {
      type: Array,
      default: () => ['未选择', '已选择']
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      leftSearch: '',
      rightSearch: '',
      leftChecked: [],
      filteredLeftList: [],
      filteredRightList: []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.leftChecked = [...val];
      }
    }
  },
  computed: {
    // 左侧列表（未选择）
    leftList() {
      let list = this.data.filter(item => !this.value.includes(item.key));
      if (this.leftSearch) {
        list = list.filter(item => item.label.toLowerCase().includes(this.leftSearch.toLowerCase()));
      }
      return list;
    },
    // 右侧列表（已选择）
    rightList() {
      let list = this.data.filter(item => this.value.includes(item.key));
      if (this.rightSearch) {
        list = list.filter(item => item.label.toLowerCase().includes(this.rightSearch.toLowerCase()));
      }
      return list;
    }
  },
  methods: {
    // 左侧复选框变化
    handleLeftCheckChange(val) {
      // 只允许新增（勾选即加入右侧）
      this.$emit('input', val);
      this.$emit('change', val, 'right');
    },
    // 右侧删除
    handleRemove(item) {
      const newValue = this.value.filter(key => key !== item.key);
      this.$emit('input', newValue);
      this.$emit('change', newValue, 'left');
    },
    handleLeftSearch() {},
    handleRightSearch() {},
  }
}
</script>

<style lang="scss" scoped>
.custom-transfer {
  display: flex;
  justify-content: space-between;
  width: 700px;
  min-width: 700px;
  height: 400px;
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;

  .transfer-panel {
    width: 48%;
    min-width: 300px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .panel-header {
      padding: 12px 15px;
      border-bottom: 1px solid #dcdfe6;
      background-color: #f5f7fa;
      .panel-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
      .search-box {
        margin-top: 8px;
      }
    }

    .panel-body {
      flex: 1;
      overflow-y: auto;
      padding: 6px 0;

      .transfer-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 15px;
        transition: background-color 0.3s;
        .item-label {
          flex: 1;
          font-size: 14px;
          color: #333;
        }
        .delete-icon {
          color: #999;
          font-size: 16px;
          cursor: pointer;
          margin-left: 8px;
          &:hover {
            color: #f56c6c;
          }
        }
      }
      .transfer-item:hover {
        background-color: #f5f7fa;
      }
      .empty-text {
        text-align: center;
        color: #999;
        padding: 20px 0;
      }
    }
  }
}
</style> 