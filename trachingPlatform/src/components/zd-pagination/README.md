# 分页统一样式封装
## 组件说明
> 基于el-pagination组件二次封装，作用统一分页风格，默认每页10条

## 组件使用
```js
import ZdPagination from "@/components/zd-pagination/index.vue";
```
```vue
<zdPagination :paginationConfig="paginationConfig" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange"></zdPagination>
```
# Attributes

| 参数                  | 类型      | 可选值                    | 默认值   | 说明                                                              | 备注                                                         |
|---------------------|---------|------------------------|-------|-----------------------------------------------------------------|------------------------------------------------------------|
| paginationConfig  | Object  |  | {}    |    兼容el-pagination原有的所有属性                                   |  |
| paginationConfig.total | Number  |   |  | 分页总条数 |  |

# Events


| 事件名           | 说明          | 回调参数  |
|---------------|-------------|-------|
| handleCurrentChange | 切换分页的回调函数   | 当前页数  |
| handleSizeChange | 切换每页数量的回调函数 | 当前每页条数 |
