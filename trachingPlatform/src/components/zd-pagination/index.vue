<template>
  <div class="zd-pagination">
    <el-pagination
        background
        @current-change="handleCurrentChange"
        @jumper-change="handleJumperChange"
        v-bind="$attrs.paginationConfig"
        :current-page="paginationConfig.currentPage"
        :page-sizes="paginationConfig.pageSizes"
        :page-size="paginationConfig.pageSize"
        :layout="paginationConfig.layout"
        :total="paginationConfig.total">
      <div class="pagination-slot">
        <span>每页</span>
        <el-select
            @change="handleSizeChange"
            size="small"
            v-model="paginationConfig.pageSize"
            filterable
            allow-create
            default-first-option
        >
          <el-option
              v-for="(item, index) of paginationConfig.pageSizes"
              :key="index"
              :value="item"
              :label="item"
          ></el-option>
        </el-select>
        <span>条</span>
      </div>
    </el-pagination>
    <!--<el-button style="margin-left: 18px; color: #606A78;height: 30px;">确定</el-button>-->
  </div>
</template>

<script>
export default {
  name: "index",
  data() {
    return {
      paginationConfig:{
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50, 100, 200, 500],
        total: 0,
        layout: 'prev,pager,next, total,slot,jumper'
      }
    };
  },
  watch:{
    "$attrs.paginationConfig": {
      handler(){
        this.init()
      },
      deep: true
    }
  },
  created(){
    this.init()
  },
  methods: {
    init(){
      this.paginationConfig = {...this.paginationConfig, ...this.$attrs.paginationConfig}
      this.$nextTick(()=>{
        const pagination__total = document.querySelector(
            ".el-pagination__total"
        );
        pagination__total.innerText = `共${this.paginationConfig.total}条数据`;
      })
    },
    handleSizeChange(val) {
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val)
    },
    handleJumperChange(val){
      return false;
    }
  }
}
</script>

<style lang="scss" scoped>
.zd-pagination{
  width: 100%;
  display: flex !important;
  justify-content: center!important;
  .pagination-slot{
    color: #BBBBBB;font-weight: 400;font-size: 14px;
  }
  ::v-deep .el-pagination{
    display: flex !important;
    justify-content: center!important;
  }
}

::v-deep .el-icon-arrow-left:before {
  content: "\e792" !important;
}
::v-deep .el-icon-arrow-right:before {
  content: "\e791" !important;
}
::v-deep .el-pagination__total{
  color: #BBBBBB !important;
  margin-left: 10px !important;
}
::v-deep .el-input__inner{
  color: #BBBBBB !important;
}
::v-deep .el-pagination__editor.el-input{
  width: 30px !important;;
}
::v-deep .el-input--small .el-input__inner{
  height: 28px !important;
  line-height: 28px !important;
  color: #606A78 !important;
}
::v-deep .el-pagination__jump{
  color: #BBBBBB !important;
  margin-left: 0 !important;
}
</style>
<style>
.zd-pagination .el-pagination.is-background .el-pager li:not(.disabled).active{
  background-color: #2B66FF !important;
  color: #fff !important;
}
.zd-pagination .el-pagination.is-background .el-pager li{
  background-color: #fff !important;
  border: 1px solid #E6E6E6 !important;
  color: #606A78 !important;
}
.zd-pagination .el-pagination.is-background .btn-next, .zd-pagination .el-pagination.is-background .btn-prev{
  background-color: #fff !important;
  border: 1px solid #E6E6E6 !important;
  color: #BBBBBB !important;
}
.zd-pagination .el-pagination .el-select .el-input{
  width: 66px !important;
  height: 28px !important;
}
</style>
