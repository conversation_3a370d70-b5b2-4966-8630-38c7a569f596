
<template>
  <el-dialog
    title="提示"
    close-on-click-modal="false"
    close-on-press-escape="false"
    custom-class="upload-progress"
    :visible.sync="dialogVisibleasd"
    width="30%"
    :show-close="false">
    <span class="text">上传中,请勿进行其他操作！</span>
    <div
      v-for="(item, index) in percentageList"
      :key="index"
      class="list-item"
      v-show="item.count != 100">
      <div class="list-item-title">
        <span class="title">{{ item.name }}</span>
        <span class="percentage">{{ item.count }}%</span>
      </div>
      <el-progress
        :stroke-width="6"
        :percentage="item.count"
        color="#2B66FF"
        :show-text="false"></el-progress>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisibleasd: {
      type: Boolean,
      default: false
    },
    percentageList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {};
  },
  methods: {}
};
</script>
<style lang="scss" scoped>
::v-deep .upload-progress {
  border-radius: 4px;

  .el-dialog__header {
    background: #fff;
    border-radius: 10px 10px 0 0;
  }

  .text {
    display: block;
    margin-bottom: 20px;
    text-align: right;
    color: #2b66ff;
    font-size: 14px;
  }

  .list-item {
    margin-bottom: 20px;

    .list-item-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      span {
        font-size: 14px;
      }

      .title {
        color: #333333;
      }

      .percentage {
        color: #bbbbbb;
      }
    }
  }
}
</style>
