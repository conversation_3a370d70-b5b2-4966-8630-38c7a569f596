<template>
  <div class="add-question-dialog">
    <question-dialog :visible.sync="showAddQuestionModal" :title="getTitle">
      <div slot="rightSlot">
        <el-button size="mini" @click="preview()">预览</el-button>
        <el-button size="mini" type="primary" plain @click="saveClose()">保存</el-button>
        <el-button size="mini" type="primary" @click="saveNext()">保存并新增</el-button>
      </div>
      <div v-if="showAddQuestionModal && !editDataLoading" slot="contentLeftSlot" class="content-left-slot-dv">
        <div class="content-tree-dv">
          <category :course-id="getCourseId"
                    :current-node-id="defaultData.cateId"
                    @nodeClick="nodeClick">
          </category>
        </div>
      </div>
      <div v-if="showAddQuestionModal && !editDataLoading" slot="contentSlot">
        <add-question-content
          ref="addQuestionContentRef"
          :is-system="isSystem"
          :course-id="getCourseId"
          :default-data="defaultData">
        </add-question-content>
      </div>
    </question-dialog>
    <base-dialog :visible.sync="showPreviewModal"
                 :close-on-click-modal="false"
                 :no-footer="true"
                 :width="questiondataType == 49?'1200px':'1200px'"
                 title="题目预览">
      <div style="padding: 0 15px; max-height: calc(100vh - 200px); overflow-y: auto;">
        <question-preview-content
            v-if="showPreviewModal"
            :isAnswerHint="true"
            :data="getParams()">
        </question-preview-content>
      </div>
    </base-dialog>
  </div>
</template>

<script>
import {getCorrectAnswer, questionTypeMenu, getInitAnswerValue} from '@/components/base/question/util.js';
import {questionType} from "@/constants/questionType";
import {initApiQuestion} from "@/components/question/utils";
import {checkEmpty} from "@/utils/utils";
import eventBus from "@/utils/eventBus";
export default {
  name: 'add-question-dialog',
  components:{
    'question-dialog': () => import('@/components/base/question/question-dialog.vue'),
    'add-question-content': () => import('./add-question-content.vue'),
    'question-preview-content': () => import('./question-preview-content.vue'),
    'category': () => import('./category.vue')
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    type: { // 题目类型
      type: String | Number,
      default: ''
    },
    data: {
      type: Object,
      default: () => null
    },
    isSystem: { // 是否为平台知识点
      type: Boolean,
      default: true
    },
    courseId: { // 课程id
      type: String | Number,
      default: ''
    },
    questionCategoryId: { //分类id
      type: String | Number,
      default: ''
    }

  },
  computed: {
    showAddQuestionModal: {
      get () {
        return this.visible;
      },
      set (value) {
        this.$emit('update:visible', value);
      }
    },
    getTitle: function() {
      return this.title || (
        (this.$route.query.courseName ? `《${this.$route.query.courseName}》` : '')
        +
        (this.currentId ? '编辑试题' : '新增试题')
      )
    },
    getCourseId: function() {
      const val = this.courseId || this.$route.query.id || 0
      return Number(val)
    }
  },
  data () {
    return {
      showPreviewModal: false,
      isPreviewoperation:0,
      defaultData: {
        type: 18,
        cateId: 0
      },
      editDataLoading: false,
      saveLoading: false,
      currentId: 0,// 题目id
      status: 0,// 题目状态
      // 填空题的缓存数据,避免预览时改变数据
      courseContentsStorage: '',
      spread:null, // sprad 对象
      questiondataType:0
    }
  },
  watch: {
    showAddQuestionModal: {
      handler (val) {
        this.initData(val)
      },
      immediate: true
    },
    showPreviewModal: {
      handler (val) {
        if (!val) {
          this.closePreviewModal()
        }
      },
      immediate: true
    }
  },
  provide() {
    return {
      passToChild: this.handleDataFromChild
    };
  },
  mounted(){
    this.questionCategoryInfo = JSON.parse(sessionStorage.getItem('questionCategoryInfo'))
  },
  methods: {
    async initData (val) {
      if (val) {
        if (this.data && this.data.id) { // 题目编辑
          // 编辑
          this.currentId = this.data.id
          this.status = this.data.status  // 题目状态
          this.editDataLoading = true
          const { code, data } = await this.$api.GetQuestionDetailById({ id: this.data.id })
          this.editDataLoading = false
          if (code === 200) {
            this.commandEdit(data)
          }
        } else if (this.data) {
          // 自带默认值
          this.commandEdit(this.data)
        } else {
          // 新建
          this.commandAdd(this.type)
        }
      }
    },
    commandAdd (type, cateId) {
      this.currentId = 0  
      this.defaultData = {
        type: Number(type),
        cateId: cateId || this.questionCategoryId || 0
      }
    },
    commandEdit (data) {
      this.defaultData = initApiQuestion(data)
    },
    preview () {
      const data = this.getParams()
      this.questiondataType=data.type
      this.courseContentsStorage = sessionStorage.getItem('courseContents')
      this.showPreviewModal = true
      this.isPreviewoperation=1
    },
    closePreviewModal () {
      this.$nextTick(() => {
        sessionStorage.setItem('courseContents', this.courseContentsStorage)
      })
    },
    getParams () {
      return this.$refs.addQuestionContentRef.getParams()
    },
    async save () {
      if (this.saveLoading) return
      this.saveLoading = true
      const data = this.getParams()
      // console.log(data,'保存总数据')
      // 填空题取缓存数据
      if (data.type === questionTypeMenu.content) {
        const courseContents = sessionStorage.getItem('courseContents')
        data.optionConfig.settingArr = courseContents ? JSON.parse(courseContents) : []
      }
      // 表格题
      if (data.type === questionTypeMenu.table) {
        data.optionConfig.spread = this.spread;
      }
      const answer = getCorrectAnswer(data.type, data.optionConfig) || ''
      // console.log(answer)
      // console.log(data)
      // return 
      try {
        let ScoringItems = [] 
        // 保存值判断
        if (checkEmpty(data.title)) throw '标题不能为空'
        if ([questionTypeMenu.radio, questionTypeMenu.checkbox, questionTypeMenu.isTrue].includes(data.type)) {
          if (data.optionConfig.options.length < 2) {
            throw '选项最少需要设置两个'
          }
          const labelArr = [] // 选项值重复判断用
          data.optionConfig.options.forEach((item) => {
            if (checkEmpty(item.label)) {
              throw '选项值不能为空'
            }
            if (labelArr.includes(item.label)) {
              throw '选项值不能重复'
            }
            labelArr.push(item.label)
          })
          // 判断答案个数是否匹配
          if (data.type === questionTypeMenu.checkbox && answer.length < 2) {
            throw '多选题答案最少需要设置两个'
          } else if (answer.length === 0) {
            throw '未设置答案'
          }
        } else if (data.type === questionTypeMenu.python) {
          if (checkEmpty(data.optionConfig.settingInputArr)) {
            throw '未设置答案'
          }
          let index = 1
          for (const key in answer) {
            if (checkEmpty(answer[key])) {
              throw `第${index}个未设置答案`
            }
            index++
          }
        } else if(data.type === questionTypeMenu.pythonCode){
          // 编程题

        }  else if (data.type === questionTypeMenu.content) {
          if (checkEmpty(data.optionConfig.settingArr)) {
            throw '未设置答案'
          }
          let index = 1
          for (const key in answer) {
            if (checkEmpty(answer[key])) {
              throw `第${index}个未设置答案`
            }
            index++
          }
        } else if(data.type === questionTypeMenu.table){ //表格题
          
          if(answer.ScoringItems){
            ScoringItems = answer.ScoringItems;
            delete answer.ScoringItems
          }
          if(data.optionConfig.tableType==1){ //spread
            data.optionConfig.html = ''
          }else{
            data.optionConfig.spreadStr = ''
          }
          // console.log("表格题----",data)
        }

        if (checkEmpty(data.answerDetail) || data.answerDetail === '<p><br></p>') {
          throw '答案解析不能为空'
        }

        // 综合题 判断如果没有子题目则限制保存
        if (data.type === questionTypeMenu.synthesis && data.children.length === 0) {
          throw '综合题下至少需要添加一个子题目'
        }
        // 构建保存题目的参数
        let questionParams =  this.handleQuestionParams({
          ...data,
          id: this.currentId,
          courseId: this.getCourseId,
          status: this.status,
          answer: answer
        })

        // console.log("保存的题目参数--------------",questionParams)
        const { code, msg } = await this.$api.SaveQuestion(questionParams)
        // const { code, msg } = await this.$api.SaveQuestion({
        //   "id": this.currentId,
        //   "title": data.title,*
        //   "courseId": this.getCourseId || 0,
        //   "questionType": data.type,
        //   'NoAutoScore': data.type==46||data.type==47?1:0,// 是否是自动判分
        //   "questionCategoryId": this.defaultData.cateId,
        //   "scoringType":data.type == 49?4:data.type === questionTypeMenu.checkbox ? 0 : data.type?data.type:2, // 计分规则
        //   "sort": 0,
        //   "answer": data.type==5||data.type==46||data.type==47? JSON.stringify(answer):data.type==49?JSON.stringify(data.optionConfig.checkListData):JSON.stringify({
        //     longArray: answer
        //   }),
        //   "ScoringItems":JSON.stringify(ScoringItems),// 表格题自由计分
        //   "contentData": data.type==49?JSON.stringify(data.optionConfig.questionfiles):this.handleContentData(data.optionConfig), //  扩展字段  前端使用配置信息
        //   // "unAnswerData": "",
        //   "description": data.desc,
        //   "answerAnalysis": data.answerDetail, // 答案解析
        //   // "comment": "string",
        //   // "isDelete": 0,
        //   // "tenantId": 0,
        //   // "used": 0,
        //   // "accuracy": 0,
        //   "difficulty": data.difficulty, // 难易
        //   "status": this.status,
        //   // "degree": 0,
        //   // "createTime": "2024-09-03T09:53:38.855Z",
        //   // "createBy": 0,
        //   // "updateTime": "2024-09-03T09:53:38.855Z",
        //   // "updateBy": 0,
        //   "knowledgeTags": data.knowledge.map((item) => ({
        //     "knowledgeId": item.id,
        //     "type": item.knowledgeType,
        //     "name": item.name
        //   })),
        //   "skillTags": data.skill.map((item) => ({
        //     "knowledgeId": item.id,
        //     "type": item.knowledgeType,
        //     "name": item.name
        //   })),
        //   children:data.children,// 子题目
        // })
        if (code === 200) {
          this.$message.success('保存成功！')
          this.saveLoading = false
          this.currentId = 0
          this.defaultData = {
            type: data.type,
            cateId: this.defaultData.cateId
          }
          this.$emit('success')
        } else {
          // this.$message.success(msg)
          this.saveLoading = false
        }
      } catch (e) {
        this.$message.warning(e)
        this.saveLoading = false
        return Promise.reject()
      }
    },
    async saveClose () {
      const data = this.getParams()
       if(data.type == 49){
          eventBus.$emit('handlecollect',data.type)
          if(data.optionConfig.questionfiles.questionfiles.length == 0){
            this.$message.error('请上传分录单据或附件');
            return
          }
          if(data.optionConfig.checkListData!=='' && data.optionConfig?.checkListData.data.bodyItems.length ==0){
            this.$message.error('请填写分录');
            return
          }
          if(data.optionConfig.checkListData== ''){
            return
          } 
      }

      await this.save()
      this.showAddQuestionModal = false
    },
    async saveNext () {
      const data = this.getParams()
      if(data.type == 49){
        eventBus.$emit('handlecollect',data.type)
        if(data.optionConfig.questionfiles.questionfiles.length == 0){
            this.$message.error('请上传分录单据或附件');
            return
        }
        if(data.optionConfig.checkListData!=='' && data.optionConfig?.checkListData.data.bodyItems.length ==0){
            this.$message.error('请填写分录');
            return
        }
        if(data.optionConfig.checkListData== ''){
            return
        } 
      }
      await this.save()
      this.commandAdd(this.defaultData.type, this.defaultData.cateId)
      this.editDataLoading = true
      await this.$nextTick()
      this.editDataLoading = false
    },
    // 构建题目保存的参数
    handleQuestionParams(qObject){
      // 题目判分类型
      const handleScoringType = (qObject)=>{
        if(qObject.type == 49){
          return 4
        }else if(qObject.type === questionTypeMenu.checkbox){
          return 0
        }else{
          return qObject.optionConfig.scoreRadio?qObject.optionConfig.scoreRadio:2
        }
      }
      // 题目答案
      const handleAnswer = (qObject)=>{
        if (qObject.type === 5 || qObject.type === 46 || qObject.type === 47) {
          return JSON.stringify(qObject.answer);
        } else if (qObject.type === 49) {
          return qObject.optionConfig.checkListData?JSON.stringify(qObject.optionConfig.checkListData):qObject.answer;
        } else {
          if(JSON.stringify(qObject.answer).indexOf('longArray')!=-1){
            return JSON.stringify({ longArray: qObject.answer.longArray });
          }else{
            return JSON.stringify({ longArray: qObject.answer });
          }
        }
      }
     // 处理 contentData 字段 // 绑定 this 上下文
      const handleQsContentData = (qObject)=>{
        
        // console.log("handleQsContentData-------------------",qObject)
        if (qObject.type === 49) {
          return JSON.stringify(qObject.optionConfig.questionfiles);
        } else if(qObject.type === 50){ // 综合题 移除optionConfig中的 children 子题目数据
          delete qObject.optionConfig.children;
          return this.handleContentData(qObject.optionConfig);
        }  else {
          return this.handleContentData(qObject.optionConfig);
        }
      } 
      const handleParams = (qObject)=>{
        // console.log("qObject-------------------",qObject)
        return {
          "id": qObject.id, // 题目id
          "title": qObject.title,
          "courseId": qObject.courseId || 0,
          "questionType": qObject.type,
          'NoAutoScore': (qObject.type==47||qObject.questionType==47)?1:0,// 是否是自动判分 （简答题，代码编程题）
          "questionCategoryId": this.defaultData.cateId,
          "scoringType":handleScoringType(qObject), // 计分规则
          "sort": qObject.sort||0,
          "answer": handleAnswer(qObject),
          "ScoringItems":JSON.stringify(qObject.ScoringItems),// 表格题自由计分
          "contentData": handleQsContentData(qObject), //  扩展字段  前端使用配置信息
          // "unAnswerData": "",
          "description": qObject.desc,
          "answerAnalysis": qObject.answerDetail, // 答案解析
          // "comment": "string",
          // "isDelete": 0,
          // "tenantId": 0,
          // "used": 0,
          // "accuracy": 0,
          "difficulty": qObject.difficulty, // 难易
          "status": qObject.status,
          // "degree": 0,
          // "createTime": "2024-09-03T09:53:38.855Z",
          // "createBy": 0,
          // "updateTime": "2024-09-03T09:53:38.855Z",
          // "updateBy": 0,
          "knowledgeTags": qObject.knowledge? qObject.knowledge.map((item) => ({
            "knowledgeId": item.id,
            "type": item.knowledgeType,
            "name": item.name
          })):[],
          "skillTags":  qObject.skill?qObject.skill.map((item) => ({
            "knowledgeId": item.id,
            "type": item.knowledgeType,
            "name": item.name
          })):[],
        }
      }
      let params =  handleParams(qObject);
      if(qObject.children&&qObject.children.length>0){ // 子题目
        params = {
          ...params,
          children: qObject.children.map((item) => handleParams({
            ...item,
            courseId: this.getCourseId,
            status: this.status,
            id:item.id||0,
            difficulty: item.difficulty|| 0, // 子题目没有难度系数
            'NoAutoScore': (item.type==47)?1:0,// 是否是自动判分 （简答题，代码编程题）
          }))
        }
      }
      return params
    },
    nodeClick (node) {
      this.defaultData.cateId = node.id
    },
    // 处理前端配置信息
    handleContentData(data){
      if(data.spread){
        delete data.spread
      }
      return JSON.stringify(data)
    },
    handleDataFromChild(spread) {
      this.spread = spread;
      // 处理数据...
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.add-question-modal) {
  .el-button--primary.is-plain {
    border-color: var(--theme_primary_color);
    background-color: #fff;
    &:hover, &:focus, &:active {
      //background-color: var(--theme_primary_color) !important;
      border-color: var(--theme_button_hover_color);
      background-color: #fff;
      color: var(--theme_button_hover_color);
    }
  }
  .content-left-slot-dv {
    margin-right: 15px;
    background-color: #fff;
    border-radius: 10px;
    width: 250px;
    position: relative;
    .content-tree-dv {
      position: absolute;
      width: 100%;
      height: 100%;
      padding: 15px;
      overflow-y: auto;
    }
  }
}
</style>