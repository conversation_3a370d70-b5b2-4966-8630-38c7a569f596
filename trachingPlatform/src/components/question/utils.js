/*
 * 题库：后端返回接口数据与前端数据的转换
 * */
export const initApiQuestion = (data) => {
    if (!data) return {};
    let info = {
        id: data.id || 0,
        type: data.questionType,
        questionType: data.questionType,
        title: data.title,
        desc: data.description,
        answerDetail: data.answerAnalysis,
        optionConfig: data.contentData ? JSON.parse(data.contentData) : null,
        // knowledge: data.knowledgeTags,
        // skill: data.skillTags,
        complexity: data.complexity,
        difficulty: data.complexity,
        cateId: data.questionCategoryId,
        // answer:data.answer,
        answer: (typeof data.answer == 'string' && data.questionType != 49) ? JSON.parse(data.answer) : data.answer,
        sort: data.sort, // 排序
    }

    if (data.questionType == 50) { // 综合题 将子题目存储在optionConfig 中
        // console.log("父级id----------",data)
        info.optionConfig.children = data.children ? data.children.map(item => {
            let childInfo = {
                id: item.id || 0,
                parentId: data.id, //子题目的父级id
                type: item.questionType,
                questionType: item.questionType,
                title: item.title,
                desc: item.description,
                answerDetail: item.answerAnalysis,
                optionConfig: item.contentData ? JSON.parse(item.contentData) : null,
                knowledge: item.knowledgeTags,
                skill: item.skillTags,
                complexity: item.complexity,
                cateId: item.questionCategoryId,
                answer: (typeof item.answer == 'string' && item.questionType != 49) ? JSON.parse(item.answer) : item.answer,
                sort: item.sort, // 子题目排序
                score: item.score || 0,
                isReadonly: item.isReadonly, // 题目只读的状态
            }
            return {
                ...childInfo,
                questionDetail: initApiQuestion(item), // 子题目信息
            }
        }) : [];
    }
    return info
}