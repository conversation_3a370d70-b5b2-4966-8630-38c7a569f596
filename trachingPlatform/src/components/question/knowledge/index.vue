<template>
  <div class="question-knowledge-index">
    <div style="display: flex; align-items: center; flex-wrap: wrap;">
      <el-button v-if="canAdd" type="primary" plain class="add-btn" @click="add()">+</el-button>
      <el-tag v-for="(item, index) in defaultData"
              :key="item.id" type="info"
              :class="canDelete ? 'knowledge-span' : ''"
      >
        <span>{{ item.name }}</span>
        <i v-if="canDelete" class="el-icon-error" @click="del(index)"></i>
      </el-tag>
    </div>
    <div v-if="canAdd && showAddModal">
      <system-add-modal v-if="isSystem"
                        v-model="showAddModal"
                        :default-value="defaultData"
                        :title="title"
                        :type="type"
                        @submit="submit"
      >
      </system-add-modal>
      <teach-add-modal v-else
                       v-model="showAddModal"
                       :default-value="defaultData"
                       :title="title"
                       :type="type"
                       :course-id="courseId"
                       @submit="submit"
      >
      </teach-add-modal>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';

export default {
  name: 'question-knowledge-index',
  props: {
    type: {
      type: Number,
      default: 1
    },
    title: {
      type: String,
      default: ''
    },
    canAdd: {
      type: Boolean,
      default: true
    },
    canDelete: {
      type: Boolean,
      default: true
    },
    defaultData: {
      type: Array,
      default: () => []
    },
    isSystem: { // 是否为平台知识点
      type: Boolean,
      default: true
    },
    courseId: { // 课程id，isSystem为true的时候必须传
      type: String | Number,
      default: ''
    }
  },
  components: {
    'system-add-modal': () => import('./system-add-modal.vue'),
    'teach-add-modal': () => import('./teach-add-modal.vue')
  },
  data () {
    return {
      showAddModal: false
    }
  },
  methods: {
    add () {
      this.showAddModal = true
    },
    del (index) {
      this.defaultData.splice(index, 1)
    },
    submit (list) {
      this.$emit('update:defaultData', cloneDeep(list))
      this.showAddModal = false
    }
  }
}
</script>

<style scoped lang="scss">
.question-knowledge-index {
  .add-btn {
    margin-right: 10px;
    padding: 5px 8px !important;
    margin-bottom: 5px;
  }
  :deep(.el-tag) {
    border-radius: 3px 3px 3px 3px;
    border: 1px solid #EBEBEB;
    background-color: #F6F8FA;
    font-weight: 400;
    font-size: 13px;
    color: #666;
    margin-right: 10px;
    margin-bottom: 5px;
  }
  .knowledge-span {
    position: relative;
    .el-icon-error {
      color: #A7A7A7;
      position: absolute;
      top: -8px;
      right: -8px;
      display: none;
    }
    &:hover {
      background-color: #fff;
      color: var(--theme_primary_color);
      border-color: var(--theme_primary_color);
      .el-icon-error {
        display: block;
        cursor: pointer;
      }
    }
  }
}
</style>