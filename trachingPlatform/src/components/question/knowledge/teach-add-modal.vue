<template>
  <base-dialog :visible.sync="_value" :title="'关联' + title" width="800px" @submit="submit">
    <div class="question-knowledge-add-modal">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <div>
          <el-input v-model="searchVal" placeholder="搜索" style="width: 220px; margin-right: 12px;"
                    clearable
                    maxLength="30"
                    @clear="search()"
                    @keyup.enter.native="search()">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="search()"></i>
          </el-input>
          <el-button type="primary" plain
                     @click="showAddModal(currentSelectNode.parentId)">
            <i class="el-icon-circle-plus"></i> 添加同级{{ title }}
          </el-button>
          <el-button v-if="currentSelectNode.id" type="primary" plain
                     @click="showAddModal(currentSelectNode.id)">
            <i class="el-icon-circle-plus"></i> 添加子级{{ title }}
          </el-button>
        </div>
        <div v-if="currentSelectNode.id">
          <el-button @click="singleEdit(currentSelectNode)">编辑</el-button>
          <el-button @click="singleDelete(currentSelectNode)">删除</el-button>
        </div>
      </div>
      <div class="content">
        <div class="left-tree">
          <el-tree ref="treeRef"
                   :data="treeData"
                   node-key="id"
                   default-expand-all
                   :props="{ children: 'children', label: 'name' }"
                   :filter-node-method="filterNode"
                   :highlight-current="true"
                   :show-checkbox="true"
                   :check-strictly="true"
                   :expand-on-click-node="false"
                   @node-click="handleNodeClick"
                   @check="handleCheck">
          </el-tree>
        </div>
        <div class="right-select-list">
          <div class="select-length">已选 <span class="num">{{selectedList.length}}</span> 个</div>
          <div class="select-list">
            <div v-for="(item, index) in selectedList" :key="item.id">
              <span class="item-tag">
                {{ item.name }}
                <i class="el-icon-error" @click="deleteItem(index)"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- <material-drawer
          v-if="addModal"
          :disabledForm="['knowledgeType']"
          :title="title"
          :openType="openType"
          :fromInitData="fromInitData"
          :isRelateStated="addModal"
          @manageResponse="manageResponse">
      </material-drawer> -->
    </div>
  </base-dialog>
</template>

<script>
import { addModalMixins } from './addModalMixins.js'
import {cloneDeep} from "lodash";

export default {
  name: 'question-knowledge-add-modal',
  mixins: [addModalMixins],
  props: {
    courseId: { // 课程id，isSystem为true的时候必须传
      type: String | Number,
      default: ''
    }
  },
  components: {
    // 'material-drawer': () => import('@/components/material-drawer/index.vue')
  },
  watch: {
  },
  data () {
    return {
      addModal: false,
      openType: 3, // 公共仓详情隐藏所属分类跟标签 1 公共知识点 2课程知识 3个人知识点
      fromInitData: {
        /*
        * 4 = Person (个人知识点)
        * 5 = Warehouse (知识仓知识点)
        * 6 = Course (课程知识点)
        * */
        createType: 4,
        courseId: 0,
        parentId: 0,
        knowledgeType: 0
      }
    }
  },
  methods: {
    async getData () {
      let { code, data } = await this.$api.PostFavoritesKnowledgesTree({
        courseId: this.courseId,
        KnowledgeKinds: this.type,
        orderBy: 'createTime desc'
      });
      if (code === 200) {
        this.treeData = data || [];
        this.selectedList = this.defaultValue ? cloneDeep(this.defaultValue) : []
        this.$nextTick(() => {
          this.$refs.treeRef.setCheckedKeys(this.selectedList.map((item) => (item.id || item.knowledgeId)))
        })
      }
    },
    search () {
      this.$refs.treeRef.filter(this.searchVal)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    showAddModal (parentId) {
      this.fromInitData.parentId = parentId || 0
      this.fromInitData.courseId = this.courseId
      this.fromInitData.knowledgeType = this.type
      this.addModal = true
    },
    // 个人编辑知识点
    async singleEdit (node) {
      let res = await this.$api.FavoritesKnowledgeFindByID({ id: node.id });
      if (res.code === 200 && res.data) {
        sessionStorage.setItem("knowledge", JSON.stringify(res.data));
        this.addModal = true;
        // 编辑的赋值parentId，在初始化的时候 ，就能保存值
        this.fromInitData.parentId = res.data.parentId;
        this.fromInitData.courseId = this.courseId
        this.fromInitData.knowledgeType = this.type
      }
    },
    // 创建成功回调
    manageResponse (val) {
      this.addModal = false
      sessionStorage.removeItem("knowledge");
      this.getData()
    },
    // 删除个人仓数据
    singleDelete (node) {
      this.$confirm("确认删除该数据?").then(async () => {
        let { code } = await this.$api.FavoritesKnowledgeDelete({ id: node.id });
        if (code === 200) {
          this.$message.success('删除成功！')
          this.handleNodeClick({}) // 重置节点信息
          this.getData();
        }
      }).catch(() => { });
    },
  }
}
</script>

<style scoped lang="scss">
@import "./addModalStyle";
</style>