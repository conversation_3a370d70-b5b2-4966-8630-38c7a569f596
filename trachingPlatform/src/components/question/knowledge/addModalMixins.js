export const addModalMixins = {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    },
    title: {
      type: String,
      default: ''
    },
    defaultValue: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _value: {
      get () {
        return this.value;
      },
      set (value) {
        this.$emit('input', value);
      }
    }
  },
  data () {
    return {
      searchVal: '',
      treeData: [],
      selectedList: [],
      currentSelectNode: {}
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData () {
    },
    search () {
    },
    filterNode (value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    handleNodeClick (data) {
      this.currentSelectNode = data
    },
    /**
     * 勾选知识点
     * @param {*} data
     * @param {*} node
     */
    async handleCheck(data, node) {
      if (node.checkedKeys.length > 3) {
        this.$message.warning('最多选择3个！')
        this.$refs.treeRef?.setChecked(data.id, false);
        return
      }
      let index = this.selectedList.findIndex(val => val.id === data.id)
      if (index > -1) {
        this.selectedList.splice(index, 1)
      } else {
        this.selectedList.push(data)
      }
    },
    deleteItem (index) {
      this.$refs.treeRef?.setChecked(this.selectedList[index].id, false);
      this.selectedList.splice(index, 1)
    },
    submit () {
      this.$emit('submit', this.selectedList)
    }
  }
}