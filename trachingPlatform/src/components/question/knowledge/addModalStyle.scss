
.question-knowledge-add-modal {
  :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
    background-color: #F6F8FA;
    color: var(--theme_primary_color);
  }
  :deep(.el-tree-node__content) {
    height: 36px !important;
  }
  :deep(.el-input--suffix .el-input__inner){
    padding-right: 50px;
  }
  .content {
    height: 400px;
    display: flex;
    border: 1px solid #E7E7E7;
    margin-top: 15px;
    border-radius: 5px;
    .left-tree {
      overflow-y: auto;
      height: 100%;
      padding: 15px;
      width: 50%;
      border-right: 1px solid #E7E7E7;
    }
    .right-select-list {
      overflow-y: auto;
      height: 100%;
      padding: 20px;
      width: 50%;
      .select-length {
        color: #ABABAB;
        .num {
          color: var(--theme_primary_color);
        }
      }
      .select-list {
        padding-top: 20px;
        display: grid;
        grid-gap: 25px;
        .item-tag {
          background: #F6F8FA;
          padding: 5px 10px;
          border-radius: 3px 3px 3px 3px;
          border: 1px solid #EBEBEB;
          position: relative;
          .el-icon-error {
            color: #A7A7A7;
            position: absolute;
            top: -8px;
            right: -8px;
            cursor: pointer;
            display: none;
          }
          &:hover {
            background: #fff;
            padding: 5px 10px;
            border-radius: 3px 3px 3px 3px;
            border: 1px solid var(--theme_primary_color);
            color: var(--theme_primary_color);
            .el-icon-error {
              display: block;
            }
          }
        }
      }
    }
  }
}