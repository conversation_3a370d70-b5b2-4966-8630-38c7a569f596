<template>
  <base-dialog :visible.sync="_value" :title="'关联' + title" width="800px" @submit="submit">
    <div class="question-knowledge-add-modal">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <div>
          <el-input v-model="searchVal" placeholder="搜索"
                    style="width: 220px; margin-right: 12px;"
                    clearable
                    maxLength="30"
                    @clear="search"
                    @keyup.enter.native="search">
            <i slot="suffix" class="el-input__icon el-icon-search" @click="search"></i>
          </el-input>
          <el-button type="primary" plain
                     @click="showAddModal(currentSelectNode.parentId)">
            <i class="el-icon-circle-plus"></i> 添加同级{{ title }}
          </el-button>
          <el-button v-if="currentSelectNode.id" type="primary" plain
                     @click="showAddModal(currentSelectNode.id)">
            <i class="el-icon-circle-plus"></i> 添加子级{{ title }}
          </el-button>
        </div>
        <div v-if="currentSelectNode.id">
          <el-button @click="showAddModal('', currentSelectNode)">编辑</el-button>
          <el-button @click="deleteNode(currentSelectNode)">删除</el-button>
        </div>
      </div>
      <div class="content">
        <div class="left-tree">
          <el-tree ref="treeRef"
                   :data="treeData"
                   node-key="id"
                   :default-expand-all="false"
                   :props="{ children: 'children', label: 'name' }"
                   :filter-node-method="filterNode"
                   :highlight-current="true"
                   :show-checkbox="true"
                   :check-strictly="true"
                   :expand-on-click-node="false"
                   :load="loadNode"
                   lazy
                   @node-click="handleNodeClick"
                   @check="handleCheck">
          </el-tree>
        </div>
        <div class="right-select-list">
          <div class="select-length">已选 <span class="num">{{selectedList.length}}</span> 个</div>
          <div class="select-list">
            <div v-for="(item, index) in selectedList" :key="item.id">
              <span class="item-tag">
                {{ item.name }}
                <i class="el-icon-error" @click="deleteItem(index)"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <el-drawer :title="title" custom-class="known-drawer"
                 :append-to-body="true"
                 :visible.sync="addModal.show"
                 direction="rtl"
                 :show-close="false"
                 :wrapperClosable="false"
                 :modal="false">
        <template slot="title">
          <div class="material-text">{{ addModal.id ? `编辑${title}` : `添加${title}` }}</div>
          <div class="drawer-btn">
            <el-button type="primary" @click="knowledgeSave">提交</el-button>
            <el-button @click="addModal.show = false">取消</el-button>
          </div>
        </template>
        <div class="drawer-body">
          <el-form ref="form" :rules="rules" :model="addModal" label-position="top">
            <el-form-item :label="`${title}名称`" style="width: 100%" prop="name">
              <el-input v-model="addModal.name" @input="addModal.name = addModal.name.trim()" placeholder="输入名称"></el-input>
            </el-form-item>
            <el-form-item style="width: 100%; padding-top: 10px;" class="tinymce-box">
              <ZdEditor :editor-default-html="addModal.desc"
                        :editor-config="{ placeholder: `输入${title}内容`, height: '400px' }"
                        @editorHtml="addModal.desc = $event">
              </ZdEditor>
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>
    </div>
  </base-dialog>
</template>

<script>
import { addModalMixins } from './addModalMixins.js'
import { cloneDeep } from 'lodash';
import {checkStringEmpty} from "@/utils/common";

export default {
  name: 'question-knowledge-add-modal',
  mixins: [addModalMixins],
  components: {
    'ZdEditor': () => import("@/components/zd-editor/index.vue")
  },
  watch: {
  },
  data () {
    return {
      addModal: {
        show: false,
        name: '',
        desc: '',
        id: '',
        parentId: ''
      },
      rules: {
        name: [
          { required: true, message: "请输入知识点名称", trigger: "blur" },
          { min: 0, max: 30, message: "长度在30个字符内", trigger: "blur" }
        ]
      }
    }
  },
  methods: {
    async getData () {
      this.treeData = await this.apiGetCyxyKnowledge({
        parentId: 0
      })

      this.selectedList = this.defaultValue ? cloneDeep(this.defaultValue) : []
      this.$nextTick(() => {
        this.$refs.treeRef.setCheckedKeys(this.selectedList.map((item) => (item.id || item.knowledgeId)))
      })
    },
    async apiGetCyxyKnowledge (params) {
      let { code, data } = await this.$api.GetCyxyKnowledge({
        params: {
          knowledgeType: this.type,
          orderBy: 'createTime desc',
          ...params
        }
      })
      if (code === 200) {
        return data || []
      }
    },
    async loadNode (node, resolve) {
      if (node.level > 0) {
        const data = await this.apiGetCyxyKnowledge({
          parentId: node.data.id
        })
        resolve(data)
      }
    },
    async search () {
      this.treeData = await this.apiGetCyxyKnowledge({
        name: this.searchVal
      })
    },
    showAddModal (parentId, data = {}) {
      this.addModal.name = data.name || ''
      this.addModal.desc = data.desc || ''
      this.addModal.id = data.id || 0
      this.addModal.parentId = data.parentId || parentId || 0
      this.addModal.show = true
    },
    async knowledgeSave () {
      await this.$refs["form"].validate(async (valid) => {
        if (valid) {
          const { code } = await this.$api.SaveCyxyKnowledge({
            "id": this.addModal.id,
            "parentId": this.addModal.parentId,
            "cyxyCourseId": 0,
            "name": this.addModal.name,
            "functionType": this.type, // 方法类型(0:方法类,1:业务类)
            "knowledgeType": this.type, // 知识类型(0:传统知识,1:业务知识)
            "secondLable": 0,
            "groupId": 0,
            "description": this.addModal.desc,
            "formula": '',
          })
          if (code === 200) {
            this.$message.success('保存成功！')
            this.addModal.show = false
            this.getData()
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      })
    },
    async deleteNode (node) {
      this.$zdDialog({
        width:'400px',
        center:true,
        contTitle: '确定要删除吗?',
      }).then(async()=>{
        const { code } = await this.$api.DeleteCyxyKnowledge({
        id: node.id
      })
      if (code === 200) {
        this.$message.success('删除成功！')
        this.handleNodeClick({}) // 重置节点信息
        this.handleSelectedList(node) // 同时移除掉选中的
        this.getData()
      }
      })
    },
    // 删除时要判断 已选的也要过滤掉被删除的
    handleSelectedList(node){
      this.selectedList = this.selectedList.filter(v=>v.id!=node.id)
    },
  }
}
</script>

<style scoped lang="scss">
@import "./addModalStyle";
</style>