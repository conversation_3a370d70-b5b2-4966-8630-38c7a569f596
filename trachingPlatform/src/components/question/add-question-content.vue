<template>
  <div class="add-question-content">
    <div class="question-type-radios">
      <el-radio
        v-for="(qTypeText, qType) in questionTypeLabel"
        :key="'q-type-' + qType"
        v-model="type"
        :label="qType"
        :disabled="defaultData.title"
        border
        size="small"
        @change="changeType"
      >
        {{ qTypeText }}
      </el-radio>
    </div>
    <div v-if="!changeLoading" class="add-question-content">
      <div>
        <form-setting :type="type"
                      :config.sync="optionConfig"
                      :title.sync="title"
                      :desc.sync="desc"
                      :answer="answer"
                      >
        </form-setting>
      </div>
      <div>
        <p class="part-label">解析</p>
        <editor v-model="answerDetail" placeholder="输入答案解析"></editor>
      </div>
      <div style="display: flex; align-items: center;">
        <div class="part-label" style="width: 70px;">难易度</div>
        <difficulty-set v-model="difficulty"></difficulty-set>
      </div>
      <!-- <div style="display: flex; align-items: center;">
        <div style="width: 70px;">排序</div>
        <el-input style="width:250px" type="number" :min="0" :max="9999" v-model="sort" />
      </div> -->
    </div>
  </div>
</template>

<script>
import {
  getAnswerDispatch,
  questionInitConfig,
  questionTypeLabel
} from '@/components/base/question/util.js'
import cloneDeep from 'lodash/cloneDeep';

export default {
  name: 'add-question-content',
  components:{
    'form-setting': () => import('@/components/base/question/answer-form-setting.vue'),
    'editor': () => import('@/components/base/question/editor/index.vue'),
    'difficulty-set': () => import('@/components/base/question/difficulty/set.vue'),
    // 'knowledge': () => import('./knowledge/index.vue'),
    // 'category-select': () => import('./category-select.vue')
  },
  props: {
    defaultData: {
      type: Object,
      default: () => {}
    },
    isSystem: { // 是否为平台知识点
      type: Boolean,
      default: true
    },
    courseId: { // 课程id
      type: String | Number,
      default: ''
    },
    data:{
      type: Object,
      default: () => {}
    }
  },
  watch:{
    defaultData:{
      handler (newVal, oldVal) {
        console.log("defaultData--------------",newVal)
        this.initData(this.defaultData)
      },
      deep: true,
      immediate:true,
    }
  },
  data () {
    return {
      questionTypeLabel: questionTypeLabel,
      type: '',
      optionConfig: {},
      title: '',
      desc: '',
      answerDetail: '',
      // knowledge: [],
      // skill: [],
      difficulty: 'Easy',
      sort:0,// 序号
      changeLoading: false,
      cateId: 0,
      answer:'',//答案
    }
  },
  created () {
  },
  methods: {
    getAnswerDispatch,
    initData (data = {}) {
      console.log("初始化数据data----------------------",data)
      this.type = data.type || ''
      this.title = data.title || ''
      this.desc = data.desc || ''
      this.answerDetail = data.answerDetail || ''
      this.optionConfig = data.optionConfig || cloneDeep(questionInitConfig[this.type])
      this.knowledge = data.knowledge || []
      this.skill = data.skill || []
      this.difficulty = data.difficulty || data.difficulty === 0 ? data.difficulty : 'Easy'
      this.sort = data.sort || 0
      this.cateId = data.cateId || 0
      this.answer = data.answer||''; 
    },
    changeType (ty) {
      this.changeLoading = true
      this.initData({ type: ty })
      this.$nextTick(() => {
        this.changeLoading = false
      })
    },
    getParams () {
      return {
        type: this.type,
        title: this.title,
        desc: this.desc,
        optionConfig: this.optionConfig,
        answerDetail: this.answerDetail,
        knowledge: this.knowledge,
        skill: this.skill,
        difficulty: this.difficulty,
        sort: this.sort,// 排序
        cateId: this.cateId,
        answer:this.answer,// 题目答案
        children:this.optionConfig.children,//子题目
      }
    }
  }
}
</script>

<style scoped lang="scss">
.add-question-content {
  display: grid;
  grid-gap: 20px;
  .question-type-radios {
    border-bottom: 1px solid #F2F3F5;
    :deep(.el-radio) {
      padding: 7px 19px 0 10px;
      border: none;
      height: 30px;
      margin-right: 10px;
      margin-bottom: 10px;
      color: #5C6075;

      .el-radio__input {
        display: none;
      }
    }

    :deep(.el-radio.is-checked) {
      // background-color: var(--theme_primary_color);
      // color: #0070FC!important;
      border: none;
      .el-radio__label {
        color: #0070FC !important;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          width: 42px;
          height: 2px;
          bottom: -18px;
          left: 6px;
          background-color: #0070FC!important;
        }
      }
    }
  }

  .part-label{
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 15px;
    color: #333333;line-height:40px;
  }
}
</style>