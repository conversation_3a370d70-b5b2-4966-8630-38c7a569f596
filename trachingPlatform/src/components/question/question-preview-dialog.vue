<template>
  <question-dialog :visible.sync="showQuestionModal" :title="getTitle" :backText="backText">
    <div v-if="showQuestionModal" slot="contentSlot">
      <el-tooltip v-if="previewIds &&  previewIds.length > 1" class="item" effect="dark" content="预览上一题" placement="bottom">
        <i class="el-icon-arrow-left" @click="toBefore()"></i>
      </el-tooltip>
      <el-tooltip v-if="previewIds &&  previewIds.length > 1" class="item" effect="dark" content="预览下一题" placement="bottom">
        <i class="el-icon-arrow-right" @click="toNext()"></i>
      </el-tooltip>
      <div v-if="previewIds &&  previewIds.length > 1 && data.questionType !== 50"
           style="margin-left: -10px; font-weight: 700; font-size: 16px;padding-bottom: 12px;"
      >【第{{ previewIds.indexOf(dataId) + 1 }}题】
      </div>
      <question-preview-content v-if="!loading" :data="detailData" :isAnswerHint="isAnswerHint" :show-form="showForm"></question-preview-content>
    </div>
    <div v-if="showQuestionModal" slot="rightSlot">
      <slot name="rightSlot"></slot>
    </div>
  </question-dialog>
</template>

<script>
import {initApiQuestion} from "@/components/question/utils";

export default {
  name: 'question-preview-modal',
  components:{
    'question-dialog': () => import('@/components/base/question/question-dialog.vue'),
    'question-preview-content': () => import('./question-preview-content.vue')
  },
  props: {
    data: {
      type: Object,
      default: () => null
    },
    showForm: {
      type: Boolean,
      default: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    isAnswerHint: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    previewIds: {
      type: Array,
      default: () => []
    },
    backText:{
      type: String,
      default: '返回题库'
    }
  },
  computed: {
    showQuestionModal: {
      get () {
        return this.visible;
      },
      set (value) {
        this.$emit('update:visible', value);
      }
    },
    getTitle: function() {
      return this.title || (
          (this.$route.query.courseName ? `《${this.$route.query.courseName}》` : '')
          +
          '题目预览'
      )
    }
  },
  watch: {
    visible: {
      handler: function (val) {
        if (val) {
          this.dataId = this.data.id
          this.getDetail()
        }
      },
      immediate: true
    }
  },
  data () {
    return {
      detailData: null,
      dataId: '',
      loading:false,
    }
  },

  methods: {
    async getDetail () {
      const { code, data } = await this.$api.GetQuestionDetailById({ id: this.dataId })
      if (code === 200) {
        this.detailData = null
        await this.$nextTick()
        this.detailData = initApiQuestion(data)
      }
    },
    async toBefore () {
      this.loading = true
      const index = this.previewIds.indexOf(this.dataId)
      if (index > 0) {
        this.dataId = this.previewIds[index - 1]
        await this.getDetail()
        this.loading = false
      } else {
        this.$message.info('这已经是本页第一道题目了')
      }
    },
    async toNext () {
      this.loading = true
      const index = this.previewIds.indexOf(this.dataId)
      if (index < this.previewIds.length - 1) {
        this.dataId = this.previewIds[index + 1]
        await this.getDetail()
        this.loading = false
      } else {
        this.$message.info('这已经是本页最后一道题目了')
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.question-preview-modal) {
}

.el-icon-arrow-left, .el-icon-arrow-right {
  position: fixed;
  top: 50%;
  font-size: 32px;
  color: rgb(226, 226, 226);
  cursor: pointer;
  border: 1px solid #f0f0f0;
  border-radius: 32px;
  background-color: #fff;
  padding: 10px;
}
.el-icon-arrow-left {
  left: calc(50% - 680px);
}
.el-icon-arrow-right {
  right: calc(50% - 680px);
}
</style>