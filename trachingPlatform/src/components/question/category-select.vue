<!--试题分类下拉框-->
<template>
  <div class="question-category-select">
    <el-popover v-model="popVisible" placement="bottom" width="250" trigger="click">
      <div style="max-height: 400px; overflow-y: auto; margin: 0 -12px; padding: 0 16px;">
        <category :show-action="false"
                  :show-all="false"
                  :course-id="courseId"
                  :current-node-id="newValue"
                  @initSuccess="initSuccess"
                  @nodeClick="nodeClick">
        </category>
      </div>
      <div slot="reference" class="el-select" style="width: 250px;">
        <div class="el-input el-input--medium el-input--suffix">
          <input type="text"
                 ref="inputRef"
                 :value="selectedNode.name"
                 readonly="readonly"
                 autocomplete="off"
                 placeholder="请选择"
                 class="el-input__inner">
          <span class="el-input__suffix">
            <span class="el-input__suffix-inner">
              <i class="el-select__caret el-input__icon" :class="popVisible ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
            </span>
          </span>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>

export default {
  name: 'question-category-select',
  components: {
    'category': () => import('./category.vue')
  },
  props: {
    value: {
      type: String | Number,
      default: ''
    },
    courseId: {
      type: String | Number,
      default: ''
    },
  },
  computed: {
    newValue: {
      get () {
        return this.value || 0
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  data () {
    return {
      popVisible: false,
      selectedNode: {}
    }
  },
  created () {
  },
  methods: {
    initSuccess (data) {
      const getList = (list) => {
        let arr = []
        list.forEach(item => {
          arr.push(item)
          if (item.children) {
            arr = arr.concat(getList(item.children))
          }
        })
        return arr
      }
      const dataList = getList(data)
      this.selectedNode = dataList.find(item => item.id === this.newValue) || {}
      this.changeNewValue()
    },
    nodeClick (node) {
      this.$refs.inputRef.focus()
      this.selectedNode = node
      this.changeNewValue()
      this.popVisible = false
    },
    changeNewValue () {
      this.newValue = this.selectedNode.id
    }
  }
}
</script>

<style scoped lang="scss">
.question-category-select {
}
</style>