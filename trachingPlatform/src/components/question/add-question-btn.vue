
<template>
  <div class="add-question-btn">
    <slot v-if="$slots.actionBtn" name="actionBtn"></slot>
    <!--trigger="click"-->
    <el-dropdown v-else placement="bottom" @command="commandAdd">
      <el-button type="primary"><i class="el-icon-plus"></i> 新增试题</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="(qTypeText, qType) in questionTypeLabel"
          :key="qType"
          :command="{ type: qType }">
          <div style="min-width: 80px; text-align: center;">{{ qTypeText }}</div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <div v-if="visible">
      <add-question-dialog :visible.sync="visible"
                           :type="currentType"
                           :data="defaultData"
                           :is-system="isSystem"
                           :course-id="courseInfo?.id"
                           :questionCategoryId="questionCategoryId"
                           @success="success"
      >
      </add-question-dialog>
    </div>
  </div>
</template>

<script>
import { questionTypeLabel } from '@/components/base/question/util.js';

export default {
  name: 'add-question-btn',
  components:{
    'add-question-dialog': () => import('./add-question-dialog.vue')
  },
  props: {
    courseInfo: { // 课程信息
      type: Object,
      default: () => ({ id: '',  classificationName: '' })
    },
    isSystem: { // 是否为平台知识点
      type: Boolean,
      default: true
    },
    questionCategoryId: { //分类id
      type: String | Number,
      default: ''
    }
  },
  data () {
    return {
      questionTypeLabel: questionTypeLabel,
      visible: false,
      currentType: '',
      defaultData: null
    }
  },
  mounted(){
  },
  methods: {
    commandAdd ({ type, data = null }) {
      this.currentType = type
      this.defaultData = data || null
      this.visible = true
    },
    success () {
      this.$emit('success')
    }
  }
}
</script>

<style scoped lang="scss">
.add-question-btn {
  display: inline-block;
  :deep(.el-button--primary) {
    &:hover, &:focus, &:active {
      background-color: var(--theme_primary_color) !important;
      border-color: var(--theme_primary_color) !important;
      color: #fff !important;
    }
  }
}
</style>