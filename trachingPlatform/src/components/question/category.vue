<!--试题分类-->
<template>
  <div class="question-category">
    <div class="cate-tree">
      <div v-if="showSearch" class="search-input">
        <el-input v-model="searchKey" placeholder="搜索分类" clearable @clear="search" @keyup.enter.native="search">
          <i slot="suffix" class="el-input__icon el-icon-search" @click="search"></i>
        </el-input>
      </div>
      <div class="tree-dv">
        <div v-if="showAll" class="all-node tree-item" @click="handleNodeClick({})">
          <div class="node-left"
               :class="!currentSelectNode.id ? 'node-left-active' : ''">
            <img class="folder-img" src="" />
            <span>全部</span>
          </div>
          <el-popover placement="right-start"
                      width="111"
                      trigger="hover"
                      popper-class="question-category-action-popover">
            <ul>
              <li @click="addCate()">增加子类</li>
            </ul>
            <div slot="reference" @click.stop v-if="showAction">
              <i class="el-icon-more"></i>
            </div>
          </el-popover>
        </div>
        <el-tree ref="treeRef"
            :style="{ marginLeft: showAll ? '15px' : '0' }"
            :data="treeData"
            :filter-node-method="filterNode"
            default-expand-all
            :expand-on-click-node="true"
            empty-text=""
            @node-click="handleNodeClick">
          <div class="tree-item" slot-scope="{ node, data }">
            <div :class="['node-left', currentSelectNode.id === data.id ? 'node-left-active' : '']">
              <img class="folder-img" src="" />
              <span>{{ data.name }}</span>
            </div>
            <el-popover placement="right-start"
                        width="86"
                        trigger="hover"
                        popper-class="question-category-action-popover"
                        @show="changActionVisible(true, data, node)"
                        @hide="changActionVisible(false, data)">
              <ul>
                <li @click="addCate(data.parentId)">增加同级</li>
                <li v-if="node.level < 4" @click="addCate(data.id)">增加子类</li>
                <li @click="editCate(data)">重命名</li>
                <li @click="delCate(data.id)">删除</li>
              </ul>
              <div slot="reference" @click.stop v-if="showAction">
                <i class="el-icon-more" :class="actionVisibleId === data.id ? 'action-visible' : ''"></i>
              </div>
            </el-popover>
          </div>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
import {checkEmpty} from "@/utils/utils";

export default {
  name: 'question-category',
  components: {
  },
  props: {
    // 是否显示操作
    showAction: {
      type: Boolean,
      default: true
    },
    // 是否显示搜索
    showSearch: {
      type: Boolean,
      default: true
    },
    // 是否显示全部
    showAll: {
      type: Boolean,
      default: true
    },
    // 当前选中节点
    currentNodeId: {
      type: Number | String,
      default: 0
    },
    courseId: {
      type: Number | String,
      default: 0
    }
  },
  data () {
    return {
      treeData: [],
      searchKey: '',
      currentSelectNode: {}, // 当前选择的节点
      actionVisibleId: '' // 当前弹出操作项的分类id
    }
  },
  created () {
    this.getDataTree()
  },
  watch:{
    courseId:{
      handler(newVal,val){
        this.getDataTree()
      }
    }
  },
  methods: {
    async getDataTree () {
      const { code, data } = await this.$api.GetQuestionCategoryTree({ courseId: this.courseId });
      if (code === 200) {
        this.treeData = data
        this.currentSelectNode = {
          id: this.currentNodeId || ''
        }
        this.$emit('initSuccess', data)
      }
    },
    async addCate (pId = 0) {
      const { value } = await this.$prompt('', '输入分类名', {
        inputValidator: (val) => {
          if (checkEmpty(val)) {
            return '输入的分类名不能为空'
          }
          if (val.length > 50) {
            return '输入的分类名长度不能超过50'
          }
          return true
        }
      })
      const params = {
        id: 0, // 0：新建
        parentId: pId || 0, // 0：无父id
        name: value.trim(),
        type: 0, // 预留参数
        courseId: this.courseId // 课程id
      }
      const { code, data } = await this.$api.AddQuestionCategory(params)
      if (code === 200) {
        this.$message.success('新增成功！')
        await this.getDataTree()
        this.$emit('addNode', data)
      }
    },
    async editCate (data) {
      const { value } = await this.$prompt('', '输入分类名', {
        inputValue: data.name,
        inputValidator: (val) => {
          if (checkEmpty(val)) {
            return '输入的分类名不能为空'
          }
          if (val.length > 50) {
            return '输入的分类名长度不能超过50'
          }
          return true
        }
      })
      const params = {
        ...data,
        name: value.trim()
      }
      params.courseId = this.courseId
      const { code } = await this.$api.AddQuestionCategory(params)
      if (code === 200) {
        this.$message.success('编辑成功！')
        await this.getDataTree()
      }
    },
    async delCate (pId = '') {
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该分类及下级分类都会被删除, 是否继续?',
      })
      const params = {
        id: pId
      }
      const { code } = await this.$api.DeleteQuestionCategory(params)
      if (code === 200) {
        this.$message.success('删除成功！')
        await this.getDataTree()
        this.$emit('deleteNode', pId)
      }
    },
    changActionVisible (val, data, node) {
      if (val) {
        this.actionVisibleId = data.id
      } else {
        this.actionVisibleId = ''
      }
    },
    search () {
      this.$refs.treeRef.filter(this.searchKey)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    handleNodeClick (data) {
      this.currentSelectNode = data
      this.$emit('nodeClick', this.currentSelectNode)
    }
  }
}
</script>

<style scoped lang="scss">
.question-category {
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.el-tree-node__expand-icon) {
    display: none !important;
  }
  :deep(.el-tree-node__content) {
    height: 40px !important;
  }
  .search-input {
    padding-bottom: 5px;
    :deep(.el-input__inner) {
      padding-right: 52px;
      background-color: #f6f8fa !important;
      border: none !important;
      border-radius: 4px !important;
    }
  }
  .cate-tree {
    background-color: #fff;
    flex: 1;
    .tree-dv {
      .all-node {
        line-height: 40px;
        cursor: pointer;
        &:hover {
          background-color: var(--theme_font_hover_color);
        }
      }
      .tree-item {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .node-left {
          display: flex;
          align-items: center;
          font-size: 15px;
          color: #333333;
          max-width: calc(100% - 18px);
          overflow: hidden;
          .folder-img {
            width: 20px;
            height: 20px;
            margin-top: -2px;
            margin-right: 3px;
          }
        }
        .node-left-active {
          color: var(--theme_primary_color) !important;
        }
        .el-dropdown-block {
          display: inline-block !important;
        }
        .el-icon-more {
          color: #D9D9D9;
          transform: rotate(90deg);
          font-size: 13px;
          &:hover {
            color: var(--theme_primary_color) !important;
          }
        }
        .action-visible {
          color: var(--theme_primary_color) !important;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.question-category-action-popover {
  min-width: 20px;
  ul {
    cursor: pointer;
    line-height: 30px;
    li {
      &:hover {
        color: var(--theme_primary_color) !important;
      }
    }
  }
}
</style>