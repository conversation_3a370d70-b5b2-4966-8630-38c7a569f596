<template>
  <div>
    <!-- 项目重引入的字体图标的呈现 -->
     <div class="top-part">
       <h3>字体图标列表</h3>
       <!-- 添加搜索输入框 -->
       <el-input style="width:300px" v-model="searchKeyword" placeholder="输入关键字搜索图标" />
     </div>
    <div class="icon-content">
      <!-- 使用计算属性 filteredIconList 过滤图标列表 -->
      <div v-for="icon in filteredIconList" :key="icon" class="icon-item">
        <span :class="['iconfont', icon]"></span>
        <p class="label">{{ icon }}</p>
      </div>
    </div>
  </div>
</template>
<script>
const { getIconListFromCss } = require('./iconUtils.js');
export default {
  data() {
    return {
      iconList: [],
      // 新增搜索关键字变量
      searchKeyword: ''
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    async getList() {
      this.iconList = await getIconListFromCss();
    }
  },
  // 新增计算属性，用于过滤图标列表
  computed: {
    filteredIconList() {
      return this.iconList.filter(icon =>
        icon.includes(this.searchKeyword)
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.top-part{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 20px;
  h3 {
    margin: 0;
  }
  .el-input{
    margin-left: 20px;
  }
}
.icon-content {
  height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-wrap: wrap;
  .icon-item {
    display: block;
    width: 150px;
    height: 100px;
    margin: 10px;
    text-align: center;
    background-color: #fff;
    color: #666;
    .iconfont {
      font-size: 30px;
      color: #666;
    }
    .label {
      font-size: 16px;
    }
  }
}
</style>