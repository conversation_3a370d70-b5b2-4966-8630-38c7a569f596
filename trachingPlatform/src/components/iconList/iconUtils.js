// 移除 fs 模块，因为浏览器环境不支持
// const fs = require('fs'); 
const path = require('path');

async function getIconListFromCss() {
    // 构建相对路径
    const cssFilePath = path.join(__dirname, '../../static/css/icon-font/iconfont.css');
    // 假设在浏览器中运行，使用相对路径
    const relativePath = cssFilePath.replace(__dirname, ''); 
    try {
        // 使用 fetch API 获取 CSS 文件内容
        const response = await fetch(relativePath);
        const cssContent = await response.text();
        const iconClassRegex = /\.([a-zA-Z0-9_-]+):before/g;
        const iconList = [];
        let match;
        while ((match = iconClassRegex.exec(cssContent))!== null) {
            iconList.push(match[1]);
        }
        return iconList;
    } catch (error) {
        console.error('Error fetching CSS file:', error);
        return [];
    }
}

module.exports = {
    getIconListFromCss
};