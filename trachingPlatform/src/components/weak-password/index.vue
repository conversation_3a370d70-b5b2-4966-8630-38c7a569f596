<template>
 <div class="weak-password-page">
    <p class="title-tip">为了您的账号安全，有以下信息待完善</p>
    <p class="form-tip">登陆安全   <span>当前密码强度不符合要求，请及时修改密码</span> </p>

    <div class="weak-password">
      <span class="label">密码强度：</span>
      <span class="step-i">
        <i class="blue"></i>
        <i class="gray"></i>
        <i class="gray"></i>
        {{text}}
      </span>
    </div>
    <el-button class="skip-btn" @click="editPassWord">立即修改</el-button>
 </div>
</template>
<script>
export default {
  name: '',
  props: '',
  components: {
  },
  data() {
    return {
      text:'弱',
    }
  },
  methods:{
    editPassWord(){
      this.$router.push({
        path:'/personal',
        query:{
          edit: 1,
        }
      })
    }
  }
}
</script>
 
<style lang="scss" scoped>
 .weak-password-page{
  width: 550px;
  height: 330px;
  background: #FFFFFF;
  text-align: center;

  .title-tip{
    height: 86px;
    line-height:100px;
    font-size: 18px;
    border-bottom: 1px solid #D9D9D9;
    position:relative;
    &::before{
      content: '';
      width:46px;
      height:14px;
      display:block;
      position: absolute;
      bottom: -9px;
      left:50%;
      margin-left:-23px;
      background:url('../../assets/public/login/tip.png')no-repeat;
    }
  }
  .form-tip{
    margin-top:20px;
    position: relative;
    font-size: 16px;
    text-align: left;
    padding-left: 14px;
    color: #333;
    &::before{
      content: '';
      position:absolute;
      top: 0;
      left:0;
      width: 3px;
      height: 16px;
      background: #2B61FA;
      border-radius: 5px 5px 5px 5px;
    }

    span{
      margin-left:48px;
      color: #909090;
    }
  }
  .weak-password{
    margin-top:46px;
    .label{
      color:#333;
    }
    .step-i{
      
      color:#333;
      i{
        display: inline-block;
        width: 45px;
        height: 6px;
        margin-right:10px;
      }

      .gray{
        background-color: #BCBCBC;
      }
      .blue{
        background-color: #2B66FA;
      }
    }
  }
  .skip-btn{
    margin-top:58px;
    color:#fff;
    width: 248px;
    font-size: 18px;
    height: 50px;
    background: linear-gradient( 270deg, #5877FB 0%, #114BFA 81%, #0F4AFA 83%, #0041FA 100%);
    box-shadow: 0px 3px 6px 1px rgba(0,65,250,0.42);
    border-radius: 30px 30px 30px 30px;
  }
 }
</style>