<template>
  <div
    id="app"
    v-cloak
    style="height: 100%">
    <router-view v-if="!$route.meta.keepAlive" />

     <!-- <div class="aIshow" v-show="isAuthaI">
      <div class="control-contentro" v-show="isAiOpen?false:true" id="box1">
        <div class="control-boxro">
          <div class="control-item-box" @click="handleAiControl">
            <img src="./assets/discussion/rodic.gif"/>
          </div>
        </div>
      </div>
    </div> -->
     <!-- <AiDialog :isOpen="isAiOpen" @handlecloseAiModal="handlecloseAiModal" v-show="isAiOpen" ></AiDialog> -->
  </div>
</template>
<script>
import { mapGetters } from "vuex"
import { setupCopy } from "@/utils/copy.js"

import doTokenSuccess from "@/utils/doTokenSuccess";
// import AiDialog from "@/components/ai-dialog/index.vue";

export default {
  components: { 
    // AiDialog
  },
  data() {
    return {
      // isAiOpen:false,
      // isAuthaI:false,

    }
  },
  computed: {
    ...mapGetters({
    })
  },
  created(){

  },
  mounted() {
    //  this.dargfun()
  },
  methods:{
    dargfun(){
            let that=this
            let clickTimeout;
            var box1 = document.getElementById("box1");
            box1.onmousedown = function(e){
              that.isMouseDown = true;
              // console.log(e,'事件类型')
                if (box1.setCapture) {
                    box1.setCapture();
                }
                //设置鼠标不论点哪个位置都不会偏移，点在元素哪个位置就是哪个位置
                e = e || window.e;
                var ol = e.clientX - box1.offsetLeft;
                var ot = e.clientY - box1.offsetTop;
                document.onmousemove = function(e){
                    e = e || window.e;
                    var left = e.clientX - ol;
                    var top = e.clientY - ot;
                    //记住要+'px'
                    box1.style.left = left + 'px';
                    box1.style.top = top + 'px';
                     box1.style.pointerEvents = 'none'
                };
                //设置的是document.onmouseup才不会出现元素重叠时，鼠标抬起失效
                document.onmouseup = function(){
                    box1.style.pointerEvents = null
                    document.onmousemove = null;
                    document.onmousedown = null;
                    document.onmouseup = null;
                    box1.releaseCapture && box1.releaseCapture();

                };
                // 解决拖拽过程中鼠标抬起仍旧处于拖拽状态问题
                document.ondragstart = function(ev) {
                    ev.preventDefault();
                };
                document.ondragend = function(ev) {
                    ev.preventDefault();
                };
                return false;
            }
    },
    handleAiControl(){
      
    }
  }
}
</script>
<style lang="scss">
[v-cloak] {
  display: none;
}
body {
  position: relative;
}

/* 默认颜色 */
:root {
  --theme_font_hover_color: #f5f7fa;
  /* 主色调 */
  --color-primary: #3367fa;
  /* 主色调第2级 */
  --color-primary2: #4e71ff;
  /* 主色调第3级 */
  --color-primary3: #5e7ffd;
  /* 主色调第4级 */
  --color-primary4: #768bff;
  /* 主色调第5级 */
  --color-primary5: #8b9dff;
  /* 成功 */
  --color-success: #13ce66;
  /* 警告 */
  --color-warning: #f59337;
  /* 危险 删除按钮 */
  --color-danger: #ff0000;
  /* 备注文字 */
  --color-info: #bbbbbb;
  /* 主题色 */
  --theme_primary_color: #2b66ff;
  /* 背景色 */
  --theme_bg_color: #0973f1;
  /* 渐变色 */
  --theme_bg_color1: linear-gradient(270deg, #8833d6 0%, #1d3aed 100%);
  /*#75D3BC */
  --theme_bg_header: #0973f1;
  /* 按钮颜色 */
  --theme_button_color: #0973f1;
  /*#3ABD9D */
  --theme_button_bg_color: #0973f1;
  /*按钮悬浮 */
  --theme_button_hover_color: #0561cd;
  --theme_button_hover2_color: #fff;
  /*#F1FFFB */
  /* 边框颜色 */
  --theme_border_color: #0973f1;
  /*#3ABD9D */
  /*字体颜色 */
  --theme_font_color: #0973f1;
  /**#666 */
  /* 鼠标悬浮 颜色 */
  --them_hover_color: #f4f8fd;
  /*#E8F8F4 */
  /* 头部字体颜色 */
  --theme_header_font_color: #fff;
  /* sideBar-bg */
  --theme_sideBar_bg_color: #1f2834;
}

iframe {
  border: none;
}


.control-contentro {
  width: 64px;
  height: 64px;
  position: fixed;
  bottom: 72px;
  right: 72px;
  z-index: 2000;
  .control-boxro {
    position: relative;
    width: 64px;
    height: 64px;
    .control-item-active-box,
    .control-item-box {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius:50%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
    }
    .control-item-box {
      img {
        width: 64px;
        height: 64px;
      }
    }
  }
}
</style>
