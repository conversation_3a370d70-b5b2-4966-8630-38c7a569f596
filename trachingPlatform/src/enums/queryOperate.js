export const operateEnum = {
  /// <summary>
  /// 等于
  /// </summary>
  equal: 1,
  /// <summary>
  /// 小于
  /// </summary>
  smalle: 2,
  /// <summary>
  /// 大于
  /// </summary>
  greaterThan: 3,
  /// <summary>
  /// 小于等于
  /// </summary>
  lessThanOrEqual: 4,
  /// <summary>
  /// 大于等于
  /// </summary>
  greaterThanOrEqual: 5,
  /// <summary>
  /// 不等于
  /// </summary>
  notEqual: 6,
  /// <summary>
  /// 相似
  /// </summary>
  like: 7,
};

const names = {
  equal: '等于',
  smalle: '小于',
  greaterThan: '大于',
  lessThanOrEqual: '小于等于',
  greaterThanOrEqual: '大于等于',
  notEqual: '不等于',
  like: '相似',
};

export const operateSelectEnum = Object.keys(operateEnum).map(key => {
  return {
    value: operateEnum[key],
    label: names[key]
  };
});
