/*
 * @Author: ztong <EMAIL>
 * @Date: 2024-07-29 09:36:00
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-07-29 10:01:32
 * @FilePath: \fusion_front\src\enums\scene.js
 * @Description: 基础枚举类型
 */
// /// <summary>
// /// 动态表单
// /// </summary>
// ;(动态表单 = 1),
//   /// <summary>
//   /// 通用票据
//   /// </summary>
//   (通用票据 = 3),
//   /// <summary>
//   /// 葡萄层
//   /// </summary>
//   (葡萄城 = 2),
//   /// <summary>
//   /// 业务基础数据
//   /// </summary>
//   (业务基础数据 = 4),
//   /// <summary>
//   /// 列表
//   /// </summary>
//   (列表 = 5)
export const DYNAMIC_SCENE_TYPE = 1
export const SHEET_SCENE_TYPE = 2
export const BILL_SCENE_TYPE = 3
export const BASE_DATA_SCENE_TYPE = 4
export const DATA_SOURCE_SCENE_TYPE = 5
export const DATA_SOURCE_SUBLEDGER_TYPE = 6
export const DATA_ATTACHMENT_TYPE = 7
export const sceneTypeEnum = [
  {
    label: '动态表单',
    value: DYNAMIC_SCENE_TYPE
  },
  {
    label: '葡萄城',
    value: SHEET_SCENE_TYPE
  },
  {
    label: '通用票据',
    value: BILL_SCENE_TYPE
  },
  {
    label: '业务基础数据',
    value: BASE_DATA_SCENE_TYPE
  },
  {
    label: '列表',
    value: DATA_SOURCE_SCENE_TYPE
  },
  {
    label: '附件',
    value: DATA_ATTACHMENT_TYPE
  }
]

export const menuTypeEnum = [
  {
    label: '目录',
    value: 1
  },
  {
    label: '场景',
    value: 2
  },
  {
    label: '链接',
    value: 3
  }
]
