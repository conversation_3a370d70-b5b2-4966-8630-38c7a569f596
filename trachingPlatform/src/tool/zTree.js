import { getPositionByBindPath } from './spreadTool.js';

function ZTree(elementId, nodes, spread) {
  var setting = {
    edit: {
      enable: true,
      showRemoveBtn: false,
      showRenameBtn: false,
      drag: {
        // isCopy: false,
        // isMove: true,
        // prev: true,
        // next: true,
        // inner: true
      }
    },
    data: {
      simpleData: {
        enable: true,
      },
      key: {
        title: "title",
      },
    },
    callback: {
      onDrag: (event, treeId, treeNodes) => this._onDrag(event, treeId, treeNodes),
      onDragMove: (event, treeId, treeNodes) => this._onDragMove(event, treeId, treeNodes),
      onDrop: (event, treeId, treeNodes, targetNode, moveType) => this._onDrop(event, treeId, treeNodes, targetNode, moveType),
      onClick: (event, treeId, treeNodes) => this._onClick(event, treeId, treeNodes),
      onDblClick: (event, treeId, treeNodes) => this._onDoubleClick(event, treeId, treeNodes),
    },
  };

  this.element = document.getElementById(elementId);
  this.spread = spread;

  $.fn.zTree.init($(this.element), setting, nodes);
}

ZTree.prototype._onDrag = function(event, treeId, treeNodes) {
  if (!this.spread) {
    return;
  }
  this.spread.getHost().style.position = "relative";
  return true;
};

ZTree.prototype._onDragMove = function(event, treeId, treeNodes) {
  this._onDocumentMouseMove(event);
  return true;
};

ZTree.prototype._onDrop = function(event, treeId, treeNodes, targetNode, moveType) {
  this._onDocumentMouseUp(event, treeNodes);
  this._removeBlock();
};

ZTree.prototype._onDocumentMouseMove = function(evt) {
  evt.preventDefault();
  const hitInfo = this._getHitTestInfo(evt);
  if (!hitInfo) {
    return;
  }
  if (hitInfo.row === undefined || hitInfo.col === undefined) {
    return;
  }
  if (isNaN(hitInfo.row) || isNaN(hitInfo.col)) {
    return;
  }
  evt.stopPropagation();
  if (hitInfo.rowViewportIndex !== 1 || hitInfo.colViewportIndex !== 1) {
    return;
  }
  const activeSheet = this.spread.getActiveSheet();
  const rect = activeSheet.getCellRect(hitInfo.row, hitInfo.col);
  this._highlightBlock(rect);
};
ZTree.prototype._onDocumentMouseUp = function(evt, items) {
  evt.preventDefault();
  const hitInfo = this._getHitTestInfo(evt);
  if (!hitInfo) {
    return;
  }
  if (hitInfo.row === undefined || hitInfo.col === undefined) {
    return;
  }
  if (isNaN(hitInfo.row) || isNaN(hitInfo.col)) {
    return;
  }
  evt.stopPropagation();
  if (hitInfo.rowViewportIndex !== 1 || hitInfo.colViewportIndex !== 1) {
    return;
  }
  const activeSheet = this.spread.getActiveSheet();
  if (items && items.length) {
    const item = items[0];
    if (item.children && item.children.length) { // 表格
      const tablePosition = {
        x: hitInfo.col,
        y: hitInfo.row,
        len: item.children.length,
      };
      sessionStorage.setItem("tablePosition", JSON.stringify(tablePosition));
      let table = activeSheet.tables.add("Table" + item.code, hitInfo.row, hitInfo.col, 2, item.children.length);
      table.autoGenerateColumns(false);
      let cloumns = [];
      for (let j = 0; j < item.children.length; j++) {
        const child = item.children[j];
        // 做调度的标记
        const tableTag = {
          // columnId: child.columnId,
          // comId: child.comId,
          // dataObjectId: child.dataObjectId,
          // label: child.label,
          // projectId: child.projectId,
          // objectTempId: child.objectTemplateId,
          tableId: child.tableId,
          columnId: child.id,
          isDispatch: child.isDispatch,
        };

        activeSheet.setTag(hitInfo.row, hitInfo.col + j, JSON.stringify(tableTag));
        let tableColumn = new GC.Spread.Sheets.Tables.TableColumn();
        tableColumn.name(child.name);

        tableColumn.dataField(`${child.columnCode}`);
        // tableColumn._ps.isFormula = true;
        cloumns.push(tableColumn);
      }

      table.bindColumns(cloumns);

      table.bindingPath(item.code);
      //
    } else { // 单个绑定
      // 做调度的标记
      const tableTag = {
        // comId: items[0].comId,
        // dataObjectId: items[0].dataObjectId,
        // label: items[0].label,
        // projectId: items[0].projectId,
        // objectTempId: items[0].objectTemplateId,
        tableId: items[0].tableId,
        columnId: items[0].id,
        isDispatch: items[0].isDispatch,
      };
      activeSheet.setTag(hitInfo.row, hitInfo.col, JSON.stringify(tableTag));
      activeSheet.setBindingPath(hitInfo.row, hitInfo.col, `${items[0].code}.${items[0].columnCode}`);
    }
    activeSheet.repaint();
  }
};
ZTree.prototype._highlightBlock = function(rect) {
  if (!this.decoration) {
    this.decoration = document.createElement("div");
    this.decoration.style.position = "absolute";
    this.decoration.style.border = "1px solid blue";
    this.decoration.style.boxShadow = "0px 0px 4px 0px #007eff";
    this.decoration.style.zIndex = "1000";
    this.spread.getHost().appendChild(this.decoration);
  }
  this.decoration.style.width = rect.width - 1 + "px";
  this.decoration.style.height = rect.height - 1 + "px";
  this.decoration.style.left = rect.x + "px";
  this.decoration.style.top = rect.y + "px";
};
ZTree.prototype._removeBlock = function() {
  if (this.decoration) {
    this.decoration.remove();
    this.decoration = undefined;
  }
};

ZTree.prototype._getHitTestInfo = function(event) {
  if (!this.spread) {
    return;
  }
  const activeSheet = this.spread.getActiveSheet();
  if (!activeSheet) {
    return;
  }
  const canvas = this.spread.getHost().querySelector("canvas[gcuielement]");
  const t = this._getOffset(canvas);
  return activeSheet.hitTest(event.pageX - t.left, event.pageY - t.top);
};
ZTree.prototype._getOffset = function(element) {
  let left = 0;
  let top = 0;
  while (element) {
    left += element.offsetLeft;
    top += element.offsetTop;
    element = element.offsetParent;
  }
  return {
    left: left,
    top: top,
  };
};
// 双击事件
ZTree.prototype._onDoubleClick = function(event, treeId, treeNodes) {
  // let { code, columnCode, dataFormat } = treeNodes;
  // getPositionByBindPath(this.spread, code, columnName, dataFormat, false);
  const { code, columnCode, dataFormat } = treeNodes;
  getPositionByBindPath(this.spread, code, columnCode, dataFormat, false);
};
// 单击事件
ZTree.prototype._onClick = function(event, treeId, treeNodes) {
  const { code, columnCode, dataFormat } = treeNodes;
  getPositionByBindPath(this.spread, code, columnCode, dataFormat, true);
  // let { code, columnName, dataFormat } = treeNodes;
  // getPositionByBindPath(this.spread, code, columnName, dataFormat, true);
};
export { ZTree };
