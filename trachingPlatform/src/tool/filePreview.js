import { filesType} from '@/enums/dispatch.js'

/**
 *
 * @param {*} activeName
 * @param {*} fileType
 * @returns
 */
export const showImg = function (fileType) {
  var filestype = [0,1,2,3]; //, 4
  var isext = filestype.includes(fileType);
  return isext;
};

/**
 * 获取文档封面图片
 * @param {*} fileUrl
 * @returns
 */
export const showCover = function (fileUrl) {
  if (fileUrl == "" || fileUrl == null) return;
  var xurl;
  if (window.location.href.indexOf("https") > -1) {
    let url = "https://ow365.cn/";
    if (fileUrl.indexOf("https") > -1) {
      xurl = url + "?i=24269&n=5&ssl=1&info=1&&htool=1furl=";
    } else {
      xurl = url + "?i=24269&n=5&info=1&&htool=1furl=";
    }
  } else {
    let url = "http://ow365.cn/";
    if (fileUrl.indexOf("https") > -1) {
      xurl = url + "?i=24269&n=5&ssl=1&info=1&&htool=1&furl=";
    } else {
      xurl = url + "?i=24269&n=5&info=1&&htool=1&furl=";
    }
  }
  xurl += encodeURIComponent(fileUrl);
  if (
    fileUrl.indexOf("jpg") > -1 ||
    fileUrl.indexOf("png") > -1 ||
    fileUrl.indexOf("gif") > -1
  ) {
    return fileUrl;
  } else {
    return xurl;
  }
};

/**
 * 获取文档附件预览
 * @param {*} fileUrl
 * @param {*} type 文件类型
 * @returns
 */
export const showPreview = function (fileUrl,type,i= 24269) {
  let previewType=5;// 文件预览模式默认值 5

  // 如果 type 存在 则判断文件类型 来对应
  if(type){
    let typeObj = filesType.find(v=>v.value==type)
    if(typeObj){
      switch (typeObj.label) {
        case 'Word':
          previewType = 3
          break;
        case 'PDF':
          previewType = 5
          break;
        case 'PPT':
          previewType = 5
          break;
        default:
          break;
      }
    }
  }

  if (fileUrl == "" || fileUrl == null) return;
  var xurl;
  //
  if (window.location.href.indexOf("https") > -1) {
    let url = "https://ow365.cn/";
    if (fileUrl.indexOf("https") > -1) {
      xurl = url + `?i=${i}&n=${previewType}&ssl=1&furl=`;
    } else {
      xurl = url + `?i=${i}&n=${previewType}&furl=`;
    }
  } else {
    let url = "http://ow365.cn/";
    if (fileUrl.indexOf("https") > -1) {
      xurl = url + `?i=${i}&n=${previewType}&ssl=1&furl=`;
    } else {
      xurl = url + `?i=${i}&n=${previewType}&furl=`;
    }
  }
  xurl += encodeURIComponent(fileUrl);
  if (
    fileUrl.indexOf("jpg") > -1 ||
    fileUrl.indexOf("png") > -1 ||
    fileUrl.indexOf("gif") > -1
  ) {
    return fileUrl;
  } else {
    return xurl;
  }
};

// 获取视频第一帧的函数
export const getVideoFirstFrame = function (videoUrl) {
  // 创建video元素
  const video = document.createElement("video");
  video.src = videoUrl;
  video.setAttribute("crossOrigin", "Anonymous"); // 处理跨域
  video.setAttribute("preload", "auto"); // auto|metadata|none

  //const video = document.getElementsByClassName("upload-demo video");
  //const video = document.getElementById("video");
  // 等待视频加载完成
  return new Promise((resolve, reject) => {
    video.addEventListener("loadedmetadata", () => {
      // 创建canvas元素
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // 将视频第一帧绘制到canvas上
      const ctx = canvas.getContext("2d");

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // 将canvas图像转换为base64格式的数据URI
      const dataUrl = canvas.toDataURL();

      // 返回base64格式的数据URI
      resolve(dataUrl);
    });

    // 如果视频加载出错，则返回错误信息
    video.addEventListener("error", () => {
      reject(`Failed to load video: ${videoUrl}`);
    });
  });
};
