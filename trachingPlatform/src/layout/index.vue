<template>
  <!-- screenType -->
  <transition name="el-zoom-in-center">
    <div v-show="isShow" v-loading="loading" class="layout-box" :class="[$route.name, bgType]">
      <HeaderPrimary :isAnswerStatus="false" @submitPaper="submitPaper" />
      <sidebar class="sidebar-container" />
      <div class="view">
        <div class="tab-bar">
          <tags></tags>
        </div>
        <div>
          <router-view />
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { HeaderPrimary, Sidebar, tags, } from './components';
import { mapGetters } from 'vuex';
export default {
  name: 'Layout',
  components: {
    HeaderPrimary,
    Sidebar,
    tags,
  },
  data() {
    return {
      bgType: '',
      isShow: true,
      loading: false,
    };
  },
  computed: {
    ...mapGetters({
    })
  },
  watch: {

  },
  mounted() {
    window.addEventListener('beforeunload', () => {
      sessionStorage.removeItem('cutTask');
    });
    let cutTask = sessionStorage.getItem('cutTask');
    if (cutTask && cutTask == 1) {
      this.isShow = false;
      setTimeout(() => {
        this.isShow = true;
      }, 250);
    }


  },

  methods: {

    // 交卷事件
    submitPaper() {

    },
  }
};
</script>

<style lang="scss" scoped>
.box {
  transition: all 1s;
  opacity: 1;
}

.layout-box {
  width: 100vw;
  height: 100vh;
  background-size: 100% 100%;

}

.sidebar-container {
  transition: width 0.28s;
  width: 220px !important;
  height: 100%;
  position: fixed;
  font-size: 0px;
  // top: 0.643rem;
  top: 6.5vh;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: hidden;
}

.view {
  margin-left: 220px;
  height: calc(100vh - 0.6rem);
  background: #F5FAFE;
  border: 1px solid transparent;
}
</style>
