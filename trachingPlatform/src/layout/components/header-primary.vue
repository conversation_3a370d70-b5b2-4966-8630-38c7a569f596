
<template>
  <div class="mainheader" v-if="!isSHowHeard">
    <div class="header" :class="showBottomLine ? '' : 'hide-bottom-line'"
      :style="colorType == 1 ? 'background:#fff' : ''">
      <div class="header-l">
        <div class="headerIcon" v-if="showLogo" @click="handleHome">
          <img alt="logo" :src="require('../../assets/header/logo-circle.png')" />
          <div :style="colorType == 1 ? 'color:#222' : ''" class="headerword">
            四流融合数字化教学系统
          </div>
        </div>
        <div v-else class="header-back">
          <span class="el-icon-arrow-left" @click="back"></span>
        </div>
        <div class="headerNav" v-if="showHeaderNav">
          <ul v-if="!isWorkBranch" class="nav">
            <li v-for="(item) in navList" :key="item.index" class="item" :class="{ active: nowIndex === item.index }"
              @click="tabClick(item, item.index)">
              {{ item.name }}
            </li>
            <!-- v-show="navList.length!=0" -->
            <span ref="activeBar" class="bottom-bar"></span>
          </ul>
        </div>
      </div>
      <div class="header-r">
      
        <div v-if="isWorkBranch">
          <span :style="colorType == 1 ? 'color:#fff' : ''" class="change-front" @click="backCase"><i
              class="iconfont icon-gongneng"></i> 前台</span>
        </div>

        

        <div class="tag-icon-box" @click="backMain" v-if="showReturnHome">
          <div class="img-box">
            <i class="icon iconfont icon-back"></i>

          </div>
          <span :style="colorType == 1 ? 'color:#222' : ''">返回主页</span>
        </div>
        <div v-if="QRcodeObj.isQRcode&&false" class="el-dropdown-link" style="display: flex;margin-left: 20px">
          <img style="width: 14px;height: 16px" src="../../assets/teaching/message.png">
          <span class="message">消息</span>
        </div>
      
        <div class="header-r-box-clickbox" v-if="QRcodeObj.isQRcode&&false">
          <el-dropdown>
            <span class="el-dropdown-link">
              <img style="width: 17px;height: 15px" src="../../assets/teaching/group-icon.png">
              <span class="message">服务群</span>
            </span>
            <el-dropdown-menu slot="dropdown" class="service-dropdown">
              <div class="service-box">
                <img src="@/assets/teaching/server-logo.png" alt="">
                <p class="class-p-sevrve">四流融合服务群</p>
                <img width="130" height="130" :src="QRcodeObj.QRcodeUrl" alt="">
              </div>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <!-- QRcodeObj.isQRcode&& -->
        <identityBtn :type="2" v-if="$route.path=='/teachingWork'&&userInfo.userType!=1" />
        <div :style="colorType == 1 ? 'color:#222' : ''" class="help" v-if="showHelp && helpObj.isHelpCenter">
          <a :href="helpObj.helpUrl" target="_blank" rel="noopener noreferrer">
            <i class="iconfont icon-bangzhuzhongxin3"></i>
            <span>帮助中心</span>
          </a>
        </div>
        <div class="linex" :style="colorType == 1 ? 'background:#222' : ''" v-if="showHelp && helpObj.isHelpCenter"></div>
        <el-badge :value="messageCount" :max="99" class="item" v-if="messageCount > 0">
          <i class="el-icon-chat-line-round" style=" font-size: 24px; color: #fff; "></i>
        </el-badge>
      
        <div class="user">
          <el-dropdown class="dropdown" @command="handleCommand">
            <div class="el-dropdown-link" :style="colorType == 1 ? 'color:#222' : ''">
              <img :src="userInfo.photo
        ? userInfo.photo
        : require('../../assets/header/toux.png')
        " alt="" />
              <div>
                <p :style="!isWorkBranch?'font-weight: 400;font-size: 14px;color: #FFFFFF;':'font-weight: 400;font-size: 14px;color: #333;'">{{ userInfo.realName || decodeURI(userInfo.nickname) }}</p>
                <p :style="!isWorkBranch?'font-weight: 400;font-size: 10px;color: #E1EAFF;':'font-weight: 400;font-size: 10px;color: #666;'">{{userInfo.phone_number}}</p>
              </div>
              <img style="width: 9px;height: 6px;margin-right: 0" src="../../assets/header/arrow-down.png">
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="个人中心" v-if="$route.query.edit != 1&&!($route.query.isForeground==='true')">个人中心</el-dropdown-item>
              <el-dropdown-item command="退出登录" divided>退出登录</el-dropdown-item>
              <el-dropdown-item command="授权登录" v-if="['0', '6'].includes(userInfo.userType)&&!($route.query.isForeground==='true')">授权登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <classroomChooseDialog v-if="classroomDialogVisible" :visible.sync="classroomDialogVisible" />
      <el-dialog title="授权登录" class="loginDialogVisible" :visible.sync="loginDialogVisible">
        <div>
          <el-input placeholder="请输入用户名" v-model="authorizedAccount" clearable>
          </el-input>
          <el-button type="primary" @click="authorizedLogin">授权登录</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="aIshow" v-show="isAuthaI">
      <div class="control-contentro" v-show="isAiOpen?false:true" id="box1">
        <div class="control-boxro">
          <div class="control-item-box" @click="handleAiControl">
            <img src="../../assets/discussion/rodic.gif"/>
          </div>
        </div>
      </div>
    </div>
    <AiDialog :isOpen="isAiOpen" @handlecloseAiModal="handlecloseAiModal" v-show="isAiOpen" ></AiDialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { resetRouters } from "@/router/index";
import classroomChooseDialog from "@/components/screen/classroom-choose-dialog.vue";
import identityBtn from "./identityBtn.vue";
import AiDialog from "@/components/ai-dialog/index.vue";
import { openUniqueTab,openOrFocusTab } from '@/utils/common.js'
export default {
  name: "HeaderPrimary",
  components: {
    classroomChooseDialog,
    identityBtn,
    AiDialog
  },
  props: {
    isWorkBranch: {
      default: false,
      type: Boolean
    },
    colorType: {} // 头部背景色区分 1 控制台头部 为白色
  },

  data() {
    return {
      isSHowHeard: null,
      type: 2, // 身份类型 1 学生 2 教师
      showBottomLine: true,
      classroomDialogVisible: false,
      loginDialogVisible: false,
      nowIndex: 1,
      messageCount: 0,
      tabIndex: 1,// 导航点击的下标
      helpObj: {
        isHelpCenter: false,
        helpUrl: 'https://zavavsx3uyw.feishu.cn/wiki/JVV7w8wPgi3za1kdTS2c3ssGndf'
      },
      QRcodeObj: {
        isQRcode: false,
        QRcodeUrl: ''
      },
      authorizedAccount: '',
      navList: [],//头部导航列表
      isAuthaI:true,
      isAiOpen:false,
    };
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"],
      // sysMenuList:['getSysMenuList']
    }),
    showReturnHome() {
      // this.$route.path === "/prepareLessons" ||//9-4郴州需求
      return this.$route.path === "/teachingWork" ||
        this.$route.path === "/courseModal/courseLibrary"
        ? true
        : false;
    },
    showHeaderNav() {
      let result = true;

      switch (this.$route.path) {
        case "/voucherTopic":
        case "/Twordaccount":
        case "/homework/report/detailaccount":
        case "/homework/report/subjectbalancesheet":
        case "/homework/report/totalaccount":
        case "/homework/manual-accounting/balancesheet":
        case "/homework/manual-accounting/income":
        case "/teachingWork": //教师首页
        case "/courseModal/courseLibrary": //示范课程
        case "/courseModal/courseDtails": //示范课程详情
        case "/homework/create": //创建任务
        case "/homework/submit": //发布任务
        case "/prepareLessons"://备课
        case "/taskDetails": //教师发任务详情
        case "/personal": //个人中心
        {
          //备课
          result = false;
          break;
        }
      }
      //弱密码强制修改
      if (this.$route.query.edit == 1) {
        result = false
      }
      return result;
    },
    showHelp() {
      let result = true;

      switch (this.$route.path) {
        case "/voucherTopic":
        case "/Twordaccount":
        case "/homework/report/detailaccount":
        case "/homework/report/subjectbalancesheet":
        case "/homework/report/totalaccount":
        case "/homework/manual-accounting/balancesheet":
        case "/homework/manual-accounting/income":
        case "/teachingWork": //教师首页
        case "/prepareLessons": {
          //备课
          result = false;
          break;
        }
      }

      return result;
    },
    showLogo() {
      let result = true;
      if (this.$route.path == '/courseModal/courseDtails') {
        result = false;
      } else if (this.$route.query.edit == 1) {
        result = false;
      } else if (this.$route.path == '/prepareLessons') {
        result = false;
      } else if (this.$route.path == '/taskDetails') {
        result = false;
      } else if (this.$route.path == '/homework/submit') {
        result = false;
      } else if (this.$route.path == '/personal') {
        result = false;
      }
      return result;
    },
    helpsClick(){
      // console.log(1111)
      // window.open('https://zavavsx3uyw.feishu.cn/wiki/JVV7w8wPgi3za1kdTS2c3ssGndf')
    }
    // navList() {
    //   if (this.$route.path === "/teaching") {
    //     return [];
    //   }
    //   const sysMenuList = this.sysMenuList.map(v=> v.routeUrl)
    //   const allMenu = [
    //     // { name: '首页', path: '/home', index: 0 },
    //     { name: "场景", path: "/case-list", index: 1 },
    //     // { name: '场景', path: '/sence', index: 2 },
    //     // { name: '模版', path: '/template', index: 3 },
    //     { name: "教学", path: "/teaching", index: 2 },
    //     { name: "知识仓", path: "/knowledge", index: 3 },
    //     { name: "素材库", path: "/material", index: 4 },
    //     { name: "政策库", path: "/policy", index: 5 }
    //     // { name: '工作台', path: '/workTable', index: 8 },
    //   ]

    //   return sysMenuList.length>0?allMenu.filter(v=>{
    //     if(sysMenuList.includes(v.path)){
    //       return v
    //     }
    //   }):allMenu;
    // }
  },
  watch: {
    "$route.path": {
      handler(val) {
        if (val) {
          if (this.$route.path === "/teaching") { // 判断教学路由
            this.navList = [];
          }else{
            this.getNavList()
          }
          // navbar 下划线动效
          const target = this.navList.find(v => v.path === val);
          let nowIndex = target?.index;
          // case 默认第一个
          if (val.match(/^\/case\//)) {
            nowIndex = 1;
          }
          this.showBottomLine = val !== "/teaching"
          switch (val) {
            case "/voucherManage":
            case "/billpool":
            case "/setaccount":
            case "/taccount":
            case "/accountpreview":
            case "/subledger":
            case "/subjectSummarySheet":
            case "/totalaccount":
            case "/balancesheet":
            case "/income":
            case "/flowssheet":
            case "/equityChangeSheet":
            case "/auxiliaryAccounting":
            case "/summary":
            case "/subject":
            case "/openningBalance":
            case "/currency":
            case "/formula":
            case "/rejectinvoice":
              nowIndex = 1;
              break;
            case "/knowledge":
            case "/knowledgeDetail":
              nowIndex = 3;
              break;
            case "/homework/create":
              nowIndex = 4;
              break;
            case "/personal":
              nowIndex = 99;
              break;
          }
          if (nowIndex) {
            this.nowIndex = nowIndex;
            if (this.nowIndex !== 2) this.setBarLeft(this.nowIndex);
          }
        }
      },
      immediate: true
    },
    loginDialogVisible: {
      handler(val) {
        if (!val) {
          this.authorizedAccount = ''
        }
      }
    },
  },
  created() {
    this.isSHowHeard = sessionStorage.getItem('isSHowHeard')
  },
  mounted() {
    this.dargfun()
    // setTimeout(()=>{
    //   this.userInfo = JSON.parse(sessionStorage.getItem('userInfo'))
    // },1000)
    const Index = JSON.parse(sessionStorage.getItem("activenames"));
    if (Index) {
      this.nowIndex = Index;
      this.setBarLeft(Index);
    }
    // this.HelpDocSearch();
    this.ContactGroupQRSearchBy();
    // 获取当前用户配置的权限
    // this.getPermissions();
    if (this.$route.path === "/teaching") { // 判断教学路由
      this.navList = [];
    }else{
      this.getNavList();// 更新顶部导航
    }
  },
  methods: {
    dargfun(){
            let that=this
            let clickTimeout;
            var box1 = document.getElementById("box1");
            box1.onmousedown = function(e){
              that.isMouseDown = true;
              // console.log(e,'事件类型')
                if (box1.setCapture) {
                    box1.setCapture();
                }
                //设置鼠标不论点哪个位置都不会偏移，点在元素哪个位置就是哪个位置
                e = e || window.e;
                var ol = e.clientX - box1.offsetLeft;
                var ot = e.clientY - box1.offsetTop;
                document.onmousemove = function(e){
                    e = e || window.e;
                    var left = e.clientX - ol;
                    var top = e.clientY - ot;
                    //记住要+'px'
                    box1.style.left = left + 'px';
                    box1.style.top = top + 'px';
                     box1.style.pointerEvents = 'none'
                };
                //设置的是document.onmouseup才不会出现元素重叠时，鼠标抬起失效
                document.onmouseup = function(){
                    box1.style.pointerEvents = null
                    document.onmousemove = null;
                    document.onmousedown = null;
                    document.onmouseup = null;
                    box1.releaseCapture && box1.releaseCapture();

                };
                // 解决拖拽过程中鼠标抬起仍旧处于拖拽状态问题
                document.ondragstart = function(ev) {
                    ev.preventDefault();
                };
                document.ondragend = function(ev) {
                    ev.preventDefault();
                };
                return false;
            }
    },
    handlecloseAiModal(){
      this.isAiOpen=false
    },
    handleAiControl(){
        this.isAiOpen=true
    },
    openUseIdeaClick() {//使用意见反馈跳转
      let routeUrl = this.$router.resolve({
        path: '/feedBackIndex'
      })
      window.open(routeUrl.href, "_blank");
    },
    //统计任务详情返回
    returnBack() {
      if (this.$route.query.needClose) {
        window.close();
      } else {
        const isTeacher = sessionStorage.getItem("isTeacher");
        let path = "";
        if (isTeacher) {
          path = `/teachingWork?id=${this.getCourseInfo.id}&courseName=${this.getCourseInfo.courseName}`;
        } else {
          path = "/studentTask";
        }
        this.$router.push({
          path
        });
    }
   },
    async authorizedLogin() {
      if (this.authorizedAccount) {
        window.localStorage.setItem("permissionAccount", this.authorizedAccount)
        this.loginDialogVisible = false
        window.open("/", "_blank")
      } else {
        this.$message('请输入用户名!')
      }
    },
    back() {
      if(this.$route.path == '/courseModal/courseDtails'){
        //示范课程
        if(this.$route.query.history){
          this.$router.go(-1)
        }else{
          this.$router.push('/teaching')
        }
      }else if(this.$route.path == '/prepareLessons'){
        this.$router.push({
          path:'/teachingWork',
          query:this.$route.query
        })
      }else{
        // this.$router.go(-1)
        if (history.length > 1) {
          this.$router.go(-1);
        } else {
          window.close();
        }
      }
    },
    //服务群
    async ContactGroupQRSearchBy() {
      let paramData = {
        params: {
          schoolId: this.userInfo.tenantId
        }
      };
      let { data, code } = await this.$api.ContactGroupQRSearchBy(paramData);
      if (code === 200) {
        this.QRcodeObj.isQRcode = false;
        if (data.length > 0) {
          this.QRcodeObj.isQRcode = true;
          this.QRcodeObj.QRcodeUrl = data[0].url;
        }
      }
    },
    //帮助中心
    async HelpDocSearch() {
      let paramData = {
        params: {
          sysType: 13,
          userType: this.userInfo.userType
        }
      };
      let { data, code } = await this.$api.HelpDocSearchBy(paramData);
      if (code === 200) {
        this.helpObj.isHelpCenter = false;
        if (data.length > 0) {
          this.helpObj.isHelpCenter = true;
          this.helpObj.helpUrl = data[0].url;
        }
      }
    },
    // 投屏授课
    handleScreenTeaching() {
      this.classroomDialogVisible = true;
    },
    backMain() {
      this.getNavList()
      this.isWorkBranch = false
      this.$router.replace("/teaching");
    },
    // 返回首页
    handleHome() {
      this.$router.push({
        path: "/"
      });
    },
    // 处理高亮位置
    handlePosition(val) {
      let leftPosition = -400;
      // console.log('this.tabIndex',this.tabIndex);
      switch (val) {
        case 1:
          leftPosition = 64;
          break;
        case 2:
          leftPosition = 174;
          break;
        case 3:
          leftPosition = 278;
          break;
        case 4:
          leftPosition = 388;
          break;
        case 5:
          leftPosition = 497;
          break;
      }
      return `${leftPosition}px`;
    },
    /**
     * @param {* 模板信息} module
     * @param {* 菜单下标} index
     */
    tabClick(module, index) {
      this.tabIndex = index;
      // console.log(module, "modulemodulemodule");
      if (module.path === "/teaching") {
        console.log('module', module);
        let routeUrl = this.$router.resolve({ path: module.path });
        // openUniqueTab(routeUrl.href,'teaching') // 跳转新界面
        let  userInfo = JSON.parse(sessionStorage.getItem('userInfo'));
        openOrFocusTab(routeUrl.href,userInfo)
        // window.open(routeUrl.href, true);
        // window.open(routeUrl.href);
      } else {
        this.nowIndex = module.index;
        this.$router.push(module.path);
        this.setBarLeft(module.index);
      }
      //  NextLoading.start();
      sessionStorage.removeItem("activenames");
    },
    // 设置底部bar的left 样式
    setBarLeft(index) {
      this.$nextTick(() => {
        let barDom = document.querySelector(".bottom-bar");
        if (barDom) {
          barDom.style.left = this.handlePosition(index);
          barDom.style.width = "54px";
          if (index === 1 || index === 2) {
            barDom.style.width = "40px";
          }
        }
      });
    },
    handleCommand(command) {
      if (command === "退出登录") {
        // 清空任务栏
        this.$store.commit('setGlobalTaskFlag',false);
        this.$store.commit("newHomeWorkInfo", {
          taskInfo: {
            details: []
          }
        });
        //
        resetRouters();
        this.$router.replace("/login");
        // 教师端
      } else if (command === "个人中心") {
        this.$router.push("/personal");
      } else if (command === "授权登录") {
        this.loginDialogVisible = true
      }
    },
    backCase() {
      if (this.$route.query.redirect) {
        this.$router.push(this.$route.query.redirect);
      } else {
        this.$router.push({
          path: "/case-list"
        });
      }
    },
    // 获取头部导航栏列
    getNavList() {
      let sysMenuList = [];
      // sysMenuList = this.sysMenuList?.map(v=> v.routeUrl)
      const allMenu = [
        // { name: '首页', path: '/home', index: 0 },
        { name: "场景", path: "/case-list", index: 1 },
        // { name: '场景', path: '/sence', index: 2 },
        // { name: '模版', path: '/template', index: 3 },
        { name: "教学", path: "/teaching", index: 2 },
        { name: "知识仓", path: "/knowledge", index: 3 },
        { name: "素材库", path: "/material", index: 4 },
        { name: "政策库", path: "/policy", index: 5 }
        // { name: '工作台', path: '/workTable', index: 8 },
      ]
      // 临时判断 如果授权了 按照授权的显示菜单 如果授权列表为空 则显示全部

      this.navList = sysMenuList.length > 0 ? allMenu.filter(v => {
        if (sysMenuList.includes(v.path)) {
          return v
        }
      }).map((v, i) => {
        return {
          ...v,
          index: (i + 1),
        }
      }) : allMenu;
    },
    // 获取用户配置的权限
    // getPermissions(){
    //   this.$api.GetPermissions({"sysType": 13}).then(res => {
    //     if(res.code == 200){
    //     //  将配置的模块菜单权限 存储到vuex
    //     this.$store.commit('setSysMenuList',res.data);
    //     this.getNavList();// 更新顶部导航
    //     }
    //   })
    // }
  }
};
</script>

<style lang="scss">
$color-1e: #1e56fb;
$color-f: #fff;
$color-l1: #30baff;
$color-l2: #0041fa;

.header {
  width: 100%;
  min-width: 1200px;
  height: 60px;
  background-image: url("../../assets/header/header-bg.png");
  background-size: 100% 60px;
  //background: linear-gradient(270deg, #5877fb 0%, #0041fa 100%);
  //box-shadow: 0px 0px 9px 0px rgba(0, 0, 0, 0.13);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &.hide-bottom-line {
    .bottom-bar {
      display: none;
    }
  }

  .header-l {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .headerIcon {
      display: flex;
      align-items: center;
      padding-left: 20px;
      cursor: pointer;

      .headerword {
        width: 190px;
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #fff;
        padding-left: 8px;
      }
    }

    .header-back {
      color: white;
      padding-left: 20px;
      font-size: 24px;

      >span {
        cursor: pointer;
      }
    }

    .headerNav {
      .nav {
        display: flex;
        position: relative;

        .item {
          cursor: pointer;
          text-align: center;
          min-width: 50px;
          margin-left: 60px;
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #fff;
          line-height: 52px;
          opacity: 0.85;

          &:hover {
            color: #fff;
            opacity: 1;
            font-weight: 600;
          }
        }

        .active {
          color: #fff;
          font-weight: 600;
          opacity: 1;
          // z-index: 10 // border-bottom: 3px solid #fff;
        }

        .bottom-bar {
          width: 55px;
          height: 7px;
          border-radius: 4px;
          background: #0039db;
          position: absolute;
          bottom: 8px;
          left: -400px;
          transition: left 0.5s;
          z-index: 9;
        }
      }
    }

    .work-branch {
      color: $color-f;
      cursor: pointer;
      border: 1px silver solid;
      padding: 2px 14px;
      border-radius: 10px;
      font-size: 14px;
      margin-left: 14px;
    }
  }

  .header-r {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 20px;

    .header-r-box-clickbox {
      margin-left: 20px;
      cursor: pointer;
      font-size: 14px;
      color: #FFFFFF;

      .el-dropdown {
        color: #FFFFFF;

        .el-dropdown-link {
          display: flex;
          align-items: center;
        }
      }

      .iconfont {
        font-size: 21px;
        margin-right: 10px;
      }
    }

    .change-front {
      width: 82px;
      height: 34px;
      background: linear-gradient(270deg, #30BAFF 0%, #0041FA 100%);
      border-radius: 4px;
      opacity: 0.83;
      display: inline-block;
      color: #fff;
      /* text-align: center; */
      padding-left: 36px;
      line-height: 34px;
      font-size: 14px;
      cursor: pointer;
      position: relative;

      i {
        font-size: 18px;
        position: absolute;
        top: 0px;
        left: 14px;
      }
    }

    .header-in {
      display: flex;
      align-items: center;

      img {
        width: 19px;
        height: 19px;
      }
    }

    .tag-icon-box {
      width: 115px;
      height: 30px;
      border-radius: 50px 50px 50px 50px;
      opacity: 1;
      border: 1px solid #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      // margin-right: 45px;
      cursor: pointer;
      color: #ffffff;

      &:hover {
        box-shadow: 0px 1px 3px 1px rgba(0, 39, 149, 0.47);
      }

      span {
        font-size: 14px;
      }

      .icon-back {
        font-size: 20px;
      }
    }

    .work-branch-skip {
      width: 34px;
      height: 34px;
      background: linear-gradient(270deg, $color-l1 0%, $color-l2 100%);
      border-radius: 4px;
      opacity: 0.83;
      color: $color-f;
      padding: 0;
      margin-right: 20px;
    }

    .header-tltle {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #fff;
      padding-right: 20px;
      cursor: pointer;
    }

    .headerhj {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #fff;
      padding-left: 6px;
      cursor: pointer;
    }




    .front-web {
      width: 82px;
      height: 34px;
      background: linear-gradient(270deg, $color-l1 0%, $color-l2 100%);
      border-radius: 4px;
      opacity: 0.83;
      color: $color-f;
      padding: 0;
      margin-right: 20px;
    }

    .console {
      width: 115px;
      height: 30px;
      border-radius: 50px 50px 50px 50px;
      background: transparent;
      border: 1px solid #ffffff;
      font-size: 14px;
      color: white;
      cursor: pointer;

      &:hover {
        background: #5884fd;
      }

      img {
        vertical-align: bottom;
        margin-right: 10px;
      }
    }

    .line {
      width: 1px;
      height: 60px;
      background: #ffffff;
      margin: 0 45px;
    }

    .help {
      color: white;
      font-size: 14px;
      cursor: pointer;
      margin-right: 10px;
      line-height: 60px;
      margin-left: 30px;

      >a {
        display: flex;
        align-items: center;

        >i {
          margin-right: 10px;
          font-size: 20px;
        }
      }
    }

    .linex {
      width: 1px;
      height: 16px;
      background: #ffffff;
      margin: 0 30px;
    }

    .item {
      img {
        vertical-align: top;
        cursor: pointer;
      }

      .el-badge__content {
        top: 2px;
        right: 10px;
        height: 14px;
        line-height: 14px;
        padding: 0 4px;
      }
    }

    .message {
      font-size: 14px;
      cursor: pointer;
      color: white;
      margin-left: 6px;
    }

    .user {
      // width: 120px;
      height: 28px;
      margin-left: 20px;

      .el-dropdown {
        color: white;

        .el-dropdown-link {
          display: flex;
          align-items: center;

          img {
            width: 28px;
            height: 28px;
            border-radius: 15px;
            margin-right: 10px;
          }
        }
      }
    }
  }

  .loginDialogVisible {
    .el-dialog {
      border-radius: 10px;
      width: 400px;

      .el-dialog__header {
        background-color: #F7F7F7;
        border-radius: 10px 10px 0 0;
      }

      .el-dialog__body {
        display: flex;
        align-items: center;

        >div {
          margin: 30px auto;

          .el-input {
            width: 200px;
            margin-right: 20px;
          }
        }
      }
    }
  }
  .feed-back-box {
    position: fixed;
    right: 17px;
    bottom: 126px;
    cursor: pointer;
    z-index: 99;
  }
}
.control-contentro {
  width: 64px;
  height: 64px;
  position: fixed;
  bottom: 72px;
  right: 72px;
  z-index: 2000;
  .control-boxro {
    position: relative;
    width: 64px;
    height: 64px;
    .control-item-active-box,
    .control-item-box {
      width: 64px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius:50%;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
    }
    .control-item-box {
      img {
        width: 64px;
        height: 64px;
      }
    }
  }
}
.service-dropdown {
  border-radius: 15px;

  .service-box {
    width: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 15px;
    margin-top: 5px;

    .class-p-sevrve {
      font-size: 14px;
      color: #010101;
      margin-bottom: 6px;
    }
  }
}
.drawer-history{
  .drawer-history-cont{
    //padding: 20px ;
    .history-title{
      padding: 20px;
      border-bottom: 1px solid;
    }
    .history-cont{

    }
  }
}
.feed-back-popper.el-tooltip__popper {
  width: 90px;
  text-align: center;
  height: 43px;
  line-height: 43px;
  font-weight: 400;
  font-size: 13px;
  color: #333333;
  padding: 0;
}
.feed-back-popper.el-tooltip__popper .popper__arrow {
  border-left-color: #fff!important;
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;
}
.feed-back-popper.el-tooltip__popper .popper__arrow:after {
  border-left-color: #fff!important;
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;
}
</style>
