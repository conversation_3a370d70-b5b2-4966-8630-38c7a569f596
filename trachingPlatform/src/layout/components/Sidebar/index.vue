<template>
  <div class="side-bar-menus">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        router
        class="el-menu-vertical-demo"
        :unique-opened="true"
        :default-openeds="openeds"
        :background-color="bg"
        :default-active="currentPath"
        :text-color="isToggleTheme?'#333333':'#7A93B2'"
        active-text-color="#FFFFFF"
        @select="handlePaths">
        <sidebarItem :menu-list="routes" :isShow="true" :module-list="moduleList" />
        <div style="height:100px;background-color: var(--theme_sideBar_bg_color)"></div>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import sidebarItem from "./sidebar-item";
import token from "@/utils/token";
import eventBus from "@/utils/eventBus";
export default {
  components: {
    sidebarItem,
  },
  // "/purchaseManage", "/accountingManage", "/marketManage"
  data() {
    return {
      currentPath: "/enterpriseInfo",
      openeds: [],
      routeObj: {},
      moduleList: [],
      bg: 'var(--theme_sideBar_bg_color)'
    };
  },
  computed: {
    routes() {
      const routes = this.$router.options.routes.filter((e) => {
        return e.path === "/main" && e.children;
      });
      return routes[0]?.children || [];
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },

    // variables() {
    //   return variables;
    // },
    isCollapse() {
      return !this.sidebar.opened;
    },
    ...mapGetters({
      isToggleTheme: ['isToggleTheme']
    })
  },
  watch: {
    $route: {
      handler(val) {
        this.currentPath = val.path;
      },
      deep: true,
    },
  },
  created() {
    // let belong=token.getCurrentStudentInfo()
    // if(belong){
    //   const hide =belong.taskBelonging==1? [
    //       '/financeManage',
    //       '/marketManage',
    //       '/allotManage',
    //       '/contractManage',
    //       // '/accountingManage',
    //       '/specificProcess'
    //     ]:[
    //       '/financeManage',
    //       //'/marketManage',
    //       '/allotManage',
    //       '/contractManage',
    //       // '/accountingManage',
    //       '/specificProcess']
    //     if(hide.includes(index)){
    //       this.$message({
    //         type:'warning',
    //         message:'暂无权限',
    //         offset:260
    //       })
    //     }
    // }
  },
  mounted() {
    eventBus.$on('onGetModule', () => {
      this.moduleList = token.getModuleCompleteInfo();
    });
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    handlePaths(index) {
      if (!index) return;
      // sessionStorage.removeItem("inshowflag");
      const belong = token.getCurrentStudentInfo();
      // const hide = belong.taskBelonging == 1 ? [
      //   "/financeManage",
      //   // "/financeManage2",
      //   "/marketManage",
      //   // "/allotManage",
      //   "/contractManage",
      //   "/specificProcess", //生产工艺单耗
      //   "/customerInfo", //客户信息
      //   "/productionLedger", //生产台账
      //   "/salesDataPool", //销售数据池
      //   "/productionAnalysis", //生产能力分析
      //   "/creditLedger", //信贷台账
      //   "/productionPlan", //生产计划
      //   '/bankCreditProgram',//年度授信计划
      // ]
      //   : [
      //     "/financeManage",
      //     "/financeManage2",
      //     "/allotManage",
      //     "/contractManage",
      //     "/specificProcess",
      //   ];
      // const commonHide = ["/financeManage2", "/allotManage"]
      // if (caseInfo) {
      //   caseInfo.competitionType = caseInfo.competitionType ? caseInfo.competitionType : 0;
      // }
      const moduleList = token.getModuleCompleteInfo();
      const hide = [
        // '/creditLedger', //信贷台账
        // '/bankCreditProgram', //年度授信计划
        '/financingBacklog', //筹资-业务结构
        // '/purchaseList',//物料档案
        // '/supplier',//供应商
        // '/stockDetails',//存货明细
        // '/purchasePool',//采购需求池
        '/purchaseBacklog', //采购-业务结构
        // '/productionPlan', //生产计划
        // '/productionAnalysis', //生产能力分析
        // '/specificProcess', //生产工艺单耗
        // '/productionLedger', //生产台账
        '/productBacklog', //生产-业务结构
        // '/customerInfo', //客户信息
        // '/salesDataPool', //销售数据池
        '/marketBacklog', //销售-业务结构
        '/financeBacklog', //投资-业务结构
        '/allotBacklog', //分配-业务结构
        '/costBacklog', //成本管控-业务结构
      ];
      this.getRouteInfo(this.routes, index);
      const i = moduleList.findIndex(val => val.business == this.routeObj.meta.moduleId);
      if (i < 0 && hide.includes(index) && belong.taskBelonging == 1) {
        this.$message({
          type: "warning",
          message: "暂无权限",
          offset: 260,
        });
      }
    },
    getRouteInfo(routes, path) {
      const len = routes.length;
      for (let i = 0; i < len; i++) {
        const item = routes[i];
        if (item.path == path) {
          this.routeObj = item;
          return true;
        }
        if (item.children && item.children.length > 0) {
          if (this.getRouteInfo(item.children, path)) {
            return true;
          }
        }
      }
    }
  },
};
</script>

<style lang="scss">
.side-bar-menus {
  .el-scrollbar {
    height: calc(100% - 0.2rem);
  }

  .el-menu {
    padding-top: 5px;
    box-sizing: border-box;
    border-right: none;
    // position: relative;
    // top: 5px;

  }

  .el-scrollbar__view,
  .el-menu {
    height: calc(100% - 0.2rem);
    // padding-bottom: 100px;
    // box-sizing: border-box;
  }

  .el-submenu__title,
  .el-menu-item {
    line-height: 50px;
    height: 50px;
    text-align: left;
    font-size: 14px;
  }
}
</style>
