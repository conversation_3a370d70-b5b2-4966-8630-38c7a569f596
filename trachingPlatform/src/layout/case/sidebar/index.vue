<template>
  <div :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        background-color="#202F84"
        text-color="#fff"
        active-text-color="#fff"
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in items"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      items: [
        {
          path: '/case/case1/page/page1',
          name: 'dynamicPage',
          meta: { title: '销售管理' },
          children: [
            {
              path: '/case/case1/page/page1',
              name: 'dynamicPage',
              meta: { title: '销售订单' },
            },
            {
              path: '/case/case1/page/page2',
              name: 'dynamicPage',
              meta: { title: '销售报价单' },
            },
          ],
        },
        {
          path: '/case/case1/form/form1',
          name: 'dynamicPage',
          meta: { title: 'form1' },
        },
        {
          path: '/case/case1/form/form2',
          name: 'dynamicPage',
          meta: { title: 'form2' },
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return {}
    },
    isCollapse() {
      return !this.sidebar.opened
    },
  },
}
</script>
