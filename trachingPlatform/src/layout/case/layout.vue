<template>
  <div :class="classObj" class="app-wrapper case-app">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <div class="navbar">
          <div class="left-content">
            <div class="title">深圳飞龙动力电池材料有限公司</div>
            <div class="date">{{ date }}</div>
          </div>
          <div class="right-content">
            <el-button @click="handleWorkspace" size="small">控制台</el-button>
          </div>
        </div>
        <tagsView v-if="needTagsView" />
      </div>
      <div class="content-wrapper">
        <caseApp />
      </div>
    </div>
  </div>
</template>

<script>
import caseApp from './caseApp'
import tagsView from './tagsView/index.vue'
import sidebar from './sidebar/index.vue'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'
import moment from 'moment'

export default {
  name: 'Layout',
  components: {
    caseApp,
    sidebar,
    tagsView,
  },
  data() {
    return {
      date: moment(new Date()).format('YYYY-MM-DD'),
    }
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile',
      }
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    },
    handleWorkspace() {
      this.$router.push({
        path: `/threeSence?type=third`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import './styles/mixin.scss';
@import './styles/variables.scss';
@import './styles/transition.scss';
@import './styles/sidebar.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
  .navbar {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    height: 46px;
    background: #ffffff;
    border-bottom: 1px solid #ccc;
    .left-content {
      display: flex;
      gap: 10px;
    }
  }
  .content-wrapper {
    background: #fff;
    border-radius: 4px;
    margin: 10px;
    margin-top: 90px;
    flex: 1;
  }
  .sidebar-container {
    background: url('../../assets/images/nav-bottom-bg.png') no-repeat bottom
      left;
    background-color: #202f84;
    ::v-deep .el-submenu__title i {
      color: #fff;
    }
    ::v-deep .el-scrollbar {
      height: 100%;
    }
    ::v-deep .el-menu {
      background-color: #202f84;
      color: #fff;
      .el-submenu__title, .el-menu {
        color: #fff !important;
      }
    }
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
