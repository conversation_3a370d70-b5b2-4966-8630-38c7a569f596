<template>
  <div class="question-card">
    <div class="card-header">
      <!-- <span>{{'序号: '+serialNumber}}</span> -->
      <div class="serial-number">{{ serialNumber + '.' }}</div>
      <!-- <div class="line"></div> -->
      <span>{{questionTypeLabel[question.questionType]}}</span>
      <div class="line"></div>
      <span>{{ getDifficultyDispatch(question.difficulty) }}</span>
      <div class="line" v-if="question.knowledgeTags.length>0 || question.skillTags.length>0"></div>
      <div class="btn" v-for="item in question.knowledgeTags" :key="item.knowledgeId">{{item.name}}</div>
      <div class="btn" v-for="item in question.skillTags" :key="item.knowledgeId">{{item.name}}</div>

    </div>
    <div class="question-content">
      <answerFormPreview 
        :type="questionData.type"
        :config="questionData.optionConfig"
        :answerValue="questionData.answer"
        :title="questionData.title"
        :desc="questionData.desc"
      ></answerFormPreview>
    </div>
    <div class="card-bottom">
      <div class="question-info">
        <span>{{ question.updateTime }} 更新</span>
        <div class="line"></div>
        <span>
          <span class="count">{{question.used}}</span>
          <span> 次组卷</span>
        </span>
      </div>
      <div class="functional-area">
        <!-- <el-button type="text">收藏</el-button>
        <div class="line"></div> -->
        <!-- <el-button type="text" @click="openDialog">纠错</el-button>
        <div class="line"></div> -->
        <el-button type="text" @click="showAnswerModal = true">答案详情</el-button>
        <!-- <div :class="[question.isJoinSchoolQuestionBank?'join-our-school-disable':'join-our-school']" v-if="isSchool" @click="JoinTheQuestion">{{question.isJoinSchoolQuestionBank?'已加入本校题库':'加入本校题库'}}</div> -->
        <!-- &&question.questionType!=50 -->
        <div v-if="taskSending" :class="['but',!question.selectAsTask?'addtask':'removetask']" @click="addTask($event)">{{!question.selectAsTask?'加入任务篮':'已加入任务栏'}}</div>
        <div v-if="isCourseDesign" :class="['but',!question.inserted?'addtask':'removetask']" @click="InsertQuestions($event)">{{!question.inserted?'选择':'已勾选'}}</div>
      </div>
    </div>

    <el-dialog
      title="纠错"
      :visible.sync="dialogVisible"
      width="40%"
      class="error-correction"
      > 
      <div>
        <el-input
          type="textarea"
          :autosize="{ minRows: 4,maxRows:4}"
          placeholder="请输入内容"
          v-model="textarea">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>

    <base-dialog :visible.sync="showAnswerModal"
                 :close-on-click-modal="false"
                 :no-footer="true"
                 class="answerDetailsDialog"
                 width="800px"
                 title="答案详情">
      <div style="padding: 0 15px; max-height: calc(100vh - 200px); overflow-y: auto;">
        <question-preview-content
            v-if="showAnswerModal"
            :isAnswer="true"
            :show-form="questionData.type==5||questionData.type==46?true:false"
            :data="previewQuestionData">
        </question-preview-content>
      </div>
    </base-dialog>

  </div>
</template>

<script>
import { questionTypeLabel,getDifficultyDispatch } from '@/components/base/question/util.js'
import {initApiQuestion} from "@/components/question/utils.js";
import answerFormPreview from '@/components/base/question/answer-form-preview.vue'
import eventBus from "@/utils/eventBus";
import { mapGetters } from 'vuex';

export default {
data() {
  return {
    questionTypeLabel:questionTypeLabel,
    getDifficultyDispatch:getDifficultyDispatch,
    questionData:'',
    previewQuestionData: {},
    butStatus:'add',
    dialogVisible:false,
    textarea:'',
    showAnswerModal:false
  };
},
props:{
  question:{
    type:Object,
    default:0
  },
  taskSending:{
    type:Boolean,
    default:false
  },
  isCourseDesign:{
    type:Boolean,
    default:false
  },
  courseId:{
    type:Number,
    default:0
  },
  serialNumber:{
    type:Number,
    default:0
  },
  changeTaskSelect:{
    type:Function,
    default:()=>{}
  },
  changeInsertStatus:{
    type:Function,
    default:()=>{}
  },
  isSchool:{
    type:Boolean,
    default:false
  },
},
components:{
  answerFormPreview,
  'question-preview-content': () => import('@/components/question/question-preview-content.vue')
},
computed:{
  ...mapGetters({
    insertTopicList: ['insertTopicList'],
  }),
},
mounted() {
  this.questionData = initApiQuestion(this.question)
  //console.log(this.questionData,'iro')
  this.previewQuestionData = initApiQuestion(this.question)
},
methods:{
  //加入任务篮
  addTask(e){
    if(!this.question.selectAsTask){
      let domPosition ={
          x:e.x,
          y:e.y,
        }
      eventBus.$emit('openAnimate',domPosition) //动画执行
      eventBus.$emit('tikuAddTask',this.question,true)
    }else{
      eventBus.$emit('tikuAddTask',this.question,false)
    }
    this.changeTaskSelect(this.question.id)
  },
  InsertQuestions(e){
    if(!this.question.inserted){
      let domPosition ={
        x:e.x,
        y:e.y,
      }
      eventBus.$emit('openAnimateInsert',domPosition) //动画执行
      eventBus.$emit('topicInsertCourse',this.question,true)
    }else{
      eventBus.$emit('topicInsertCourse',this.question,false)
    }
    
    this.changeInsertStatus(this.question.id)
  },
  //加入本校题库
  async JoinTheQuestion(){
    if(this.question.isJoinSchoolQuestionBank) return
    let data = {
      "questionId": this.question.id,
      "currentCourseId": this.$route.query.id
    }
    let { code } = await this.$api.JoinTheQuestion(data)
    if(code == 200){
      this.$message.success('操作成功!')
      this.question.isJoinSchoolQuestionBank = true
    }
  },
  //纠错
  async QuestionCorrection(){
    if(!this.textarea){
      this.$message('请输入纠错意见!')
      return
    }
    let data = {
      questionId: this.question.id,
      courseId: this.courseId,
      content: this.textarea,
    }
    let res = await this.$api.QuestionCorrection(data)
    if(res.code == 200){
      this.$message.success('操作成功!')
      this.dialogVisible = false
    }
  },
  openDialog(){
    this.dialogVisible = true
  },
  submit(){
    this.QuestionCorrection()
  }
},
};
</script>
<style lang='scss' scoped>
.question-card{
  width: 100%;
  box-sizing: border-box;
  padding: 0 20px;
  background-color: #FBFBFB;
  border-radius: 3px;
  margin-bottom: 20px;
  border: 1px solid #DDE2E9;
  &:hover{
    border: 1px solid var(--theme_primary_color);
    //box-shadow: 0px 3px 6px 1px rgba(0,0,0,0.16);
  }
  ::v-deep .el-dialog{
    .el-dialog__footer{
      padding-top: 20px;
    }
  }
  .card-header{
    width: 100%;
    min-height: 44px;
    display: flex;
    align-items: center;
    overflow: hidden;
    flex-wrap: wrap;
    .serial-number{
      width: 20px;
      height: 20px;
      background: url('../../../../assets/question/serial-number.png')no-repeat center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: white;
      margin-right: 14px;
    }
    .line{
      width: 1px;
      height: 12px;
      background: #B1B1B1;
      margin: 0 15px;
    }
    >span{
      font-size: 13px;
      color: #747474;
    }
    .btn{
      height: 20px;
      background: #FFFFFF;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #EBEBEB;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10px;
      font-size: 12px;
      color: #787D83;
      margin: 5px 0;
      margin-right: 20px;
    }
  }
  .question-content{
    // height: 200px;
    border-top: 1px dashed #DDDDDD;
    border-bottom: 1px dashed #DDDDDD;
    padding: 12px 0;
    :deep(.answer-form-preview) {
      .question-title {
        font-weight: 500;
        color: #333;
      }
      //.question-desc {
      //  display: none;
      //}
      .options-preview {
        display: grid;
        grid-template-columns: 50% 50%;
        grid-gap: 10px;
        color: #787D83;
        font-size: 14px;
        .options-list-item {
          margin-bottom: 0;
          &::before {
            content: "";
            width: 12px;
            height: 12px;
            min-width: 12px;
            border: 1px solid #E2E2E2;
            margin-right: 10px;
            background-color: #fff;
            margin-top: 3px;
          }
        }
        .option-select-tag {
          border: none;
          width: auto;
          margin-right: 5px;
          &:after {
            content: ".";
          }
          &:hover {
            background-color: rgba(255,255,255,0);
            color: #787D83;
          }
        }
      }
    }
  }

  .card-bottom{
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .question-info{
      font-size: 12px;
      color: #B1B1B1;
      display: flex;
      .line{
        width: 1px;
        height: 12px;
        background: #B1B1B1;
        margin:  0 15px;
      }
      .count{
        color: var(--theme_primary_color);
      }
    }
    .functional-area{
      display: flex;
      align-items: center;
      .join-our-school{
        padding:  0 10px;
        height: 30px;
        color: #2B66FA;
        font-size: 12px;
        background: #FFFFFF;
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #2B66FA;
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        margin-left: 20px;
      }
      .join-our-school-disable{
        padding:  0 10px;
        height: 30px;
        color: white;
        font-size: 12px;
        background: #B1B1B1;
        border-radius: 3px 3px 3px 3px;
        border: 1px solid #B1B1B1;
        cursor: pointer;
        text-align: center;
        line-height: 30px;
        margin-left: 20px;
      }
      .line{
        width: 1px;
        height: 12px;
        background: #B1B1B1;
        margin:  0 15px;
      }
      .but{
        width: 93px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        font-size: 12px;
        border-radius: 3px;
        margin-left: 20px;
        cursor: pointer;
      }
      .addtask{
        background: #2B66FA;
        color: white;
      }
      .removetask{
        background: #b7b7b7;
        color: #ffffff;
      }
    }
  }
  .answerDetailsDialog{
    .base-dialog{
      min-height: 100px !important;
      .answerDetails{
        
      }
    }
  }
}
</style>