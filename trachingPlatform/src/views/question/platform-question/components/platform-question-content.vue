<template>
  <div class="platform-question-content">
    <zdDefaultPage size="large" msg="暂无数据" type="noData" v-if="(taskSending || isCourseDesign) && coursesList.length == 0"></zdDefaultPage>
    <div class="content" v-else>
      <div class="left-tree">
        <el-dropdown @command="changeCourse" trigger="click">
          <div class="course"> <span class="course-name">{{currentCourse.classificationName}}</span> <span class="el-icon-arrow-down"></span> </div>
          <el-dropdown-menu slot="dropdown" style="max-height: calc(100vh - 250px); overflow-y: auto;">
            <el-dropdown-item v-for="item in coursesList" :command="{id:item.id}" :key="item.id" class="dropdown-itemss">
              <div class="ur-li" v-if="!item.isSelect">
                <span>{{ item.classificationName }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div class="tree">
          <category :show-action="false" :show-search="false" :courseId="currentCourse.id" @nodeClick="treeNodeClick"></category>
        </div>
      </div>
      <div class="right-question">
        <div class="screen">
          <div class="top">
            <span>题目 :</span>
            <div class="tables">
              <div v-for="(topic,key) in questionTypeLabel" :class="{'topic':true,'active':search.topicType == key}" :key="key" @click="search.topicType = key">{{ topic }}</div>
            </div>
          </div>
          <div class="bottom">
            <span>难度 :</span>
            <div class="tables">
              <div v-for="difficulty in difficultyOptions" :class="{'topic':true,'active':search.difficulty == difficulty.value}" :key="difficulty.value" @click="search.difficulty = difficulty.value">{{ difficulty.label }}</div>        
            </div>
          </div>
        </div>
        <div class="question">
          <div class="search">
            <el-input
              placeholder="搜索"
              clearable
              @clear="getQuestionList"
              v-model="searchVal">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="searchQuestionList"></i>
            </el-input>
            <div>
              <div class="addBut" @click="addQuestion" v-if="administrator">
                <i class="el-icon-plus"></i>
                <span style="margin-right: 0;">新增/管理试题</span>
              </div>
              <span>共计 <span>{{pageData.total}}</span> 道题</span>
            </div>
          </div>
          <!-- v-infinite-scroll="load" -->
          <div class="ti-list" v-if="questionList.length>0"  infinite-scroll-distance="50px">
            <questionCard  v-for="(item, index) in questionList" :serialNumber="index+1" :key="item.id" :question="item" :taskSending="taskSending" :isCourseDesign="isCourseDesign" :courseId="currentCourse.id" :changeTaskSelect="changeTaskSelect" :changeInsertStatus="changeInsertStatus" :isSchool="isSchool"></questionCard>
            <ZdPagination 
              style="margin-top:20px"
              :paginationConfig="pageData"
              @handleCurrentChange="handleCurrentChange" 
             >
            </ZdPagination>
          </div>
          <div class="ti-list" v-else>
            <zdDefaultPage  
            size="large" 
            msg="暂无数据" 
            type="noData"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import category from '@/components/question/category.vue'
import zdDefaultPage from "@/components/zd-default-page/index.vue";
import ZdPagination from "@/components/zd-pagination/index.vue";
import questionCard from './question-card.vue'
import { questionTypeLabel, difficultyOptions } from '@/components/base/question/util.js';
import eventBus from "@/utils/eventBus";
import { mapGetters } from "vuex";


export default {
  components:{
    category,
    questionCard,
    zdDefaultPage,
    ZdPagination,
  },
  props:{
    administrator:{
      type:Boolean,
      default:false
    },
    taskSending:{
      type:Boolean,
      default:false
    },
    isCourseDesign:{
      type:Boolean,
      default:false
    },
    isSchool:{
      type:Boolean,
      default:false
    },
  },
  data() {
    return {
      questionTypeLabel:{...questionTypeLabel,0:'全部'},
      difficultyOptions: [{ label: '全部', value : -1 },...difficultyOptions],
      search:{
        topicType:0,
        difficulty:-1
      },
      searchVal:'',
      questionList:[],
      currentCourse:{},
      coursesList:[],
      pageData:{ // 分页对象
        pageSize:10,
        pageNumber:1,
        total:0,
        currentPage:1,
        layout:'prev, pager, next, total, jumper'
      },
      serialNumber:0,
      // pageNation:{ // 分页对象
      //   currentPage:1,
      //   pageSize:4,
      //   total:0,
      //   layout:'prev, pager, next, total, jumper'
      // },
    };
  },
  computed:{
    ...mapGetters({
      newHomeWorkInfo: ["newHomeWorkInfo"],
      insertTopicList: ["insertTopicList"],
    }),
  },
  watch:{
    search:{
      handler(newVal){
        // debugger
        // console.log(newVal);
        this.pageData.pageNumber = 1
        this.questionList = []
        this.getQuestionList()
      },
      deep:true
    },
  },
mounted() {
  this.GetCoursesList()
  eventBus.$off("setTaskStatus"); // 单个任务移除 状态同步
  eventBus.$on("setTaskStatus", task => {
    // console.log('99999',task);
    this.questionList.forEach(e=>{
      if(e.id == task.key.split('_')[1]){
        e.selectAsTask = false
      }
    })
  }); 
  // 单个任务移除 状态同步
  eventBus.$on('cancelInsertion',(val)=>{
    console.log('单个任务移除状态同步',val,this.insertTopicList);
    // let insertTopicList = this.insertTopicList
    // const index = insertTopicList.findIndex(e=>e.id == val)
    // insertTopicList.splice(index,1)
    // this.$store.commit('insertTopicList',insertTopicList)
    this.changeInsertStatus(val)
  })

},
methods:{
  back(){
    this.$router.back()
  },
  handleCurrentChange(val){ // 切换页码
    this.pageData.currentPage = val;
    this.pageData.pageNumber = val;
    this.getQuestionList()
     // 切换分页后将滚动条置为顶部
     this.$nextTick(() => {
        const tiListElement = this.$el.querySelector('.ti-list');
        if (tiListElement) {
          tiListElement.scrollTop = 0;
        }
      });
  },
  //滚动分页
  async load(){
    let doc = document.querySelector('.ti-list')
    // console.log('doc',doc.scrollHeight,doc.clientHeight);
    if(doc.scrollHeight>doc.clientHeight){
      // alert('触发')
      this.pageData.pageNumber = this.pageData.pageNumber + 1
      if(this.currentCourse.id){
        await this.getQuestionList(true)
      }
    }
  },
  searchQuestionList(){
    this.pageData.pageNumber = 1
    this.pageData.currentPage = 1
    this.getQuestionList()
  },
  addQuestion(){
    if(this.currentCourse){
      this.$router.push({path:'/platform-question-manage',query:{id:this.currentCourse.id,courseName:this.currentCourse.classificationName}})
    }else{
      this.$router.push({path:'/platform-question-manage',query:{}})
    }
  },
  //获取课程列表
  async GetCoursesList(){
    let data = {
      "params": {
        isDelete:false
      },
    }
    let res = await this.$api.GetCourses(data)
    if(res.code == 200){
      if(res.data.length > 0) {
        res.data.forEach(e => {
          e.isSelect = false
        });
        this.currentCourse = res.data[0]
        res.data[0].isSelect = true
      }
      this.coursesList = res.data
      this.getQuestionList()
      // sessionStorage.setItem('tikuCurCourseInfo',JSON.stringify(this.tikuCurCourseInfo))
    }
  },
  changeCourse(command){
    // this.GetCoursesList(command.id)
    //课程列表不展示当前课程
    this.coursesList.forEach(e=>{
      if(e.id == command.id){
        e.isSelect = true
        this.currentCourse = e
      }else{
        e.isSelect = false
      }
    })
    this.search = {
      topicType:0,
      difficulty:-1
    },
    this.searchVal = ''
    this.getQuestionList()
  },
  //获取题目列表
  async getQuestionList(pageTurning){
    //滚动翻页 加载完所有数据后 不再请求
    // console.log(this.pageData.pageNumber,Math.ceil(this.pageData.total/this.pageData.pageSize));
    if(this.pageData.pageNumber > Math.ceil(this.pageData.total/this.pageData.pageSize)&&this.pageData.pageNumber!=1 && this.pageData.total>0) return
    let data = {
      params:{
        title:this.searchVal,
        courseId:this.currentCourse.id || 0,
        questionCategoryId:this.questionCategoryId,
        status:1,
        parentId: 0, // 查询非子题目
      },
      pageIndex:this.pageData.pageNumber,
      pageSize:this.pageData.pageSize,
      orderBy:'publishTime desc'
    }
    if(this.search.topicType != 0){
      data.params.questionType = this.search.topicType
    }
    if(this.search.difficulty != -1){
      data.params.difficulty = this.search.difficulty
    }
    // if(this.searchVal){
    //   alert('44')
    //   this.pageData.pageNumber = 1
    //   console.log(this.pageData.pageNumber,data);
    //   data.params.title = this.searchVal
    // }
    let res = await this.$api.GetQuestionList(data)
    if(res.code == 200){
      //判断题目是否在任务栏中
      res.data.forEach(e=>{
        e.selectAsTask = false
        this.newHomeWorkInfo.taskInfo.details.forEach(item=>{
          if(item.key == `question_${e.id}`){
            e.selectAsTask = true
          }
        })
      })
      //课程设计插入试题,判断是否已加入
      if(this.isCourseDesign){
        res.data.forEach(e=>{
          e.inserted = false
          this.insertTopicList.forEach(item=>{
            if(item.id == e.id){
              e.inserted = true
            }
          })
        })
      }
      if(pageTurning){
        // console.log('66666666666',pageTurning);
        // alert('滚动',pageTurning)
        // this.questionList = [...this.questionList,...res.data]
        this.questionList = res.data
      }else{
        // alert('替换')
        this.questionList = res.data
      }
      
      this.pageData.total = res.recordCount
    }
  },
  //改变题目选中状态
  changeTaskSelect(id){
    this.questionList.forEach(e=>{
      if(e.id == id){
        e.selectAsTask = !e.selectAsTask
      }
    })
  },
  //插入题目改变题目选中状态
  changeInsertStatus(id){
    this.questionList.forEach(e=>{
      if(e.id == id){
        e.inserted = !e.inserted 
      }
    })
  },
  getTreeIds(node) {
    const res = []
    res.push(node.id)
    if (node.children) {
      node.children.forEach((ch) => {
        res.push(...this.getTreeIds(ch))
      })
    }
    return res
  },
  treeNodeClick(val){
    // this.questionCategoryId = val.id
    if(val.children && val.children.length>0){
      this.questionCategoryId = this.getTreeIds(val)
    }else{
      this.questionCategoryId = val.id
    }
    this.pageData.pageNumber = 1
    this.pageData.currentPage  = 1
    this.getQuestionList()

    // sessionStorage.setItem('questionCategoryInfo',JSON.stringify(val))
  },
},
};
</script>

<style lang='scss' scoped>
.dropdown-itemss{
  width: 258px;
  padding:0;
  .ur-li{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding: 0 20px;
    >span{
      display: block;
      max-width: 80%;
      overflow:hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &:hover{
      background:#D3E3FD;
    }
  }
}
.platform-question-content{
  width: 100%;
  height: 100%;
  .content{
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .left-tree{
        // max-height: calc(100vh - 104px);
        height: 100%;
        width: 258px;
        background: white;
        display: flex;
        flex-direction: column;
        .tree{
          padding: 10px;
          flex: 1;
          overflow-y: auto;
        }
        .course{
          width: 258px;
          height: 40px;
          background: #2B66FA;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          color: white;
          cursor: pointer;
          .el-icon-arrow-down{
            margin-left: 5px;
          }
          .course-name{
            display: inline-block;
            max-width: 200px;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          } 
        }
      }
      .right-question{
        width: calc(100% - 278px);
        display: flex;
        flex-direction: column;
        height: 100%;
        .screen{
          // height: 82px;
          background: #FFFFFF;
          margin-bottom: 20px;
          box-sizing: border-box;
          padding: 0 24px;
          .top,.bottom{
            // height: 40px;
            display: flex;
            align-items: flex-start;
            font-size: 14px;
            color: #333333;
            padding: 6px 0;
            >span{
              display: block;
              margin-right: 15px;
              margin-top: 10px;
            }
            .topic{
              padding: 5px 10px;
              margin: 4px 0;
              margin-right: 40px;
              cursor:pointer;
              border: 1px solid #FFFFFF;
            }
            .active{
              border: 1px solid #2B66FA;
              color: #2B66FA;
              border-radius: 3px;
            }
            .tables{
              flex: 1;
              display: flex;
              flex-wrap: wrap;
            }
          }
          .top{
            border-bottom: 1px dashed #DDDDDD;
            position: relative;
            // .topic{
            //   margin-right: 30px;
            // }
          }
        }
        .question{
          flex: 1;
          min-height: 0;
          background: white;
          box-sizing: border-box;
          // padding: 0 24px;
          display: flex;
          flex-direction: column;
          .search{
            height: 60px;
            padding: 0 24px;
            border-bottom: 1px dashed #DDDDDD;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .el-input{
              width: 184px;
              ::v-deep .el-input__inner{
                padding-right: 52px;
                background: #F6F8FA;
              }
              .el-input__icon{
                cursor: pointer;
              }
            }
            >div{
              display: flex;
              align-items: center;
              .addBut{
                width: 133px;
                height: 24px;
                border-radius: 3px 3px 3px 3px;
                border: 1px solid #2B66FA;
                font-size: 14px;
                color: #2B66FA;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                margin-right: 30px
              }
              >span{
                font-size: 14px;
                color: #333333;
                >span{
                  color: #FF0000;
                }
              }
            }
          }
          .ti-list{
            flex: 1;
            min-height: 0;
            padding: 0 24px;
            box-sizing: border-box;
            padding-top: 20px;
            overflow-y: auto;
            margin-bottom: 20px;
          }
        }
      }

    }
}
</style>