<!--
 * @Author: wujiawei <EMAIL>
 * @Date: 2024-09-03 10:32:02
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2025-02-08 08:53:44
 * @FilePath: \fusion_front\src\views\question\platform-question\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="platform-question">
    <div class="head">
      <div class="back" @click="back">
        <span class="el-icon-arrow-left"></span>
        <span>返回</span>
      </div>
      <span class="title">平台题库</span>
      <!-- <span class="el-icon-close" @click="back"></span> -->
    </div>
    <div class="main">
      <platformQuestionContent :administrator="true" ></platformQuestionContent>
    </div>
  </div>
</template>

<script>
import platformQuestionContent from './components/platform-question-content.vue'

export default {
  components:{
    platformQuestionContent
  },
  data() {
    return {
    };
  },
  methods:{
    back() {
      this.$router.go(-1);
    }
  }

};
</script>
<style lang='scss' scoped>
.platform-question{
  width: 100vw;
  height: 100vh;
  background: #F6F8FA;
  .head{
    width: 100%;
    height: 50px;
    background: #D3E3FD;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding: 0 40px;
    position: relative;
    color: #2B66FA;
    .title{
      font-size: 16px;
      font-weight: bold;
    }
    .back,.el-icon-close{
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .back{
      position: absolute;
      left: 40px;
    }
    
  }
  .main{
    //width: calc(100vw - 720px);
    width: 1200px;
    height: calc(100vh - 50px);
    margin: auto;
    padding: 20px 0;
    // .breadcrumb{
    //   height: 44px;
    //   line-height: 44px;
    //   font-size: 14px;
    //   span{
    //     &:nth-child(1){
    //       color: #787D83;
    //     }
    //     &:nth-child(2){
    //       color: #2B66FA;
    //     }
    //   }
    // }
    // .content{
    //   width: 100%;
    //   height: 100%;
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: flex-start;
    //   .left-tree{
    //     max-height: calc(100vh - 104px);
    //     width: 258px;
    //     background: white;
    //     .tree{
    //       padding: 10px;
    //     }
    //     .course{
    //       width: 258px;
    //       height: 40px;
    //       background: #2B66FA;
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       font-size: 14px;
    //       color: white;
    //       cursor: pointer;
    //       .el-icon-arrow-down{
    //         margin-left: 5px;
    //       }
    //       .course-name{
    //         display: inline-block;
    //         max-width: 200px;
    //         overflow:hidden;
    //         white-space: nowrap;
    //         text-overflow: ellipsis;
    //       } 
    //     }
    //   }
    //   .right-question{
    //     width: calc(100% - 278px);
    //     display: flex;
    //     flex-direction: column;
    //     height: 100%;
    //     .screen{
    //       height: 82px;
    //       background: #FFFFFF;
    //       margin-bottom: 20px;
    //       box-sizing: border-box;
    //       padding: 5px 24px;
    //       .top,.bottom{
    //         height: 40px;
    //         display: flex;
    //         align-items: center;
    //         font-size: 14px;
    //         color: #333333;
    //         span{
    //           margin-right: 15px;
    //         }
    //         .topic{
    //           padding: 5px 10px;
    //           margin-right: 40px;
    //           cursor:pointer;
    //           border: 1px solid #FFFFFF;
    //         }
    //         .active{
    //           border: 1px solid #2B66FA;
    //           color: #2B66FA;
    //           border-radius: 3px;
    //         }
    //       }
    //       .top{
    //         border-bottom: 1px dashed #DDDDDD;
    //         position: relative;
    //         .addBut{
    //           width: 133px;
    //           height: 24px;
    //           border-radius: 3px 3px 3px 3px;
    //           border: 1px solid #2B66FA;
    //           font-size: 14px;
    //           color: #2B66FA;
    //           display: flex;
    //           justify-content: center;
    //           align-items: center;
    //           cursor: pointer;
    //           position: absolute;
    //           right: 0;
    //         }
    //         // .topic{
    //         //   margin-right: 30px;
    //         // }
    //       }
    //     }
    //     .question{
    //       flex: 1;
    //       min-height: 0;
    //       background: white;
    //       box-sizing: border-box;
    //       // padding: 0 24px;
    //       display: flex;
    //       flex-direction: column;
    //       .search{
    //         height: 60px;
    //         padding: 0 24px;
    //         border-bottom: 1px dashed #DDDDDD;
    //         display: flex;
    //         justify-content: space-between;
    //         align-items: center;
    //         .el-input{
    //           width: 184px;
    //           ::v-deep .el-input__inner{
    //             background: #F6F8FA;
    //           }
    //           .el-input__icon{
    //             cursor: pointer;
    //           }
    //         }
    //         >span{
    //           font-size: 14px;
    //           color: #333333;
    //           >span{
    //             color: #FF0000;
    //           }
    //         }
    //       }
    //       .ti-list{
    //         flex: 1;
    //         min-height: 0;
    //         padding: 0 24px;
    //         box-sizing: border-box;
    //         padding-top: 20px;
    //         overflow-y: auto;
    //       }
    //     }
    //   }

    // }
  }
}

</style>