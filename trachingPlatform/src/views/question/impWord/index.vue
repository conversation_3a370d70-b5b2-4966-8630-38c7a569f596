<template>
  <div>
    <div ref="actionsBox" class="flex-btn-pageTop">
      <FlexExportButton :title="'下载模板'" :fileName="'导入试题模板'" :url="downWord_url" :downLoadFn="downLoadFile" />
      <!-- <input type="file" @click.native="importTemplate">模板导入</input>
      <button @click.native="exampleVisible = true" type="plain">格式示例</button>
      <button @click.native="clearText" type="plain">清空文本</button> -->
    </div>
    <input style="display" ref="file" type="file" @change="handleFileChange" accept=".docx" />
    <div class="quiz-generator">
      <el-input 
        type="textarea" 
        class="pub-area custom-textarea" 
        v-model="inputText" 
        placeholder="在这里输入试题">
      </el-input>
      <div class="pub-area">
        <div class="right-area">
          <div class="topic-item content" v-for="(item, index) in questionList" :key="index">
            <question-preview-content v-if="true" :showSort="true" :sort="(index + 1)" :data="item">
            </question-preview-content>
          </div>
        </div>
        <div style="text-align:center; margin-top:2px;"><flex-button style="width: 200px" :loading="btnLoading"
            @click.native="importHandle">导入</flex-button></div>
      </div>
    </div>
  </div>
</template>

<script>
import mammoth from 'mammoth';
import { createQuestion, handleQuestionTitle, handleQuestionDetails, parseInput } from '@/utils/questionParser.js';

export default {
  data() {
    return {
      inputText: '',
      dtoList: [], // 生成的试题数组
      quesAnswerList: [],
      btnLoading: false,
      question: {
        type: 1,
        title: "这是到底是什么字段",
        desc: "<p>我国的火警报警电话是（C）</p>",
        optionConfig: {
          "options": [
            {
              "label": "110",
              "value": "A",
              "isAnswer": false
            },
            {
              "label": "120",
              "value": "B",
              "isAnswer": false
            },
            {
              "label": "119",
              "value": "C",
              "isAnswer": true
            },
            {
              "label": "911",
              "value": "D",
              "isAnswer": false
            }
          ]
        },
        answerDetail: "<p> 我国的火警报警电话是119。</p>",
        knowledge: [
          {
            "knowledgeId": 0,
            "type": 0,
            "name": "123"
          }
        ],
        skill: [],
        difficulty: 4,
        sort: 1,
        cateId: 0,
        answer: "{\"longArray\":[\"C\"]}"
      },
      questionList: [

      ],
      optionItem:
      {
        label: "",
        value: "",
        isAnswer: false
      }
      ,
      options: [

      ],
      parsedText: {},
    }
  },
  name: 'preview-question',
  components: {
    'question-preview-content': () => import('@/components/question/question-preview-content.vue'),
  },
  methods: {
    handleFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        // 创建FileReader来读取文件
        const reader = new FileReader();
        // 文件读取完成后触发的事件
        reader.onload = (event) => {
          const arrayBuffer = event.target.result;

          // 提取原始文本
          // mammoth.extractRawText({ arrayBuffer: arrayBuffer }).then(rawTextResult => {
          //   const rawText = rawTextResult.value;

          //   // 转换为 HTML
          //   mammoth.convertToHtml({ arrayBuffer: arrayBuffer }).then(htmlResult => {
          //     let html = htmlResult.value;
          //     // 从原始文本中提取换行信息并添加到 HTML 中
          //     const lines = rawText.split(/\r?\n/);
          //     lines.forEach((line, index) => {
          //       if (line.trim() === '') {
          //         // 在 HTML 中添加换行符 \n
          //         html = html.replace(lines[index + 1] || '', '\n' + (lines[index + 1] || ''));
          //       }
          //     });

          //     // 赋值操作，将 HTML 内容保存在 data 属性中
          //     this.inputText = html.replace(/^\s*[\r\n]/gm, '');
          //   }).catch(error => {
          //     console.error('HTML conversion error: ', error);
          //   });
          // }).catch(error => {
          //   console.error('Raw text extraction error: ', error);
          // });
          // Mammoth.js 用于转换arrayBuffer到文本
          mammoth.extractRawText({ arrayBuffer: arrayBuffer }).then(result => {
            this.inputText = result.value.replace(/^\s*[\r\n]/gm, ''); // 赋值操作，将文本内容保存在data属性中
          }).catch(error => {
            console.error('File reading error: ', error);
          });
        };
        // 读取文件为ArrayBuffer
        reader.readAsArrayBuffer(file);
      }
    },
    importTemplate() {
      this.$refs.file.value = ''
      this.$refs.file.click();
    },
    generateQuestions() {
      this.questionList = [];
      let parsedText = parseInput(this.inputText);
      // debugger
      this.parsedText = parsedText;
      console.log(parsedText);
      this.quesAnswerList.length = 0;
      this.dtoList = parsedText.map((item, ind) => {
        let question = createQuestion(ind);
        const longArray = [];
        item.forEach((it, idx) => {
          if (idx === 0) {
            handleQuestionTitle(it, question, longArray);
          } else {
            handleQuestionDetails(it, question, longArray);
          }
        });
        console.log("q-", question);
        if (question.type) {
          this.questionList.push(question);
          return question;
        } else {
          return null;
        }
      }).filter(it => it !== null && it !== undefined && it);

    },
    importHandle() {
      if (this.dtoList.length === 0) return this.$message.warning("请先在左边输入试题")
      let isOk = true
      // 答案必填校验
      for (let i = 0; i < this.dtoList.length; i++) {
        if (this.dtoList[i].questionType === 1 && this.quesAnswerList[i].answer === "") {
          isOk = false
          return this.$message.error(`请填写第${i + 1}题选择题的答案`)
        } else if (this.dtoList[i].questionType === 2 && this.quesAnswerList[i].answer.length === 0) {
          isOk = false
          return this.$message.error(`请填写第${i + 1}题选择题的答案`)
        } else if (this.dtoList[i].questionType === 4 && this.quesAnswerList[i].answer === "") {
          isOk = false
          return this.$message.error(`请填写第${i + 1}题判断题的答案`)
        } else if (this.dtoList[i].questionType === 3 && this.quesAnswerList[i].answer === "") {
          isOk = false
          return this.$message.error(`请填写第${i + 1}题填空题的答案`)
        } else if (this.dtoList[i].questionType === 5 && this.quesAnswerList[i].answer === "") {
          isOk = false
          return this.$message.error(`请填写第${i + 1}题简答题的答案`)
        }
      }

      for (let i = 0; i < this.dtoList.length; i++) {
        if (this.dtoList[i].questionType === 1 || this.dtoList[i].questionType === 2) {
          // 选择题判断答案是否有重复的，如有两个选项C
          const answerHasDuplicates = this.hasDuplicateProperty(this.dtoList[i].answerList, "answer")
          if (answerHasDuplicates) {
            isOk = false
            return this.$message.warning(`第${i + 1}题选择题有重复的答案，请检查`)
          }
          // 选择题判断选项是否有重复的，如有两个选项B
          const hasDuplicates = this.hasDuplicateProperty(this.dtoList[i].questionOptionList, "questionOption")
          if (hasDuplicates) {
            isOk = false
            return this.$message.warning(`第${i + 1}题选择题选项中有重复的选项，请检查`)
          }

          const tempOptionList = this.dtoList[i].questionOptionList.map(it => it.questionOption)
          // 选择题判断选项是否按照顺序排列并且以A开始，如选项ABCDE 但是填写了EDCBA、CD、ACD等
          if (!this.isValidSequence(tempOptionList)) {
            isOk = false
            return this.$message.warning(`第${i + 1}题选择题选项没按照字母顺序排列，请检查`)
          }

          for (let j = 0; j < this.dtoList[i].answerList.length; j++) {
            // 选择题判断答案是否不在选项范围，如选项只有ABCD 但是填写了F为正确答案
            const hasValue = this.hasSpecificValue(this.dtoList[i].questionOptionList, "questionOption", this.dtoList[i].answer);
            if (!hasValue) {
              isOk = false
              return this.$message.warning(`第${i + 1}题选择题答案有不在选项范围的，请检查`)
            }
          }
          if (!isOk) return
        }
      }
      if (!isOk) return
      this.btnLoading = true

    },

    // 判断答案选项是否按照顺序排列
    isValidSequence(arr) {
      const validSequence = ['A', 'B', 'C', 'D', 'E'];
      // 检查数组是否为空或第一个元素不是'A'
      if (arr.length === 0 || arr[0] !== 'A') {
        return false;
      }
      // 遍历数组并检查每个元素是否在有效序列中
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== validSequence[i]) {
          return false; // 如果不匹配，则返回false
        }
      }

      return true; // 如果所有条件都满足，返回true
    },

    // 检查数组中对象某个属性的值是否有重复的
    hasDuplicateProperty(arr, propertyName) {
      let seenValues = new Set();
      for (let i = 0; i < arr.length; i++) {
        if (seenValues.has(arr[i][propertyName])) {
          return true; // 发现重复
        }
        seenValues.add(arr[i][propertyName]);
      }
      return false; // 未发现重复
    },

    // 检查数组中对象的某个属性的值是否存在某个值
    hasSpecificValue(arr, propertyName, valueToFind) {
      return arr.some(obj => obj[propertyName] === valueToFind);
    },
  },
  watch: {
    inputText: {
      handler(newText) {
        this.generateQuestions();

        console.log("题目文本格式---",newText);
      },
      immediate: true
    }
  },
}
</script>

<style lang='scss' scoped>
.quiz-generator {
  display: flex;
  justify-content: space-between;

  .pub-area {
    width: 49.50%;
    height: 800px;
    border: 1px solid #5907f1;
    overflow-y: auto;

  }

  .custom-textarea {
    ::v-deep .el-textarea__inner {
      height: 800px !important;
      border: 1px solid #747474 !important;
      border-radius: none;
      /* 或者您想要的任何高度 */
      /* 若想限制最小或最大高度，可以用min-height或max-height */
    }
  }

  .left-area,
  .right-area {
    width: 100%;
    height: 100%;
    border: 1px solid #5907f1;
    overflow-y: auto;

    .topic-item {
      border-bottom: 1px dashed #bdbdbd;
      padding: 10px;

      &>p {
        padding: 4px;

        &>span {
          color: rgb(203, 94, 94);
        }
      }
    }
  }
}
</style>