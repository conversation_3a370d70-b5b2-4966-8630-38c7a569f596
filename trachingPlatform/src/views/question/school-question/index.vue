<template>
  <div class="school-question">
    <div class="question-bank-tabs">
      <button :class="{'active':tabStatus == 1}" @click="tabStatus = 1">课程题库</button>
      <button :class="{'active':tabStatus == 2}" @click="tabStatus = 2">平台题库</button>
    </div>
    <div class="question-bank-content">
      <div class="classification" v-if="tabStatus == 1">
        <div class="nav">
          <span>试题分类</span>
          <div class="category-div">
            <category :show-action="showAction" :show-search="true" :courseId="$route.query.id" @nodeClick="treeNodeClick"></category>
          </div>
        </div>
        <div class="content">
          <question-bank-table :courseId="$route.query.id" :questionCategoryId="questionCategoryId" :isCourseDesign="isCourseDesign" :taskSending="taskSending" :isSchool="true"></question-bank-table>
        </div>
      </div>
      <div class="platform-question" v-else-if="tabStatus == 2">
        <platformQuestionContent v-if="tabStatus == 2" :taskSending="taskSending" :isCourseDesign="isCourseDesign" :isSchool="true"></platformQuestionContent>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from "vuex"
import category from '@/components/question/category.vue'
import platformQuestionContent from '../platform-question/components/platform-question-content.vue'
import eventBus from "@/utils/eventBus";

export default {
  name: 'questionBank',
  components:{
    category,
    platformQuestionContent,
    'question-bank-table':()=>import('../platform-question-manage/components/question-bank-table.vue')
  },
  props:{
    taskSending:{
      type:Boolean,
      default:false
    },
    showAction:{
      type:Boolean,
      default:true
    },
    isCourseDesign:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      tabStatus:1,
      questionCategoryId:0//分类iD
    };
  },
  watch:{
    tabStatus:{
      handler(newVal){
        this.questionCategoryId = 0
      }
    }
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"]
    })
  },
  beforeDestroy() {
  },
  mounted() {
    // eventBus.$off('tikuAddTask')
    // eventBus.$on('tikuAddTask', (position,isSelect) => {
    //   // console.log('sadf',position,isSelect);
    //   // this.$emit(isSelect ? "addTask" : "removeTask", position);
    // })
  },
  methods:{
    handleSelectionChange(val){

    },
    handleClick(row){
      this.$router.push('/topicPreview')
    },
    handleNodeClick(data) {
      console.log(data);
    },
    treeNodeClick(val){
      console.log('777777',val);
      this.questionCategoryId = val.id
      sessionStorage.setItem('questionCategoryInfo',JSON.stringify(val))
    },
    getTreeIds(node) {
      const res = []
      res.push(node.id)
      if (node.children) {
        node.children.forEach((ch) => {
          res.push(...this.getTreeIds(ch))
        })
      }
      return res
    },
    treeNodeClick(val){
      // this.questionCategoryId = val.id
      if(val.children && val.children.length>0){
        this.questionCategoryId = this.getTreeIds(val)
      }else{
        this.questionCategoryId = val.id
      }
      // this.pageData.pageNumber = 1
      // this.getQuestionList()

      sessionStorage.setItem('questionCategoryInfo',JSON.stringify(val))
    },
  },
  destroyed() {
  },
};
</script>

<style lang="scss">
.school-question{
  background: #F6F8FA;
  padding-top: 30px;
  height: 100%;
  .question-bank-tabs{
    height: 36px;
    background: #F2F2F2;
    box-sizing: border-box;
    padding-left: 70px;
    display: flex;
    align-items: flex-end;
    >button{
      width: 120px;
      height: 30px;
      //background: #F8F8F8;
      font-size: 14px;
      color: #666666;
      margin-right: 10px;
      border: none;
      cursor: pointer;
    }
    .active{
      background: #FFFFFF;
      color: var(--theme_primary_color);
    }
  }
  .question-bank-content{
    width: 100%;
    height: calc(100% - 36px);
    display: flex;
    justify-content: center;
    align-items: center;
    .platform-question{
      //width: 1510px;
      // height: 695px;
      width: calc(100% - 60px);
      height: calc(100% - 40px);

    } 
    .classification{
      //width: 1510px;
      // height: 695px;
      width: calc(100% - 60px);
      height: calc(100% - 40px);
      display: flex;
      .nav{
        width: 245px;
        height:100%;
        background-color: #fff;
        margin-right: 10px;
        border-radius: 10px 0 0 10px;
        box-sizing: border-box;
        padding: 20px;
        display: flex;
        flex-direction: column;
        >span{
          font-size: 14px;
          color: #333333;
        }
        .category-div{
          height: 100%;
          background: white;
          margin-top: 15px;
          //padding: 10px;
          //border: 1px solid #DDE2E9;
          //border-radius: 4px;
        }
      }
      .content{
        flex: 1;
        min-height: 0;
        min-width: 0;
        background: #FFFFFF;
        border-radius: 0 10px 10px 0;
        box-sizing: border-box;
        padding: 30px 30px 0 30px;
        .el-table {
          border: 1px solid #E7E7E7;
          font-size: 14px;
          color: #222222;
          // margin-top: 20px;
          .el-table__cell {
            border: none;
          }
        }
        .el-pagination{
          text-align: center;
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
