<!--
 * @Author: wujiawei <EMAIL>
 * @Date: 2024-09-05 10:10:19
 * @LastEditors: wujiawei <EMAIL>
 * @LastEditTime: 2024-09-06 09:55:46
 * @FilePath: \fusion_front\src\views\question\platform-question-manage\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="platform-question-manage">
    <div class="head">
      <div class="back" @click="back">
        <span class="el-icon-arrow-left"></span>
        <span>返回</span>
      </div>
      <span class="title">平台题库管理</span>
      <span class="el-icon-close" @click="back"></span>
    </div>
    <div class="main">
      <div v-if="coursesList.length>0" class="content">
        <div class="left-nav">
          <div class="top">
            <span>{{tikuCurCourseInfo.classificationName}}</span>
            <div class="but">
              <el-dropdown trigger="click" @command="changeCourse">
                <el-button type="text" icon="el-icon-arrow-down"></el-button>
                <el-dropdown-menu slot="dropdown" style="max-height: calc(100vh - 180px); overflow-y: auto;">
                  <el-dropdown-item v-for="item in coursesList" :command="{id:item.id,name:item.classificationName}" :key="item.id" class="dropdown-items">
                    <div class="ur-li" v-if="!item.isSelect">
                      <span>{{ item.classificationName }}</span>
                      <el-dropdown placement="right" @command="handleCommand">
                        <span class="el-icon-more" style="transform: rotate(90deg)"></span>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item :command="{status:'editCourse',id:item.id}">修改</el-dropdown-item>
                          <el-dropdown-item :command="{status:'delCourse',id:item.id}">删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <div class="line"></div>
              <span class="el-icon-circle-plus" @click="openAddCurseDialog('新增')"></span>
            </div>
          </div>
          <div class="bottom">
            <span>试题分类</span>
            <div class="tree">
              <category :show-action="true"
                        :show-search="true"
                        :courseId="tikuCurCourseInfo.id"
                        @addNode="QuestionBankCourseClassification"
                        @deleteNode="QuestionBankCourseClassification"
                        @nodeClick="treeNodeClick">
              </category>
            </div>
          </div>
        </div>
        <div class="right-content">
          <div class="question-info">
            <div class="item">
              <span>试题</span>
              <span> {{ courseInfo.questionCount }}</span>
            </div>
            <div class="item">
              <span>已使用试题</span>
              <span> {{ courseInfo.usedQuestionCount }}</span>
            </div>
            <div class="item">
              <span>分类数量</span>
              <span> {{ courseInfo.categoryCount }}</span>
            </div>
          </div>
          <div class="qtable">
            <question-bank-table ref="questionBankTable" :courseId="$route.query.id || tikuCurCourseInfo.id" :questionCategoryId="questionCategoryId" :getCurrentCourseInfo="QuestionBankCourseClassification"></question-bank-table>
          </div>
        </div>
      </div>
      <div class="blank-add" v-else>
        <img src="@/assets/images/no-result.png" alt="">
        <!-- <span>请创建课程</span> -->
        <el-button type="primary" icon="el-icon-circle-plus" @click="openAddCurseDialog('新增')">创建课程</el-button>
      </div>
    </div>
    <!-- 新增课程弹窗   -->
    <el-dialog
      :title="dialogStatus"
      :visible.sync="dialogVisible"
      class="add-curse-dialog"
      >
      <div v-if="['新增','修改'].includes(dialogStatus)">
        <el-form :label-position="labelPosition" ref="ruleForm":rules="rules" label-width="78px" :model="addCurseData">
          <el-form-item label="所属院系" prop="bigCategoryId">
            <el-select v-model="addCurseData.bigCategoryId" filterable placeholder="请选择" @change="bigCategoryChange">
              <el-option
                v-for="item in CategoryList"
                :key="item.id"
                :label="item.categoryName + '---' + item.education"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属专业" prop="smallCategoryId">
            <el-select v-model="addCurseData.smallCategoryId" filterable :disabled="!addCurseData.bigCategoryId" placeholder="请选择">
              <el-option
                v-for="item in SmallCategoryList"
                :key="item.id"
                :label="item.categoryName"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程名称" prop="classificationName">
            <el-input v-model="addCurseData.classificationName" maxlength="20"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div class="del" v-else-if="dialogStatus == '删除'">
        <img src="@/assets/question/del-icon.png" alt="">
        <span>确定删除？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import category from '@/components/question/category.vue'

export default {
components:{
  category,
  'question-bank-table':()=>import('./components/question-bank-table.vue')
},
data() {
  return {
    dialogVisible:false,
    dialogStatus:'',
    addCurseData:{
      bigCategoryId:null,
      smallCategoryId:null,
      classificationName:null
    },
    CategoryList:[],//课程大类
    SmallCategoryList:[],//课程小类
    coursesList:[],
    editCourseId:'',
    tikuCurCourseInfo:{},
    questionCategoryId:0,
    courseInfo:{},
    rules:{
      bigCategoryId:[{ required: true, message: '请选择所属院系', trigger: 'change' }],
      smallCategoryId:[{ required: true, message: '请选择所属专业', trigger: 'change' }],
      classificationName:[{ required: true, message: '请输入课程名称', trigger: 'blur' }],
    }
  };
},
async created(){
  await this.GetCoursesList()
  await this.QuestionBankCourseClassification()
},
mounted() {
},
watch:{
  dialogVisible:{
    handler(newVal,val){
      if(!newVal){
        this.addCurseData = {
          bigCategoryId:null,
          smallCategoryId:"",
          classificationName:null
        }
        this.$refs.ruleForm.resetFields()
      }
    }
  }
},
methods:{
  back(){
    this.$router.back()
  },
  bigCategoryChange(){  
    this.SmallCategoryLists()
    //大类变化清空小类
    this.addCurseData.smallCategoryId = ""
  },
  openAddCurseDialog(val){
    this.dialogStatus = val
    //获取课程大类
    if(val == '新增' || val == '修改'){
      this.BigCategoryList()
    }
    this.dialogVisible = true
  },
  //获取课程列表
  async GetCoursesList(){
    let data = {
      "params": {
        isDelete:false
      },
    }
    let res = await this.$api.GetCourses(data)
    if(res.code == 200){
      res.data.forEach(e => {
        e.isSelect = false
      })

      if(this.$route.query.id){
        let courId = this.$route.query.id
        res.data.forEach(e=>{
          if(e.id == courId){
            e.isSelect = true
          }
        })
        this.tikuCurCourseInfo = res.data.find(e=>e.id == courId)                                                                                            
      }else{
        res.data[0].isSelect = true
        this.tikuCurCourseInfo = res.data[0]
        console.log('-----------',this.tikuCurCourseInfo);
      }
      this.coursesList = res.data
      this.$router.replace({path:'/platform-question-manage',query:{id:this.tikuCurCourseInfo.id,courseName:this.tikuCurCourseInfo.classificationName}})
      // console.log(this.tikuCurCourseInfo);
      // sessionStorage.setItem('tikuCurCourseInfo',JSON.stringify(this.tikuCurCourseInfo))
    }
  },
  //获取课程大类
  async BigCategoryList(){
    let res = await this.$api.BigCategoryList({})
    if(res.code == 200){
      this.CategoryList = res.data
    }
  },
  //获取课程小类
  async SmallCategoryLists(){
    let res = await this.$api.SmallCategoryList({bigCategoryId:this.addCurseData.bigCategoryId})
    if(res.code == 200){
      this.SmallCategoryList = res.data
    }
  },
  //新增编辑课程
  async addCurse(){
    let res = await this.$api.AddCurse(this.addCurseData)
    if(res.code == 200){
      if(this.dialogStatus == '新增'){
        this.$message.success('新增成功')
      }else if(this.dialogStatus == '修改'){
        this.$message.success('修改成功')
      }
      this.GetCoursesList()
      this.dialogVisible = false
    }
  },
  handleCommand(command){
    this.editCourseId = command.id
    if(command.status == 'delCourse'){//删除课程
      this.openAddCurseDialog('删除')
    }else if(command.status == 'editCourse'){//修改课程
      let obj = this.coursesList.find(e=>e.id == command.id)
      this.addCurseData.bigCategoryId = obj.bigCategoryId
      this.addCurseData.smallCategoryId = obj.smallCategoryId
      this.addCurseData.classificationName = obj.classificationName
      this.addCurseData.id = obj.id
      this.SmallCategoryLists()
      this.openAddCurseDialog('修改')
    }
  },  
  //删除课程
  async deleteCourse(){
    let res = await this.$api.DeleteptCourse({id:this.editCourseId})
    if(res.code == 200){
      this.$message.success('删除成功')
      this.dialogVisible = false
      this.GetCoursesList()
    }
  },
  //获取当前课程信息
  async QuestionBankCourseClassification(){
    let res = await this.$api.QuestionBankCourseClassification({id:this.tikuCurCourseInfo.id})
    if(res.code == 200){
      this.courseInfo = {questionCount:res.data.questionCount,usedQuestionCount:res.data.usedQuestionCount,categoryCount:res.data.categoryCount}
    }
  },
  //切换课程
  async changeCourse(command){
    this.tikuCurCourseInfo = this.coursesList.find(e=>e.id == command.id)
    this.coursesList.forEach(e=>{
      if(e.id == command.id){
        e.isSelect = true
      }else{
        e.isSelect = false
      }
    })
    this.questionCategoryId = 0
    this.$router.replace({path:'/platform-question-manage',query:{id:command.id,courseName:command.name}})
    //切换课程后获取课程统计数据
    await this.QuestionBankCourseClassification()
  },
  getTreeIds(node) {
    const res = []
    res.push(node.id)
    if (node.children) {
      node.children.forEach((ch) => {
        res.push(...this.getTreeIds(ch))
      })
    }
    return res
  },
  treeNodeClick(val){
    // console.log('55555',val);
    if(val.children && val.children.length>0){
      this.questionCategoryId = this.getTreeIds(val)
    }else{
      this.questionCategoryId = val.id
    }
    // sessionStorage.setItem('questionCategoryInfo',JSON.stringify(val))
  },

  submit(){
    if(['新增','修改'].includes(this.dialogStatus)){
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.addCurse()
        } else {
          return false;
        }
      });

    }else if(this.dialogStatus == '删除'){
      this.deleteCourse()
    }
  }
},
};
</script>
<style lang='scss' scoped>
.dropdown-items{
  width: 245px;
  padding:0;
  .ur-li{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    >span{
      display: block;
      width: 80%;
      overflow:hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &:hover{
      background:#D3E3FD;
    }
  }
}
.platform-question-manage{
  width: 100vw;
  height: 100vh;
  background: #F6F8FA;
  // position: relative;
  .add-curse-dialog{
    ::v-deep .el-dialog{
      width: 600px;
      border-radius: 10px;
      .el-dialog__footer{
        padding-top: 20px;
      }
      .el-dialog__header{
        background: #F7F7F7;
        border-radius: 10px 10px 0 0;
      }
      .el-select{
        width: 100%;
      }
    }
    .del{
      color: #606A78;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      img{
        margin: 20px 0;
      }
    }
  }
  .head{
    width: 100%;
    height: 50px;
    background: #D3E3FD;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 40px;
    color: #2B66FA;
    .title{
      font-size: 16px;
      font-weight: bold;
    }
    .back,.el-icon-close{
      font-size: 14px;
      cursor: pointer;
      display: flex;
      color: #333333;
      align-items: center;
      &:hover{
        color: #2B66FA;
      }
    }
    
  }
  .main{
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    margin: auto;
    margin-top: 20px;
    .blank-add{
      width: 100%;
      height: calc(100% - 43px);
      display: flex;
      flex-direction: column;
      background: white;
      justify-content: center;
      align-items: center;
      img{
        width: 200px;
        margin-bottom: 20px;
      }
    }
    .content{
      width: 100%;
      height: calc(100% - 43px);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .left-nav{
        width: 245px;
        height: 100%;
        // background: white;
        border-radius: 10px 0 0 10px;
        .top{
          width:100%;
          height: 44px;
          margin-bottom: 20px;
          background: url("../../../assets/question/curse-back.png") no-repeat center;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 10px;
          >span{
            font-size: 16px;
            color: #FFFFFF;
            display: block;
            width: 80%;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .but{
            color: white;
            font-size: 16px;
            height: 50%;
            display: flex;
            align-items: center;
            ::v-deep .el-icon-arrow-down{
              font-size: 16px;
              color: white;
            }
            >span{
              cursor: pointer;
            }
            .line{
              width: 1px;
              height: 100%;
              background: white;
              margin: 0 8px;
            }
          }
        }
        .bottom{
          width: 100%;
          height:calc(100% - 70px);
          background:white;
          border-radius: 10px 0 0 10px;
          box-sizing: border-box;
          padding: 20px;
          >span{
            font-size: 14px;
            color: #333333;
          }
          .tree{
            height: calc(100% - 33px);
            box-sizing: border-box;
            padding: 10px;
            border: 1px solid #DDE2E9;
            margin-top: 15px;
            overflow-y: auto;
            border-radius: 4px;
            //padding: 5px;
          }
        }
      }
      .right-content{
        width: calc(100% - 260px);
        height: calc(100% - 6px);
        background: white;
        border-radius: 0 10px 10px 0;
        box-sizing: border-box;
        padding: 20px;
        padding-top: 0;
        display: flex;
        flex-direction: column;
        .question-info{
          width: 100%;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          height: 43px;
          border-bottom: 1px dashed #D9D9D9;
          margin-bottom: 20px;
          .item{
            font-size: 14px;
            margin-left: 70px;
            span{
              &:nth-child(1){
                color: #787D83;
              }
              &:nth-child(2){
                color: #2B66FA;
              }
            }
          }
        }
        .qtable{
          flex: 1;
          min-height: 0;
        }
      }
    }
  }
}
</style>