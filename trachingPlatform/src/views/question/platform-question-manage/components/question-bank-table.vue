<template>
  <div class="question-bank-table">
    <div class="btn">
      <div>
        <add-question-btn ref="addQuestionBtnRef" :is-system="!isSchool" :questionCategoryId="currntCategoryId" v-if="!taskSending && !isCourseDesign" @success="success"></add-question-btn>
        <!-- <el-button style="margin-left: 10px;">导入试题</el-button> -->
        <el-button class="mb-10" @click="openDialog('移动分类')" style="margin-left: 10px;" v-if="!taskSending && !isCourseDesign">移动到分类</el-button>
        <el-button class="mb-10" @click="openDialog('批量删除')" v-if="!taskSending && !isCourseDesign">删除试题</el-button>
        <el-button class="mb-10" @click="batchRelease('发布')" v-if="!isSchool">批量发布</el-button>
        <el-button class="mb-10" @click="batchRelease('取消发布')" v-if="!isSchool">批量取消</el-button>
        <el-select class="mb-10" v-model="questionType" placeholder="请选择题型" clearable @change="searchQuestion">
          <el-option
            v-for="(qTypeText, qType) in questionTypeLabel"
            :key="qType"
            :label="qTypeText"
            :value="qType">
          </el-option>
        </el-select>
        <el-select class="mb-10" v-model="founder" placeholder="创建人" clearable @change="searchQuestion" filterable>
          <el-option
            v-for="item in founderArr"
            :key="item.id"
            :label="item.realName"
            :value="item.id">
          </el-option>
        </el-select>
        <el-select class="mb-10" v-model="sortType" placeholder="排序" clearable @change="searchQuestion" filterable>
          <el-option label="按时间升序" value="id asc"> </el-option>
          <el-option label="按时间降序" value="id desc"> </el-option>
          <el-option label="按序号升序" value="sort asc"> </el-option>
          <el-option label="按序号降序" value="sort desc"> </el-option>
        </el-select>
        <el-select class="mb-10" v-model="publishStatus" placeholder="发布状态" v-if="!isSchool" clearable @change="searchQuestion" filterable>
          <el-option
            v-for="item in [{name:'已发布',id:1},{name:'未发布',id:0}]"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
        <el-input
          placeholder="搜索"
          clearable
          @clear="searchQuestion"
          @keyup.enter.native="searchQuestion"
          v-model="searchVal">
          <i slot="suffix" class="el-input__icon el-icon-search" @click="searchQuestion"></i>
        </el-input>
      </div>

      <div class="addtaskBtn" v-if="taskSending" @click="addToTask($event)">加入任务篮</div>
      <div class="addtaskBtn askdh" v-if="isCourseDesign" @click="addToTask($event)">选择</div>
    </div>
    <div class="question-table">
      <baseTable
        :columns="columns"
        :action="action"
        :pagination="true"
        rowKey="id"
        :treeProps="treeProps"
        :selectable="true"
        height="630"
        :tableData="questionList"
        :pageNumber="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :reserveSelection="true"
        @sortChange="sortChange"
        @handlePageChange="handlePageChange"
        @handleSizeChange="handleSizeChange"
        @handlePrev="handlePrev"
        @handleNext="handleNext"
        @selectedRows="handleSelectionChange"
      > 
        <template slot="itemSlot" slot-scope="{ scope }">
          <el-tag v-if="scope.row.status === 1" type="success">已发布</el-tag>
          <el-tag v-else type="info">未发布</el-tag>
        </template>
        <template slot="questionTitle" slot-scope="{ scope }">
          <span style="text-align: left">{{ scope.row.title }}</span>
        </template>
      </baseTable>
    </div>

    <el-dialog
      :title="dialogStatus"
      :visible.sync="dialogVisible"
      class="set-classification"
      >
      <div class="del" v-if="['批量删除','删除'].includes(dialogStatus)">
        <img src="@/assets/question/del-icon.png" alt="">
        <span>确定删除？</span>
      </div>
      <div v-else-if="dialogStatus == '移动分类'" class="edit-class">
        <span>移动到:</span>
        <div>
          <category v-if="dialogVisible" :show-action="false" :show-search="false" :course-id="$route.query.id || courseId" :show-all="false" @nodeClick="treeNodeClick"></category>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <question-preview-dialog v-if="visibles" ref="questionPreviewDialogRef"
                             :visible.sync="visibles"
                             :data="currentTopic"
                             :isAnswerHint="true"   
                             :previewIds="questionList.map(item => item.id)">
      <template slot="rightSlot">
        <div>
          <el-button size="mini" type="primary" plain @click="visibles = false">关闭</el-button>
          <el-button v-if="!isSchool" size="mini" type="primary" plain
                     @click="publishQuestion({ row: currentTopic }, 'detail')"
          >{{ currentTopic.status === 1 ? '取消发布' : '发布' }}</el-button>
          <el-button size="mini" type="primary"
                     @click="editQuestion({ row: currentTopic }, 'detail')"
          >编辑</el-button>
        </div>
      </template>
    </question-preview-dialog>

  </div>
</template>

<script>
import category from '@/components/question/category.vue'
import baseTable from '@/components/base/table/baseTable.vue';
import {getDifficultyDispatch, questionTypeLabel} from "@/components/base/question/util";
import cellItem from "@/components/base/table/cellItem.vue";
import { mapGetters } from "vuex"
import eventBus from "@/utils/eventBus";

export default {
  components:{
    cellItem,
    category,
    baseTable,
    'add-question-btn': () => import('@/components/question/add-question-btn.vue'),
    'question-preview-dialog': () => import('@/components/question/question-preview-dialog.vue'),
  },
  props:{
    courseId:{
      type:Number,
      default:0
    },
    questionCategoryId:{
      type:Number,
      default:0
    },
    taskSending:{
      type:Boolean,
      default:false
    },
    isSchool:{
      type:Boolean,
      default:false
    },
    getCurrentCourseInfo:{
      type:Function,
      default:()=>{}
    },
    isCourseDesign:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      questionTypeLabel: questionTypeLabel,
      questionList:[],
      pagination:{
        pageSize:10,
        currentPage:1,
        total:''
      },
      searchVal:'',
      questionType:null,
      dialogVisible:false,
      dialogStatus:'',
      multipleSelection:[],
      movingTargetClassifyId:'',
      currentTopic:{},
      visibles:false,
      columns:[
        {field:'title',label:'试题标题',slotName:'questionTitle', minWidth: '220'},
        {field:'id',label:'试题ID',align:'center', minWidth: '100'},
        {field:'questionTypeText',label:'题型',align:'center',minWidth:'80'},
        {field:'difficultyText',label:'难易',align:'center',minWidth:'80',sortable:'custom'},
        {field:'used',label:'使用量',align:'center',minWidth:'80',sortable:'custom'},
        {field:'createByName',label:'创建人',align:'center',minWidth:'90'},
        {field:'createTime',label:'创建时间',align:'center',width:'170px',sortable:'custom'},
        {field:'updateTime',label:'最近一次更新时间',align:'center',width:'170px'},
        {field:'status',label:'题目状态',align:'center', width:'100px',slotName: 'itemSlot'},
      ],
      action: {
        width: 200,
        fixed: 'right',
        align:'center',
        label: '操作',
        slot:true,
        items: [
          {
            name: '预览',
            type: 'text',
            click: (row) => this.topicOperation(row.row),
          },
          {
            name: '编辑',
            type: 'text',
            style:(row)=>this.coreJs(row),
            click: (row) => this.editQuestion(row),
          },
          {
            name: '删除',
            type: 'text',
            class:'del-btn',
            style:(row)=>this.coreJs(row),
            click: (row) => this.delQuestion(row,row),
          },
          {
            name: '发布',
            type: 'text',
            click: (row) => this.publishQuestion(row),
            status:(row)=>this.releaseText(row)
          },
        ],
      },
      founderArr:[],
      founder:'',
      orderBy:'',
      publishStatus:'',
      previewModalIsEdited: false, // 是否预览页编辑了数据
      actionPosition: 'list' , // 操作位置  list:列表处  detail:预览页
      // 表格属性配置
      treeProps:{
        children: 'children', // 不显示 children 数据
      },
      sortType:'id asc',//排序类型
    };
  },
  mounted() {
    // this.tikuCurCourseInfo = JSON.parse(sessionStorage.getItem('tikuCurCourseInfo'))
    // this.getQuestionList()
    if(this.isSchool){
      this.columns = [
        {field:'title',label:'试题标题',slotName:'questionTitle', minWidth: '200'},
        {field:'questionTypeText',label:'题型',align:'center',minWidth:'90'},
        {field:'difficultyText',label:'难易',align:'center',sortable:'custom',minWidth:'90'},
        {field:'used',label:'使用量',align:'center',sortable:'custom',minWidth:'90'},
        {field:'accuracyStr',label:'正确率',align:'center',sortable:'custom',minWidth:'90'},
        {field:'createTime',label:'创建时间',align:'center',sortable:'custom',width:'180px'},
      ]

      this.action.items = [
        {
          name: '预览',
          type: 'text',
          click: (row) => this.topicOperation(row.row),
        },
        {
          name: '编辑',
          type: 'text',
          click: (row) => this.editQuestion(row),
        },
        {
          name: '删除',
          type: 'text',
          class:'del-btn',
          click: (row) => this.delQuestion(row,row),
        },
      ]
    }
    if(this.taskSending || this.isCourseDesign){
      this.action = null
    }
    this.QuestionCreateByList()
    // if(this.founderArr.find(e=>e.id == this.userInfo.id)){
    //   this.founder = Number(this.userInfo.id)
    // }
  },
  computed:{
    ...mapGetters({
      userInfo: ["userInfo"]
    }),
    currntCategoryId(){
      let data = ''
      if(Array.isArray(this.questionCategoryId)){
        data = this.questionCategoryId[0]
      }else{  
        data = this.questionCategoryId
      }
      return data
    }
  },
  watch:{
    questionCategoryId:{
      handler(newVal,val){
        // console.log('分类');
        this.pagination.currentPage = 1
        this.pagination.pageSize = 10
        this.getQuestionList()
      }
    },
    pagination: {
      handler(newVal) {
        // console.log('分页');
        this.getQuestionList()
      },
      deep: true,
      immediate: true,
    },
    "$route":{
      handler(newVal){
        this.init()
        this.getQuestionList()
      }
    },
    visibles: {
      handler(newVal,val){
        if (!newVal && val) {
          if (this.previewModalIsEdited) {
            this.$confirm('检测到你变更了数据，是否刷新列表?', '提示', {
              confirmButtonText: '刷新',
              cancelButtonText: '不刷新',
              type: 'warning'
            }).then(() => {
              this.getQuestionList()
              this.getCurrentCourseInfo()
            })
            this.previewModalIsEdited = false
          }
        }
      }
    }
  },
  methods:{
    init(){
      this.pagination.currentPage = 1
      this.pagination.pageSize = 10
      this.founder = ''
      this.questionType = null
      this.searchVal = ''
      this.publishStatus = ''
      // this.questionCategoryId = 0
    },
    async QuestionCreateByList(){
      let res = await this.$api.QuestionCreateByList({courseId:this.$route.query.id || this.courseId})
      if(res.code == 200){
        this.founderArr = res.data
      }
    },
    sortChange(val){
      if(!val.order){
        this.orderBy = ''
        this.getQuestionList()
      }
      if(val.order == 'ascending'){
        if(val.prop == 'difficultyText'){
          this.orderBy = 'difficulty asc'
        }else if(val.prop == 'accuracyStr'){
          this.orderBy = 'accuracy asc'
        }else{
          this.orderBy = `${val.prop} asc`
        }
      }else if(val.order == 'descending'){
        if(val.prop == 'difficultyText'){
          this.orderBy = 'difficulty desc'
        }else if(val.prop == 'accuracyStr'){
          this.orderBy = 'accuracy desc'
        }else{
          this.orderBy = `${val.prop} desc`
        }
      }
      this.getQuestionList()
    },
    addToTask(e){
      if(this.multipleSelection.length == 0){
        this.$message('请先选择题目!')
        return
      }
      let domPosition ={
        x:e.x,
        y:e.y,
      }
      if(this.isCourseDesign){
        eventBus.$emit('openAnimateInsert',domPosition) //动画执行
        eventBus.$emit('topicInsertCourse',this.multipleSelection,true)
      }else{
        eventBus.$emit('openAnimate',domPosition) //动画执行
        eventBus.$emit('tikuAddTask',this.multipleSelection,true)
      }
    },
    releaseText(row){
      if(row.row.status === 1){
        return '取消'
      }else{
        return '发布'
      }
    },
    coreJs(row){
      if(row.row.status === 1){
        return 'color:#D0D0D0'
      }else{
      }
    },
    //分页
    handlePageChange(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: val,
      }
    },
    handleSizeChange(val) {
      this.pagination = {
        ...this.pagination,
        pageSize: val,
      }
    },
    handlePrev(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: this.pagination.currentPage - 1,
      }
    },
    handleNext(val) {
      this.pagination = {
        ...this.pagination,
        currentPage: this.pagination.currentPage + 1,
      }
    },
    delQuestion(val){
      if(val.row.status == 1){
        this.$message('已发布的题目无法删除,如需删除,请先取消发布')
      }else if(val.row.status == 0){
        this.openDialog('删除',val)
      }

    },
    async publishQuestion({ row }, pos = 'list'){
      this.actionPosition = pos;
      await this.$zdDialog({
        contImg: '',
        contTitle: row.status ? '确定取消发布' : '确定发布',
        contDesc: row.status ? '取消发布后老师将看不到此题目' : '发布后老师可以将此题目作为选题',
      })
      const { code } = await this.$api.PublishQuestion({
        questionId: row.id,
        isPublishing: !row.status
      })
      if (code === 200) {
        this.$message.success('操作成功！')
        if (this.actionPosition === 'list') {
          await this.getQuestionList()
        } else {
          row.status = row.status === 1 ? 0 : 1
          this.previewModalIsEdited = true
        }
      }
    },
    async batchRelease(val){
      if(this.multipleSelection.length == 0) {
        this.$message(val == '发布'?'请选择要发布的题目!':'请选择要取消发布的题目!')
        return
      }
      await this.$zdDialog({
        contImg: '',
        contTitle: val == '发布'?'确定发布':'取消发布',
        contDesc: val == '发布'?'发布后老师可以将此题目作为选题':'取消发布后老师不能将此题目作为选题',
      })
      let data = this.multipleSelection.map(e=>{
        return e.id
      })
      if(val == '发布'){
        const { code } = await this.$api.batchRelease(data)
        if (code === 200) {
          this.$message.success('操作成功！')
          await this.getQuestionList()
        }
      }else if(val == '取消发布'){
        this.QuestionCancelPublish(data)
      }

    },
    async QuestionCancelPublish(data){
      let res = await this.$api.QuestionCancelPublish(data)
      if(res.code == 200){
        this.$message.success('操作成功！')
        await this.getQuestionList()
      }
    },
    editQuestion ({ row }, pos = 'list') {
      this.actionPosition = pos
      if(!this.isSchool && row.status == 1){
        this.$message('已发布的题目无法编辑,如需编辑,请先取消发布')
      }else{
        this.$refs.addQuestionBtnRef.commandAdd({
          type: row.type,
          data: row
        })
      }
    },
    topicOperation(data){
      this.currentTopic = data
      this.visibles = true
    },
    openDialog(val,data){
      this.dialogStatus = val

      if(val == '移动分类'){
        if(this.multipleSelection.length == 0){
          this.$message('请选择要移动的题目!')
          return
        }
      }else if(val == '批量删除'){
        if(this.multipleSelection.length == 0){
          this.$message('请选择要删除的题目!')
          return
        }
      }else if(val == '删除'){
        // console.log('7777',data);
        this.currentTopic = data.row
      }
      this.dialogVisible = true

    },
    searchQuestion(){
      this.pagination.currentPage = 1
      this.getQuestionList()
    },
    //获取题目列表
    async getQuestionList(){
      let data = {
        params:{
          title:this.searchVal,
          questionType:this.questionType,
          courseId: this.$route.query.id || this.courseId,
          parentId: 0, // 查询非子题目
        },
        pageIndex:this.pagination.currentPage,
        pageSize:this.pagination.pageSize,
        orderBy: this.sortType,
      }
      
      if(this.orderBy){
        data.orderBy = this.orderBy
      }
      if(this.questionCategoryId){
        data.params.questionCategoryId = this.questionCategoryId
      }
      if(this.founder){
        data.params.createBy = this.founder
      }
      if(this.publishStatus != undefined){
        data.params.status = this.publishStatus
      }
      let res = await this.$api.GetQuestionList(data)
      if(res.code == 200){
        this.questionList = res.data.map((item) => {
          item.questionTypeText = questionTypeLabel[item.questionType]
          item.difficultyText = getDifficultyDispatch(item.difficulty)
          return item
        })
        this.pagination.total = res.recordCount
      }
    },
    //新增题目成功刷新页面数据
    async success(){
      if (this.actionPosition === 'list') {
        await this.getQuestionList()
        await this.getCurrentCourseInfo()
      } else {
        this.previewModalIsEdited = true
        await this.$refs.questionPreviewDialogRef?.getDetail()
      }
    },
    treeNodeClick(val){
      this.movingTargetClassifyId = val.id
    },
    //批量删除题目请求
    async BatchDelete(data){
      let res = await this.$api.DeleteplByIds(data)
      if(res.code == 200){
        this.$message({ message: '删除成功', type: 'success' });
        this.dialogVisible = false
        let currentPage = Math.ceil((this.pagination.total - data.length) / this.pagination.pageSize)
        if(this.pagination.currentPage > currentPage){
          this.pagination.currentPage = this.pagination.currentPage - 1
        }
        this.getQuestionList()
      }
    },
    //批量移动分类
    async MoveQuestion(arr){
      let data = {
        "questionCategoryId": this.movingTargetClassifyId,
        "ids": arr
      }
      let res = await this.$api.MoveQuestion(data)
      if(res.code == 200){
        this.$message({ message: '移动成功', type: 'success' });
        this.dialogVisible = false
        if(Array.isArray(this.questionCategoryId) && !this.questionCategoryId.find(e=>e == this.movingTargetClassifyId)){
          let currentPage = Math.ceil((this.pagination.total - arr.length) / this.pagination.pageSize)
          if(this.pagination.currentPage > currentPage){
            this.pagination.currentPage = this.pagination.currentPage - 1
          }
        }
        this.getQuestionList()
      }
    },
    //删除单个题目
    async DeleteQuestion(){
      let data = {
        id:this.currentTopic.id
      }
      let res = await this.$api.DeleteQuestion(data)
      if(res.code == 200){
        this.$message({ message: '删除成功', type: 'success' });
        this.dialogVisible = false
        //删除表格当前页最后一行数据
        let currentPage = Math.ceil((this.pagination.total - 1) / this.pagination.pageSize)
        if(this.pagination.currentPage > currentPage){
          this.pagination.currentPage = this.pagination.currentPage - 1
        }
        this.getQuestionList()
        this.getCurrentCourseInfo()
      }
    },


    submit(){
      if(this.dialogStatus == '批量删除'){
        let arr = []
        this.multipleSelection.forEach(e=>{
          arr.push(e.id)
        })
        this.BatchDelete(arr)
      }else if(this.dialogStatus == '移动分类'){
        let arr = []
        this.multipleSelection.forEach(e=>{
          arr.push(e.id)
        })
        this.MoveQuestion(arr)
      }else if(this.dialogStatus == '删除'){
        this.DeleteQuestion()
      }
    },
    handleSelectionChange(val) {
      // console.log('7777',val);
      this.multipleSelection = val;
    }
  },
};
</script>
<style lang='scss' scoped>
.question-bank-table{
  width: 100%;
  height: 100%;
  // display: flex;
  // flex-direction: column;
  .btn{
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .addtaskBtn{
      width: 100px;
      height: 30px;
      background: #f59337;
      border-radius: 20px;
      color: white;
      font-size: 14px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      padding: 0 15px;
    }
    .askdh{
      background: #2b66ff;
    }
    .mb-10{
      margin-bottom:10px;
    }
    .el-button{
      margin: 0;
      margin-right: 10px !important;
    }
    .el-select{
      width: 120px;
      margin-right: 10px;
    }
    .el-input{
      width: 200px;
      margin-right: 10px;
      ::v-deep .el-input__inner{
        padding-right: 53px;
      }
      .el-input__icon{
        cursor: pointer;
      }
    }
  }
  .question-table{
    flex: 1;
    min-height: 0;
    margin-bottom: 30px;

    ::v-deep .base-table-wrapper{
      min-height: 580px;
    }
    // ::v-deep .del-btn{
    //   color: black;

    //   ::v-deep .el-button{
    //     color: black;
    //   }
    // }
  }

  ::v-deep .el-table{
    border: 1px solid #E7E7E7;
    // max-height: 665px;
    // 隐藏children 行数据的展开图标
    
    .el-table__cell{
      border: none;
      text-align: center;
    }
    .is-center {
      .cell {
        justify-content: center !important;
      }
    }
    .is-left {
      .cell {
        text-align: left;
      }
    }
  }
  ::v-deep .el-pagination{
    text-align: center;
    margin-top: 20px;
  }
  .set-classification{
    ::v-deep .el-dialog{
      width: 600px;
      border-radius: 10px;
      .el-dialog__header{
        border-radius: 10px 10px 0 0;
        background: #F7F7F7;
      }
      .el-dialog__footer{
        padding-top: 20px;
      }

    }
    .edit-class{
      width: 100%;
      margin: 0 10px;
      max-height: 175px;
      overflow-y: auto;
      >span{
        font-size: 14px;
        color: #333333;
      }
    }
    .del{
      display: flex;
      flex-direction: column;
      align-items: center;
      img{
        margin-bottom: 20px;
      }
    }
  }
}
</style>
<style lang="scss">
.question-table {
  .base-table-wrapper .el-table .el-table__expand-icon {
    display: none;  /* 隐藏展开图标 */
  }
}
</style>