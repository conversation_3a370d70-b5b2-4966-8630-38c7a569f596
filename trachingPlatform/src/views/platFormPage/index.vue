<template>
    <transition name="el-zoom-in-center">
      <div class="layout-box">
        <platFormHeader />
        <div class="layout-main">
          <platFormAside />
          <div class="layout-content">
            <!-- <tabView /> -->
            <div style="background:#fff;height:100%">
              <router-view></router-view>
            </div>
          </div>
        </div>
      </div>
    </transition>
</template>
  
<script>
export default {
    name: "platFormLayout",
    components: {
    platFormHeader: () => import('./components/header.vue'), // 导入组件
    platFormAside: () => import('./components/aside.vue'), // 导入组件
    tabView: () => import('./components/tabView.vue'), // 导入组件
    },
    data() {
    return {
        msg: 'Welcome'
    }; 
    }
};
</script>

<style lang="scss" scoped>
  // 定义过渡动画
  .el-zoom-in-center-enter-active,
  .el-zoom-in-center-leave-active {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  }
  
  .el-zoom-in-center-enter,
  .el-zoom-in-center-leave-to {
    opacity: 0;
    transform: scaleX(0);
  }
  
  // 布局盒子样式
  .layout-box {
    width: 100%;
    height: 100%;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
  
  // 主布局样式
  .layout-main {
    flex: 1;
    display: flex;
    min-height: 0;
    height: 100%;
    flex-direction: row;
  }
  
  // 侧边栏样式
  .schoolAside {
    width: 200px;
    height: 100%;
  }
  
  // 内容区域样式
  .layout-content {
    flex: 1;
    padding: 0px;
    overflow-y: auto;
    // padding:20px;
    background: #F6F8FA;
    min-height: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
  }
  </style>