<template>
    <div class="user-manage-page" v-loading="loading">
      <section class="top-tool">
        <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @change="initTableData()" @blur="initTableData()" clearable placeholder="请搜索名字" suffix-icon="iconfont icon-sousuo"></el-input>
        <el-select @change="initTableData()" style="width:220px;margin-right:10px;" clearable filterable v-model="tableConfig.searchParams.userType" placeholder="请选择类型">
          <el-option v-for="item in userType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <addButton @addEvent="addEvent" />
        <exportButton />
        <importButton />
        <reflashButton @reflashEvent="reflashEvent" />
      </section>
      <table2 
        @selectionChange="selectionChange" 
        :notShowSearch="notShowSearch" 
        ref="tablePreview" 
        @selection-change="handleSelectionChange" 
        :selectable="tableConfig.selectable" 
        :data="tableConfig.tableData" 
        :columns="tableConfig.columns" 
        :queryFormConfig="tableConfig.queryConfig" 
        :total="tableConfig.total"
        :pagination="tableConfig.pagination" 
        :height="height" 
        :paginationLayout="tableConfig.paginationLayout" 
        :firstLoad="firstLoad" 
        :searchParams="tableConfig.searchParams" 
        @handleSearch="handleSearch">
        <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
        <template #operate>
          <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
            <template slot-scope="scope">
              <el-button v-for="(item, index) in operateBtns" type="text"
                         v-show="isShowOperateBtn(scope, item)"
                         :class="item.class || ''"
                         @click="btnClick(scope.row, item)" :key="index"
                         :disabled="scope.row.dataLocked">
                {{ item.name }}
              </el-button>
            </template>
          </el-table-column>
        </template>
      </table2>
  
      <!-- 新增账号 -->
      <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" className="add-account-dialog" :title="!form.id?'新增':'编辑'">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="top">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名" maxLength="20"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
          </el-form-item>
          <el-form-item label="所属角色" prop="userType">
            <el-select v-model="form.userType" multiple placeholder="请输入所属角色">
              <el-option v-for="item in userType" :value="item.value" :key="item.value" :label="item.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工号" prop="workNumber">
            <el-input type="number" v-model="form.workNumber" placeholder="请输入工号"></el-input>
          </el-form-item>
          <el-form-item style="text-align:right;">
            <el-button type="default" @click="dialogVisible = false;">取消</el-button>
            <el-button type="primary" @click="submitForm" v-preventReClick>保存</el-button>
          </el-form-item>
        </el-form>
      </baseDialog> 
  
  
      <el-dialog :visible.sync="stopDialog" custom-class="stop-dialog" title="提示">
        <p>确定要{{form.enabled?'停用':'启用'}}该账号吗？</p>
        <div style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button style="width:80px;" type="default" @click="stopDialog = false;">取消</el-button>
          <el-button style="width:80px;" type="primary" @click="handleStop" v-preventReClick>保存</el-button>
        </div>
      </el-dialog>
  
      <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
        <detail :detailInfo="detailInfo" />
        <div style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
        </div>
      </baseDialog>
    </div>
  </template>
  
  <script>
  import { UserType } from "@/constants/common.js"; // 导入常量
  import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
  
  export default {
    name: "userManage",
    data() {
      // 定义手机号校验规则
      const validatePhone = (rule, value, callback) => {
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
          callback(new Error('请输入有效的手机号码'));
        } else {
          callback();
        }
      };
      return {
        userType: UserType.filter(v=>v.type!=1&&v.type!=2&&v.type!=32&&v.type!=16), // 用户类型
        height:'660px',
        tableConfig: {
          columns: [
            {
              label: "序号",
              type: "index",
              width: 80,
            // },{
            //   label: "ID",
            //   prop: "id",
            },
            {
              label: "工号",
              prop: "workNumber",
            },
            {
              label: "姓名",
              prop: "name",
            },
            {
              label: "联系方式",
              prop: "phone",
            },
            {
              label: "角色",
              prop: "userType",
              formatter: (row, column, cellValue, index) => {
                if (!cellValue) return '';
                // 分割多个角色值
                const values = cellValue.split(','); 
                const labels = values.map(value => {
                  const item = UserType.find(item => item.value === value.trim());
                  return item ? item.label : '';
                });
                // 过滤掉空标签并使用逗号连接
                return labels.filter(label => label).join(', '); 
              }
            },
            {
              label: "用户状态",
              prop: "enabled",
              width: 100,
              align: "center", // 居中对齐
              formatters: (row, column, cellValue, index) => {
                return cellValue?'<span style="display:inline-block;text-align:center;width: 50px;height: 20px;background: #EDF2FF;border-radius: 4px;color:#0070FC;">已启用</span>':'<span style="display:inline-block;text-align:center;width: 50px;height: 20px;background: #FBEEEE;border-radius: 4px;color:#F11B1B;">已停用</span>';
              }
            },
            {
              label: "创建时间",
              prop: "createTime",
              formatter: (row, column, cellValue, index) => {
                return formatISOString(cellValue);
              }
            }
          ], // 表格列
          tableData: [], // 表格数据
          pagination: true,
          total: 0,
          searchParams: {
            pageIndex: 1,
            pageSize: 12,
            searchWord: "", // 搜索关键字
            userType: '' // 用户类型
          },
          btnList: {
            detail: {
              enable: true
            }
          },
          // 补全模板中使用的变量
          notShowSearch: false,
          selectable: true,
          pagination: true,
          paginationLayout: 'total, prev, pager, next, jumper',
          queryConfig: {},
        },
        firstLoad: true,
        loading: false,
        actionBtns: [
          // { id: 1, name: '新增' }
        ],
        operateBtns: [
          { name: '详情',class:'default-btn' },
          { name: '编辑',class:'default-btn' },
          { name: '重置密码',class:'default-btn' },
          { name: '停用',class:'default-btn' },
          { name: '启用',class:'default-btn' },
          { name: '删除',class:'del-btn' }
        ],
        operateWidth: 280,
        collegeList:[],// 学院列表
        classList:[],// 班级列表
        courseList:[],// 课程列表
        dialogVisible:false,// 弹窗是否显示
        form:{ // 新增表单
          id:'', // ID
          name:'', // 名称
          phone:'', // 联系方式
          userType:'', // 账号类型
          workNumber:'', // 工号
  
        },
        // 添加校验规则
        rules: {
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { validator: validatePhone, trigger: 'blur' }
          ],
          name: [
            { required: true, message: '请输入姓名', trigger: 'blur',validator: (rule, value, callback) => {
                // 去除前后空格
                const trimmedValue = value.trim(); 
                if (trimmedValue === '') {
                  callback(new Error('请输入姓名'));
                } else {
                  callback();
                }
              } } ,
            
          ],
          userType: [
            { required: true, message: '请选择角色', trigger: 'change' } 
          ]
        },
        stopDialog:false, // 停用弹窗
        detailDialogVisible:false, // 详情弹窗
        detailInfo:{}, // 详情信息
      }; 
    },
    components: {
      // 补全模板中使用的组件
      addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
      reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
      exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
      importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
      baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
      detail:()=>import('@/views/schoolPage/userManage/components/detail.vue'),
      empty:()=>import("@/components/base/empty.vue"),
    },
    mounted() {
      // 初始化表格数据
      // this.initTableData();
      this.getCollegeList(); // 获取学院列表
    },
    methods: {
      // 初始化表格数据
      async initTableData(val){
        this.loading = true;
        val?this.tableConfig.searchParams.collegeId = val:'';
        let res = await this.$api.GetTeacherList(this.tableConfig.searchParams)
        this.tableConfig.tableData = res.data.items
        this.tableConfig.total = res.data.total;
        setTimeout(() => {
          this.loading = false;
        }, 300);
      },
      getCollegeList(){ // 获取学院列表
        this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
          this.collegeList = res.data.items;
        })
      },
      // 刷新
      reflashEvent(){
        this.initTableData()
      },
      addEvent(){
        this.dialogVisible = true;
        this.form = { // 新增表单
          id:'', // ID
          name:'', // 名称
          phone:'', // 联系方式
          userType:'', // 账号类型
          workNumber:'', // 工号
        };
      },
      handleStop(){ // 停用/启用
        this.$api.EnableTeacher({userId:this.form.id,enable:!this.form.enabled}).then(res=>{
          this.stopDialog = false;
          this.initTableData()
        })
      },
      // 修改保存方法，先进行表单校验
      submitForm() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.save();
          } else {
            console.log('表单校验失败');
            return false;
          }
        });
      },
      save(){
        // 定义手机号码正则表达式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(this.form.phone)) {
          this.$message.error('请输入有效的手机号码');
          return;
        }
    
        this.$refs.form.validate((valid) => {
          if (valid) {
            const selectUserType = [];
            UserType.forEach(item=>{
              if(this.form.userType.includes(item.value)){
                selectUserType.push(item.type)
              }
            })
            
            // 计算按位或结果
            const userTypeBitwiseOr = selectUserType.reduce((acc, cur) => acc | cur, 0);
            let params={ // 提交参数
              id: this.form.id,
              headerImage: this.form.headerImage,
              name: this.form.name,
              phone: this.form.phone,
              collegeId: this.form.collegeId,
              // teachCourseIds: this.form?.courses.map(v=>v.id),
              workNumber: this.form.workNumber,
              userType: userTypeBitwiseOr,
            }
            if(this.form.id){ // 编辑
              this.$api.UpdateTeacher(params).then(res=>{
                if(res.errCode==0){
                  this.dialogVisible = false;
                  this.$message({
                    type: 'success',
                    message: '编辑成功'
                  })
                  this.initTableData()
                }
              })
              return;
            }
            this.$api.CreateTeacher(params).then(res=>{
              if(res.errCode==0){
                this.dialogVisible = false;
                this.$message({
                    type: 'success',
                    message: '保存成功'
                  })
                this.initTableData()
                this.resetForm(); // 重置表单
              }
            })
          } else {
            console.log('表单校验失败');
            return false;
          }
        });
       
      },
      // 新增重置表单方法
      resetForm() {
        this.form = { 
          id: '', 
          name: '', 
          phone: '', 
          userType: '', 
          workNumber: '', 
        };
      },
      // 补全模板中使用的方法
      selectionChange(selection) {
        console.log('selection changed:', selection);
      },
      handleSelectionChange(selection) {
        console.log('handle selection changed:', selection);
      },
      handleSearch(params) {
        console.log('search params:', params);
        this.tableConfig.searchParams = params;
        this.initTableData()
        // 这里可以添加搜索逻辑
      },
      isShowOperateBtn(scope, item) {
        // 这里可以添加操作按钮显示逻辑
        if (item.name === '停用') {
          return scope.row.enabled
        }else if (item.name === '启用') {
          return scope.row.enabled==false
        }else{
          return true;
        }
       
      },
      changeSelection(selection) {
        this.$emit("changeSelection", selection);
      },
      formatDataSourceBtns(btn) {
        if (btn.type === "detail") {
          btn.enable = this.tableConfig.btnList.detail.enable;
        }
        return btn;
      },
      handlePlus(btn) {
        this.$emit("handlePlus", btn); 
      },
      btnClick(row, btn) {
        console.log('button clicked:', row, btn);
        // 这里可以添加按钮点击逻辑
        switch (btn.name) {
          case '详情':
            this.detailDialogVisible = true;
            // 转换 userType 为中文标签
            let userTypeLabels = [];
            if (row.userType) {
              const userTypeValues = row.userType.split(',').map(v => v.trim());
              userTypeLabels = userTypeValues.map(value => {
                const item = UserType.find(item => item.value === value);
                return item ? item.label : value;
              });
            }
            this.detailInfo = {
              ...row,
              userType: userTypeLabels.join(', '),
              children: [ ],
            };
            break;
          case '编辑':
            this.dialogVisible = true;
            this.form = { // 编辑表单
              ...row,
              userType: row.userType ? row.userType.split(',').map(v => v.trim()) : [] 
            }
            console.log(this.form)
            break;
          case '重置密码':
            // 这里可以添加重置密码逻辑
            break;
          case '停用':
            // 这里可以添加停用逻辑
            this.form = { // 编辑表单
              ...row
            }
            this.stopDialog = true;
            break;
          case '启用':
            // 这里可以添加停用逻辑
            this.form = { // 编辑表单
              ...row
            }
            this.stopDialog = true;
            break;
          case '删除':
            // 这里可以添加删除逻辑
            this.handleDelete(row)
            break; 
        }
      },
      async handleDelete(row){
        await this.$zdDialog({
          contImg: '',
          contTitle: '确定删除?',
          contDesc: '确定后，该账号会被删除, 是否继续?',
        })
        this.$api.DeleteTeacher({id:row.id}).then(res=>{
          this.initTableData()
        })
      }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .user-manage-page {
    padding: 20px;
    .top-tool{
      display: flex;
      align-items: center;
      margin-bottom: 20px;
  
      .serach-input{
        width: 300px;
        margin-right: 10px;
        ::v-deep .el-input__suffix{
          padding-right: 6px;
        }
      }
    }
  }
  
  ::v-deep .add-account-dialog{
    .el-select{
      width: 100%;
    }
  }
  
  </style>
  <style>
  
    .stop-dialog{
      width:500px;
      height:190px;
    }
  </style>