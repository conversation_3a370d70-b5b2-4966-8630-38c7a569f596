<template>
  <div class="school-manage-page" v-loading="loading">
    <!-- 条件渲染：根据currentView显示学校管理列表或学校详情 -->
    <template v-if="currentView === 'list'">
      <section class="top-tool">
        <el-input class="serach-input" clearable v-model="tableConfig.searchParams.searchWord" @blur="initTableData" placeholder="请搜索学校名称" suffix-icon="iconfont icon-sousuo"></el-input>
        <el-select class="serach-input" clearable v-model="tableConfig.searchParams.schoolType" placeholder="请选择学校类型">
          <el-option v-for="item in SchoolType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select class="serach-input" clearable v-model="tableConfig.searchParams.educationLevel" placeholder="请选择学历类型">
          <el-option v-for="item in educationLevel" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <addButton @addEvent="addCollege" />
        <exportButton />
        <importButton />
        <reflashButton @reflashEvent="reflashEvent" />
        
      </section>
      <table2 
        @selectionChange="selectionChange" 
        :notShowSearch="notShowSearch" 
        ref="tablePreview" 
        @selection-change="handleSelectionChange" 
        :selectable="tableConfig.selectable" 
        :data="tableConfig.tableData" 
        :columns="tableConfig.columns" 
        :queryFormConfig="queryConfig" 
        :total="tableConfig.total"
        :pagination="tableConfig.pagination" 
        :height="height" 
        :paginationLayout="tableConfig.paginationLayout" 
        :firstLoad="firstLoad" 
        :searchParams="tableConfig.searchParams" 
        @handleSearch="handleSearch">
        <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>
    </template>
    
    <!-- 显示学校详情组件 -->
    <template v-else-if="currentView === 'detail'">
      <schoolDetail :selectedSchool="selectedSchool" @backToList="backToList" />
    </template>
    
    <baseDialog :noFooter="true" :showToScreen="false" width="800px" :visible.sync="dialogVisible" :title="!form.id?'新增院校':'编辑院校'">
      <el-form ref="form" :model="form" label-width="100px" label-position="top" :rules="rules">
        <el-row :gutter="10">
          <el-col :span="12">
             <el-form-item label="单位类型" prop="schoolType">
                <el-radio-group v-model="form.schoolType" @change="handleChange">
                  <el-radio v-for="item in SchoolType" :label="item.value" :key="item.value">{{item.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item label="服务截止时间" prop="serviceDeadline">
              <el-date-picker
                v-model="form.serviceDeadline"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择服务截止时间"
              />
            </el-form-item> 
          </el-col>
        </el-row>
       
        <el-row :gutter="10">
          <el-col :span="12">
             <el-form-item label="学院名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入学院名称" maxLength="20"></el-input>
              </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="合作类型" prop="cooperationType">
                <el-select style="width:100%;" clearable filterable v-model="form.cooperationType" placeholder="请选择合作类型">
                  <el-option v-for="item in cooperationType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
              <el-form-item label="学历层次" prop="educationLevel">
                <el-select style="width:100%;" clearable filterable v-model="form.educationLevel" placeholder="请选择学历层次">
                  <el-option v-for="item in educationLevel" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="logo" prop="cooperationType">
                <el-upload
                  class="avatar-uploader"
                  :action="actionUrl"
                  :show-file-list="false"
                  :data="uploadData"
                  :headers="uploadHeaders"
                  :on-success="handleAvatarSuccess">
                  <img v-if="imageUrl" :src="imageUrl" class="avatar">
                  <i v-else class="el-icon-plus uploader-icon"></i>
                </el-upload>
                <span class="upload-tip">建议图片尺寸为 280x160px，支持格式：jpg、png</span>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
              <el-form-item label="联系人" prop="contact">
                <el-input v-model="form.contact" placeholder="请输入联系人"></el-input>
              </el-form-item>
          </el-col>
          <el-col :span="12">
              <el-form-item label="联系电话" prop="telephone">
                <el-input v-model="form.telephone" placeholder="请输入联系电话"></el-input>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="所在省份" prop="province">
              <el-select style="width:100%;" clearable filterable v-model="form.province" placeholder="请选择所在省份">
                <el-option v-for="item in provinceList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在城市" prop="region">
              <el-select style="width:100%;" clearable filterable v-model="form.region" placeholder="请选择所在城市">
                <el-option v-for="item in cityList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详细地址" prop="district">
          <el-input type="textarea" v-model="form.district" placeholder="请输入所在区县"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 

    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>

    <!-- 学校额度设置 -->
    <baseDialog :noFooter="true" title="学校额度设置" width="720px" :visible.sync="quotaDialogVisible">
      <el-form ref="quotaForm" :model="quotaForm" label-width="100px" label-position="top" :rules="quotaRules">
        <el-form-item label="分配学校 " prop="name">
          <el-input v-model="quotaForm.schoolName" disabled placeholder="学校名称"></el-input>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="分配教材总额度" prop="totalQuota">
              <el-input v-model="quotaForm.textbookCount" placeholder="请输入总名额"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item label="当前剩余额度" prop="usedQuota">
                <el-input v-model="quotaForm.textbookQuota" placeholder="请输入已用名额"></el-input>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
             <el-form-item label="管理端账号额度" prop="usedQuota">
          <el-input v-model="quotaForm.managementAccountQuota" placeholder="请输入"></el-input>
        </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item label="教师账号额度" prop="usedQuota">
              <el-input v-model="quotaForm.teacherAccountQuota" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="学生账号额度" prop="usedQuota">
          <el-input v-model="quotaForm.studentAccountQuota" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="quotaDialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="saveQuota" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog>
  </div>
</template>

<script>
import { SchoolType, educationLevel, cooperationType } from "@/constants/common.js";
import { mapGetters } from "vuex";
import token from "@/utils/token.js";
export default {
  name: "schoolPage",
  data() {
    // 定义自定义校验函数
    const validateTextbookQuota = (rule, value, callback) => {
      if (value > this.quotaInfo.avaQuota) {
        callback(new Error(`教材额度不能大于 ${this.quotaInfo.avaQuota}`));
      } else {
        callback();
      }
    };
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      height:'660px',
      SchoolType,
      educationLevel,
      cooperationType,
      provinceList: [
        { label: '北京市', value: '北京市' },
        { label: '上海市', value: '上海市' },
        { label: '天津市', value: '天津市' },
        { label: '重庆市', value: '重庆市' },
        { label: '河北省', value: '河北省' },
        { label: '山西省', value: '山西省' },
        { label: '辽宁省', value: '辽宁省' },
        { label: '吉林省', value: '吉林省' },
        { label: '黑龙江省', value: '黑龙江省' },
        { label: '江苏省', value: '江苏省' },
        { label: '浙江省', value: '浙江省' },
        { label: '安徽省', value: '安徽省' },
        { label: '福建省', value: '福建省' },
        { label: '江西省', value: '江西省' },
        { label: '山东省', value: '山东省' },
        { label: '河南省', value: '河南省' },
        { label: '湖北省', value: '湖北省' },
        { label: '湖南省', value: '湖南省' },
        { label: '广东省', value: '广东省' },
        { label: '海南省', value: '海南省' },
        { label: '四川省', value: '四川省' },
        { label: '贵州省', value: '贵州省' },
        { label: '云南省', value: '云南省' },
        { label: '陕西省', value: '陕西省' },
        { label: '甘肃省', value: '甘肃省' },
        { label: '青海省', value: '青海省' },
        { label: '内蒙古自治区', value: '内蒙古自治区' },
        { label: '广西壮族自治区', value: '广西壮族自治区' },
        { label: '西藏自治区', value: '西藏自治区' },
        { label: '宁夏回族自治区', value: '宁夏回族自治区' },
        { label: '新疆维吾尔自治区', value: '新疆维吾尔自治区' },
        { label: '台湾省', value: '台湾省' },
        { label: '香港特别行政区', value: '香港特别行政区' },
        { label: '澳门特别行政区', value: '澳门特别行政区' }
      ],
      cityList: [
        // 北京市
        { label: '北京', value: '北京' },
        // 上海市
        { label: '上海', value: '上海' },
        // 广东省
        { label: '广州', value: '广州' },
        { label: '深圳', value: '深圳' },
        { label: '佛山', value: '佛山' },
        { label: '东莞', value: '东莞' },
        { label: '珠海', value: '珠海' },
        { label: '汕头', value: '汕头' },
        { label: '韶关', value: '韶关' },
        { label: '河源', value: '河源' },
        { label: '梅州', value: '梅州' },
        { label: '惠州', value: '惠州' },
        { label: '汕尾', value: '汕尾' },
        { label: '东莞', value: '东莞' },
        { label: '中山', value: '中山' },
        { label: '江门', value: '江门' },
        { label: '阳江', value: '阳江' },
        { label: '湛江', value: '湛江' },
        { label: '茂名', value: '茂名' },
        { label: '肇庆', value: '肇庆' },
        { label: '清远', value: '清远' },
        { label: '潮州', value: '潮州' },
        { label: '揭阳', value: '揭阳' },
        { label: '云浮', value: '云浮' },
        // 江苏省
        { label: '南京', value: '南京' },
        { label: '无锡', value: '无锡' },
        { label: '徐州', value: '徐州' },
        { label: '常州', value: '常州' },
        { label: '苏州', value: '苏州' },
        { label: '南通', value: '南通' },
        { label: '连云港', value: '连云港' },
        { label: '淮安', value: '淮安' },
        { label: '盐城', value: '盐城' },
        { label: '扬州', value: '扬州' },
        { label: '镇江', value: '镇江' },
        { label: '泰州', value: '泰州' },
        { label: '宿迁', value: '宿迁' },
        // 浙江省
        { label: '杭州', value: '杭州' },
        { label: '宁波', value: '宁波' },
        { label: '温州', value: '温州' },
        { label: '嘉兴', value: '嘉兴' },
        { label: '湖州', value: '湖州' },
        { label: '绍兴', value: '绍兴' },
        { label: '金华', value: '金华' },
        { label: '衢州', value: '衢州' },
        { label: '舟山', value: '舟山' },
        { label: '台州', value: '台州' },
        { label: '丽水', value: '丽水' },
        // 四川省
        { label: '成都', value: '成都' },
        { label: '自贡', value: '自贡' },
        { label: '攀枝花', value: '攀枝花' },
        { label: '泸州', value: '泸州' },
        { label: '德阳', value: '德阳' },
        { label: '绵阳', value: '绵阳' },
        { label: '广元', value: '广元' },
        { label: '遂宁', value: '遂宁' },
        { label: '内江', value: '内江' },
        { label: '乐山', value: '乐山' },
        { label: '南充', value: '南充' },
        { label: '眉山', value: '眉山' },
        { label: '宜宾', value: '宜宾' },
        { label: '广安', value: '广安' },
        { label: '达州', value: '达州' },
        { label: '雅安', value: '雅安' },
        { label: '巴中', value: '巴中' },
        { label: '资阳', value: '资阳' },
        // 山东省
        { label: '济南', value: '济南' },
        { label: '青岛', value: '青岛' },
        { label: '淄博', value: '淄博' },
        { label: '枣庄', value: '枣庄' },
        { label: '东营', value: '东营' },
        { label: '烟台', value: '烟台' },
        { label: '潍坊', value: '潍坊' },
        { label: '济宁', value: '济宁' },
        { label: '泰安', value: '泰安' },
        { label: '威海', value: '威海' },
        { label: '日照', value: '日照' },
        { label: '临沂', value: '临沂' },
        { label: '德州', value: '德州' },
        { label: '聊城', value: '聊城' },
        { label: '滨州', value: '滨州' },
        { label: '菏泽', value: '菏泽' },
        // 河南省
        { label: '郑州', value: '郑州' },
        { label: '开封', value: '开封' },
        { label: '洛阳', value: '洛阳' },
        { label: '平顶山', value: '平顶山' },
        { label: '安阳', value: '安阳' },
        { label: '鹤壁', value: '鹤壁' },
        { label: '新乡', value: '新乡' },
        { label: '焦作', value: '焦作' },
        { label: '濮阳', value: '濮阳' },
        { label: '许昌', value: '许昌' },
        { label: '漯河', value: '漯河' },
        { label: '三门峡', value: '三门峡' },
        { label: '南阳', value: '南阳' },
        { label: '商丘', value: '商丘' },
        { label: '信阳', value: '信阳' },
        { label: '周口', value: '周口' },
        { label: '驻马店', value: '驻马店' },
        // 湖北省
        { label: '武汉', value: '武汉' },
        { label: '黄石', value: '黄石' },
        { label: '十堰', value: '十堰' },
        { label: '宜昌', value: '宜昌' },
        { label: '襄阳', value: '襄阳' },
        { label: '鄂州', value: '鄂州' },
        { label: '荆门', value: '荆门' },
        { label: '孝感', value: '孝感' },
        { label: '荆州', value: '荆州' },
        { label: '黄冈', value: '黄冈' },
        { label: '咸宁', value: '咸宁' },
        { label: '随州', value: '随州' },
        // 湖南省
        { label: '长沙', value: '长沙' },
        { label: '株洲', value: '株洲' },
        { label: '湘潭', value: '湘潭' },
        { label: '衡阳', value: '衡阳' },
        { label: '邵阳', value: '邵阳' },
        { label: '岳阳', value: '岳阳' },
        { label: '常德', value: '常德' },
        { label: '张家界', value: '张家界' },
        { label: '益阳', value: '益阳' },
        { label: '郴州', value: '郴州' },
        { label: '永州', value: '永州' },
        { label: '怀化', value: '怀化' },
        { label: '娄底', value: '娄底' },
        // 陕西省
        { label: '西安', value: '西安' },
        { label: '铜川', value: '铜川' },
        { label: '宝鸡', value: '宝鸡' },
        { label: '咸阳', value: '咸阳' },
        { label: '渭南', value: '渭南' },
        { label: '延安', value: '延安' },
        { label: '汉中', value: '汉中' },
        { label: '榆林', value: '榆林' },
        { label: '安康', value: '安康' },
        { label: '商洛', value: '商洛' },
        // 重庆市
        { label: '重庆', value: '重庆' },
        // 其他省份城市...
        // 安徽省
        { label: '合肥', value: '合肥' },
        { label: '芜湖', value: '芜湖' },
        { label: '蚌埠', value: '蚌埠' },
        { label: '淮南', value: '淮南' },
        { label: '马鞍山', value: '马鞍山' },
        // 福建省
        { label: '福州', value: '福州' },
        { label: '厦门', value: '厦门' },
        { label: '莆田', value: '莆田' },
        { label: '三明', value: '三明' },
        { label: '泉州', value: '泉州' },
        // 河北省
        { label: '石家庄', value: '石家庄' },
        { label: '唐山', value: '唐山' },
        { label: '秦皇岛', value: '秦皇岛' },
        { label: '邯郸', value: '邯郸' },
        // 山西省
        { label: '太原', value: '太原' },
        { label: '大同', value: '大同' },
        { label: '阳泉', value: '阳泉' },
        // 内蒙古自治区
        { label: '呼和浩特', value: '呼和浩特' },
        { label: '包头', value: '包头' },
        { label: '赤峰', value: '赤峰' },
        // 辽宁省
        { label: '沈阳', value: '沈阳' },
        { label: '大连', value: '大连' },
        { label: '鞍山', value: '鞍山' },
        // 吉林省
        { label: '长春', value: '长春' },
        { label: '吉林', value: '吉林' },
        // 黑龙江省
        { label: '哈尔滨', value: '哈尔滨' },
        { label: '齐齐哈尔', value: '齐齐哈尔' },
        // 江西省
        { label: '南昌', value: '南昌' },
        { label: '九江', value: '九江' },
        // 广西壮族自治区
        { label: '南宁', value: '南宁' },
        { label: '柳州', value: '柳州' },
        { label: '桂林', value: '桂林' },
        // 海南省
        { label: '海口', value: '海口' },
        { label: '三亚', value: '三亚' },
        // 贵州省
        { label: '贵阳', value: '贵阳' },
        { label: '遵义', value: '遵义' },
        // 云南省
        { label: '昆明', value: '昆明' },
        { label: '曲靖', value: '曲靖' },
        // 西藏自治区
        { label: '拉萨', value: '拉萨' },
        // 甘肃省
        { label: '兰州', value: '兰州' },
        { label: '嘉峪关', value: '嘉峪关' },
        // 青海省
        { label: '西宁', value: '西宁' },
        // 宁夏回族自治区
        { label: '银川', value: '银川' },
        // 新疆维吾尔自治区
        { label: '乌鲁木齐', value: '乌鲁木齐' },
        { label: '克拉玛依', value: '克拉玛依' }
      ],
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          // {
          //   label: "ID",
          //   prop: "id",
          // },
          {
            label: "学院名称",
            prop: "name",
          },
          {
            label: "LOGO",
            prop: "logo",
            formatter: (row, column, cellValue) => {
              return `<img src="${cellValue}" alt="lolo" style="width: 50px; height: 50px;">`;
            },
            formatters: (row, column, cellValue) => {
              return `<img src="${cellValue}" alt="lolo" style="width: 50px; height: 50px;">`;
            }
          },
          {
            label: "单位类型",
            prop: "schoolType",
            formatter: (row, column, cellValue) => {
              let target = this.SchoolType.find(v=>v.value==cellValue);
              return target.label;
            }

          },
          {
            label: "学历层次",
            prop: "educationLevel",
            formatter: (row, column, cellValue) => {
              let target = this.educationLevel.find(v=>v.value==cellValue);
              return target.label;
            }

          },
          {
            label: "合作类型",
            prop: "cooperationType",
            formatter: (row, column, cellValue) => {
              let target = this.cooperationType.find(v=>v.value==cellValue);
              return target.label;
            }

          },
          {
            label: "联系人",
            prop: "contact",
          },
          {
            label: "联系电话",
            prop: "telephone",
          },
          {
            label: "所在省份",
            prop: "province",
          },
          {
            label: "地区",
            prop: "region",
          },
          {
            label: "地址",
            prop: "address",
          },
          {
            label: "分配额度",
            prop: "textbookQuota",
          },
          {
            label: "管理账号额度",
            prop: "managementAccountQuota",
          },
          {
            label: "教师账号额度",
            prop: "teacherAccountQuota",
          },
          {
            label: "学生账号额度",
            prop: "studentAccountQuota",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
          {
            label: "服务截止时间",
            prop: "serviceDeadline",
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord:'',
          schoolType:'',
          educationLevel:'',
        },
        btnList: {
          detail: {
            enable: true
          }
        },
         // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
      },
     
      queryConfig: {},
      firstLoad: true,
      loading: false,
      actionBtns: [
        // // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '设置额度',class:'default-btn' },
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 220,
      dialogVisible:false,// 弹窗是否显示
      form:{ // 新增表单
        id:0, // 院校ID
        name:'', // 学校名称
        schoolType:'', // 单位类型
        educationLevel:'', // 学历类型
        cooperationType:'', // 合作类型
        contact:'',
        telephone:'',
        province:'',
        region:'',
        address:'',
        logo:'',
        serviceDeadline:'', // 服务截止时间
      },
      imageUrl:'',
      actionUrl:window.FILEIP, // 上传图片地址
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      quotaForm:{
        schoolId:'',
        schoolName:'',
        managementAccountQuota:'',
        teacherAccountQuota:'',
        studentAccountQuota:'',
        textbookCount:'',
        textbookQuota:'',
      },
      quotaDialogVisible:false,
      // 添加表单校验规则
      rules: {
        name: [
          { required: true,message: '学院名称不能为空', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('学院名称不能为空'));
              } else {
                callback();
              }
            } }
        ],
        // principalUserId: [
        //   { required: true,message: '负责人不能为空', trigger: 'change' }
        // ],
        phone: [
          { required: true,message: '联系电话不能为空', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        textbookQuota: [
          { required: true,message: '教材额度不能为空', trigger: 'blur' },
          // 添加自定义校验规则
          { validator: validateTextbookQuota, trigger: 'blur' } 
        ]
      },
      teacherList :[],
      quotaInfo:{}, // 额度
      detailInfo:{}, // 详情信息
      detailDialogVisible:false, //详情
      currentView: 'list', // 当前视图：'list' 或 'detail'
      selectedSchool: null, // 当前选中的学校信息
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/collegeManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
    schoolDetail: () => import('./detail.vue'),
  },
  mounted() {
    // 初始化表格数据
    // this.initTableData();
    this.getTeacherList();
   },
  methods: {
    // 初始化表格数据
    async initTableData(){
      this.loading = true;
      let res = await this.$api.SchoolGetList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    getTeacherList(){
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000,userType:'SchoolAdmin,SchoolEmployee,Teacher'}).then(res=>{
        this.teacherList = res.data.items.filter(v=> v.enabled);
      })
    },
    // 选择负责人
    selectPrincipal(){
      let target = this.teacherList.find(item=>item.id == this.form.principalUserId);
      this.form.principal = target.name;
      this.form.phone = target.phone;
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addCollege(type) {
      this.getCollegeQuotaInfo({isCreated:true,editCollegeId: 0});
      this.dialogVisible = true; 
      this.form = { // 新增表单
        id:0, // 院校ID
        name:'', // 名称
        schoolType:'', // 单位类型
        educationLevel:'', // 学历类型
        cooperationType:'', // 合作类型
        contact:'',
        telephone:'',
        province:'',
        region:'',
        address:'',
        logo:'',
        serviceDeadline:'', // 服务截止时间
      }
    },
    handleAvatarSuccess(file) { // 预览图片
      console.log(file);
      this.imageUrl = file.data; // 图片地址
      this.form.logo = file.data; // 图片地址

    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.SchoolUpdate(this.form).then(res=>{
              if(res.errCode==0){
                this.$message.success("更新成功")   
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.SchoolCreate(this.form).then(res=>{
            if(res.errCode==0){
                this.$message.success("新增成功")   
                this.dialogVisible = false;
                this.initTableData()
              }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    // 返回学校管理列表
    backToList() {
      this.currentView = 'list';
      this.selectedSchool = null;
      // 清理localStorage中的学校ID
      window.localStorage.removeItem('currentSchoolId');
      // 刷新列表数据
      this.initTableData();
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      this.tableConfig.searchParams = params;
      this.initTableData()
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async getCollegeQuotaInfo(params){
      let res = await this.$api.GetCollegeQuotaInfo(params)
      this.quotaInfo = res.data;
      this.form.totalQuota = `${res.data.avaQuota}/${res.data.totalQuota}`
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      switch (btn.name) {
        case '详情':
          // 存储当前选中的学校ID，供子组件使用
          window.localStorage.setItem('currentSchoolId', row.id);
          token.setSchoolId(row.id)
          // 设置当前选中的学校信息
          this.selectedSchool = row;
          // 切换到详情视图
          this.currentView = 'detail';
          break;
        case '编辑':
          this.form = { // 编辑表单
            id:row.id, // 院校ID
            name:row.name, // 名称
            schoolType:row.schoolType, // 单位类型
            educationLevel:row.educationLevel, // 学历类型
            cooperationType:row.cooperationType, // 合作类型
            contact:row.contact,
            telephone:row.telephone,
            province:row.province,
            region:row.region,
            address:row.address,
            logo:row.logo,
            serviceDeadline:row.serviceDeadline, // 服务截止时间
          }
          // 这里可以添加编辑逻辑
          this.getCollegeQuotaInfo({isCreated:false,editCollegeId:row.id})
          this.dialogVisible = true;
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
        case '设置额度':
          this.quotaForm = {
            schoolId:row.id,
            schoolName:row.name,
            managementAccountQuota:row.managementAccountQuota,
            teacherAccountQuota: row.teacherAccountQuota,
            studentAccountQuota: row.studentAccountQuota,
            textbookCount: row.textbookCount,
            textbookQuota:row.textbookQuota,
          }
          this.quotaDialogVisible = true;
          break; 
      }
      // 这里可以添加按钮点击逻辑
    },
    getMajorList(collegeId){
     
    },
    // 设置额度
    saveQuota(){
      this.$refs.quotaForm.validate((valid) => {
        if (valid) {
          this.$api.SchoolSetQuota({
            ...this.quotaForm,
          }).then(res=>{
            if(res.errCode==0){
              this.$message.success("设置成功")   
              this.quotaDialogVisible = false;
              this.initTableData()
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该学校会被删除, 是否继续?',
      })
      this.$api.SchoolDelete({id:row.id}).then(res=>{
        console.log("删除院校",res)
        this.initTableData()
      })

    }
  },
};
</script>

<style lang="scss" scoped>
.school-manage-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
  
}
</style>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.avatar {
  width: 120px;
  height: 80px;
  display: block;
}
</style>