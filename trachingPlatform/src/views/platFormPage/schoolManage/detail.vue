<template>
  <div class="school-detail-container">
    <!-- 页面头部 -->
    <div class="school-detail-header">
      <div class="school-name">{{ schoolName }}</div>
      <el-button type="text" @click="handleBackToList" class="back-button">返回</el-button>
    </div>
    
    <!-- 导航标签 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick" class="school-detail-tabs">
      <el-tab-pane label="院系管理" name="collegeManage">
        <collegeManage ref="collegeManage" />
      </el-tab-pane>
      <el-tab-pane label="专业管理" name="professionalManage">
        <professionalManage ref="professionalManage" />
      </el-tab-pane>
      <el-tab-pane label="班级管理" name="classManage">
        <classManage ref="classManage" />
      </el-tab-pane>
      <el-tab-pane label="教师管理" name="teacherManage">
        <teacherManage ref="teacherManage" />
      </el-tab-pane>
      <el-tab-pane label="学生管理" name="studentManage">
        <studentManage ref="studentManage" />
      </el-tab-pane>
      <el-tab-pane label="课程教材" name="courseTextbook">
        <div class="course-textbook-container">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="sub-tab-header">课程管理</div>
              <courseManage ref="courseManage" />
            </el-col>
            <el-col :span="12">
              <div class="sub-tab-header">教材管理</div>
              <textbookManage ref="textbookManage" />
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'schoolDetail',
  props: {
    selectedSchool: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      activeTab: 'collegeManage',
      schoolId: '',
      schoolName: ''
    };
  },
  components: {
    collegeManage: () => import('@/views/platFormPage/collegeManage/index.vue'),
    professionalManage: () => import('@/views/platFormPage/professionalManage/index.vue'),
    classManage: () => import('@/views/platFormPage/classManage/index.vue'),
    teacherManage: () => import('@/views/platFormPage/teacherManage/index.vue'),
    studentManage: () => import('@/views/platFormPage/studentManage/index.vue'),
    courseManage: () => import('@/views/platFormPage/courseManage/index.vue'),
    textbookManage: () => import('@/views/platFormPage/textbookManage/index.vue')
  },
  mounted() {
    // 从props中获取学校信息
    if (this.selectedSchool) {
      this.schoolId = this.selectedSchool.id;
      this.schoolName = this.selectedSchool.name;
    } else {
      // 兼容旧的路由参数方式
      this.schoolId = this.$route.query.schoolId || window.localStorage.getItem('currentSchoolId');
      this.schoolName = this.$route.query.schoolName || '学校详情';
    }
    
    // 确保学校ID存储在localStorage中，供子组件使用
    if (this.schoolId) {
      window.localStorage.setItem('currentSchoolId', this.schoolId);
    }
  },
  methods: {
    // 处理返回学校管理列表事件
    handleBackToList() {
      this.$emit('backToList');
    },
    
    // 处理标签切换事件
    handleTabClick(tab) {
      // 当切换到新标签时，可以在这里添加逻辑
      // 例如刷新当前标签的内容
      const tabName = tab.name;
      if (tabName === 'courseTextbook') {
        // 刷新课程和教材管理组件
        if (this.$refs.courseManage && this.$refs.courseManage.initTableData) {
          this.$refs.courseManage.initTableData();
        }
        if (this.$refs.textbookManage && this.$refs.textbookManage.initTableData) {
          this.$refs.textbookManage.initTableData();
        }
      } else if (this.$refs[tabName] && this.$refs[tabName].initTableData) {
        // 刷新其他单个组件
        this.$refs[tabName].initTableData();
      }
    }
  },
  // 监听学校信息变化
  watch: {
    selectedSchool: {
      handler(newVal) {
        if (newVal) {
          this.schoolId = newVal.id;
          this.schoolName = newVal.name;
          window.localStorage.setItem('currentSchoolId', newVal.id);
          // 刷新当前标签的内容
          this.handleTabClick({ name: this.activeTab });
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.school-detail-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 100px);
}

.school-detail-header {
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button {
  margin-right: 20px;
}

.school-name {
  display: inline-block;
  font-size: 20px;
  padding-left: 20px;
  font-weight: 600;
  color: #333;
}

.school-detail-tabs {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.course-textbook-container {
  padding: 10px 0;
}

.sub-tab-header {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  padding-left: 5px;
  border-left: 3px solid #0070FC;
}

// 确保子组件在详情页面中正常显示
::v-deep .el-tabs__content {
  padding-top: 20px;
}

::v-deep .el-tabs__item.is-active {
  color: #0070FC;
}

::v-deep .el-tabs__active-bar {
  background-color: #0070FC;
}


.college-manage-page{
  padding-top: 0;
}
</style>