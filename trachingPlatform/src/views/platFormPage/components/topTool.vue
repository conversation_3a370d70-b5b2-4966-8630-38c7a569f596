<template>
   <section class="top-tool">
      <el-input class="serach-input" v-model="keyWord" placeholder="请搜索学校名称/负责人/联系电话" suffix-icon="iconfont icon-sousuo"></el-input>
      <addButton @addEvent="addEvent" />
      <exportButton />
      <importButton />
    </section>
</template>

<script>
export default {
    data() {
       return {
          keyWord:''
       } 
    },
    props:{
      type:{
        type:String,
        default:''
      }
    },
    components:{
      addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
      exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
      importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    },
    methods:{
      addEvent(){ // 新增
        this.$emit('addEvent',this.type)
      },
      search(){ // 搜索
        this.$emit('search',this.keyWord)
      }
    }

}
</script>

<style lang="scss" scoped>
.top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
</style>