<template>
  <div class="plat-aside">
    <div class="work-plantfotm">
      <h3 @click="skipWork" :class="{ active: $route.path === '/platForm/workBranch' }">
        <i class=" iconfont icon-riFill-home-6-fill"></i>
        <p>工作台</p> 
      </h3>
    </div>

    <div v-for="(group, groupType) in groupedNavList" :key="groupType" class="nav-group">
      <h4 class="group-title">
        {{ getGroupTitle(groupType) }}
      </h4>
      <div 
        class="link-item" 
        v-for="(item, index) in group" 
        :key="index" 
        :class="{ active: isActive(item.path) }"
      >
        <router-link active-class="active" :to="item.path">
          <i :class="['iconfont', item.meta.icon]"></i> {{ item.meta.title }}
        </router-link>
      </div>
    </div>
    
  </div>
</template>

<script>
import eventBus from "@/utils/eventBus.js";
import platFormRouter from "@/router/module/platForm/platForm-router";
export default {
  name: "platFormPage",
  data() {
    return {
      navlist: [],
      groupedNavList: {} // 用于存储分组后的导航列表
    }; 
  },
  mounted() {
   this.handleRouter();
  },
  methods: {
    handleRouter(){
      this.navlist = platFormRouter[0].children.filter(v=>v.meta.type);
      this.groupNavList();
    },
    skipWork(){
      this.$router.push({path:'/platForm/workBranch'});
    },
    // 判断当前路由是否匹配，支持子路由
    isActive(path) {
      // 如果是学校管理，检查当前路径是否以学校管理路径开头（支持子路由）
      if (path === '/platForm/schoolManage') {
        return this.$route.path.startsWith('/platForm/schoolManage');
      }
      // 其他菜单项保持原有的精确匹配
      return this.$route.path === path;
    },
    // 对导航列表进行分组
    groupNavList() {
      this.groupedNavList = this.navlist.reduce((groups, item) => {
        const type = item.meta.type||'other';
        // 增加判断，如果类型是base_child则不参与分组
        if (type !== 'base_child') {
          if (!groups[type]) {
            groups[type] = [];
          }
          groups[type].push(item);
        }
        return groups;
      }, {});
    },
    // 获取分组标题
    getGroupTitle(type) {
      const titles = {
        base: '基础管理',
        resource: '资源管理',
        account: '账户管理',
        // system: '系统管理',
        other: '其他'
      };
      return titles[type] || '其他';
    }
  }
};
</script>

<style lang="scss" scoped>
.plat-aside {
  width: 240px;
  height: calc(100vh - 100px) ;
  background-color: #fff;
  padding: 20px 10px;

  .work-plantfotm{
    height: 46px;
    border-bottom: 1px solid #ccc;
    h3{
      font-size: 14px;
      font-weight: 500;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 38px;
      border-radius: 4px;
      cursor: pointer; 
      padding-left: 46px;
      margin: 0;
      &:hover{
        background: #0070FC;
        p{
          color: #fff;
        }
        .iconfont{
        color:#fff
        }
      }

      &.active{
        background: #0070FC;
        p{
          color: #fff;
        }
        .iconfont{
        color:#fff
        }
      }
      .iconfont{
        color:#C0C6D6
      }
      p{
        margin-left: 10px;
        line-height: 38px;
      }
    }
  }
  .up-title{
    text-align: center;
    border-bottom: 1px solid #ccc;
    line-height: 50px;
    margin:  0;
  }
  .nav-group {
    margin-top: 20px;
    
    .group-title {
      font-size: 13px;
      color: #A7AEBD;
      padding: 0 23px;
      margin-bottom: 10px;
    }
  }
  .link-item {
    text-align: left;
    margin-top: 10px;
    height: 38px;
    line-height: 38px;
    border-radius: 4px;
    padding-left:46px;
    // 为背景色和文字颜色添加过渡效果
    transition: background-color 0.3s ease, color 0.3s ease; 
    a {
      color: #333;
      text-decoration: none;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px; // 字体大小
      display: block; // 使链接块级元素，以便填充整个 li 元素
      .iconfont{
        color:#C0C6D6;
        transition: color 0.3s ease; 
      }
    }
    &:hover {
      background-color: #0070FC; // 鼠标悬停时的背景颜色
      cursor: pointer;
      a {
        color: #fff; // 鼠标悬停时的文字颜色;
        .iconfont{
          color:#fff;
        }
      }
    }
    &.active {
      background-color: #0070FC; // 激活时的背景颜色
      a {
        color: #fff; // 鼠标悬停时的文字颜色;
        .iconfont{
          color:#fff;
        }
      }
    }
    .iconfont{
      margin-right: 10px; // 图标与文字之间的间距
      font-size: 16px; // 图标大小
    }
  }
}
</style>