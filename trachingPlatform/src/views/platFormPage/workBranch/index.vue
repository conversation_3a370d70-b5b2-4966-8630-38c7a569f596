<template>
    <div class="plant-branch-page">
        <Notice />
        <div class="content">
            <div class="title">
                <p><i class="iconfont icon-jianzhu06-F"></i>管理统计 <span>全平台管理相关工作数据统计</span> </p>
            </div>
            <div class="base-data">
                <h4 class="part-label">基础数据</h4>
                <div class="data-box">
                    <div class="data-item" v-for="(item,index) in baseData1" :key="index">
                        <div class="data-label">
                            {{item.label}}
                            <el-tooltip class="item" :content="item.tip" placement="right">
                                <i v-if="item.tip" class="iconfont icon-wenhao"></i>
                            </el-tooltip>
                        </div>
                        <div class="data-value">{{item.value}}</div>
                    </div>
                    <div class="data-chart">
                        <div id="pieEcharts" style="width:342px;height:102px;"></div>
                    </div>
                </div>
                <div class="data-box" style="margin:20px 0;border:none">
                    <div class="data-item" v-for="(item,index) in baseData2" :key="index">
                        <div class="data-label">
                            {{item.label}}
                            <el-tooltip class="item" :content="item.tip" placement="right">
                                <i class="iconfont icon-wenhao"></i>
                            </el-tooltip>
                        </div>
                        <div class="data-value">{{item.value}}</div>
                    </div>
                </div>
            </div>
            <div class="data-static">
                <div class="textBook-data">
                    <h4 class="part-label">教材排行榜</h4>
                    <div id="bar1" style="width:760px;height:340px;">
                    </div>
                </div>
                <div class="user-data">
                    <h4 class="part-label">学校用户数排行榜</h4>
                    <div id="bar2" style="width:760px;height:340px;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            statisticsList:[],
            baseData1:[
                {
                    label: '合作学校',
                    value: 1560,
                    tip: '提示文字'
                },
                {
                    label: '中职学校',
                    value: 780,
                    tip: ''
                },
                {
                    label: '高职学校',
                    value: 520,
                    tip: ''
                },
                {
                    label: '本科学校',
                    value: 260,
                    tip: ''
                },
                {
                    label: '近七日新增院校',
                    value: 86,
                    tip: '' 
                }
            ],
            baseData2:[
                {
                    label: '创建学院数',
                    value: 46,
                    tip: '提示文字'
                },
                {
                    label: '教师数量',
                    value: 486,
                    tip: '提示文字' 
                }, 
                {
                    label: '学生数量',
                    value: 5860,
                    tip: '提示文字'
                },
                {
                    label: '班级数量',
                    value: 60,
                    tip: '提示文字'
                },
                {
                    label: '累计申请教材额度',
                    value: 586,
                    tip: '提示文字'
                },
                {
                    label: '累计已使用额度',
                    value: 68923,
                    tip: '提示文字'
                },
                {
                    label: '累计创建课程数量',
                    value: 1260,
                    tip: '提示文字'
                },
                {
                    label: '累计上架教材数',
                    value: 169,
                    tip: '提示文字' 
                }
            ],
            chartPie:null,
            pieOption: {
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: '6%',
                    left: 'right',
                    orient: 'vertical', // 设置图例垂直排列
                    textStyle: {
                        fontSize: 12, // 设置图例文字大小
                    },
                    itemWidth: 10, // 设置图例标记的宽度
                    itemHeight: 10, // 设置图例标记的高度
                    align: 'left' // 设置文字在右，色块在左
                },
                series: [
                    {
                    name: '学校统计',
                    type: 'pie',
                    radius: ['80%', '45%'],
                    avoidLabelOverlap: false,
                    // 修改标签配置，让标签显示在右侧
                    label: {
                        show: true,
                        position: 'right',
                        formatter: function(params) {
                            console.log('当前标签参数:', params); // 打印参数信息，方便调试
                            return params.name;
                        },
                        color: function(params) {
                            const colorMap = {
                                '中职': '#FFB573',
                                '高职': '#759FF8',
                                '本科': '#6FDEB4'
                            };
                            console.log('当前类别名称:', params.name); // 打印类别名称
                            return colorMap[params.name] || '#333';
                        }
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 20,
                            fontWeight: 'bold'
                        }
                    },
                    // 调整标签线配置
                    labelLine: {
                        show: true,
                        length: 18,
                        length2: 10
                    },
                    data: [
                        { value: 580, name: '本科',itemStyle: { color: '#6FDEB4' } },
                        { value: 1048, name: '中职',itemStyle: { color: '#FFB573' } },
                        { value: 735, name: '高职',itemStyle: { color: '#759FF8' } },
                    ]
                    }
                ]  
            },
            chartBar1:null,
            bar1Option: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                    }
                },
              
                legend: {
                    data: ['申请额度', '使用额度'],
                    left: 'right', // 设置图例水平居中
                    top: '5%', // 设置图例距离顶部 5%
                    textStyle: {
                        color: '#666' // 设置图例文字颜色
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    // 可以单独为每个图例项设置颜色
                    pageIconColor: '#409EFF', // 分页图标颜色
                    pageTextStyle: {
                        color: '#666' // 分页文字颜色
                    },
                    // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                    // 如果要单独设置每个图例标记颜色，可结合 series 数据
                    // 下面这种方式在实际使用中可以结合 series 对应数据
                    data: [
                        {
                            name: '申请额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#759FF8' // 单独设置 '申请额度' 文字颜色
                            }
                        },
                        {
                            name: '使用额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#6FDEB4' // 单独设置 '使用额度' 文字颜色
                            }
                        }
                    ]
                },
                xAxis: [
                    {
                    type: 'category',
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    axisPointer: {
                        type: 'shadow'
                    }
                    }
                ],
                yAxis: [
                    {
                    type: 'value',
                    name: '额度',
                    min: 0,
                    max: 250,
                    interval: 50,
                    axisLabel: {
                        formatter: '{value} '
                    }
                    },
                ],
                series: [
                    {
                    name: '申请额度',
                    type: 'bar',
                    barWidth: '20px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                     // 设置申请额度柱子颜色
                     itemStyle: {
                        color: '#759FF8'
                    },
                    data: [
                        2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                    ]
                    },
                    {
                    name: '使用额度',
                    type: 'bar',
                    barWidth: '20px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                     // 设置使用额度柱子颜色
                     itemStyle: {
                        color: '#6FDEB4'
                    },
                    data: [
                        2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                    ]
                    },
                ]
            },
            chartBar2:null,
            bar2Option: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                    }
                },
                legend: {
                    data: ['申请额度', '使用额度'],
                    left: 'right', // 设置图例水平居中
                    top: '5%', // 设置图例距离顶部 5%
                    textStyle: {
                        color: '#666' // 设置图例文字颜色
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    // 可以单独为每个图例项设置颜色
                    pageIconColor: '#409EFF', // 分页图标颜色
                    pageTextStyle: {
                        color: '#666' // 分页文字颜色
                    },
                    // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                    // 如果要单独设置每个图例标记颜色，可结合 series 数据
                    // 下面这种方式在实际使用中可以结合 series 对应数据
                    data: [
                        {
                            name: '申请额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#FFB573' // 单独设置 '申请额度' 文字颜色
                            }
                        },
                        {
                            name: '使用额度',
                            icon: 'roundRect',
                            textStyle: {
                                color: '#DA6FDC' // 单独设置 '使用额度' 文字颜色
                            }
                        }
                    ]
                },
                xAxis: [
                    {
                    type: 'category',
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    axisPointer: {
                        type: 'shadow'
                    }
                    }
                ],
                yAxis: [
                    {
                    type: 'value',
                    name: '额度',
                    min: 0,
                    max: 250,
                    interval: 50,
                    axisLabel: {
                        formatter: '{value} '
                    }
                    },
                ],
                series: [
                    {
                    name: '申请额度',
                    type: 'bar',
                    barWidth: '20px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                    // 设置申请额度柱子颜色
                    itemStyle: {
                        color: '#FFB573'
                    },
                    data: [
                        2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                    ]
                    },
                    {
                    name: '使用额度',
                    type: 'bar',
                    barWidth: '20px',
                    tooltip: {
                        valueFormatter: function (value) {
                        return value;
                        }
                    },
                    // 设置使用额度柱子颜色
                    itemStyle: {
                        color: '#DA6FDC'
                    },
                    data: [
                        2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                    ]
                    },
                ]
            }
        };
    },
    components: {
       Notice: ()=>import('./components/notice.vue'),
    },
    mounted() {
        this.initBar();
    },
    methods: {
        /**
         * 初始化饼状图
         */
        initBar() {
            this.chartPie = echarts.init(document.getElementById("pieEcharts"));
            this.chartPie.setOption(this.pieOption);

            this.chartBar1 = echarts.init(document.getElementById("bar1"));
            this.chartBar1.setOption(this.bar1Option);

            this.chartBar2 = echarts.init(document.getElementById("bar2"));
            this.chartBar2.setOption(this.bar2Option);

        },
        
    }
}
</script>
<style lang="scss">
.plant-branch-page{
    background-color: #F6F8FA;
    height: 100%;
    overflow: hidden;
    .content{
       padding: 0 20px;
        .title{
            font-size: 16px;
            color: #333;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            i{
                color: #409EFF;
                font-size: 20px;
                margin-right: 10px;   
            }
            span{
             font-family: PingFangSC, PingFang SC;
             font-weight: 400;
             font-size: 14px;
             margin-left: 8px;
             color: #999999;
            }
        }
        .part-label{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            position: relative;
            padding-left: 12px;
            margin: 0px;
            &::before{
                content: "";
                display: inline-block;
                position: absolute;
                width: 3px;
                height: 12px;
                background: #0070FC;
                border-radius: 2px;
                left: 0;
                top: 3px;
            }
        }
       .base-data{
            height: 328px;
            background: #FFFFFF;
            padding: 20px;
            .data-box{
                display: flex;
                justify-content: flex-start;
                margin-top: 10px;
                padding-bottom: 20px;
                border-bottom: 1px solid #F2F3F5;
            }
           .data-item{
                width: 216px;
                height: 100px;
                background: #FBFBFB;
                border-radius: 4px; 
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                padding-left: 20px;
                margin-right: 15px;
                .data-label{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    margin-bottom: 4px;
                }
                .data-value{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 26px;
                    color: #0070FC;
                }
           }

           .data-chart{
                width:243px;
                height:102px
           }
       }
       .data-static{
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        .textBook-data{
            width: 50%;
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
        }
        .user-data{
            width: 49%;
            height: 392px;
            padding: 20px;
            background: #FFFFFF;
        }
       }
    }
}
</style>