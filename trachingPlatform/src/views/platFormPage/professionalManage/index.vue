<template>
  <div class="major-manage-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @change="initTableData()" @blur="initTableData()" clearable placeholder="请搜索学校名称/负责人/联系电话" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select style="margin-right:10px;" filterable clearable @change="initTableData()" v-model="tableConfig.searchParams.collegeId" placeholder="请选择院校">
        <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addMajor" />
      <exportButton />
      <importButton />
      <reflashButton @reflashEvent="reflashEvent" />
    </section>
    <!-- :max-height="height"  -->
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :height="height" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>
    <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" :title="!form.id?'新增专业':'编辑专业'">
      <el-form ref="form" :model="form" label-width="80px"  label-position="top" :rules="rules">
        <el-form-item label="专业名称" prop="name">
          <el-input v-model="form.name" maxLength="20" placeholder="请输入专业名称"></el-input>
        </el-form-item>
        <el-form-item label="所属学院" prop="collegeId">
          <el-select style="width:100%;" v-model="form.collegeId" placeholder="请选择" @change="selectCollege">
            <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="principal">
          <el-select style="width:100%;" clearable filterable v-model="form.principalUserId" placeholder="请选择负责人" @change="selectPrincipal">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="教材额度" prop="textbookQuota">
          <el-input v-model="form.textbookQuota" type="number" placeholder="请输入教材额度" :max="quotaInfo.avaQuota"></el-input>
        </el-form-item>
        <el-form-item label="剩余额度/二级学院总额度">
          <el-input :disabled="true" v-model="form.totalQuota"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo"/>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>

  </div>
</template>

<script>
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数

export default {
  name: "schoolPage",
  data() {
    // 定义自定义校验函数
    const validateTextbookQuota = (rule, value, callback) => {
      if (value > this.quotaInfo.avaQuota) {
        callback(new Error(`教材额度不能大于 ${this.quotaInfo.avaQuota}`));
      } else {
        callback();
      }
    };
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      // 补全模板中使用的变量
      detailInfo:{}, // 详情信息
      height:'660px', // 表格高度
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          // },{
          //   label: "ID",
          //   prop: "id",
          },{
            label: "专业名称",
            prop: "name",
          },{
            label: "所属学院",
            prop: "collegeId",
            formatter:(row) => {
              let target = this.collegeList.find(item => item.id == row.collegeId);
              return target?.name; // 这里可以根据需要返回不同的内容
            }
          },
          {
            label: "负责人",
            prop: "principal",
          },
          {
            label: "联系方式",
            prop: "phone",
          },
          {
            label: "教材额度",
            prop: "textbookQuota",
          },
          {
            label: "教材数量",
            prop: "textbookCount",
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter:(row) => {
              return formatISOString(row.createTime); // 这里可以根据需要返回不同的内容 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord:'',
          collegeId:'', // 院校id
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
      },
      // 补全模板中使用的变量
      queryConfig: {},
      total: 0,
      firstLoad: true,
      loading: false,
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 150,
      dialogVisible: false, // 新增专业弹窗
      form: { // 新增专业表单
        id:0,
        name: "",
        collegeId: '',
        collegeName:'',
        principal:'',
        principalUserId: '',
        phone: "",
        textbookQuota: 0,
        // textbookCount:0, // 教材数量
        totalQuota:''
      },
      collegeList:[],// 学院列表
      quotaInfo:{ // 额度信息
        avaQuota:0, // 可用额度
        totalQuota:0, // 总额度
      },
      rules: {
        name: [
          { required: true,message: '专业名称不能为空', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('专业名称不能为空'));
              } else {
                callback();
              }
            } }
        ],
        collegeId: [
          { required: true,message: '所属学院不能为空', trigger: 'blur' }
        ],
        // principal: [
        //   { required: true,message: '负责人不能为空', trigger: 'blur' }
        // ],
        phone: [
          { required: true,message: '联系电话不能为空', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        textbookQuota: [
          { required: true,message: '教材额度不能为空', trigger: 'blur' },
          // 添加自定义校验规则
          { validator: validateTextbookQuota, trigger: 'blur' }
        ]
      },
      teacherList :[],// 教师列表
      allPersonList:[],//
      detailDialogVisible:false, //详情
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/collegeManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    this.getCollegeList() // 获取学院列表
    this.initTableData() // 初始化表格数据
    this.getTeacherList() // 初始化负责人
  },
  methods: {
    // 初始化表格数据
    async initTableData(){
      this.loading = true; // 显示加载中
      let res = await this.$api.GetMajorList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false; // 隐藏加载中 
      },300)
    },
    getTeacherList(val){
      if(val){
        this.$api.GetTeacherList({pageIndex:1,pageSize:1000,collegeId:val,userType:'SchoolAdmin,SchoolEmployee,Teacher'}).then(res=>{
          let list = res.data.items.filter(v=> v.enabled)
          this.teacherList = this.allPersonList.concat(list)
        })
      }else{
        this.$api.GetTeacherList({pageIndex:1,pageSize:1000,userType:'SchoolAdmin,SchoolEmployee'}).then(res=>{
          this.allPersonList = res.data.items.filter(v=> v.enabled);
          this.teacherList = res.data.items.filter(v=> v.enabled);
        })
      }
     
    },
    // 获取专业额度信息
    async GetMajorQuotaInfo(params){
      let res = await this.$api.GetMajorQuotaInfo(params)
      this.quotaInfo = res.data;
      this.form.totalQuota = `${res.data.avaQuota}/${res.data.totalQuota}`
    },
    // 选择负责人
    selectPrincipal(){
      let target = this.teacherList.find(item=>item.id == this.form.principalUserId);
      this.form.principal = target.name;
      this.form.phone = target.phone;
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
        // if(this.collegeList.length != 0){ // 如果没有学院，就不显示筛选
        //   this.tableConfig.searchParams.collegeId = this.collegeList[0].id; // 默认选中第一个
        // }
      })
    },
    selectCollege(val){ // 选择学院
      let target = this.collegeList.find(item=>item.id == this.form.collegeId);
      this.form.collegeName = target.name;
      this.GetMajorQuotaInfo({isCreated:true,collegeId:this.form.collegeId,editMajorId:0});
      this.form.principal = ''; // 清空负责人
      this.form.principalUserId = ''; // 清空负责人id
      this.getTeacherList(val) // 获取教师列表
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addMajor(){ // 新增专业
      this.dialogVisible = true; // 打开弹窗
      this.form = { // 重置表单
        id:0,
        name: "",
        collegeId: '',
        collegeName:'',
        principal:'',
        principalUserId: '',
        phone: "",
        textbookQuota: 0,
        totalQuota:''
      }
    },
    save(){
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        // 这里可以添加保存逻辑
        if(this.form.id){ // 编辑
          this.$api.UpdateMajor(this.form).then(res=>{
            if(res.errCode==0){
              this.$message({ type: 'success', message: '更新成功' });
              this.$refs.form.resetFields(); // 重置表单
              this.dialogVisible = false;
              this.initTableData()
            }
          })
          return;
        }
        this.$api.CreateMajor(this.form).then(res=>{
          if(res.errCode==0){
            this.$message({ type:'success', message: '新增成功' });
            console.log("专业详情",res)
            this.$refs.form.resetFields(); // 重置表单
            this.dialogVisible = false;
            this.initTableData()
          }
        })
      });
     
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
      this.tableConfig.searchParams = params;
      this.initTableData();
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async btnClick(row, btn) {
      // console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '详情':
          this.detailDialogVisible = true;
           // 获取学院下的专业列表
           this.detailInfo = {
              ...row,
              children:[
                // {
                //   title:'关联教材',
                //   list:row.textbooks,
                //   name:'secend',
                //   columns:[
                //     {
                //       label: "序号",
                //       type: "index",
                //       width: 80,
                //     },
                //     {
                //       label: "教材名称",
                //       prop: "name",
                //     }, 
                //     {
                //       label: "专业大类",
                //       prop: "majorCategory",
                //     },
                //     {
                //       label: "主编",
                //       prop: "publishedChiefEditor",
                //     },
                //   ]
                // }
              ]
            }
          break;
        case '编辑':
           this.form ={
            ...row,
            totalQuota:''
          }
          this.GetMajorQuotaInfo({isCreated:false,collegeId:row.collegeId,editMajorId:row.id});
          this.dialogVisible = true;
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该专业会被删除, 是否继续?',
      })
      this.$api.DeleteMajor({id:row.id}).then(res=>{
        console.log("删除专业",res)
        this.initTableData()
      })

    }
  },
};
</script>

<style lang="scss" scoped>
.major-manage-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}
</style>