<template>
    <div class="permission-manage-page" v-loading="loading">
        <div class="left-nav">
          <div class="top-btn">
            <el-button @click="addGroup(1)" class="add-btn"><i class="iconfont icon-xinjian"></i> 新增分组</el-button>
            <el-button @click="addGroup(2)" class="add-btn"><i class="iconfont icon-xinjian"></i> 新增角色</el-button>
          </div>
          <div>
            <p class="all-tip">全部</p>
            <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick">
              <template slot-scope="{ data }">
                <span class="tree-node-content">
                  <span>{{ data.name }}</span>
                  <span class="tree-node-actions" style="margin-left: 10px;">
                    <i class="iconfont icon-bianji" @click.stop="editGroup(data)" title="编辑"></i>
                    <i class="iconfont icon-shanchu" @click.stop="deleteGroup(data)" title="删除"></i>
                  </span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
        <div class="right-content">
            <div class="top-tool">
              <el-input class="search-input" v-model="tableConfig.searchParams.keyword" placeholder="请输入角色名称" clearable></el-input>
              <el-select class="search-select" v-model="tableConfig.searchParams.enable" placeholder="请选择状态">
                <el-option v-for="item in enableList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <addButton/>
              <reflashButton/>
              <exportButton/>
              <importButton/>
            </div>
            <table2 
              @selectionChange="selectionChange" 
              :notShowSearch="notShowSearch" 
              ref="tablePreview" 
              @selection-change="handleSelectionChange" 
              :selectable="tableConfig.selectable" 
              :data="tableConfig.tableData" 
              :columns="tableConfig.columns" 
              :queryFormConfig="tableConfig.queryConfig" 
              :total="tableConfig.total"
              :pagination="tableConfig.pagination" 
              :paginationLayout="tableConfig.paginationLayout" 
              :firstLoad="firstLoad" 
              :height="height" 
              :searchParams="tableConfig.searchParams" 
              @handleSearch="handleSearch">
              <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
              <template #operate>
                <el-table-column v-if="tableConfig.operateBtns && tableConfig.operateBtns.length" label="操作" fixed="right" :width="200">
                  <template slot-scope="scope">
                    <el-button v-for="(item, index) in tableConfig.operateBtns" type="text"
                              v-show="isShowOperateBtn(scope, item)"
                              :class="item.class || ''"
                              @click="btnClick(scope.row, item)" :key="index"
                              :disabled="scope.row.dataLocked">
                      {{ item.name }}
                    </el-button>
                  </template>
                </el-table-column>
              </template>
            </table2>
        </div>

        <!-- 新增分组 -->
        <baseDialog :noFooter="true" title="新增分组" width="500px" :visible.sync="addGroupDialogVisible">
          <el-form :model="groupForm" :rules="rules" ref="formRef" label-width="120px">
            <el-form-item label="分组名称" prop="name">
              <el-input v-model="groupForm.name" placeholder="请输入分组名称" />
            </el-form-item>
          </el-form>
          <div style="text-align:right;margin-bottom:0;margin-top:40px;">
            <el-button type="default" @click="addGroupDialogVisible = false;">取消</el-button>
            <el-button type="primary" @click="submitForm()">确定</el-button>
          </div>
        </baseDialog>

        <!-- 新增角色 -->
         <baseDialog :noFooter="true" :title="isViewDetail ? '角色详情' : '新增角色'" width="500px" :visible.sync="roleDialogVisible">
          <el-form class="role-form" :model="roleForm" :rules="roleRules" ref="roleFormRef" label-width="120px">
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="roleForm.name" placeholder="请输入角色名称" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="角色编码" prop="code">
              <el-input v-model="roleForm.code" placeholder="请输入角色编码" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="角色标识" prop="identity">
              <el-input v-model="roleForm.identity" placeholder="请输入角色标识" :disabled="isViewDetail" />
            </el-form-item>
            <el-form-item label="所属分组" prop="groupId">
              <el-select v-model="roleForm.groupId" placeholder="请选择所属分组" :disabled="isViewDetail">
                <el-option v-for="group in data" :key="group.id" :label="group.name" :value="group.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否启用" prop="enable">
              <el-switch v-model="roleForm.enable" active-text="启用" inactive-text="禁用" :disabled="isViewDetail" />
            </el-form-item>
          </el-form>
          <div style="text-align:right;margin-bottom:0;margin-top:40px;">
            <el-button type="default" @click="handleRoleDialogClose">取消</el-button>
            <el-button v-if="!isViewDetail" type="primary" @click="submitRoleForm()">确定</el-button>
          </div>
        </baseDialog>
    </div>
</template>
  
<script>
  import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
import { getToken } from "@/utils/token";
  
  export default {
    name: "permissionManagePage",
    data() {
      return {
        loading:false,
        data:[],
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        addGroupDialogVisible:false,
        isEdit: false,
        isEditRole: false,
        isViewDetail: false,
        groupForm:{
          name:'',
          parentId:0,
        },
        roleDialogVisible:false,
        roleForm:{
          groupId: "",
          code: "",  // 角色编码
          name: "",  // 角色名称
          identity: "", // 角色标识
          enable: true, // 是否启用
        },
        rules:{
          name:[
            { required: true, message: '请输入分组名称', trigger: 'blur' },
          ],
        },
        roleRules:{
          name:[
            { required: true, message: '请输入角色名称', trigger: 'blur' },
          ],
          code:[
            { required: true, message: '请输入角色编码', trigger: 'blur' },
          ],
          identity:[
            { required: true, message: '请输入角色标识', trigger: 'blur' },
          ],
          groupId:[
            { required: true, message: '请选择所属分组', trigger: 'change' },
          ],
        },
        notShowSearch: false,
        firstLoad: true,
        height: 560,
        enableList:[
          {
            value:true,
            label:'启用',
          },
          {
            value:false,
            label:'禁用',
          },
        ],
        tableConfig:{
          tableData:[],
          columns:[
            {
              prop: 'id',
              label: 'ID',
            },
            {
              prop: 'name',
              label: '角色名称',
              width: '120'
            },
            {
              prop: 'code',
              label: '角色编码',
              width: '120'
            },
            {
              prop: 'identity',
              label: '角色标识',
              width: '120'
            },
            {
              prop: 'enable',
              label: '是否启用',
              width: '120',
              formatter:(row)=>{
                return row.enable?'启用':'禁用';
              }
            },
            {
              prop: 'groupName',
              label: '所属分组',
              width: '110',
            },
            {
              prop: 'createTime',
              label: '创建时间',
            },
            {
              prop: 'isDeleted',
              label: '是否删除',
              width: '110',
              formatter:(row)=>{
                return row.isDeleted?'已删除':'未删除';
              }
            },
          ],
          queryConfig:{
            keyword:'',
            enable:true,
            groupId:0,
          },
          total:0,
          pagination:{
            currentPage:1,
            pageSize:10,
            total:0,
          },
          paginationLayout:'total, sizes, prev, pager, next, jumper',
          selectable:true,
          operateBtns:[
            { name: '详情',class:'default-btn' },
            { name: '编辑',class:'default-btn' },
            { name: '删除',class:'del-btn' }
          ],
          searchParams:{
            keyword:'',
            enable:true,
            groupId:0,
          },
          height:560,
        }
      }; 
    },
    components: {
      // 补全模板中使用的组件
      addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
      reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
      exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
      importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
      empty:()=>import("@/components/base/empty.vue"),
      baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    },
    mounted() {
      this.getGroupList();
      this.getRoleList();
    },
    methods: {
      // 获取分组列表
      getGroupList(){
        this.$api.RoleGetGroupList({}).then(res=>{
          if(res.errCode == 0){
            this.data = res.data;
          }
        })
      },
      // 获取角色列表
      getRoleList(){
        this.$api.RoleGetRoleList(this.tableConfig.searchParams).then(res=>{
          if(res.errCode == 0){
            this.tableConfig.tableData = res.data;
          }
        })
      },
      handleNodeClick(data){
        this.tableConfig.searchParams.groupId = data.id;
        this.getRoleList()
      },
      handleSearch(params){
        this.tableConfig.searchParams = params;
      },
      handleSelectionChange(val){
        console.log(val);
      },
      selectionChange(val){
        // 处理选择变化
        console.log('Selection changed:', val);
      },
      isShowOperateBtn(scope, item) {
        // 这里可以添加操作按钮显示逻辑
        return true;
      },
      btnClick(row, item) {
        console.log(row, item);
        // 按钮点击逻辑
        switch (item.name) {
          case '详情':
            this.showRoleDetail(row);
            break;
          case '编辑':
            this.editRole(row);
            break;
          case '删除':
            this.handleDelete(row)
            break; 
        }
      },
      // 查看角色详情
      showRoleDetail(row) {
        this.isViewDetail = true;
        this.roleForm = {
          id: row.id,
          groupId: row.groupId,
          code: row.code,
          name: row.name,
          identity: row.identity,
          enable: row.enable
        };
        this.roleDialogVisible = true;
      },
      
      // 处理角色弹窗关闭
      handleRoleDialogClose() {
        this.roleDialogVisible = false;
        this.isViewDetail = false;
      },
      // 编辑角色
      editRole(row) {
        this.isEditRole = true;
        this.roleForm = {
          id: row.id,
          groupId: row.groupId,
          code: row.code,
          name: row.name,
          identity: row.identity,
          enable: row.enable
        };
        this.roleDialogVisible = true;
      },
      async handleDelete(row){
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '确定后，该角色会被删除, 是否继续?',
          })
          this.$api.RoleDeleteRole({id:row.id}).then(res=>{
            if(res.errCode == 0){
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getRoleList();
            }
          })
      },
      // 新增分组或角色
      addGroup(type){
        if(type === 1){
          this.isEdit = false;
          this.groupForm = {
            name:'',
            parentId:0,
          };
          this.addGroupDialogVisible = true;
        } else if(type === 2){
          this.addRole();
        }
      },
      
      // 编辑分组
      editGroup(data){
        this.isEdit = true;
        this.groupForm = {
          id: data.id,
          name: data.name,
          parentId: data.parentId || 0,
        };
        this.addGroupDialogVisible = true;
      },
      
      // 删除分组
      async deleteGroup(data){
        try {
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '确定后，该分组及其下所有角色会被删除, 是否继续?',
          });
          
          // 调用删除分组的API
          this.$api.RoleDeleteGroup({id: data.id}).then(res => {
            if(res.errCode == 0){
              this.$message({
                message: '删除成功',
                type: 'success'
              });
              this.getGroupList();
              this.getRoleList();
            }
          });
        } catch (error) {
          // 用户取消删除
          console.log('取消删除');
        }
      },
      submitForm(){
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            if (this.isEdit) {
              // 编辑分组
              this.$api.RoleUpdateGroup(this.groupForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '编辑成功',
                    type: 'success'
                  });
                  this.addGroupDialogVisible = false;
                  this.$refs.formRef.resetFields();
                  this.getGroupList();
                }
              })
            } else {
              // 新增分组
              this.$api.RoleCreateGroup(this.groupForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '新增成功',
                    type: 'success'
                  });
                  this.addGroupDialogVisible = false;
                  this.$refs.formRef.resetFields();
                  this.getGroupList();
                }
              })
            }
          } else {
            this.$message({
              message: '请填写完整信息',
              type: 'error'
            });
          }
        });
      },
      // 新增角色
      addRole(){
        // 重置角色表单
        this.isEditRole = false;
        this.roleForm = {
          groupId: "",
          code: "",
          name: "",
          identity: "",
          enable: true,
        };
        this.roleDialogVisible = true;
      },
      // 提交角色表单
      submitRoleForm(){        
        this.$refs.roleFormRef.validate((valid) => {
          if (valid) {
            if (this.isEditRole) {
              // 编辑角色
              this.$api.RoleUpdateRole(this.roleForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '角色编辑成功',
                    type: 'success'
                  });
                  this.roleDialogVisible = false;
                  this.isEditRole = false;
                  this.getRoleList();
                }
              });
            } else {
              // 新增角色
              this.$api.RoleCreateRole(this.roleForm).then(res=>{
                if(res.errCode == 0){
                  this.$message({
                    message: '角色新增成功',
                    type: 'success'
                  });
                  this.roleDialogVisible = false;
                  // 刷新角色列表
                  this.getGroupList();
                }
              });
            }
          } else {
            this.$message({
              message: '请填写完整信息',
              type: 'error'
            });
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
    .permission-manage-page{
      display: flex;
      justify-content: space-between;
      height: 100%;
      padding: 20px;
      background: #F6F8FA;
      padding-bottom: 0;
      box-sizing: border-box;
      .left-nav{
        width:240px;
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        padding:10px;
        border-right:1px solid #E7E7E7;
        .top-btn{
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
        .add-btn{
          width: 105px;
          height: 38px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #E7E7E7;
          padding:0;
          color: #333333;
        }

         .all-tip{
          height: 38px;
          line-height:38px;
          background: #F1F7FF;
          border-radius: 4px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #026CFF;
          text-align: left;
          font-style: normal;
          padding-left:20px;
          margin-top:12px;
          margin-bottom:10px;
        }

        ::v-deep .el-tree-node__content{
          height: 38px;
          line-height:38px;
        }
        
        .tree-node-content{
          width: calc(100% - 30px);
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          font-size:14px;
          color:#333;
        }
        
        .tree-node-actions{
          visibility: hidden;
        }
        
        ::v-deep .el-tree-node__content:hover .tree-node-actions{
          visibility: visible;
        }
        
        .iconfont{
          margin: 0 5px;
          cursor: pointer;
          font-size: 14px;
        }
        
        .icon-bianji{
          color: #409EFF;
          font-size: 14px;
        }
        
        .icon-shanchu{
          color: #F56C6C;
          font-size: 18px;
        }
      }

      .right-content{
        flex: 1;
        height: 100%;
        background: #fff;
        box-sizing: border-box;
        padding: 20px;
        .top-tool{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .search-input{
            width: 300px;
            margin-right:10px;
          }
          .search-select{
            width: 220px;
            margin-right:10px;
          }
        }
       
      }
    }

    .role-form{
      ::v-deep .el-form-item{
        margin-bottom: 20px!important;
      }
    }
  </style>