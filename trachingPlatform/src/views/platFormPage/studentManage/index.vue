<template>
  <div class="student-manage-page" v-loading="loading">

    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @blur="initTableData()" clearable placeholder="请搜索学生姓名/联系电话" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select @change="selectTopCollege" style="width:220px;margin-right:10px;" clearable filterable v-model="tableConfig.searchParams.collegeId" placeholder="请选择院校">
        <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-select @change="selectTopMajor" style="width:220px;margin-right:10px;" clearable filterable v-model="tableConfig.searchParams.majorId" placeholder="请选择所属专业">
      <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addEvent" />
      <exportButton @exportEvent="exportEvent" />
      <importButton @importEvent="importEvent" type="Student"/>
      <reflashButton @reflashEvent="reflashEvent" />
    </section>

    <div class="student-content">
      <div class="class-list">
        <div :class="['class-item',tableConfig.searchParams.classId==''?'active':'']"  @click="changeClass()">
          <p>全部</p>
        </div>
        <div :class="['class-item',tableConfig.searchParams.classId==item.id?'active':'']" v-for="item in allClassList" @click="changeClass(item)" :key="item.id">
          <p>{{item.name}}</p>
        </div>
      </div>
      <div class="table-list">
        <table2 
          @selectionChange="selectionChange" 
          :notShowSearch="notShowSearch" 
          ref="tablePreview" 
          @selection-change="handleSelectionChange" 
          :selectable="tableConfig.selectable" 
          :data="tableConfig.tableData" 
          :columns="tableConfig.columns" 
          :queryFormConfig="tableConfig.queryConfig" 
          :total="tableConfig.total"
          :pagination="tableConfig.pagination" 
          :height="height" 
          :paginationLayout="tableConfig.paginationLayout" 
          :firstLoad="firstLoad" 
          :searchParams="tableConfig.searchParams" 
          @handleSearch="handleSearch">
          <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
          <template #operate>
            <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
              <template slot-scope="scope">
                <el-button v-for="(item, index) in operateBtns" type="text"
                          v-show="isShowOperateBtn(scope, item)"
                          :class="item.class || ''"
                          @click="btnClick(scope.row, item)" :key="index"
                          :disabled="scope.row.dataLocked">
                  {{ item.name }}
                </el-button>
              </template>
            </el-table-column>
          </template>
        </table2>
      </div>
    </div>

    <!-- 新增编辑 学生 -->
    <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" :title="!form.id?'新增学生':'编辑学生'">
      <el-form ref="form" :model="form" label-position="top" :rules="rules">
        <el-form-item label="学生姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入学生姓名" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="学号" prop="studentNo">
          <el-input v-model="form.studentNo" placeholder="请输入学号"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select style="width:100%" placeholder="请选择性别" v-model="form.sex">
            <el-option value="Female" label="女"></el-option>
            <el-option value="Male" label="男"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属年级" prop="classGrade">
          <el-select  style="width:100%" placeholder="请选择所属年级" v-model="form.classGrade">
            <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属学院" prop="collegeId">
          <el-select style="width:100%" placeholder="请选择所属学院"  @change="selectCollege" clearable filterable v-model="form.collegeId">
            <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属专业" prop="majorId">
          <el-select style="width:100%" placeholder="请选择所属专业"  v-model="form.majorId" @change="selectMajor">
            <el-option v-for="item in majorList" :value="item.id" :key="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属班级" prop="classIds">
          <el-select style="width:100%" placeholder="请选择所属班级" multiple clearable filterable v-model="form.classIds" @change="selectClass">
            <el-option v-for="item in classList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="!detail" style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
  </div>
</template>

<script>
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数

import { exportExcel } from '@/utils/common.js' // 导入导出excel

export default {
  name: "schoolPage",
  data() {
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          // },{
          //   label: "ID",
          //   prop: "id",
          },
          {
            label: "学生姓名",
            prop: "name",
          },
          {
            label: "性别",
            prop: "sex",
            width: 80,
            formatter(row, column, cellValue, index) {
              return cellValue === 'Female' ? '女' : '男'
            }
          },
          {
            label: "联系方式",
            prop: "phone",
          },
          {
            label: "所属班级",
            prop: "classes",
            formatter(row, column, cellValue, index) {
              return cellValue.map(item=>item.className).join(',')
            }
          },
          {
            label: "所属专业",
            prop: "majorName",
          },
          {
            label: "所属学院",
            prop: "collegeName",
          },
          {
            label: "年级",
            prop: "classGrade",
            width: 80,
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter(row, column, cellValue, index) {
              return formatISOString(cellValue) 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord: "", // 搜索关键字
          collegeId: "" ,// 所属学院ID
          majorId: "", // 所属专业ID
          classId: "", // 所属班级ID
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
        queryConfig: {},
      },
      // 补全模板中使用的变量
      firstLoad: true,
      loading: false,
      actionBtns: [ ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '调班',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 220,
      collegeList:[],// 学院列表
      majorList:[],// 专业列表
      classList:[],// 班级列表
      allClassList:[],// 所有班级列表
      gradeList:[],// 年级列表
      courseList:[],// 课程列表
      dialogVisible:false,// 弹窗是否显示
      form:{ // 新增表单
        id:'', // 班级ID
        name:'', // 名称
        studentNo:'', // 学号
        phone:'', // 联系方式
        sex: 'Male', // 性别 Female-女 Male-男
        classGrade:'', // 所属年级
        collegeId:'', // 所属学院ID
        majorId:'', // 所属专业ID
        classIds:[], // 所属班级ID
        classes:[], // 所属班级ID
        majorName:'', // 所属专业名称
        collegeName:'', // 所属学院名称
      },
      rules: { // 表单校验规则
        name: [
          { required: true, message: '请输入学生姓名', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('请输入学生姓名'));
              } else {
                callback();
              }
            }  }
        ],
        studentNo: [
          { required: true, message: '请输入学生学号', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入学生联系方式', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        sex: [
          { required: true, message: '请选择学生性别', trigger: 'change' }
        ],
        classGrade: [
          { required: true, message: '请选择学生所属年级', trigger: 'change' } 
        ],
        collegeId: [
          { required: true, message: '请选择学生所属学院', trigger: 'change' } 
        ],
        majorId: [
          { required: true, message: '请选择学生所属专业', trigger: 'change' }
        ],
        classId: [
          { required: true, message: '请选择学生所属班级', trigger: 'change' }
        ],
      },
      detail:false, // 详情
      height:'660px',
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // 初始化表格数据
    this.getGradeList(); // 获取年级列表
    // this.initTableData();
    this.getCollegeList(); // 获取学院列表

    this.getAllClassList(); // 获取所有班级列表
  },
  methods: {
    changeClass(item){
      if(item){
        this.tableConfig.searchParams.classId = item.id;
      }else{
        this.tableConfig.searchParams.classId = '';
      }
    },
    // 获取所有班级列表
    getAllClassList(){
      let params = {
        pageIndex:1,
        pageSize:100,
        collegeId:this.tableConfig.searchParams.collegeId,
        majorId:this.tableConfig.searchParams.majorId
      }
      this.$api.GetClassList(params).then(res=>{
        this.allClassList = res.data.items;
      })
    },
    getGradeList(){ // 获取年级列表
      // 获取当前年份
      const currentYear = new Date().getFullYear();
      // 往前推四年，往后推一年
      for (let i = -4; i <= 1; i++) {
        const year = currentYear + i;
        this.gradeList.push({
          id: year,
          name: `${year}级`,
        });
      }
    },
    // 初始化表格数据
    async initTableData(val){
      this.loading = true;
      val?this.tableConfig.searchParams.collegeId = val:'';
      let res = await this.$api.GetStudentList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false; 
      },300)
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
      })
    },
    selectCollege(val,flag){ // 选择学院
      this.form.collegeId = val;
      if(!flag){ // 编辑
        this.form.majorId = '';
        this.form.classIds = []; //
      }
      this.$api.GetMajorList({pageIndex:1,pageSize:100,collegeId:val}).then(res=>{
        this.majorList = res.data.items;
      })
    },
    selectTopCollege(){
      this.initTableData();
      this.$api.GetMajorList({pageIndex:1,pageSize:100,collegeId:this.tableConfig.searchParams.collegeId}).then(res=>{
        this.majorList = res.data.items;
      })

      this.getAllClassList(); // 获取所有班级列表
    },
    selectTopMajor(){
      this.initTableData(this.tableConfig.searchParams.collegeId);
      this.getAllClassList(); // 获取所有班级列表
    },
    selectMajor(val,flag){ // 选择专业
      let params = {
        pageIndex:1,
        pageSize:100,
        // collegeId:this.form.collegeId,
        majorId:this.form.majorId
      }
      this.$api.GetClassList(params).then(res=>{
        this.classList = res.data.items;
        if(!flag) {
          this.form.classIds = []; // 清空选中的班级
        }
      })

    },
    selectClass(val){ // 选择班级
      this.form.classes = [];
      // 遍历选中的班级 ID 数组
      val.forEach(id => { 
          const target = this.classList.find(item => item.id === id);
          if (target) {
            // 将找到的班级对象添加到 form.classes 中
            this.form.classes.push({
              "classId": target.id,
              "className": target.name,
              "classType":  target.classType,
              "classGrade": target.grade,
            }); 
          }
        });
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    // 导出
    exportEvent(){     
      let params={
        type:"Student",
      }
      exportExcel('/Excel/DownloadTemplate',params,'学生模板')
    },
    // 导入
    importEvent(file){
     this.initTableData()
    },
    addEvent(){
      this.dialogVisible = true;
      this.form = { // 新增表单
        id:'', // 班级ID
        name:'', // 名称
        studentNo:'', // 学号
        phone:'', // 联系方式
        sex: 'Male', // 性别 Female-女 Male-男
        classGrade:'', // 所属年级
        collegeId:'', // 所属学院ID
        majorId:'', // 所属专业ID
        classIds:[], // 所属班级ID
        classes:[], // 所属班级ID
        majorName:'', // 所属专业名称
        collegeName:'', // 所属学院名称
      }
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = { // 新增
            ...this.form,
          }
          delete params.classIds;
          if(this.form.id){ // 编辑
            this.$api.UpdateStudent(params).then(res=>{
              if(res.errCode==0){
                this.$message({
                  message: '更新成功',
                  type: 'success'
                })
                this.$refs.form.resetFields();
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateStudent(params).then(res=>{
            if(res.errCode==0){
              this.$message({
                message: '新增成功',
                type:'success'
              })
              this.$refs.form.resetFields();
              this.dialogVisible = false;
              this.initTableData()
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      this.tableConfig.searchParams = params;
      this.initTableData()
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '详情':
          this.detail = true
          this.dialogVisible = true;
            this.form = { // 编辑表单
              ...row,
              classIds:row.classes.map(item=>item.classId)
            }
            let majorId = row.majorId; // 获取专业ID
            // 当院校有值时触发专业列表查询
            if (this.form.collegeId) {
              this.selectCollege(this.form.collegeId, true);
              // 当专业有值时查询班级列表
              if (this.form.majorId) {
                // 修正 selectMajor 方法的参数传递
                this.selectMajor(this.form.majorId,true); 
              }
            }
          break;
        case '调班':
        case '编辑':
          this.detail = false;
          this.dialogVisible = true;
          console.log(row);
          this.form = { // 编辑表单
            ...row,
            classIds:row.classes.map(item=>item.classId)
          }
           // 当院校有值时触发专业列表查询
         
           if (this.form.collegeId) {
            this.selectCollege(this.form.collegeId, true);
            // 当专业有值时查询班级列表
            if (this.form.majorId) {
              // 修正 selectMajor 方法的参数传递
              this.selectMajor(this.form.majorId,true); 
            }
          }
          break;

        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该学生会被删除, 是否继续?',
      })
      this.$api.DeleteStudent({id:row.id}).then(res=>{
        this.initTableData()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.student-manage-page {
  padding: 20px;


  .student-content{
    display: flex;
    .class-list{
      width:240px;
      height: 600px;
      overflow-y: auto;
      .class-item{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        line-height: 38px;
        border-radius: 4px;
        padding: 0 10px;
        cursor: pointer; 
        &:hover{
          background: #F1F7FF;
        }
        &.active{
          background: #F1F7FF;
        }
        p{
          color: #333; 
          font-size: 14px;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
    .table-list{
      width: calc(100% - 240px);
      margin-left: 20px;
    }
  }
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}
</style>