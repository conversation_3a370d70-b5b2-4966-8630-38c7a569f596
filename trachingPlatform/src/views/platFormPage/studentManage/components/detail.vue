<template>
    <div class="detail-info-page">
        <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
            <el-row :gutter="10">
                <el-col :span="12">
                    <el-form-item label="学院名称">
                        <el-input :disabled="true" v-model="detailInfo.name"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="负责人">
                        <el-input :disabled="true" v-model="detailInfo.principal"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
                <el-col :span="12">
                    <el-form-item label="联系电话">
                        <el-input :disabled="true" v-model="detailInfo.phone"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="教材额度">
                        <el-input :disabled="true" v-model="detailInfo.textbookQuota"></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            
        </el-form>

        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane v-for="item in detailInfo.children" :key="item.title" :label="`${item.title}(${item.list.length})`" :name="item.name">
                <table2
                :data="item.list" 
                :columns="item.columns" 
                :pagination="false" 
                > </table2>
            </el-tab-pane>
       
        </el-tabs>
    </div>
</template>
<script>
export default {
    data() {
        return {
            activeName: 'first',
        }
    },
    props:{
        detailInfo: {
            type: Object,
            default: function () {
                return {};
            } 
        }
    },
    computed: {
        
    },
    watch: {
        detailInfo: {
            handler(newVal, oldVal) {
                this.form = newVal;
                console.log(this.detailInfo)

            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        handleClick(){

        }
    }
}
</script>
<style lang="">
    
</style>