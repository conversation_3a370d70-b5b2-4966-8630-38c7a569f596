
// const [425, 428, 428, 435, 431, 432, 434]
// data: [410.03, 410.03, 410.03, 410.03, 414.39, 414.39, 414.39]
const unit = "（元/吨）";
export default myChart => {
  const option = {
    toolbox: {
      right: 16,
      top: 8,
      feature: {
        dataView: {
          show: true,
          title: "查看数据",
          icon: "path://M512 277.333333A416.881778 416.881778 0 0 1 888.32 512 416.426667 416.426667 0 0 1 512 746.666667 416.426667 416.426667 0 0 1 135.68 512 416.881778 416.881778 0 0 1 512 277.333333z m0-85.333333C298.666667 192 116.451556 324.664889 42.666667 512c73.784889 187.335111 256 320 469.333333 320S907.548444 699.335111 981.333333 512C907.548444 324.664889 725.333333 192 512 192z m0 213.333333a106.723556 106.723556 0 1 1-0.056889 213.390223A106.723556 106.723556 0 0 1 512 405.333333z m0-85.333333A192.284444 192.284444 0 0 0 320 512 192.284444 192.284444 0 0 0 512 704 192.284444 192.284444 0 0 0 704 512 192.284444 192.284444 0 0 0 512 320z",
          iconStyle:{
            color: "#2B66FF",
            borderColor: "#2B66FF",
          },
          optionToContent: function ({ series, xAxis }) {
            var axisData = xAxis[0].data;
            var series0 = series[0].data;
            var series1 = series[1].data;
            var table = `<table style="width:100%;text-align:center" border=1 cellspacing=0 >
              <thead>
                <tr height=50>
                  <th>产品名称</th>
                  <th>时间</th>
                  <th>采购单价${unit}</th>
                  <th>市场平均价${unit}</th>
                  <th>差价${unit}</th>
                </tr>
              </thead>
              <tbody>`;

            for (var i = 0, l = axisData.length; i < l; i++) {
              table += `<tr height=40>
                  <td>石灰</td>
                  <td>${axisData[i]}</td>
                  <td>${series0[i]}</td>
                  <td>${series1[i]}</td>
                  <td>${(series1[i] - series0[i]).toFixed(2)}</td>
                </tr>`;
            }
            table += "</tbody></table>"
            return table;
          }
        }
        // saveAsImage: {
        //   type: "jpeg"
        // }
      }
    },
    title: {
      text: "2022年7月至2023年1月石灰价格历史数据",
      top: 4,
      left: "4%"
    },
    tooltip: {
      trigger: "axis",
      formatter: function (a,b,c,d) {
        if(a?.length===2){
          return `市场平均价：${a[1].data}${unit}</br>
            采购单价：${a[0].data}${unit}</br>
            价差：${(a[1].data - a[0].data).toFixed(2)}${unit}`;
        }else if(a?.length===1){
          return a[0]?.seriesName + "：" + a[0].data + unit;
        }else{
          return "";
        }
      }
    },
    legend: {
      top: 10,
      right: "14%",
      data: ["采购单价", "市场平均价"]
    },
    grid: {
      left: "3%",
      right: "4%",
      top: "80",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "2022年7月",
        "2022年8月",
        "2022年9月",
        "2022年10月",
        "2022年11月",
        "2022年12月",
        "2023年1月"
      ]
    },
    yAxis: {
      name: unit,
      nameGap: 20,
      nameTextStyle: { fontWeight : 'bold'},
      type: "value",
      scale: true,
      min: 400,
      max: 450
    },
    series: [
      {
        name: "采购单价",
        type: "line",
        data: [410.03, 410.03, 410.03, 410.03, 414.39, 414.39, 414.39]
      },
      {
        name: "市场平均价",
        type: "line",
        data: [425, 428, 428, 435, 431, 432, 434]
      }
    ]
  };

  myChart.setOption(option);
};
