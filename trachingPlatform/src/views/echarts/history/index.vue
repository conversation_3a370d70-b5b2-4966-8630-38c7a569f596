
<template>
  <div class="echarts-container">
    <div id="zdap-charts-history"></div>
    <EchartsDescription />
  </div>
</template>

<script>
import doChart from "./chart.js";
import EchartsDescription from "../description.vue"
export default {
  name: "EchartsHistory",
  components: {EchartsDescription},
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initEcharts();
    window.addEventListener("resize", () => {
      this.chart?.resize();
    });
  },
  methods: {
    initEcharts() {
      this.chart = echarts.init(document.getElementById("zdap-charts-history"));
      doChart(this.chart);
    }
  }
};
</script>

<style lang="scss" scoped>
.echarts-container {
  height: 100%;
  overflow: auto;
  padding-top: 4%;
  #zdap-charts-history {
    width: 1000px;
    height: 640px;
    margin: 0 auto;
  }
}
</style>
