<template>
  <div class="echarts-container">
    <div id="zdap_charts"></div>
  </div>
</template>

<script>
import doChart from "./do-chart";
import { provinces } from "./do-chart";
export default {
  name: "FormatechartsData",
  data() {
    return {
      tooltipInterval: null,
      chart: null,
      provinces
    };
  },
  beforeDestroy() {
    // 销毁ECharts实例
    this.chart && this.chart.dispose();
    this.chart = null;
    this.tooltipInterval && clearInterval(this.tooltipInterval);
    this.tooltipInterval = null;
  },
  methods: {
    // // 自动轮询展示提示框
    // intervalShowTooltip(chart, curIdx = 0) {
    //   this.tooltipInterval && clearInterval(this.tooltipInterval);
    //   const options = chart.getOption();
    //   let currentIndex = curIdx;
    //   this.tooltipInterval = setInterval(() => {
    //     let dataLen = options.series[4].data.length;
    //     if (dataLen > 0) {
    //       currentIndex = (currentIndex + 1) % dataLen; // 取余 循环展示
    //       chart.dispatchAction({
    //         type: "showTip",
    //         seriesIndex: 4,
    //         dataIndex: currentIndex
    //       });
    //     }
    //   }, 5000);
    // },
    initEcharts() {
      this.chart = echarts.init(document.getElementById("zdap_charts"));
      doChart(this.chart);
      // this.intervalShowTooltip(this.chart);
      // 第一次显示第一个tooltip
      this.chart.dispatchAction({
        type: "showTip",
        seriesIndex: 4,
        dataIndex: 0
      });
      this.chart.on("timelinechanged", event => {
        this.chart.dispatchAction({
          type: "showTip",
          seriesIndex: 4,
          dataIndex: event.currentIndex
        });
      });
      this.chart.off("click").on("click", a => {
        if (["line", "bar"].includes(a.componentSubType)) return;
        const index =
          a.dataIndex || this.provinces.findIndex(p => p === a.name);

        if (index > -1) {
          // 时间轴和地图同步
          this.chart.dispatchAction({
            type: "timelineChange",
            currentIndex: index
          });
          // this.intervalShowTooltip(
          //   this.chart,
          //   this.provinces.findIndex(p => p === a.name)
          // );
        } else {
          this.$message.warning(a.name + "暂无数据");
        }
        this.chart.dispatchAction({
          type: "showTip",
          // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
          seriesIndex: 4,
          // 可选，数据名称，在有 dataIndex 的时候忽略
          name: a.name
        });
      });
    },
    formatChartsData() {
      this.$nextTick(() => {
        this.initEcharts();
      });
    }
  },
  mounted() {
    this.formatChartsData();
    window.addEventListener("resize", () => {
      this.chart?.resize();
    });
  }
};
</script>

<style lang="scss" scoped>
.echarts-container {
  height: 100%;
  overflow: auto;
  #zdap_charts {
    min-width: 1600px;
    min-height: 800px;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
}
</style>
