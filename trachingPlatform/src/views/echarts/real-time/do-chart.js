import geoJson from "./data/map.json";
import product from "./data/product.json";
import groupBy from "lodash/groupBy";
import flatten from "lodash/flatten";
import ceil from "lodash/ceil";
import findIndex from "lodash/findIndex";
import { arrayToObject } from "@/utils/base";
// 根据省份合并数据
const productGroup = groupBy(product, "province");
// 河北、湖北 湖南 三个需要显示
export const provinces = Object.keys(productGroup);
console.log(provinces);
const lineData = [];
// 各省平均值偏差值
const deviation = {
  浙江: {
    num: 8
  },
  云南: {
    num: 5
  },
  新疆: {
    num: 5
  },
  西藏: {
    num: 4
  },
  四川: {
    num: 2
  },
  陕西: {
    num: 3
  },
  山西: {
    num: 2
  },
  山东: {
    num: 1
  },
  青海: {
    num: 1
  },
  宁夏: {
    num: 3
  },
  内蒙古: {
    num: 3
  },
  辽宁: {
    num: 1
  },
  江西: {
    num: -2
  },
  江苏: {
    num: 4
  },
  吉林: {
    num: 2
  },
  湖南: {
    num: 0
  },
  湖北: {
    num: 0
  },
  黑龙江: {
    num: 2
  },
  河南: {
    num: 2
  },
  河北: {
    num: 3
  },
  海南: {
    num: -2
  },
  贵州: {
    num: 1
  },
  广西: {
    num: -1
  },
  广东: {
    num: -3
  },
  甘肃: {
    num: 1
  },
  福建: {
    num: -1
  },
  安徽: {
    num: -2
  }
};
// 处理平均数据
provinces.forEach(p => {
  // const randNum = sample(
  //   Array.from({ length: 201 - 100 }, (_, i) => 200 + i * 3)
  // );

  const items = (productGroup[p] || [])
    .map(p => {
      return {
        ...p,
        // price: p.price - randNum
        price: p.price
      };
    })
    .filter(p => p.price > 410 && p.price < 460);
  productGroup[p] = items;
  lineData.push(
    ceil(
      items.reduce((prev, cur, index) => {
        return prev + cur.price;
      }, 0) / (items.length || 1)
    ) +
      (deviation[p]?.num || 0) +3
  );
});
// console.log(
//   ceil(
//     lineData.reduce((prev, cur, index) => {
//       return prev + cur;
//     }, 0) / (lineData.length || 1)
//   )
// );
export default myChart => {
  const colors = [
    [
      "#1DE9B6",
      "#F46E36",
      "#04B9FF",
      "#5DBD32",
      "#FFC809",
      "#FB95D5",
      "#BDA29A",
      "#6E7074",
      "#546570",
      "#C4CCD3"
    ],
    [
      "#37A2DA",
      "#67E0E3",
      "#32C5E9",
      "#9FE6B8",
      "#FFDB5C",
      "#FF9F7F",
      "#FB7293",
      "#E062AE",
      "#E690D1",
      "#E7BCF3",
      "#9D96F5",
      "#8378EA",
      "#8378EA"
    ],
    [
      "#DD6B66",
      "#759AA0",
      "#E69D87",
      "#8DC1A9",
      "#EA7E53",
      "#EEDD78",
      "#73A373",
      "#73B9BC",
      "#7289AB",
      "#91CA8C",
      "#F49F42"
    ]
  ];

  const maxVal = 500;
  const flatColors = flatten(colors);
  const points = provinces.map(p => "");
  const targetPosition = [115.543, 23.6094];
  // const targetPosition =  [113.0823, 28.2568]
  let num = 0;
  // 组合省份的坐标，颜色信息
  geoJson.features.forEach(f => {
    if (f.properties.name) {
      if (provinces.includes(f.properties?.name)) {
        const index = findIndex(provinces, p => p === f.properties?.name);
        num++;
        points[index] = {
          province: f.properties?.name,
          value: f.properties.cp,
          itemStyle: {
            color: flatColors[num]
          },
          index,
          line: {
            coords: [f.properties.cp, targetPosition],
            lineStyle: { color: flatColors[flatColors.length - num] }
          }
        };
      }
    }
  });

  echarts.registerMap("china", geoJson);

  const optionXyMap01 = {
    //底部滚动栏
    timeline: {
      data: provinces,
      axisType: "category",
      autoPlay: true,
      playInterval: 5000,
      left: "10%",
      right: "10%",
      bottom: "3%",
      width: "80%",
      label: {
        normal: {
          textStyle: {
            color: "#ddd"
          }
        },
        emphasis: {
          textStyle: {
            color: "#fff"
          }
        }
      },
      symbolSize: 10,
      lineStyle: {
        color: "#555"
      },
      checkpointStyle: {
        borderColor: "#87CEEB",
        borderWidth: 2
      },
      controlStyle: {
        showNextBtn: true,
        showPrevBtn: true,
        normal: {
          color: "#666",
          borderColor: "#666"
        },
        emphasis: {
          color: "#aaa",
          borderColor: "#aaa"
        }
      }
    },
    baseOption: {
      animation: true,
      animationDuration: 1000,
      animationEasing: "cubicInOut",
      animationDurationUpdate: 1000,
      animationEasingUpdate: "cubicInOut",
      grid: [
        //柱状图样式
        {
          right: "120px",
          top: "150px",
          bottom: "150px",
          width: "18%"
        },
        //曲线图样式
        {
          left: "5%",
          top: "14%",
          height: "30%",
          width: "68%"
        }
      ],
      tooltip: {
        trigger: "axis", // hover触发器
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: "shadow" // 默认为直线，可选为：'line' | 'shadow'
          // shadowStyle: {
          //   color: "#ccc" //hover颜色
          // }
        }
      },
      // toolbox: {
      //   show: true,
      //   feature: {
      //     mark: {
      //       show: true
      //     },
      //     dataView: {
      //       show: true,
      //       readOnly: false
      //     },
      //     magicType: {
      //       show: true,
      //       type: ["bar", "line"]
      //     },
      //     restore: {
      //       show: true
      //     },
      //     saveAsImage: {
      //       show: true
      //     }
      //   }
      // },
      legend: [],

      //地图样式
      geo: {
        // 调整位置
        // top: "280px",
        // left: "13%",
        layoutCenter: ["40%", "70%"],
        layoutSize: "100%",
        map: "china", // 地图来源
        aspectScale: 0.75, //长宽比
        zoom: 0.55, // 地图缩放级别
        roam: false, // 是否开启平移
        label: {
          normal: {
            show: true,
            textStyle: {
              color: "#fff"
            }
          },
          emphasis: {
            textStyle: {
              color: "#fff"
            }
          }
        },
        itemStyle: {
          normal: {
            borderColor: "rgba(147, 235, 248, 1)",
            borderWidth: 1,
            areaColor: {
              type: "radial",
              x: 0.5,
              y: 0.5,
              r: 0.8,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(147, 235, 248, .3)" // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#3A46C1" // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            },
            shadowColor: "#031098",
            shadowOffsetX: -2,
            shadowOffsetY: 2,
            shadowBlur: 10
          },
          emphasis: {
            areaColor: "#389BB7",
            borderWidth: 0
          }
        },
        regions: [
          {
            name: "南海诸岛",
            itemStyle: {
              areaColor: "rgba(0, 10, 52, 1)",

              borderColor: "rgba(0, 10, 52, 1)",
              normal: {
                opacity: 1,
                label: {
                  show: false,
                  color: "#009cc9"
                }
              }
            }
          }
        ]
      }
    },

    options: []
  };
  const randomItem = (arr, length) => {
    var newArr = []; // 组成的新数组初始化
    for (var i = 0; i < length; i++) {
      var index = Math.floor(Math.random() * arr.length);
      var item = arr[index];
      newArr.push(item);
      arr.splice(index, 1);
    }
    return newArr.reverse();
  };

  for (let n = 0; n < provinces.length; n++) {
    const result = productGroup[provinces[n]]
      .sort((p1, p2) => p2.price - p1.price)
      .filter(p => p.price > 435 && p.price < 445)
      .slice(0, 20);
    const provinceData = randomItem(result, 15).sort(
      (p1, p2) => p2.price - p1.price
    );
    const provinceDataObj = arrayToObject(provinceData, "company");
    const dataShadow = provinceData.map(p => maxVal);
    optionXyMap01.options.push({
      //背景色
      backgroundColor: "#242C7F",

      //标题
      title: [
        {
          text: "物料-石灰各省实时价格对比",
          top: "30px",
          x: "center", // 标题居中
          textStyle: {
            color: "#fff",
            align: "center",
            fontSize: 30
          }
        },
        {
          text: "各省石灰实时平均价格",
          left: "250px",
          // top: "200px",
          top: "7%",
          textStyle: {
            color: "#fff",
            fontSize: 20
          }
        },
        {
          id: "statistic",
          text: `${provinces[n]} 省 价格统计情况`,
          subtext: "以下是重点监控的供应商",
          subtextStyle:{
            fontSize: 16,
            textAlign: 'center'
          },
          right: "10%",
          top: "50px",
          textStyle: {
            color: "#fff",
            fontSize: 20
          }
        }
      ],
      xAxis: [
        {
          name: "（元）",
          nameLocation: "end",
          nameTextStyle: {
            color: "#fff",
            fontSize: 12,
            // margin: [0, 0, 20, 20],
            padding: [0, 0, 0, 25]
          },
          type: "value",
          scale: true,
          // position: "top",
          min: 100,
          boundaryGap: false,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true
          },
          axisTick: {
            alignWithLabel: true,
            show: true
          },
          axisLabel: {
            // margin: 2,
            textStyle: {
              fontSize: 14,
              // fontWeight: "bold",
              color: "#fff"
            }
          }
        },
        {
          gridIndex: 1,
          // boundaryGap: false,
          axisLabel: {
            interval: 0,
            marginTop: 20,
            textStyle: {
              fontSize: 14,
              color: "#fff"
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,.1)"
              // width: 2
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,.1)"
            }
          },
          data: provinces
        }
      ],
      yAxis: [
        {
          type: "category",
          axisLabel: {
            inside: true,
            fontSize: 14,
            textStyle: {
              color: "#fff"
            }
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          },
          z: 10,
          data: provinceData.map(p => `${p.company}`)
        },
        {
          name: "（元）",
          nameTextStyle: {
            padding: [15, 15, 15, -15]
          },
          gridIndex: 1,
          axisLabel: {
            marginRight: 20,
            // formatter: "{value}",
            color: "#fff",
            fontSize: 14
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#fff"
              // fontSize:33
            }
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,.1)"
            }
          },
          axisTick: {
            show: false
          },
          scale: true,
          max: 446,
          // min: 400
        }
        // {
        //   gridIndex: 1,
        //   type: "value",
        //   axisLabel: {
        //     formatter: "{value} ",
        //     textStyle: {
        //       fontWeight: "bold",
        //       color: "fff"
        //     }
        //   }
        // }
      ],

      series: [
        {
          type: "effectScatter",
          coordinateSystem: "geo",
          showEffectOn: "render",
          zlevel: 1,
          rippleEffect: {
            period: 15,
            scale: 4,
            brushType: "fill"
          },
          hoverAnimation: true,
          label: {
            normal: {
              formatter: "{b}",
              position: "right",
              offset: [15, 0],
              color: "#1DE9B6",
              show: true
            }
          },
          itemStyle: {
            normal: {
              color: "#1DE9B6",
              shadowBlur: 10,
              shadowColor: "#333"
            }
          },
          symbolSize: 14,
          data: [
            points[n],
            { value: targetPosition, itemStyle: { color: "#f34e2b" } }
          ]
        },
        //地图线的动画效果
        {
          type: "lines",
          zlevel: 2,
          effect: {
            show: true,
            period: 4, //箭头指向速度，值越小速度越快
            trailLength: 0.4, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: "arrow", //箭头图标
            symbolSize: 7 //图标大小
          },
          lineStyle: {
            normal: {
              color: "#1DE9B6",
              width: 1, //线条宽度
              opacity: 0.1, //尾迹线条透明度
              curveness: 0.3 //尾迹线条曲直度
            }
          },
          data: [points[n].line]
        },
        //柱状图
        {
          // For shadow
          type: "bar",
          itemStyle: {
            normal: {
              color: "#031098",
              barBorderRadius: 5
            }
          },
          barGap: "-100%",
          barCategoryGap: "40%",
          data: dataShadow,
          animation: false,
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: function (val) {
                return provinceDataObj[val.name]?.price;
              },
              textStyle: {
                color: "#fff",
                fontSize: "16"
              }
            }
          },
          z: 0
        },
        {
          type: "bar",
          itemStyle: {
            normal: {
              color: "#3A46C1"
            }
          },
          data: provinceData.map(p => p.price)
        },
        // 曲线图线条
        {
          xAxisIndex: 1,
          yAxisIndex: 1,
          name: "石灰平均价格",
          smooth: true,
          type: "line",
          itemStyle: {
            normal: {
              lineStyle: {
                // type: "dashed"
                //折点的颜色
                color: "#00a2e6"
              },
              color: "#01F699", //拐点的颜色
              borderColor: "#01F699" //拐点边框的颜色
            }
          },
          data: lineData,
          markPoint: {
            data: [
              {
                type: "max",
                name: "最大值",
                symbolSize: 65,
                itemStyle: {
                  normal: {
                    color: "#DD6B66"
                  }
                }
              },
              {
                type: "min",
                name: "最小值",
                symbolSize: 65
              }
            ],
            label: {
              normal: {
                show: true,
                textStyle: {
                  color: "#fff",
                  fontSize: "12"
                }
              }
            },
            itemStyle: {
              normal: {
                color: "#52C329"
              }
            }
          },
          markLine: {
            data: [
              // {
              //   type: "average",
              //   name: "平均值"
              // }
              {
                // 固定起点的 x ，用于模拟一条指向最大值的水平线
                yAxis: 440,
                xAxis: 0
              }
            ]
          }
        }
      ]
    });
  }
  myChart.setOption(optionXyMap01);
};
