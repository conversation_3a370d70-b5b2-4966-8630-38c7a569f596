<!--
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-29 16:14:20
 * @LastEditors: chenjiongming
 * @LastEditTime: 2024-04-30 09:40:22
 * @Description: 场景描述
-->
<template>
  <!-- 显示场景描述  -->
  <div
    class="echarts-description"
    :style="
      isOpenAside
        ? 'width:300px;border: 1px solid #DEE2E5;'
        : 'width:0;border:none'
    ">
    <div class="expend-div">
      <span
        class="expend-btn"
        @click="checkNav()"
        v-show="!isOpenAside">
        <i class="iconfont icon-17-Presentation"></i>
        <span class="ms-lable">场景描述</span>
        <i
          :class="[
            'iconfont',
            'sou-icon',
            isOpenAside ? 'icon-zhankai-copy' : 'icon-zhankai'
          ]"></i>
      </span>
    </div>
    <!-- </el-tooltip> -->
    <div class="scene-desc">
      <p
        class="open-expend"
        @click="checkNav()"
        v-show="isOpenAside">
        <i class="iconfont icon-17-Presentation"></i>
        <span class="ms-lable">场景描述</span>
        <i
          :class="[
            'iconfont',
            'sou-icon',
            isOpenAside ? 'icon-zhankai-copy' : 'icon-zhankai'
          ]"></i>
      </p>
      <div
        v-show="isOpenAside"
        class="description-info">
        <p>
          通过公司历史采购单价及大数据抓取的每月市场平均价格进行联合分析（公司历史采购单价来源于公司往期采购合同数据，每月市场平均价格来源于大数据抓取的每月市场数据）
          是一种有效的策略来确定合理的采购价格范围。公司历史采购单价反映了过去一段时间内公司与供应商达成的实际采购价格，而大数据抓取的每月市场平均价格则提供了每月市场上石灰的普遍价格水平。
        </p>
        <br />
        <p>
          我们可以利用这两个数据来源的信息来制定采购策略。由数据可知，我们可以以低于市场平均采购价15-25元/吨的价格进行采购。再根据市场实时价格，我们可以得出一个相对合理的采购价格范围，既考虑了公司历史采购价格的参考价值，又充分利用了市场平均价格的信息。在实际的采购谈判中，我们将根据这一范围进行讨价还价，以确保最终达成符合公司利益的采购协议。
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EchartsDescription",
  data() {
    return {
      isOpenAside: false // 是否打开场景 默认隐藏描述
    };
  },
  methods: {
    // 侧边栏
    checkNav() {
      this.isOpenAside = !this.isOpenAside;
    }
  }
};
</script>

<style lang="scss" scoped>
.echarts-description {
  position: fixed;
  width: 0;
  height: auto;
  transition: all 0.5s;
  right: 0;
  top: 0;
  background-color: #f9f9f9;
  height: 100%;
  display: flex;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #dee2e5;
  justify-content: center;
  flex-direction: column;
  .expend-div {
    cursor: pointer;
    position: absolute;
    width: 39px;
    height: 100%;
    top: 0;
    right: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .expend-btn {
      padding: 10px;
      background-color: #f9f9f9;
      color: #646e7b;
      box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
      z-index: 999;
      .ms-lable {
        display: block;
        line-height: 24px;
      }
      .sou-icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: #409efb;
        border-radius: 15px;
        color: #fff;
        line-height: 18px;
        font-size: 14px;
      }
    }
  }
  .scene-desc {
    display: flex;
    height: 100%;
    .open-expend {
      padding-top: 20px;
      width: 34px;
      text-align: center;
      text-indent: 0;
      border-right: 1px dashed #d8d8d8;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .ms-lable {
        display: block;
        padding: 0 10px;
        line-height: 24px;
      }
      .sou-icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        background-color: #409efb;
        border-radius: 15px;
        color: #fff;
        line-height: 18px;
        font-size: 14px;
      }
    }
  }
  .description-info {
    flex: 1 1 auto;
    color: #666;
    line-height: 28px;
    padding: 20px;
    text-indent: 2em;
    overflow: auto;
    max-height: calc(100% - 30px);
  }
}
</style>
