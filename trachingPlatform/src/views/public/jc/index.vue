<template>
  <div class="jc-container">
    <header class="jc-header">
      <div class="header-left flex">
        <i class="el-icon-arrow-left line-36 m-r-6 bold cursor" @click="backRoute"></i>
        <span class="bold size-16 line-36" >{{ query.textbookName }}</span>
      </div>
      <div class="header-right" v-show="directoryData.length">
        <section>
          <img src="@/assets/public/user.png" alt="">
        </section>
        <section>
          <!-- <el-button @click="handlePreview">驳回</el-button>
          <el-button @click="handlePreview">通过</el-button> -->
          <el-button @click="handlePreview">预览</el-button>
          <!-- <el-button type="primary" @click="handleSave">保存并提交审核</el-button> -->
          <!-- <el-button type="primary" @click="handleSave">保存并发布</el-button> -->
          <el-button type="primary" v-if="isEditor" @click="handleSave()">保存</el-button>
        </section>
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              <i class="el-icon-more size-12"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item class="clearfix" command="history">
                <i class="el-icon-document"></i>
                版本及编辑记录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
    </div>
    </header>
    <div class="jc-content">
      <aside class="jc-aside flex-column" :class="{'collapsed': isAsideCollapsed}">
        <!-- <div class="directory-header h-40">
          <span class="bold size-14 line-40 cursor p-x-20" @click="handlerAddSection"> <i class="el-icon-circle-plus-outline color-primary"></i> 添加章节</span>
        </div> -->
        <div class="directory-tree flex-1">
          <chapter-tree 
            ref="chapterTree"
            :chapterData="directoryData" 
            @nodeClick="handleNodeClick"
            @addNode="addNode"
            @renameNode="renameNode"
            @deleteNode="deleteNode"
            @sortNode="sortNode"
          />
        </div>
      </aside>
      <div class="toggle-aside" @click="toggleAside">
          <i :class="isAsideCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
      <main class="jc-main" v-show="directoryData.length">
        <tinymce-editor
          ref="editor"
          v-model="content"
          :disabled="disabled"
          :nowSection="{
            textbookId: nowSection.textbookId || '',
            sectionId: nowSection.id || ''
          }"
          @input="onChange"
        />
      </main>
    </div>
    
    <!-- 历史版本抽屉 -->
    <el-drawer
      title="版本"
      :visible.sync="historyDrawerVisible"
      direction="rtl"
      size="30%"
      :before-close="closeHistoryDrawer"
    >
      <div class="history-drawer-content">
        <div v-if="versionList.length === 0" class="empty-data">
          暂无历史版本记录
        </div>
        <div v-else class="version-list">
          <div 
            v-for="(item, index) in versionList" 
            :key="index" 
            class="version-item"
            @mouseenter="hoveredVersion = item.version"
            @mouseleave="hoveredVersion = null"
          >
            <div class="version-header">
              <span class="version-number">第{{ item.version }}版</span>
              <el-tag v-if="index === 0" size="small">当前</el-tag>
            </div>
            <div class="version-time">{{ item.saveTime }}</div>
            <div class="version-user">{{ item.userName }}</div>
            <div v-show="hoveredVersion === item.version" class="version-actions">
              <el-button 
                type="primary" 
                size="small" 
                @click="restoreVersion(item)"
              >还原</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import TinymceEditor from '@/components/Tinymce'
import ChapterTree from './components/chapterTree.vue'

export default {
  name: 'JcEditor',
  components: {
    TinymceEditor,
    ChapterTree
},
  data() {
    return {
      content: '',
      oldContent: '',
      disabled: false,
      directoryData: [],
      query:{},
      nowSection: {},
      saveTimer: null, // 添加定时器变量
      historyDrawerVisible: false, // 历史版本抽屉是否可见
      versionList: [], // 历史版本列表
      hoveredVersion: null, // 当前鼠标悬停的版本号
      isAsideCollapsed: false // 侧边栏是否收起
    }
  },
  computed:{
    isEditor() {
      let params = JSON.parse(this.$route.query.data);
      return (this.nowSection.editors || []).some(e => e.isMe)|| params.type =='my';
    },
  },
  mounted() {
    const { data } = this.$route.query;
    this.query = JSON.parse(data) || {};
    console.log(this.$route.query, this.query);
    //获取章节树形数据
    this.getSectionTree(this.query, this.query.id || '')
  },
  
  beforeDestroy() {
    // 组件销毁前清除定时器
    this.clearSaveTimer();
  },
  methods: {
    handlerAddSection() {
      this.$prompt('', '新建章节', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: (value) => {
          if (!value) {
            return '章节名称不能为空';
          }
        }
      }).then(({ value }) => {
        return this.$api.CreateSection({
          textbookId: this.query.textbookId,
          title: value,
          parentId: 0 // 默认添加到根节点
        });
      }).then(res => {
        if (res.errCode === 0) {
          // this.$message.success('新增成功');
          this.getSectionTree(this.query, res.data); // 刷新章节树
        }
      }).catch(err => {
        console.log(err);
        if (err === 'cancel') {
          // 判断如果directoryData数组长度为0，则调用backRoute()方法
          if (this.directoryData.length === 0) {
            this.backRoute();
          }
          return;
        }
        if (err !== 'cancel') {
          this.$message.error(err.message || '新增失败');
        }
      });
    },
    // 获取章节树形
    async getSectionTree(data, sectionId = 0) {
      try {
        const res = await this.$api.GetSectionTree({textbookId: data.textbookId, schoolId: data.schoolId})
        console.log(res);
        if (res.errCode === 0) {
          this.directoryData = res.data
          console.log(data,sectionId);
          
          if(sectionId && res.data.length > 0) {
            // 递归查找树形结构中的节点
            const data = this.findNodeById(res.data, sectionId)
            data && this.handleNodeClick(data )
            this.$nextTick(() => {
              this.$refs.chapterTree && this.$refs.chapterTree.setCurrentKey(sectionId)
            })
          }
          if(!sectionId) {
            this.handleNodeClick(res.data[0])
            this.$nextTick(() => {
              this.$refs.chapterTree && this.$refs.chapterTree.setCurrentKey(res.data[0].id)
            })
          }
          if(res.data.length === 0) {
            this.handlerAddSection()
          }
        }
      } catch (error) {
        console.log('error-GetSectionTree',error);
      }
      
    },
    findNodeById(nodes, targetId) {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, targetId);
          if (found) return found;
        }
      }
      return null;
    },
    async handleSave(type) {
      if(!this.isEditor) {
        console.log('无权限编辑');
        return
      }
      // 保存逻辑
      console.log("save-function-SectionId--",this.nowSection.id);
      if(!this.nowSection.id) {
        return
      }
      const content = this.getEditorContent()
      // const contentText = this.getContentText()
      const contentDom = this.getContentDom()
      // console.log('保存内容：', {contentDom})
      if((!content || !contentDom) && type === 'autoSave') {
      //  console.log("调用定时器保存、无内容不保存")
       return
      }

      if(!content) {
        this.$message.error('请输入内容')
        return
      }

      if(type === 'autoSave' && content === this.oldContent) {
        // console.log('相同内容不保存');
        return
      }

      try {
        const res = await this.$api.SaveSectionContent({
          sectionId: this.nowSection.id,
          content,
          saveHistory: type != 'autoSave' 
        })
        console.log(res);
        if(res.errCode === 0) {
            this.oldContent = content
          if(type != 'autoSave') {
            this.$message.success('保存内容成功')
          }
        }
        
      } catch (error)  {
        this.$message.error('保存失败')
      }
      
    },
    handlePreview() {
      // 预览逻辑
      // console.log('预览内容：', this.content)
      // 预览前先调用一次保存
      this.handleSave('autoSave')

      const params = {
        ...this.query,
        id: this.nowSection.id
      }
      this.$router.push({path: '/jc-view', query: {data: JSON.stringify(params)}})
    },

    async handleNodeClick(data) {
      console.log('点击目录节点：', data)
        // 切换到其他章节，主动保存富文本
      if(data.id !== this.nowSection.id) {
        this.handleSave('autoSave');
      }

      this.nowSection = data;
      // 设置路由中id的值为 data.id
      this.$router.replace({
        query: {
          data: JSON.stringify({
            ...this.query,
            id: data.id
          }),
        }
      })

      try {
        const res = await this.$api.SectionGetById({id: data.id})
        console.log(res);
        if(res.errCode === 0) {
          this.content = res.data.content || ''
          this.oldContent = res.data.content || ''
          this.nowSection = {...this.nowSection,...res.data};
          // console.log(this.isEditor);
          
          // 创建新定时器前先清除旧定时器
          this.clearSaveTimer();
          if(!this.isEditor) {
            return
          }
          // 创建新的定时器，每10秒自动保存一次
          // this.saveTimer = setInterval(() => {
          //   this.handleSave('autoSave');
          // }, 10000); // 10秒 = 10000毫秒
        }
      } catch (error)  {
        this.$message.error('获取内容失败')
      }
    },
    addNode({ title, parentId, type }) {
      return this.$api.CreateSection({
        textbookId: this.query.textbookId,
        title,
        parentId
      }).then(res => {
        if (res.errCode === 0) {
          // this.$message.success('新增成功');
          this.getSectionTree(this.query, res.data);
        }
        return res;
      });
    },
    renameNode({ id, title, parentId }) {
      return this.$api.UpdateSection({
        textbookId: this.query.textbookId,
        parentId,
        id,
        title
      }).then(res => {
        if (res.errCode === 0) {
          // this.$message.success('重命名成功');
          this.getSectionTree(this.query, id);
        }
        return res;
      });
    },
    deleteNode({id,parentId}) {
      return this.$api.DeleteTextbookSection({
        id
      }).then(res => {
        if (res.errCode === 0) {
          // this.$message.success('删除成功');
          this.getSectionTree(this.query, parentId || 0);
        }
        return res;
      });
    },
    onChange(e) {
      // console.log('内容变化：', e)
    },
    backRoute() {
      // 返回前清除定时器
      this.clearSaveTimer();
      this.$router.go(-1)
    },
    
    // 清除定时器的方法
    clearSaveTimer() {
      if (this.saveTimer) {
        clearInterval(this.saveTimer);
        this.saveTimer = null;
      }
    },
    handleCommand(command) {
      if(command === 'history') {
        // 打开历史版本抽屉
        this.historyDrawerVisible = true;
        // 获取历史版本列表
        this.getVersionList();
      }
    },
    
    // 获取历史版本列表
    async getVersionList() {
      if (!this.nowSection || !this.nowSection.id) {
        this.$message.warning('请先选择章节');
        return;
      }
      
      try {
        const res = await this.$api.GetSectionVersionList({ sectionId: this.nowSection.id });
        if (res.errCode === 0) {
          this.versionList = res.data || [];
        } else {
          this.$message.error(res.errMsg || '获取历史版本失败');
        }
      } catch (error) {
        console.error('获取历史版本出错:', error);
        this.$message.error('获取历史版本失败');
      }
    },
    
    // 关闭历史版本抽屉
    closeHistoryDrawer() {
      this.historyDrawerVisible = false;
      this.versionList = [];
      this.hoveredVersion = null;
    },
    
    // 还原历史版本
    async restoreVersion(item) {
      // 添加二次确认
      try {
        await this.$confirm('确认将文档还原到该版本吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        // 用户确认后，调用还原接口
        const res = await this.$api.RestoreSection({
          id: item.id
        });
        
        if (res.errCode === 0) {
          this.$message.success('还原成功');
          // 更新当前内容
          this.content = item.content || '';
          this.oldContent = item.content || '';
          // 关闭抽屉
          this.closeHistoryDrawer();
        } else {
          this.$message.error(res.errMsg || '还原失败');
        }
      } catch (error) {
        // 如果是用户取消操作，不显示错误提示
        if (error === 'cancel') {
          return;
        }
        console.error('还原历史版本出错:', error);
        this.$message.error('还原失败');
      }
    },
    
    // 切换侧边栏收缩/展开状态
    toggleAside() {
      this.isAsideCollapsed = !this.isAsideCollapsed;
    },
    
    // 处理章节排序
    sortNode({ id, parentId, previous }) {
      // console.log(id, parentId, previous)
      // return false
      // 调用排序接口
      this.$api.SetSectionSort({
        id,
        textbookId: this.query.textbookId,
        parentId:parentId,
        previous:null
      }).then(res => {
        if (res.errCode === 0) {
          this.$message.success('排序成功');
          // 刷新章节树
          this.getSectionTree(this.query, id);
        } else {
          this.$message.error(res.errMsg || '排序失败');
          // 刷新章节树，恢复原始顺序
          this.getSectionTree(this.query, id);
        }
      }).catch(error => {
        console.error('排序出错:', error);
        this.$message.error('排序失败');
        // 刷新章节树，恢复原始顺序
        this.getSectionTree(this.query);
      });
    },
    // 获取富文本中的文本信息并过滤掉弹出层等不需要存储的东西
    getEditorContent(){
      // 使用getContent()获取HTML字符串
      const htmlContent = this.$refs.editor.getContentHtml()
      
      // 创建一个临时的div元素来解析HTML字符串
      function removeAllEventListeners(element) {
        const clone = element.cloneNode(true)  // 创建元素的克隆
        element && element.parentNode.replaceChild(clone, element)  // 替换元素，这会移除所有事件监听器
        return clone
      }
      const tempDiv = removeAllEventListeners(htmlContent)
      // 1.移除词典的弹出层，获取到 .note-tooltip元素就移除掉
      const tooltips = tempDiv.querySelectorAll('.note-tooltip')
      tooltips.forEach(tooltip => {
        tooltip.parentNode.removeChild(tooltip)
      })
       // 2.图片预览的的弹出层，获取到 .image-operation-bar元素就移除掉
      const images = tempDiv.querySelectorAll('.image-operation-bar')
      images.forEach(image => {
        image.parentNode.removeChild(image)
      })
       // 3.图片预览的的弹出层，获取到 .image-operation-bar元素就移除掉
      const videos = tempDiv.querySelectorAll('.video-operation-bar')
      videos.forEach(video => {
        video.parentNode.removeChild(video)
      })
       // 4.轮播图的的弹出层，获取到 .carousel-operation-bar元素就移除掉
      const carousels = tempDiv.querySelectorAll('.carousel-operation-bar')
      carousels.forEach(carousel => {
        carousel.parentNode.removeChild(carousel)
      })
      // 获取处理后的HTML内容
      const cleanedContent = tempDiv.innerHTML
      
      return cleanedContent
    },
    getContent() {
       return this.$refs.editor.getContent()
    },
    getContentText() {
      const text = this.$refs.editor.getContentText()
       return text ? text.replaceAll(/\s|&nbsp;/g, '') :''
    },
    getContentDom() {
      // console.log(this.$refs.editor.getContent())
      const content = this.$refs.editor.getContent()
      const text = this.$refs.editor.getContentText()
      const isText =  text ? text.replaceAll(/\s|&nbsp;/g, '') :''
      return content.includes('type-image') || 
            content.includes('type-video') || 
            content.includes('type-carousel') || 
            content.includes('type-panel') ||
            content.includes('type-iframe') || 
            content.includes('note-mark') || 
            content.includes('type-literature') ||
            !!isText
    }
  }
}
</script>

<style lang="scss" scoped>
.jc-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.jc-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.1);
  z-index: 1000;
  .header-right {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}

.jc-content {
  margin-top: 60px;
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.jc-aside {
  position: relative;
  width: 280px;
  background-color: #fff;
  overflow-y: auto;
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.05);
  transition: width 0.3s ease;
  position: relative;
  z-index: 20;
  
  &.collapsed {
    width: 0;
    overflow: hidden;
  }
}
.toggle-aside {
    position: absolute;
    top: 120px;
    left: 280px;
    transform: translateY(-50%);
    width: 24px;
    height: 30px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    box-shadow: 2px 0 6px 0 rgba(0,0,0,0.1);
    z-index: 10;
    transition: left 0.3s ease;
    
    i {
      font-size: 16px;
      color: #666666;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  .collapsed + .toggle-aside {
    left: 0;
  }
.jc-main {
  flex: 1;
  overflow: hidden;
  background-color: #F7F7F7;
}

/* 历史版本抽屉样式 */

.empty-data {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

.version-list {
  .version-item {
    position: relative;
    padding: 15px 0 15px 15px;
    border-bottom: 1px solid #EBEEF5;
    transition: all 0.3s;
    cursor: pointer;
    
    &:hover {
      background-color: #F5F7FA;
    }
    
    .version-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .version-number {
        font-weight: bold;
        font-size: 16px;
      }
      .el-tag {
        border: 0;
        color: #0070FC;
        background: #EDF2FF;
      }
    }
    
    .version-time {
      color: #999999;
      margin-bottom: 5px;
      font-size: 14px;
    }
    
    .version-user {
      color: #333333;
      font-size: 14px;
      position: relative;
      padding-left: 14px;
      &::before {
        content: '·';
        font-size: 40px;
        color: #0070FC;
        position: absolute;
        left: 0;
        top: -14px;
      }
    }
    
    .version-actions {
      position: absolute;
      right: 15px;
      bottom: 0;
      transform: translateY(-50%);
    }
  }
}
.el-dropdown-link {
  border: 2px solid #666666;
  border-radius: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    padding: 2px;
    cursor: pointer;
}
</style>

<style lang="scss">
.jc-main{
  .tox .tox-toolbar__primary{
    justify-content: center;
    position: relative;
    z-index: 10;
  }
  .tox .tox-tbtn:hover{
    background-color: #F2F4FC;
  }
}
</style>

