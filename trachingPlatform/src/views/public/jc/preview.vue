<template>
    <div class="jc-container">
      <header class="jc-header">
        <div class="header-left flex"  @click="backRoute">
          <i class="el-icon-arrow-left line-36 m-r-6 bold cursor"></i>
          <span class="bold size-16 line-36">{{ query.textbookName }}</span>
        </div>
      </header>
      <div class="jc-content">
        <aside class="jc-aside flex-column" :class="{'collapsed': isAsideCollapsed}">
          <div class="directory-tree flex-1">
            <chapter-tree 
                ref="chapterTree"
                disabled
              :chapterData="directoryData" 
              @nodeClick="handleNodeClick"
            />
          </div>
        </aside>
        <div class="toggle-aside" @click="toggleAside">
          <i :class="isAsideCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
        <main class="jc-main ">
         <!-- <div class="w-p-45 h-full section-main overflow p-20 b-4" v-html="nowSection.content"></div> -->
          <js-view class="w-840 h-full section-main overflow b-4 flex" v-model="nowSection.content"></js-view>
        </main>
      </div>
    </div>
  </template>
  
  <script>
  import ChapterTree from './components/chapterTree.vue'
  import jsView from './components/views'

  export default {
    name: 'JcEditor',
    components: {
      ChapterTree,
      jsView
  },
    data() {
      return {
        content: '',
        directoryData: [],
        query:{},
        nowSection: {},
        isAsideCollapsed: false // 侧边栏是否收起
      }
    },
    mounted() {
      const { data } = this.$route.query;
      this.query = JSON.parse(data) || {};
      console.log(this.$route.query, this.query);
      this.getSectionTree(this.query)
    },
    methods: {
      // 获取章节树形
      async getSectionTree(data) {
        try {
          const res = await this.$api.GetSectionTree({textbookId: data.textbookId, schoolId: data.schoolId})
          console.log(res);
          if (res.errCode === 0) {
            this.directoryData = res.data
              const { id } = this.query
            // 递归查找树形结构中的节点
            const findNodeById = (nodes, targetId) => {
              for (const node of nodes) {
                if (node.id === targetId) {
                  return node;
                }
                if (node.children && node.children.length > 0) {
                  const found = findNodeById(node.children, targetId);
                  if (found) return found;
                }
              }
              return null;
            };
            
            const data = findNodeById(res.data, id)
            this.handleNodeClick(data )
            this.$nextTick(() => {
              this.$refs.chapterTree && this.$refs.chapterTree.setCurrentKey(id)
            })
          }
        } catch (error) {
          console.log('error-GetSectionTree',error);
        }
      },
      async handleNodeClick(data) {
        console.log('点击目录节点：', data)
        this.nowSection = data;
        try {
          const res = await this.$api.SectionGetById({id: data.id})
          console.log(res);
          if(res.errCode === 0) {
            this.content = res.data.content || ''
          this.nowSection = res.data;
        }
        } catch (error)  {
          this.$message.error('保存失败')
        }
      },
      backRoute() {
        this.$router.go(-1)
      },
      // 切换侧边栏收缩/展开状态
    toggleAside() {
      this.isAsideCollapsed = !this.isAsideCollapsed;
    },
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .jc-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f7fa;
  }
  
  .jc-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
    background-color: #fff;
    box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.1);
  
    .header-right {
      display: flex;
      gap: 10px;
    }
  }
  
  .jc-content {
    margin-top: 60px;
    flex: 1;
    display: flex;
    overflow: hidden;
  }
  
  .jc-aside {
    width: 280px;
    background-color: #fff;
    overflow-y: auto;
    box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.05);
    position: relative;
    z-index: 20;
     &.collapsed {
      width: 0;
      overflow: hidden;
    }
  }
  .toggle-aside {
    position: absolute;
    top: 100px;
    left: 280px;
    transform: translateY(-50%);
    width: 24px;
    height: 30px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0 4px 4px 0;
    box-shadow: 2px 0 6px 0 rgba(0,0,0,0.1);
    z-index: 10;
    transition: left 0.3s ease;
    
    i {
      font-size: 16px;
      color: #666666;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  .collapsed + .toggle-aside {
    left: 0;
  }
  .jc-main {
    flex: 1;
    background-color: #F7F7F7;
    overflow: auto;
    padding-top: 20px;
  }
  .section-main {
    margin: 0 auto;
    background: #ffffff;
  }
  </style>
  
  