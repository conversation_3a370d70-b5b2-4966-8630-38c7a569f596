<template>
 <div class="chapter-tree h-full p-y-20 p-x-10">
      <div v-if="chapterData.length==0" style="text-align: center;margin-top: 100px;">
        <img width="80%" src="../../../../assets/images/no-data.png" alt="无数据">
        <p style="color: #999;font-size: 14px;">暂无数据</p>
      </div>
      <el-tree
          v-else
          ref="tree"
          :data="chapterData"
          node-key="id"
          default-expand-all
          :props="defaultProps"
          :expand-on-click-node="false"
          draggable
          highlight-current
          @node-click="handleNodeClick"
          @node-drag-start="handleDragStart"
          @node-drag-enter="handleDragEnter"
          @node-drag-leave="handleDragLeave"
          @node-drag-over="handleDragOver"
          @node-drag-end="handleDragEnd"
          @node-drop="handleDrop"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag">
          <template #icon="{ node }" >
            <i
              v-if="node.expanded"
              class="icon-open"
            ></i>
            <i
              v-else
              class="icon-close"
            ></i>
          </template>
          <template #default="{ node, data }" >
            <span class="custom-tree-node p-r-10" @mouseenter="showDropdown(node)"
            @mouseleave="hideDropdown(node)">
              <!-- 添加展开/收起图标，仅对有子级的节点显示 -->
              <span class="node-title-wrapper">
                
                <i 
                  v-if="data.children && data.children.length > 0 "
                  :class="node.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"
                  class="expand-icon"
                  @click.stop="toggleExpand(node)"
                ></i>
                <i v-else-if="node.level === 1" class="expand-icon-placeholder"></i>

                <span :class="[isLevel1OrHasChildren(node, data)?'bold-title':'child-title']">{{ data.title }}</span>
              </span>

              <i v-if="!handleSelfEdit(node, data)" style="color: #C0C6D6;font-size: 16px;" class="iconfont icon-suo"></i>
              <el-dropdown @visible-change="handleDropdownVisibleChange">
                <span class="el-dropdown-link" >
                  <i
                    v-if="handleToolShow(node, data)"
                    class="el-icon-more rotate-icon"
                    style="margin-left: 10px; cursor: pointer"
                    @click.stop
                  />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item  @click.native="handleAddSibling(node, data)">添加同级</el-dropdown-item>
                  <el-dropdown-item  @click.native="handleAddChild(node, data)">添加子级</el-dropdown-item>
                  <el-dropdown-item @click.native="handleRename(node, data)">重命名</el-dropdown-item>
                  <el-dropdown-item @click.native="handleDelete(node, data)" divided>
                    <span class="color-F11B1B">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </el-tree>
 </div>
</template>
<script>

export default {
  name: '',
  props: {
    chapterData: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'title',
        value:'id'
      },
      currentNode: null
    };
  },
  methods: {
    setCurrentKey(key) {
      this.$refs.tree.setCurrentKey(key);
    },
    showDropdown(node) {
      this.$set(node, 'showDropdown', true);  // 使用Vue.set确保响应式更新
    },
    hideDropdown(node) {
      this.$set(node, 'showDropdown', false);  // 使用Vue.set确保响应式更新
    },
    handleToolShow(node, data) {
      let params = JSON.parse(this.$route.query.data);
      return node.showDropdown && !this.disabled && ((data.editors || []).some(e => e.isMe)|| params.type == 'my');
    },
    handleSelfEdit(node, data) {
      let params = JSON.parse(this.$route.query.data);
      return (data.editors || []).some(e => e.isMe)|| params.type =='my';
    },
    // 切换节点展开/收起状态
    toggleExpand(node) {
      node.expanded ? node.collapse() : node.expand();
    },
    // 判断是否为一级节点或有子节点
    isLevel1OrHasChildren(node, data) {
      return node.level === 1 || (data.children && data.children.length > 0);
    },
    // 自定义图标
    handleDropdownVisibleChange(visible) {
      if (!visible) {
        this.currentNode = null;
      }
    },
      // 添加同级节点
      handleAddSibling(node, data) {
      this.$prompt('请输入章节名称', '添加同级章节', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value) return '章节名称不能为空';
        }
      }).then(({ value }) => {
        this.$emit('addNode', {
          title: value,
          parentId: node.parent.data.id || 0,
          type: 'sibling'
        });
      });
    },
    // 添加子节点
    handleAddChild(node, data) {
      this.$prompt('请输入章节名称', '添加子章节', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value) return '章节名称不能为空';
        }
      }).then(({ value }) => {
        this.$emit('addNode', {
          title: value,
          parentId: data.id,
          type: 'child'
        });
      });
    },
    // 重命名节点
    handleRename(node, data) {
      this.$prompt('请输入新名称', '重命名章节', {
        inputValue: data.title,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value) return '章节名称不能为空';
        }
      }).then(({ value }) => {
        this.$emit('renameNode', {
          id: data.id,
          title: value,
          parentId: data.parentId
        });
      });
    },
    // 删除节点
    handleDelete(node, data) {
      this.$confirm('确定删除该章节吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('deleteNode', data);
      });
    },
    // 节点单击
    handleNodeClick(val){
      this.$emit('nodeClick',val);
    },
    handleDragStart(node, ev) {
      console.log('drag start', node);
    },
    handleDragEnter(draggingNode, dropNode, ev) {
      console.log('tree drag enter: ', dropNode.label);
    },
    handleDragLeave(draggingNode, dropNode, ev) {
      console.log('tree drag leave: ', dropNode.label);
    },
    handleDragOver(draggingNode, dropNode, ev) {
      console.log('tree drag over: ', dropNode.label);
    },
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      console.log('tree drag end: ', dropNode && dropNode.label, dropType);
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
      console.log('tree drop: ', dropNode.label, dropType);
      
      // 获取拖拽节点的id和原父节点id
      const id = draggingNode.data.id;
      
      const previous = draggingNode.data.parentId || 0; // 如果是根节点，parentId为0
      
      // 根据放置类型确定新的父节点id
      let parentId = 0;
      if (dropType === 'inner') {
        // 如果是放入节点内部，则新父节点为放置节点
        parentId = dropNode.data.id;
      } else {
        // 如果是放在节点前或后，则新父节点与放置节点同级
        parentId = dropNode.data.parentId || 0;
      }
      
      // 触发自定义事件，将排序信息传递给父组件
      this.$emit('sortNode', {
        id,
        parentId,
        previous
      });
    },
    allowDrop(draggingNode, dropNode, type) {
      if (dropNode.data.id === draggingNode.data.parentId) {
        return false;
      }
      return true; // 明确返回true，允许其他情况的拖放
    },
    allowDrag(draggingNode) {
      // 确保明确返回布尔值
      return draggingNode.data.parentId !== 0;
    }
  }
}
</script>
 
<style lang="scss">
.chapter-tree{
  overflow:auto;
  .el-tree-node{
    height:auto;
    line-height:36px;
    &.is-current > .el-tree-node__content {
      background-color: #f0f7ff;
    }
  }
  .el-tree-node__expand-icon{
    display:none;
  }

  .custom-tree-node{
    height:36px;
    line-height:36px;
    color:#333;
    font-size:14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .icon-open{
    display:inline-block;
    width:20px;
    height:20px;
    background:url('../img/open.png') no-repeat center;
    position: relative;
    top: 5px;
    margin-right: 6px;
  }
  .icon-close{
    display:inline-block;
    width:20px;
    height:20px;
    background:url('../img/close.png') no-repeat center;
    position: relative;
    top: 5px;
    margin-right: 6px;
  }
  .node-title-wrapper {
    display: flex;
    align-items: center;
  }
  .expand-icon {
    margin-right: 5px;
    cursor: pointer;
    font-size: 16px;
    width: 16px;
    display: inline-block;
    text-align: center;
    background: #EDF2FF;
    border-radius: 16px;
    color: #0070FC;
  }
  .expand-icon-placeholder {
    width: 16px;
    margin-right: 5px;
    display: inline-block;
  }
  .bold-title {
    font-weight: bold;
    color: #333333;
  }
  .child-title{
    color: #666666;
    padding-left: 26px;
  }
  .rotate-icon {
    transform: rotate(90deg);
  }
}
.color-F11B1B {
  color: #F11B1B;
}
</style>