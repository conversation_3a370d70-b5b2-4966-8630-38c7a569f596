// Literature.js 文献
// 轮播图的CSS样式
function getCss() {
    return `
      
    `;
}

// 轮播图的JS逻辑
function getJs() {
    return `
      // 初始化并处理 iframe 内容和按钮事件
        class Literature {
            constructor(wrapper) {
                this.init();
            }

            // 初始化事件
            init() {
                
            }
        }

    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
    console.log(document.querySelectorAll('.type-literature .literature-actions'))
        document.querySelectorAll('.type-literature .literature-actions').forEach(function(overlay) {
        console.log(overlay)
            overlay.remove();  
        });
    });
    `;
}



export default { getCss, getJs };