// h5-iframe.js

// 轮播图的CSS样式
function getCss() {
    return `
      .note-mark {
  background-color: #EDF2FF !important;
  border-radius: 2px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  position: relative;
}

.note-mark:hover {
  background-color: #ffe7ba !important;
}

.note-tooltip {
  position: absolute !important;
  z-index: 1002 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: #fff !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  min-width: 400px !important;
  max-width: 600px !important;
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  pointer-events: none !important; /* 修改为none，使鼠标事件穿透tooltip */
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  white-space-collapse: collapse;
}
.note-tooltip[data-placement="top"]::before {
  content: '' !important;
  position: absolute !important;
  bottom: -6px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border-width: 6px 6px 0 !important;
  border-style: solid !important;
  border-color: rgba(0, 0, 0, 0.8) transparent transparent !important;
}

.note-tooltip[data-placement="bottom"]::before {
  content: '' !important;
  position: absolute !important;
  top: -6px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border-width: 0 6px 6px !important;
  border-style: solid !important;
  border-color: transparent transparent rgba(0, 0, 0, 0.8) !important;
}

.note-tooltip[data-placement="left"]::before {
  content: '' !important;
  position: absolute !important;
  right: -6px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-width: 6px 0 6px 6px !important;
  border-style: solid !important;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.8) !important;
}

.note-tooltip[data-placement="right"]::before {
  content: '' !important;
  position: absolute !important;
  left: -6px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-width: 6px 6px 6px 0 !important;
  border-style: solid !important;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent !important;
}

.note-tooltip-content {
  margin-bottom: 8px !important;
}

.note-tooltip-item {
  display: flex !important;
  margin-bottom: 4px !important;
}

.note-tooltip-item:last-child {
  margin-bottom: 0 !important;
}

.note-tooltip-text {
  font-weight: bold !important;
  color: #409eff !important;
  margin-bottom: 8px !important;
  display: block !important;
}

.note-tooltip-label {
  color: #909399 !important;
  margin-right: 8px !important;
  flex-shrink: 0 !important;
}

.note-tooltip-value {
  flex: 1 !important;
  word-break: break-all !important;
}

.note-tooltip-actions {
  display: flex !important;
  justify-content: flex-end !important;
  margin-top: 8px !important;
  padding-top: 8px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.edit-note-btn {
  padding: 4px 12px !important;
  border: none !important;
  border-radius: 4px !important;
  background-color: #409eff !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
}

.edit-note-btn:hover {
  background-color: #66b1ff !important;
}
    `;
}

// 重点词的JS逻辑
function getJs() {
    return `
class CuNote {
    constructor(noteWrapper) {
        this.noteWrapper = noteWrapper;
        this.init();
    }

    // 初始化事件
    init() {
        // 移除旧的事件监听器（如果存在）
        const oldHandleMouseEnter = this.noteWrapper._handleMouseEnter;
        const oldHandleMouseLeave = this.noteWrapper._handleMouseLeave;
        
        if (oldHandleMouseEnter) {
            this.noteWrapper.removeEventListener('mouseenter', oldHandleMouseEnter);
        }
        
        if (oldHandleMouseLeave) {
            this.noteWrapper.removeEventListener('mouseleave', oldHandleMouseLeave);
        }
        
        // 定义鼠标移入事件处理函数
        const handleMouseEnter = async (e) => {
            // 获取data-note属性值（词条ID）
            const id = this.noteWrapper.getAttribute('data-note');
            if (!id) return;
            
            try {
                // 通过postMessage与父页面通信获取词条详情
                return new Promise((resolve) => {
                    // 创建一个唯一的消息ID
                    const messageId = 'note_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    
                    // 监听来自父页面的消息
                    const messageHandler = (event) => {
                        console.log(event)
                        // 确保消息是我们期望的响应
                        if (event.data && event.data.type === 'noteDetailResponse' && event.data.messageId === messageId) {
                            // 移除事件监听器
                            window.removeEventListener('message', messageHandler);
                            const noteData = event.data.result;
                            
                            // 移除已存在的tooltip
                            const existingTooltip = document.querySelector('.note-tooltip');
                            if (existingTooltip) {
                                existingTooltip.remove();
                            }
                            
                            // 创建tooltip
                            const tooltip = document.createElement('div');
                            tooltip.className = 'note-tooltip';
                            tooltip.innerHTML = \`
                                <div class="note-tooltip-content">
                                    <div class="note-tooltip-item note-tooltip-text">
                                        \${noteData.word || ''}
                                    </div>
                                    \${noteData.alias ? \`
                                        <div class="note-tooltip-item">
                                            <div class="note-tooltip-label">别名:</div>
                                            <div class="note-tooltip-value">\${noteData.alias}</div>
                                        </div>
                                    \` : ''}
                                    \${noteData.paraphrase ? \`
                                        <div class="note-tooltip-item">
                                            <div class="note-tooltip-label">释义:</div>
                                            <div class="note-tooltip-value">\${noteData.paraphrase}</div>
                                        </div>
                                    \` : ''}
                                </div>
                            \`;
                            
                            this.noteWrapper.appendChild(tooltip);
                            
                            // 定位tooltip
                            const rect = this.noteWrapper.getBoundingClientRect();
                            const tooltipRect = tooltip.getBoundingClientRect();
                            
                            tooltip.style.left = '0px';
                            tooltip.style.top = '40px';
                            
                            // 添加鼠标悬停事件
                            tooltip.addEventListener('mouseenter', () => {
                                tooltip.setAttribute('data-hover', 'true');
                            });
                            
                            tooltip.addEventListener('mouseleave', () => {
                                tooltip.removeAttribute('data-hover');
                                setTimeout(() => {
                                    if (!tooltip.hasAttribute('data-hover')) {
                                        tooltip.remove();
                                    }
                                }, 200);
                            });
                            
                            resolve();
                        }
                    };
                    
                    // 添加消息监听器
                    window.addEventListener('message', messageHandler);
                    
                    // 发送消息给父页面
                    window.parent.postMessage({
                        type: 'getNoteDetail',
                        id: id,
                        messageId: messageId
                    }, '*');
                    
                    // 设置超时处理
                    setTimeout(() => {
                        window.removeEventListener('message', messageHandler);
                        resolve();
                    }, 5000);
                });
            } catch (error) {
                console.error('获取词条详情出错:', error);
            }
        };

        // 定义鼠标移出事件处理函数
        const handleMouseLeave = (e) => {
            const tooltip = this.noteWrapper.querySelector('.note-tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        };

        // 保存事件处理函数到元素上，以便后续可以移除
        this.noteWrapper._handleMouseEnter = handleMouseEnter;
        this.noteWrapper._handleMouseLeave = handleMouseLeave;
        
        // 绑定事件
        this.noteWrapper.addEventListener('mouseenter', handleMouseEnter);
        this.noteWrapper.addEventListener('mouseleave', handleMouseLeave);
    }
}

// 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.note-mark').forEach((noteWrapper) => {
        new CuNote(noteWrapper);
    });
});
    `;
}



export default { getCss, getJs };