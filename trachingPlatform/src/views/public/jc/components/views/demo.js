// h5-iframe.js

// 轮播图的CSS样式
function getCss() {
    return `
      
    `;
}

// 轮播图的JS逻辑
function getJs() {
    return `
      

    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.type-iframe').forEach((carouselWrapper) => {
            
        });
        document.querySelectorAll('.iframe-overlay').forEach(function(overlay) {
            overlay.remove();  
        });
    });
    `;
}



export default { getCss, getJs };