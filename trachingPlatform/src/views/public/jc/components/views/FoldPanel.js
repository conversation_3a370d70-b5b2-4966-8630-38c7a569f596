// FoldPanel.js

// 轮播图的CSS样式
function getCss() {
    return `
      .fold-panel-wrapper {
        position: relative;
        width: 100%;
        margin: 10px 0;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        }

        .fold-panel {
        width: 100%;
        }

        .fold-panel-header {
        padding: 12px 16px;
        background: #fafafa;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        user-select: none;
        }

        .fold-panel-title {
        font-size: 14px;
        color: #333;
        }

        .fold-panel-icon {
        font-size: 12px;
        color: #666;
        transition: transform 0.3s;
        }

        .fold-panel.active .fold-panel-icon {
        transform: rotate(180deg);
        }

        .fold-panel-content {
        padding: 0;
        height: 0;
        overflow: hidden;
        transition: all 0.3s;
        }

        .fold-panel.active .fold-panel-content {
        padding: 16px;
        height: auto;
        }

        .fold-panel-text {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        }

        .fold-panel-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.3);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
        z-index: 1;
        }

        .fold-panel-wrapper:hover .fold-panel-overlay {
        opacity: 1 !important;
        pointer-events: auto !important;
        }

        .edit-fold-panel-btn {
        padding: 6px 12px;
        font-size: 14px;
        background-color: #007bff;
        border: none;
        border-radius: 4px;
        color: white;
        cursor: pointer;
        }

        .delete-fold-panel-btn {
        margin-left: 10px;
        padding: 6px 12px;
        font-size: 14px;
        background-color: #ffffff;
        border: none;
        border-radius: 4px;
        color: #000000;
        cursor: pointer;
        }
    `;
}

// 轮播图的JS逻辑
function getJs() {
    return `
      class FoldPanel {
        constructor(panelEl) {
            if (!panelEl) return;
            
            this.panelEl = panelEl;
            this.header = panelEl.querySelector('.fold-panel-header');
            this.panel = panelEl.querySelector('.fold-panel');
            
            // 初始化面板的展开/收起功能
            this.init();
        }

        // 初始化方法，绑定事件
        init() {
            if (this.header) {
                this.header.addEventListener('click', () => {
                    this.panel.classList.toggle('active');
                });
            }
        }

        // 清理方法，移除事件监听等
        destroy() {
            if (this.header) {
                this.header.removeEventListener('click', this.togglePanel);
            }
        }

        // 面板展开/收起切换
        togglePanel() {
            this.panel.classList.toggle('active');
        }
    }
    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.type-panel').forEach(function(panelEl) {
            // 移除 .fold-panel-overlay
            const overlay = panelEl.querySelector('.fold-panel-overlay');
            if (overlay) {
                overlay.remove();
            }

            // 使用 FoldPanel 类初始化面板
            new FoldPanel(panelEl);
        });
    });
    `;
}



export default { getCss, getJs };