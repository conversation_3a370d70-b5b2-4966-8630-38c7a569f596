// h5-iframe.js

// 轮播图的CSS样式
function getCss() {
    return `
      .iframe-wrapper {
        position: relative !important;
        margin: 20px auto !important;
        transition: all 0.3s !important;
        }
        .type-iframe {
          position: relative !important;
        }
        .iframe-wrapper.preview-mode {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100vh !important;
        z-index: 999999 !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        margin: 0 !important;
        }

        .iframe-container {
        position: relative !important;
        width: 100% !important;
        height: 400px !important;
        background-color: #f5f5f5 !important;
        border-radius: 4px !important;
        overflow: hidden !important;
        border: none !important;
        }

        .iframe-overlay {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 10px !important;
        opacity: 0 !important;
        transition: opacity 0.3s !important;
        pointer-events: none !important;
        }

        .iframe-wrapper:hover .iframe-overlay {
        opacity: 1 !important;
        pointer-events: auto !important;
        }

        .iframe-wrapper.preview-mode .iframe-overlay {
        display: none !important;
        }

        .iframe-overlay button {
        padding: 6px 12px !important;
        border: none !important;
        border-radius: 4px !important;
        background-color: #409eff !important;
        color: white !important;
        cursor: pointer !important;
        transition: all 0.3s !important;
        font-size: 14px !important;
        }
    `;
}

// 轮播图的JS逻辑
function getJs() {
    return `
      // 初始化并处理 iframe 内容和按钮事件
        class CuIframe {
            constructor(iframeWrapper) {
                this.iframeWrapper = iframeWrapper;
                this.iframeContainer = iframeWrapper.querySelector('.iframe-container');
                this.iframe = this.iframeContainer.querySelector('iframe');
                this.overlay = iframeWrapper.querySelector('.iframe-overlay');
                this.newWindowBtn = this.overlay.querySelector('.new-window-iframe-btn');

                this.init();
            }

            // 初始化事件
            init() {
                this.newWindowBtn.addEventListener('click', () => {
                    window.open(this.iframe.src, '_blank');
                });
            }
        }


    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.type-iframe').forEach((cuIframeWrapper) => {
            new CuIframe(cuIframeWrapper);
        });
        document.querySelectorAll('.iframe-overlay button').forEach(function(overlay) {
            if (!overlay.classList.contains('new-window-iframe-btn')) {
                overlay.remove();  
            }
        });
    });
    `;
}



export default { getCss, getJs };