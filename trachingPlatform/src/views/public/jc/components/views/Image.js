// image-iframe.js

// 图的CSS样式
function getCss() {
    return `
    .type-image img {
        cursor: pointer;
    }
    `;
}

// 图的JS逻辑
function getJs() {
    return `

    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.type-image').forEach((img) => {
            // 直接为图片元素添加点击事件，将预览请求发送给父级iframe处理
            img.addEventListener('click', function(e) {
                e.stopPropagation();
                e.preventDefault();
                
                // 生成唯一消息ID
                const messageId = 'img_preview_' + Date.now();
                
                // 向父窗口发送消息，请求全屏预览图片
                window.parent.postMessage({
                    type: 'previewImage',
                    messageId: messageId,
                    src: img.src
                }, '*');
            });
        });
    });
    `;
}



export default { getCss, getJs };