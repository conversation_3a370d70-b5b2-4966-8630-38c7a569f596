// Carousel.js

// 轮播图的CSS样式
function getCss() {
    return `
    /* 编辑状态下的样式 */
.carousel-container {
  width: 100%;
  height: 300px;
  margin: 10px 0;
  position: relative;
  overflow: hidden;
}
.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  display: flex;
  justify-content: center;
  align-items: center;
}
.carousel-slide.active {
  opacity: 1;
}
.carousel-slide img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer; /* 添加指针样式，表示可点击 */
}
.carousel-nav {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}
.carousel-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin: 0 4px;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s;
}
.carousel-dot.active {
  background: #fff;
}

.carousel-slides {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  justify-content: center;
  align-items: center;
}

.carousel-slide.active {
  display: flex;
}

.carousel-slide img {
  max-width: 100%;
  max-height: 100%;
  height: 100%;
  width: auto;
  object-fit: contain;
}

.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 10;
}
    `;
}

// 轮播图的JS逻辑
function getJs() {
    return `
      class Carousel {
      constructor(carouselWrapper) {
        this.carouselWrapper = carouselWrapper;
        this.carouselContainer = carouselWrapper.querySelector('.carousel-container');
        this.carouselSlides = this.carouselContainer.querySelectorAll('.carousel-slide');
        this.carouselDots = carouselWrapper.querySelectorAll('.carousel-dot');
        this.currentIndex = 0;

        this.init();
      }

      // 更新点样式
      updateDots() {
        this.carouselDots.forEach((dot, i) => {
          if (i === this.currentIndex) {
            dot.classList.add('active');
          } else {
            dot.classList.remove('active');
          }
        });
      }

      // 更新轮播图显示
      updateCarousel() {
        this.carouselSlides.forEach((slide, i) => {
          slide.classList.toggle('active', i === this.currentIndex);
        });
        this.updateDots();
      }

      // 点击导航点
      setupEventListeners() {
        // 设置导航点点击事件
        this.carouselDots.forEach((dot, i) => {
          dot.addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();
            this.currentIndex = i;
            this.updateCarousel();
          });
        });

        // 设置图片点击预览事件
        this.carouselSlides.forEach((slide) => {
          const img = slide.querySelector('img');
          if (img) {
            img.addEventListener('click', (e) => {
              e.stopPropagation();
              e.preventDefault();
              this.showPreview();
            });
          }
        });
      }

      // 显示预览
      showPreview() {
        // 获取轮播图数据
        const urlsJson = this.carouselWrapper.getAttribute('data-urls');
        let urls = [];
        
        try {
          urls = JSON.parse(urlsJson || '[]');
        } catch (e) {
          console.error('解析轮播图数据失败:', e);
          urls = [];
        }
        
        if (urls.length === 0) {
          // 如果没有找到URLs数据，则从当前轮播图中提取图片URL
          this.carouselSlides.forEach(slide => {
            const img = slide.querySelector('img');
            if (img && img.src) {
              urls.push(img.src);
            }
          });
        }
        
        if (urls.length === 0) return; // 如果没有图片，则不显示预览
        
        // 生成唯一消息ID
        const messageId = 'carousel_' + Date.now();
        
        // 通过postMessage向父窗口发送预览请求
        window.parent.postMessage({
          type: 'previewCarousel',
          urls: urls,
          currentIndex: this.currentIndex,
          messageId: messageId
        }, '*');
      }

      // 自动轮播
      autoSlide() {
        this.autoSlideTimer = setInterval(() => {
          this.currentIndex = (this.currentIndex + 1) % this.carouselSlides.length;
          this.updateCarousel();
        }, 3000);  // 每3秒自动切换
      }

      // 初始化轮播图
      init() {
        this.updateCarousel();
        this.setupEventListeners();
        this.autoSlide();
      }
    }

    // 使用事件监听 DOMContentLoaded 确保 DOM 加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化所有轮播图
        document.querySelectorAll('.type-carousel').forEach((carouselWrapper) => {
            new Carousel(carouselWrapper);
        });
        
        // 移除可能存在的轮播图覆盖层
        document.querySelectorAll('.carousel-overlay').forEach(function(overlay) {
            overlay.remove();  
        });
    });
    `;
}



export default { getCss, getJs };