<!--
 * @Author: ztong <EMAIL>
 * @Date: 2024-06-15 11:09:48
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2025-01-08 18:03:17
 * @FilePath: \fusion_front\src\views\public\forgetPassword.vue
 * @Description: 忘记密码
-->
 <template>
  <div class="forget-password-com">
    <div class="tabs">
      <span class="tabs-item active">找回密码</span>
    </div>
    <el-form class="password-form" ref="form" :rules="passWordRule" :model="passWordForm" label-width="0px">
      <el-form-item prop="phone">
        <el-input 
          v-model="passWordForm.phone"
          placeholder="请输入手机号"
          @input="passWordForm.phone=passWordForm.phone.trim()"
          >
          <template #prefix>
            <i class="iconfont icon-zhanghao prefix"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="verificationCode">
        <el-input class="verification-input" placeholder="请输入验证码" v-model="passWordForm.verificationCode" @input="passWordForm.verificationCode=passWordForm.verificationCode.trim()">
          <template #prefix>
            <i class="iconfont icon-yanzhengyanzhengma prefix"></i>
          </template>
        </el-input>
        <el-button class="code-btn" :disabled="isDisableBtn" @click="getPhoneCode">{{btnText}}</el-button>
      </el-form-item>
      <el-form-item prop="newPassword">
        <el-input placeholder="请输入新密码" show-password
        auto-complete="new-password" maxlength="20" v-model="passWordForm.newPassword" @input="passWordForm.newPassword=passWordForm.newPassword.trim()">
          <template #prefix>
            <i class="el-icon-lock prefix"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="rePassword">
        <el-input  placeholder="请二次确认密码" show-password
        auto-complete="new-password" maxlength="20" v-model="passWordForm.rePassword" @input="passWordForm.rePassword=passWordForm.rePassword.trim()">
          <template #prefix>
            <i class="el-icon-lock prefix"></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item >
        <el-button class="submit-password" @click="submitForm">确定</el-button>
        <el-button class="back-login" type="text" @click="$emit('backLogin')">返回登录</el-button>
      </el-form-item>
    </el-form>
  </div>
 </template>
 <script>
 import { ACE } from '@/tool/compress.js'
 export default {
   name: 'forgetPassword',
   props: '',
   components: {
   },
   data() {
    // 自定义校验规则
    const validatePass = (rule, value, callback) => {
      const hasChineseCharacters = /[\u4E00-\u9FFF]/;
      if (value === '') {
        callback(new Error('请输入新密码'));
      } else if(hasChineseCharacters.test(value)){
        callback(new Error('密码不能包含中文字符'));
      } else {
        if (this.passWordForm.rePassword !== '') {
          this.$refs.form.validateField('rePassword');
        }
        callback();
      }
    };
    const reValidatePass = (rule, value, callback) => {
      const hasChineseCharacters = /[\u4E00-\u9FFF]/;
      if (value === '') {
        callback(new Error('请二次输入新密码'));
      } else if(hasChineseCharacters.test(value)){
        callback(new Error('密码不能包含中文字符'));
      } else if (value !== this.passWordForm.newPassword) {
        callback(new Error('两次输入的密码不匹配!'));
      } else {
        callback();
      }
    };
     return {
      status:'resetPassword',
      btnText:'获取验证码',
      isDisableBtn:false,// 验证码按钮禁用状态
      passWordForm:{
        phone:'',// 电话号码
        verificationCode:'',// 验证码
        newPassword:'',//新密码
        rePassword:'',// 二次确认密码
      },
      passWordRule:{
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        verificationCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
        ],
        newPassword: [
          { min: 6, max: 20, message: '密码长度控制在 6 到 20 个字符', trigger: 'blur' },
          { required: true, validator: validatePass, trigger: 'blur' },
        ],
        rePassword: [
          { required: true, validator: reValidatePass, trigger: 'blur' },
        ],
      }
     }
   },
   created(){
    // 获取有课token
    this.$api.GetExperienceToken({loginType:3}).then(res => {
      if (res.data) {
        sessionStorage.setItem('AccessToken', res.data.access_token)
      } 
    })
   },
   mounted(){
      window.addEventListener("keydown", this.keyDown);
   },
   beforeDestroy(){
     // 销毁事件
     window.removeEventListener("keydown", this.keyDown, false);
   },
   methods:{
    keyDown(e) {
      if (e.keyCode == 13 && e.target.tagName != "BUTTON") {
        if (this.status == "resetPassword") {
          this.submitForm(); // 修改密码
        }
      }
    },
    // 获取短信验证码
    getPhoneCode(){
      let erg = /^1[3456789]\d{9}$/
      if(this.passWordForm.phone == ''){
        this.$message({
          message: '请输入手机号',
          type: 'warning',
          offset:260
        })
        return
      }else if(!erg.test(this.passWordForm.phone)){
        this.$message({
          message: '请输入正确的手机号',
          type: 'warning',
          offset:260
        })
        return
      }
      this.handleDownTime()
      let params ={ 
        phone: this.passWordForm.phone,
        type: 1, 
        verifyCode: encodeURIComponent(ACE())  
      }
      this.$api.GetVerifyCodeResetPassword(params).then(res=>{
        this.$message({
          type:'success',
          message: res.Msg||'发送成功！',
          offset: 160
        })
      })
    },
    // 提交 忘记密码
    submitForm(){
      this.$refs.form.validate(async(valid) => {
        if (valid) {
            console.log('this.passWordForm',this.passWordForm);
            let params = {
              mb: this.passWordForm.phone,
              verificationCode: this.passWordForm.verificationCode,
              password: this.passWordForm.newPassword,
              againPassword: this.passWordForm.rePassword,
            }
            let {Code,Msg} = await this.$api.RetrievePassword(params)
            if(Code==200){
              this.$message({
                type:'success',
                message:'设置成功,下次登陆时请使用新密码'
              })
              this.$emit('backLogin');// 返回登录
            }else{
              this.$message({
                type:'error',
                message: Msg
              })
            }
            
        } else {
          
          return false;
        }
      });
    },
    // 按钮倒计时
    handleDownTime(){
      let second = 60;
      this.isDisableBtn = true
      let timer = setInterval(() => {
        this.btnText=`${second}秒后可重新发送`;
        second--;
        if(second==0){
          clearInterval(timer);
          this.btnText = '获取验证码';
          this.isDisableBtn = false;
        }
      },1000)
    }
   }
 }
 </script>
  
 <style lang="scss" scoped>
  .forget-password-com{
    width: 380px;
    margin: 0 auto;
    .tabs {
      width:380px;
      display: flex;
      justify-content: center;
      margin: 0 auto;

      .tabs-item {
        color: #333;
        font-weight: 400;
        font-size: 16px;
        margin-top: 18px;
        cursor: pointer;
      }

      .active {
        position: relative;
        &::before {
          content: "";
          width: 50px;
          height: 10px;
          background: #3080f4;
          border-radius: 5px;
          position: absolute;
          top: 11px;
          left: 7px;
          opacity: 0.54;
        }
      }
    }
    ::v-deep .password-form{
      margin-top: 30px;
      .el-input__inner{
        border-radius: 30px;
        padding-left: 50px;
        height: 44px;
        border: none;
        background: #f2f5ff;
      }
      .prefix {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        line-height: 0.5;
        font-size: 16px;
        font-weight: 500;
        color: var(--color-primary3);
      }
      .verification-input{
        width:200px;
      }
      .code-btn{
        margin-left:20px;
        width: 120px;
        height: 44px;
        border-radius: 30px;
        // display: flex;
        // justify-content: center;
        align-items: center;
        background-color: #3e67fa;
        color: #ffffff;
        font-size: 14px;
        cursor: pointer;
        caret-color: transparent;
      }
      .submit-password{
        width: 380px;
        display: block;
        height: 44px;
        font-size: 18px;
        font-weight: 500;
        background: linear-gradient(270deg, #5877fb 0%, #114bfa 81%, #0f4afa 83%, #0041fa 100%);
        box-shadow: 0px 3px 6px 1px rgba(0, 65, 250, 0.42);
        color: #fff;
        border-radius: 30px;
        margin-top: 10px;
      }
      .back-login{
        float: right;
        margin-right:20px;
      }
    }
  }
 </style>

