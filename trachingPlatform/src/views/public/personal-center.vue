<template>
  <div class="personal-box">
    <!-- 个人中心 -->
    <div class="top-backg">
      <div class="title">
        <span>个人中心</span>
        <!-- <span>Personal Center</span> -->
      </div>
    </div>
    <div class="footer-backg">
      <!-- <span>湖南中德安普大数据网络科技有限公司版权所有@2023</span> -->
    </div>

    <div></div>
    <div
      v-if="status == 'index'"
      class="personal-content">
      <div class="toux">
        <img
          :src="userInfos.photo ? userInfos.photo : require('../../assets/public/personal/icon.png')"
          alt="" />
        <el-upload
          circle
          class="upload-demo"
          :action="uploadUrl"
          accept=".png,.jpg"
          :show-file-list="false"
          :on-error="upLoadError"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess">
          <el-button
            size="mini"
            icon="el-icon-edit"
            circle></el-button>
        </el-upload>
      </div>

      <div class="userName">{{ userInfo.name }}</div>

      <div class="userInfo">
        <div class="item">
          <span>身份</span>
          <span>{{ userInfo.userType | identity }}</span>
        </div>
        <div class="item school">
          <span>姓名</span>
          <div>
            <span v-if="!nameStatus" style="color: #333;font-size: 16px;">{{ userInfos.realName }}</span>
            <el-input v-else @blur="modifyName" ref="realNameInput"  v-model="realName" placeholder="请输入"></el-input>
            <el-button
              type="text"
              style="margin-left: 10px;"
              @click="editRealName">
              <i
                style="font-size: 20px"
                class="el-icon-edit"></i
            ></el-button>
          </div>
        </div>
        <div class="item">
          <span>所属学院</span>
          <span>{{ userInfo.schoolName }}</span>
        </div>
      </div>

      <div class="divider">
        <img
          src="@/assets/public/personal/divider-icon.png"
          alt="" />
      </div>

      <div class="editingArea">
        <div class="item">
          <span>设置密码</span>
          <input
            type="password"
            value="111111111"
            disabled />
          <el-button
            type="text"
            @click="status = 'changePwd'">
            <i
              style="font-size: 20px"
              class="el-icon-edit"></i
          ></el-button>
        </div>
        <div class="item">
          <span>绑定手机</span>
          <div class="input">{{ userInfos.mobilePhone }}</div>
          <el-button
            type="text"
            @click="status = 'bhoneBinding'">
            <i
              style="font-size: 20px"
              class="el-icon-edit"></i
          ></el-button>
        </div>
      </div>
    </div>

    <div
      v-if="status == 'changePwd'"
      class="changePwd">
      <div class="title">
        <span style="margin: auto;">修改密码</span>
        <el-button plain @click="status = 'index'" v-if="butShow">返回</el-button>
      </div>
      <div class="divider">
        <img
          src="@/assets/public/personal/divider-icon.png"
          alt="" />
      </div>
      <!-- <div class="item">
        <span>密码</span>
        <el-input
          show-password
          v-model="pwdForm.oldPwd"
          placeholder="请输入当前账号密码"></el-input>
      </div>
      <div class="item">
        <span>新密码</span>
        <el-input
          show-password
          v-model="pwdForm.newPwd"
          placeholder="请输入6～12位密码（大小写+数字"></el-input>
      </div>
      <div class="item">
        <span>确认新密码</span>
        <el-input
          show-password
          v-model="pwdForm.newPwds"
          placeholder="请输入6～12位密码（大小写+数字"
          @blur="shiqu"></el-input>
      </div> -->
      <el-form :model="pwdForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
        <el-form-item prop="oldPwd">
          <el-input placeholder="请输入当前账号密码" v-model.trim="pwdForm.oldPwd">
            <template #prefix>
              <i class="el-icon-lock prefix"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="newPwd">
          <el-input placeholder="请输入8～12位密码（大小写+数字)" v-model.trim="pwdForm.newPwd" :type="passwordShown ? 'text' : 'password'">
            <template #prefix>
              <i class="el-icon-lock prefix"></i>
            </template>
            <i slot="suffix" class="iconfont suffix" :class="passwordShown ? 'icon-yanjing1' : 'icon-yanjing2'"
               @click="handleTogglePassword('passwordShown')"></i>
          </el-input>
        </el-form-item>
        <el-form-item prop="newPwds">
          <el-input placeholder="请输入8～12位密码（大小写+数字)" v-model.trim="pwdForm.newPwds" :type="passwordShowns ? 'text' : 'password'">
            <template #prefix>
              <i class="el-icon-lock prefix"></i>
            </template>
            <i slot="suffix" class="iconfont suffix" :class="passwordShowns ? 'icon-yanjing1' : 'icon-yanjing2'"
               @click="handleTogglePassword('passwordShowns')"></i>
          </el-input>
        </el-form-item>
      </el-form>

      <div class="but">
        <!-- <button @click="status = 'index'">取消</button> -->
        <button  @click="submitForm('ruleForm')">确认</button>
      </div>
    </div>

    <div
      v-if="status == 'bhoneBinding'"
      class="bhoneBinding">
      <div class="title">绑定手机</div>
      <div class="divider">
        <img
          src="@/assets/public/personal/divider-icon.png"
          alt="" />
      </div>

      <div class="phoneNumber">
        <el-input
          v-model="phoneNumber"
          placeholder="请输入您的手机号">
          <i slot="prefix" class="iconfont icon-shouji"></i>
        </el-input>
      </div>

      <div class="item">
        <el-input
          v-model="imgCode"
          placeholder="请输入图形验证码">
          <i slot="prefix" class="iconfont icon-baohu"></i>
        </el-input>

        <!-- <button class="sending" @click="SendCode" >发送验证码</button> -->
        <div class="code">
          <img title="刷新验证码" v-if="verifyCode.id" :src="`data:image/png;base64,${verifyCode.data}`"
            @click="getVerifyCode" />
        </div>
      </div>
      <div class="item">
        <el-input
          v-model="code"
          placeholder="请输入您的验证码">
          <i slot="prefix" class="iconfont icon-baohu"></i>
        </el-input>

        <!-- <button class="sending" @click="SendCode" >发送验证码</button> -->
        <button
          class="sending"
          :disabled="countdown > 0"
          @click="SendCode">
          {{ countdown === 0 ? "发送验证码" : `重新发送(${countdown}s)` }}
        </button>
      </div>

      <div class="but">
        <button @click="status = 'index'">取消</button>
        <button @click="BindPhoneNumber">确认</button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      let trg = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,12}$/

      if (!value) {
        callback(new Error('请输入新密码'));
      } else if(!trg.test(value)){
        callback(new Error('密码为8～12位（大小写+数字)'));
      }else{
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      let trg = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,12}$/

      if (!value) {
        callback(new Error('请再次输入新密码'));
      } else if(!trg.test(value)){
        callback(new Error('密码为8～12位（大小写+数字)'));
      }else if(this.pwdForm.newPwd != this.pwdForm.newPwds){
        callback(new Error('两次输入的密码不一致'));
      }else{
        callback();
      }
    };
    return {
      status: "index",
      pwdForm: {},
      phoneNumber: "",
      code: "",
      uploadUrl: window.FILEIP,
      userInfos: {},
      countdown: 0,
      verifyCode: {
        id: 0,
        data: null
      },
      imgCode:'',
      realName:'',
      nameStatus:false,
      butShow:true,
      passwordShown:false,
      passwordShowns:false,
      rules:{
        oldPwd:[ { required: true, message: '请输入当前账号密码', trigger: 'blur' },],
        newPwd: [{ validator: validatePass, trigger: 'blur' }],
        newPwds: [{ validator: validatePass2, trigger: 'blur' }],
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"],
      isToggleTheme: ["isToggleTheme"]
    })
  },
  filters: {
    identity(val) {
      if (val == 0) {
        return "管理员";
      } else if (val == 1) {
        return "学生";
      } else if (val == 2) {
        return "教师";
      } else if (val == 3) {
        return "教务";
      } else if (val == 4) {
        return "专家";
      } else if (val == 5) {
        return "开发";
      } else if (val == 6) {
        return "专业组";
      } else if (val == 7) {
        return "普通用户";
      }
    }
  },
  mounted() {
    this.getUserInfo();
    if(this.$route.query?.edit==1){
      this.status = 'changePwd';
      this.butShow = false
    }
  },
  watch:{
    status:{
      handler(val){
        if(val == 'bhoneBinding'){
          this.getVerifyCode();
        }
      }
    }
  },
  methods: {
    //获取用户信息
    async getUserInfo() {
      const data = {};
      const res = await this.$api.ModifyUserInformation(data);
      // todo
      this.userInfos = res.data;
      if(this.userInfo?.photo != this.userInfos?.photo || this.userInfo?.realName != this.userInfos?.realName){
        let obj = JSON.parse(JSON.stringify(this.userInfo))
        obj.photo = this.userInfos.photo
        obj.realName = this.userInfos.realName
        this.$store.commit("userInfo", obj);
      }
    },
    editRealName(){
      this.realName = this.userInfo.realName
      this.nameStatus = true
      this.$nextTick(()=>{
        this.$refs.realNameInput.focus()
      })
    },
    handleTogglePassword(val) {
      this[val] = !this[val];
    },
    modifyName(){
      this.nameStatus = false
      if(this.realName == this.userInfos.realName) return
      this.changeUserInfo({key:'realName',value:this.realName})
    },
    //图形验证码
    async getVerifyCode() {
      if (this.readOnly) {
        return;
      }
      const { code, data } = await this.$api.GetVerifyCode({
        type: 0, //0普通验证码 1表达式验证码
        length: 4, //验证码长度
        width: 160,
        height: 44,
        pid: this.verifyCode.id,
        t: Date.now()
      });
      if (code === 200) {
        this.verifyCode = data;
        this.imgCode = "";
      }
    },
    //修改密码
    async submitForm(formName) {
      const data = {
        originalPassword:this.pwdForm.oldPwd,
        newPassword: this.pwdForm.newPwd,
        againNewPassword: this.pwdForm.newPwds
      };

      //校验  
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          console.log('error submit!!');
          return false;
        }else{
          this.userChangePassword(data)
        }
      });
    },
    async userChangePassword(data){
      const res = await this.$api.userChangePassword(data);
      if (res.code === 200) {
        this.$message.success(res.msg)
        if(this.$route.query.edit == 1){
          this.$router.back()
        }else{
          this.status = "index";
        }
        
      }
    },

    //发送验证码
    async SendCode() {
      if(!this.phoneNumber){
        this.$message('请输入手机号')
        return
      }
      if(!this.imgCode){
        this.$message('请输入图形验证码')
        return
      }
      
      const data = {
        phone: this.phoneNumber,
        verifyCode:this.imgCode,
        verifyCodeId:this.verifyCode.id,
        type: 2
      };
      const res = await this.$api.SendVerificationCode(data);
      if (res.code === 200) {
        this.startCountdown();
      }else{
        this.getVerifyCode()
      }
      console.log("绑定手机号验证码", res);
    },

    //校验验证码
    async VerificationCodes() {
      const data = {
        mb: this.phoneNumber,
        verificationCode: this.code
      };
      const res = await this.$api.identifyingCode(data);
      console.log("校验验证码", res);
    },
    //绑定手机号
    async BindPhoneNumber() {
      if(!this.phoneNumber){
        this.$message('请输入手机号')
        return
      }
      if(!this.imgCode){
        this.$message('请输入图形验证码')
        return
      }
      if(!this.code){
        this.$message('请输入短信验证码')
        return
      }
      const data = {
        mb: this.phoneNumber,
        verificationCode: this.code
      };
      const res = await this.$api.identifyingCode(data);
      console.log("验证码教育", res);
      if (!res.data) {
        return;
      }

      const dataquery = {
        mobilePhone: this.phoneNumber
      };

      const res1 = await this.$api.ChangePhoneNumber(dataquery);
      console.log(res1);
      if (res1.code === 200) {
        this.$message.success("手机号绑定成功");
        this.status = "index";
      } else {
        this.$message.error("手机号绑定失败");
      }

      // console.log('绑定手机号验证码',res);
    },
    //修改用户信息
    async changeUserInfo(val) {
      const data = {
        nikeName: val.key == 'nickname' ?val.value: this.userInfos.nickname,
        realName: val.key == 'realName' ?val.value: this.userInfos.realName,
        sex: val.key == 'sex' ?val.value: this.userInfos.sex,
        photo: val.key == 'photo' ?val.value: this.userInfos.photo,
        idNumber: val.key == 'idNumber' ?val.value: this.userInfos.idNumber,
        studentNo: val.key == 'studentNo' ?val.value: this.userInfos.studentNo,
      };
      const res = await this.$api.ObtainUserInformation(data);
      if (res.code === 200) {
        await this.getUserInfo();
        
      }
    },
    //文件上传前
    beforeUpload(file) {
      // if (file.name.indexOf('.docx') > -1 || file.name.indexOf('.doc') > -1) {
      //   this.filesType = 1
      // } else if (file.name.indexOf('.pdf') > -1) {
      //   this.filesType = 2
      // } else if (file.name.indexOf('.ppt') > -1) {
      //   this.filesType = 3
      // } else if (file.name.indexOf('.jpg') > -1 || file.name.indexOf('.png') > -1 || file.name.indexOf('.tif') > -1 || file.name.indexOf('.gif') > -1 || file.name.indexOf('.svg') > -1) {
      //   this.filesType = 4
      // } else if (file.name.indexOf('.avi') > -1 || file.name.indexOf('.mpeg') > -1 || file.name.indexOf('.mp4') > -1) {
      //   this.filesType = 6
      // }
      return true; // 返回false不会自动上传
    },
    //上传失败
    upLoadError() {
      this.$message.error("上传失败，请检查服务器");
    },
    //上传成功
    uploadSuccess(response, file, fileList) {
      console.log("response", response);
      console.log("file", file);

      if (response.code === 200 && !response.msg) {
        this.changeUserInfo({key:'photo',value:response.data});
      }
    },

    startCountdown() {
      if (this.countdown !== 0) {
        return; // 如果已经开始了倒计时则不再进行操作
      }

      this.countdown = 60; // 设置倒计时为60秒

      const timer = setInterval(() => {
        this.countdown--; // 每次间隔1秒将倒计时数量减少1

        if (this.countdown <= 0) {
          clearInterval(timer); // 当倒计时结束后清除定时器
        }
      }, 1000);
    }
  }
};
</script>

<style lang="scss" scoped>
$color: #333333;

.personal-box {
  width: 100vw;
  height: calc(100vh - 50px);
  // background: goldenrod;
  background-color: #F9FBFD;
  position: relative;
  box-sizing: border-box;
  padding-top: 170px;
  .personal-content {
    width: 650px;
    height: 555px;
    background: white;
    position: relative;
    z-index: 100;
    margin: auto;
    border-radius: 20px;
    // box-shadow: 0 0 10px 1px #eeeaea;
    box-sizing: border-box;
    padding: 68px 70px 0 70px;
    .toux {
      width: 110px;
      height: 110px;
      // background: url(var(--src))no-repeat;
      // background-image: var(--src);
      caret-color: transparent;
      margin: auto;
      position: absolute;
      top: -55px;
      left: 50%;
      transform: translate(-50%, 0);
      display: flex;
      justify-content: center;
      > img {
        width: 105px;
        height: 105px;
        border-radius: 55px;
      }
      ::v-deep .el-button {
        position: absolute;
        top: 75px;
        left: 72px;
      }
    }
    .userName {
      text-align: center;
      font-size: 16px;
      color: #333333;
    }
    .userInfo {
      height: 190px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: 36px;
      .item {
        width: 100%;
        height: 50px;
        border-radius: 25px;
        background: #f2f5ff;
        box-sizing: border-box;
        padding: 0 60px 0 25px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // caret-color: transparent;
        .code {
          width: 115px;
          height: 44px;
          border-radius: 30px;
          display: flex;
          align-items: center;

          img {
            cursor: pointer;
            // margin-top: 6px;
          }

          .question {
            display: inline-block;
            font-size: 20px;
            margin-left: 10px;
          }
        }
        span {
          &:nth-child(1) {
            font-size: 16px;
            color: #909090;
          }
          &:nth-child(2) {
            font-size: 16px;
            color: #333333;
          }
        }
      }
      .school{
        padding-right: 30px;
        .el-input{
          width: 200px;
          ::v-deep .el-input__inner{
            text-align: right;
            border: none;
            background: none;
          }
        }
      }
    }
    .divider {
      height: 1px;
      background: #d9d9d9;
      margin: 26px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      caret-color: transparent;
      img {
        margin-top: 2px;
      }
    }
    .editingArea {
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .item {
        width: 100%;
        height: 50px;
        border-radius: 25px;
        background: #f2f5ff;
        box-sizing: border-box;
        padding: 0 30px 0 25px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        caret-color: transparent;
        span {
          &:nth-child(1) {
            font-size: 16px;
            color: #909090;
          }
          &:nth-child(3) {
            font-size: 16px;
            color: #333333;
          }
        }
        input {
          flex: 1;
          text-align: right;
          height: 100%;
          margin-right: 10px;
          font-size: 20px;
          border: none;
          background: #f2f5ff;
          outline: none;
        }
        .input {
          flex: 1;
          text-align: right;
          margin-right: 10px;
        }
      }
    }
  }
  .changePwd {
    width: 650px;
    height: 500px;
    background: white;
    position: relative;
    z-index: 100;
    margin: auto;
    border-radius: 20px;
    box-shadow: 0 0 10px 1px #eeeaea;
    box-sizing: border-box;
    padding: 42px 70px 0 70px;
    .title {
      text-align: center;
      color: $color;
      font-size: 18px;
      position: relative;
      .el-button{
        position: absolute;
        right: 0;
        top:-50%;
      }
    }
    .divider {
      height: 1px;
      background: #d9d9d9;
      margin: 20px 0;
      margin-bottom: 34px;
      display: flex;
      align-items: center;
      justify-content: center;
      caret-color: transparent;
      img {
        margin-top: 2px;
      }
    }
    ::v-deep .el-input__inner {
      border-radius: 25px;
      padding-left: 60px;
      padding-right: 64px;
      height: 50px;
      border: none;
      background: #f2f5ff;
    }
    .suffix {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      line-height: 0.5;
      font-size: 20px;
      font-weight: 500;
      color: var(--color-primary3);
      cursor: pointer;
    }

    .prefix {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      line-height: 0.5;
      font-size: 16px;
      font-weight: 500;
      color: var(--color-primary3);
    }
    // .item {
    //   width: 100%;
    //   height: 50px;
    //   border-radius: 25px;
    //   background: #f2f5ff;
    //   box-sizing: border-box;
    //   padding: 0 0 0 25px;
    //   margin: 20px 0;
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   .el-input {
    //     flex: 1;
    //     ::v-deep .el-input__inner {
    //       background: #f2f5ff;
    //       border: none;
    //       border-radius: 25px;
    //       padding-right: 50px;
    //       text-align: right;
    //       font-size: 16px;
    //     }
    //   }
    //   > span {
    //     font-size: 16px;
    //     color: #909090;
    //   }
    // }
    .but {
      width: 100%;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 50px;
      button {
        width: 100%;
        height: 100%;
        border-radius: 30px;
        cursor: pointer;
        border: none;
        font-size: 16px;
        color: #999999;
        // background: linear-gradient(
        //   270deg,
        //   #5877fb 0%,
        //   #114bfa 81%,
        //   #0f4afa 83%,
        //   #0041fa 100%
        // );
        background: #F2F3F5;
        box-shadow: 0px 3 6px 1px rgba(0, 65, 250, 0.42);
      }
    }
  }
  .bhoneBinding {
    width: 650px;
    height: 500px;
    background: white;
    position: relative;
    z-index: 100;
    margin: auto;
    border-radius: 20px;
    box-shadow: 0 0 10px 1px #eeeaea;
    box-sizing: border-box;
    padding: 42px 70px 0 70px;
    .title {
      text-align: center;
      color: $color;
      font-size: 18px;
    }
    .divider {
      height: 1px;
      background: #d9d9d9;
      margin: 20px 0;
      margin-bottom: 34px;
      display: flex;
      align-items: center;
      justify-content: center;
      caret-color: transparent;
      img {
        margin-top: 2px;
      }
    }
    .item {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20px;
      .el-input {
        width: 60%;

        ::v-deep .el-input__inner {
          border-radius: 30px;
          background: #f2f5ff;
          border: none;
          padding-left: 50px;
          height: 50px;
        }
      }
      .sending {
        width: 35%;
        height: 100%;
        background: linear-gradient(
          270deg,
          #5877fb 0%,
          #114bfa 81%,
          #0f4afa 83%,
          #0041fa 100%
        );
        border-radius: 30px;
        border: none;
        font-size: 16px;
        color: white;
        cursor: pointer;
        &:hover {
          border: 1px solid white;
        }
      }
    }

    .phoneNumber,
    .item{
      .iconfont{
          position: relative;
          top: 10px;
          left: 10px;
          font-size: 28px;
        }
    }
    .phoneNumber {
      width: 100%;
      height: 50px;
      
      .el-input {
        ::v-deep .el-input__inner {
          height: 50px;
          border-radius: 30px;
          font-size: 16px;
          background: #f2f5ff;
          border: none;
          padding-left: 50px;
        }
      }
    }
    .but {
      width: 100%;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 50px;
      button {
        width: 42%;
        height: 100%;
        border-radius: 30px;
        cursor: pointer;
        margin-top: 100px;
        &:nth-child(1) {
          border: 1px solid #2b66ff;
          font-size: 16px;
          color: #2b66ff;
          background: white;
        }
        &:nth-child(2) {
          border: none;
          font-size: 16px;
          color: white;
          background: linear-gradient(
            270deg,
            #5877fb 0%,
            #114bfa 81%,
            #0f4afa 83%,
            #0041fa 100%
          );
          box-shadow: 0px 3 6px 1px rgba(0, 65, 250, 0.42);
        }
      }
    }
  }
  .top-backg {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    height: 340px;
    background: url("../../assets/images/top_banner.gif") no-repeat center/cover;
    display: flex;
    justify-content: center;
    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 50px;
      span {
        &:nth-child(1) {
          font-size: 28px;
          font-weight: bold;
          color: #6690fe;
        }
        &:nth-child(2) {
          font-size: 19px;
          color: #659ffd;
        }
      }
    }
  }
  .footer-backg {
    width: 100%;
    height: 256px;
    position: absolute;
    bottom: 0;
    background: url("../../assets/public/personal/footer-icon.png") no-repeat
      center/cover;
    display: flex;
    justify-content: center;
    > span {
      font-size: 14px;
      color: rgb(147, 151, 151);
      margin-top: 175px;
    }
  }
}
</style>
