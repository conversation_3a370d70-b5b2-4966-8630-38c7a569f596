<template>
  <div class="teacher-aside">
    <div class="teacher-info-box">
      <img class="teacher-img" :src="userInfo.user.profilePhoto||require('../../../assets/public/user.png')" alt="">
      <p class="teacher-name">{{userInfo.user.name}}</p>
      <p class=teacher-college>{{userInfo.schools[0].name}}</p>
    </div>

    <div class="link-item" v-for="(item, index) in navlist" :key="index" :class="{ active: isActive(item.path) }">
      <router-link active-class="active" :to="item.path"> <i :class="['iconfont',item.meta.icon]"></i> {{ item.meta.title }}</router-link>
    </div>
    
  </div>
</template>

<script>
import eventBus from "@/utils/eventBus.js";
import teacherRouter from "@/router/module/teacher/teacher-router";
import { mapGetters } from 'vuex'
export default {
  name: "schoolPage",
  data() {
    return {
      navlist: [],

    }; 
  },
  mounted() {
    console.log(teacherRouter);
    this.navlist = teacherRouter[0].children.filter(item => item.meta.type=='show');
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    // 判断当前路由是否匹配
    isActive(path) {
      return this.$route.path === path;
    },
  }
};
</script>

<style lang="scss" scoped>
.teacher-aside {
  width: 240px;
  height: calc(100vh - 100px) ;
  background-color: #fff;
  padding: 0 0 20px;
  .teacher-info-box{
    background: url('../../../assets/public/teacher-info-bg.png') no-repeat;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .teacher-img{
      width: 70px;
      height: 70px;
      border-radius: 50%;
    }
   .teacher-name{
      // margin-top: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #333; // 文字颜色;
      font-size: 16px; // 字体大小 
      margin:14px
   }
  .teacher-college{
      margin: 0;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 13px; // 字体大小
      color: #5C6075; // 文字颜色
   }
  }
  .up-title{
    text-align: center;
    border-bottom: 1px solid #ccc;
    line-height: 50px;
    margin:  0;
  }
  .link-item {
    text-align: left;
    height: 38px;
    line-height: 38px;
    border-radius: 4px;
    padding-left:14px;
    margin:10px 10px 0;
    // 为背景色和文字颜色添加过渡效果
    transition: background-color 0.3s ease, color 0.3s ease; 
    a {
      color: #333;
      text-decoration: none;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px; // 字体大小
      display: block; // 使链接块级元素，以便填充整个 li 元素
      .iconfont{
        color:#C0C6D6;
        transition: color 0.3s ease; 
      }
    }
    &:hover {
      background-color: #0070FC; // 鼠标悬停时的背景颜色
      cursor: pointer;
      a {
        color: #fff; // 鼠标悬停时的文字颜色;
        .iconfont{
          color:#fff;
        }
      }
    }
    &.active {
      background-color: #0070FC; // 激活时的背景颜色
      a {
        color: #fff; // 鼠标悬停时的文字颜色;
        .iconfont{
          color:#fff;
        }
      }
    }
    .iconfont{
      margin-right: 10px; // 图标与文字之间的间距
      font-size: 16px; // 图标大小
    }
  }
}
</style>