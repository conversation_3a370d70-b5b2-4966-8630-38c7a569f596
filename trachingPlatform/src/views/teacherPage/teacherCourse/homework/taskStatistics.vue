<template>
    <div class="task-statistics-page">
        <div class="task-top">
            <div class="top-left">
                <p class="title">{{taskInfo.name}}  <span class="label">{{taskStatusType[taskInfo.status]}}</span></p>
                <div class="task-info">
                    <div class="info-item">
                        <p class="val">{{taskInfo.questionCount}}</p>
                        <p class="lab">题量</p> 
                    </div>
                    <div class="info-item">
                        <p class="val">{{taskInfo.totalScore}}</p>
                        <p class="lab">卷面分</p> 
                    </div>
                    <div class="info-item">
                        <p class="val">{{taskInfo.submittedCount}}<span class="lab">/ {{taskInfo.unsubmittedCount}}</span></p>
                        <p class="lab">已交/未交</p> 
                    </div>
                    <div class="info-item">
                        <p class="val">{{taskInfo.pendingReviewCount}}</p>
                        <p class="lab">待批阅</p> 
                    </div>
                </div>
                <p class="task-time">时间：{{taskInfo.time}}</p>
            </div>
            <div class="top-right">
                <div id="chartPie" style="width:100%; height:200px"></div>
            </div>
        </div>
        <div class="task-detail">
            <div class="task-search-top">
                <el-select style="width:220px;margin-right:10px;" v-model="classId" placeholder="请选择班级">
                    <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                <el-input class="search-input" v-model="keyWord" placeholder="请搜索学生姓名/学号" suffix-icon="iconfont icon-sousuo"></el-input>
            </div>
            <!-- 表格 -->
            <table2 
            @selectionChange="selectionChange" 
            :notShowSearch="notShowSearch" 
            ref="tablePreview" 
            @selection-change="handleSelectionChange" 
            :selectable="selectable" 
            :data="tableConfig.tableData" 
            :columns="tableConfig.columns" 
            :queryFormConfig="queryConfig" 
            :total="tableConfig.total"
            :pagination="pagination" 
            :height="450" 
            :paginationLayout="paginationLayout" 
            :firstLoad="firstLoad" 
            :searchParams="searchParams" 
            @handleSearch="handleSearch">
            <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
            <template #operate>
                <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
                <template slot-scope="scope">
                    <el-button v-for="(item, index) in operateBtns" type="text"
                            v-show="isShowOperateBtn(scope, item)"
                            :class="item.class || ''"
                            @click="btnClick(scope.row, item)" :key="index"
                            :disabled="scope.row.dataLocked">
                    {{ item.name }}
                    </el-button>
                </template>
                </el-table-column>
            </template>
            </table2>
        </div>
    </div>
</template>
  
<script>
    import { taskStatusType } from '@/constants/common.js';
    export default {
        name: 'taskStatisticsPage',
        data() {
            return {
                taskStatusType,
                classId: '',
                keyWord:'',
                classList:[],// 班级列表
                taskInfo:{
                    questionCount:100, // 题量
                    totalScore:1000, // 卷面分
                    submitted:50, // 已交
                    unsubmitted:50, // 未交
                    toBeReviewed:50, // 待批阅
                    time:'2025.02.15 9:00-2025.02.15 10:00' // 时间
                },
                tableConfig: {
                columns: [
                {
                    label: "姓名",
                    prop: "name",
                    width: 80,
                },
                {
                    label: "学号",
                    prop: "num",
                },
                {
                    label: "联系方式",
                    prop: "phone",
                },
                {
                    label: "学校",
                    prop: "school",
                },
                {
                    label: "院系",
                    prop: "department", // 原 `phone` 可能是笔误，这里改为 `department`
                },
                {
                    label: "专业",
                    prop: "major", // 原 `textbook` 可能表意不准，改为 `major`
                },
                {
                    label: "班级",
                    prop: "class", // 原 `textbookNum` 可能表意不准，改为 `class`
                },
                {
                    label: "加入时间",
                    prop: "createTime",
                }
                ], // 表格列
                tableData: [
                {
                    name: '李老师',
                    num: 'T001',
                    phone: '13800138000',
                    school: '会计金融学院',
                    department: '会计系',
                    major: '会计学',
                    class: '会计1班',
                    createTime: '2024-01-01'
                },
                {
                    name: '张同学',
                    num: 'S001',
                    phone: '13900139000',
                    school: '计算机学院',
                    department: '计算机科学系',
                    major: '计算机科学与技术',
                    class: '计科1班',
                    createTime: '2024-02-01'
                },
                {
                    name: '王同学',
                    num: 'S002',
                    phone: '13700137000',
                    school: '电子信息学院',
                    department: '电子工程系',
                    major: '电子信息工程',
                    class: '电信1班',
                    createTime: '2024-03-01'
                },
                {
                    name: '赵同学',
                    num: 'S003',
                    phone: '13600136000',
                    school: '机械工程学院',
                    department: '机械制造系',
                    major: '机械设计制造及其自动化',
                    class: '机制1班',
                    createTime: '2024-04-01'
                },
                {
                    name: '刘同学',
                    num: 'S004',
                    phone: '13500135000',
                    school: '外国语学院',
                    department: '英语系',
                    major: '英语',
                    class: '英语1班',
                    createTime: '2024-05-01'
                },
                {
                    name: '陈同学',
                    num: 'S005',
                    phone: '13400134000',
                    school: '艺术学院',
                    department: '设计系',
                    major: '环境设计',
                    class: '环设1班',
                    createTime: '2024-06-01'
                },
                {
                    name: '杨同学',
                    num: 'S006',
                    phone: '13300133000',
                    school: '经济管理学院',
                    department: '工商管理系',
                    major: '市场营销',
                    class: '营销1班',
                    createTime: '2024-07-01'
                },
                {
                    name: '吴同学',
                    num: 'S007',
                    phone: '13200132000',
                    school: '法学院',
                    department: '法律系',
                    major: '法学',
                    class: '法学1班',
                    createTime: '2024-08-01'
                },
                {
                    name: '周同学',
                    num: 'S008',
                    phone: '13100131000',
                    school: '体育学院',
                    department: '体育教育系',
                    major: '体育教育',
                    class: '体教1班',
                    createTime: '2024-09-01'
                },
                {
                    name: '徐同学',
                    num: 'S009',
                    phone: '13000130000',
                    school: '医学院',
                    department: '临床医学系',
                    major: '临床医学',
                    class: '临床1班',
                    createTime: '2024-10-01'
                }
                ], // 表格数据
                pagination: true,
                total: 12,
                searchParams: {
                pageIndex: 1,
                pageSize: 12,
                searchWord:'',
                collegeId:675409883629445, // 院校id
                },
                btnList: {
                detail: {
                    enable: true
                }
                },
                // 补全模板中使用的变量
                notShowSearch: false,
                selectable: true,
                pagination: true,
                paginationLayout: 'total, sizes, prev, pager, next, jumper',
            },
            // 补全模板中使用的变量
            queryConfig: {},
            total: 0,
            firstLoad: true,
            loading: false,
            actionBtns: [
                // { id: 1, name: '新增' }
            ],
            operateBtns: [
                { name: '批阅',class:'default-btn' },
                { name: '查看',class:'default-btn' },
            ],
            operateWidth: 180, // 操作列宽度

            barOption: {
                color: ['#07C392', '#E7E7E7'], 
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {},
                series: [ {
                name: 'form',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: true,
                    position: 'outside',
                    formatter: '{b}: {d}%' 
                },
                emphasis: {
                    // label: {
                    //     show: true,
                    //     fontSize: 20,
                    //     fontWeight: 'bold'
                    // }
                },
                labelLine: {
                    show: true,
                     // 将引导线设置为折线
                     smooth: false, 
                    length: 10, 
                    length2: 20 
                },
                data: [
                    { value: 50, name: '已交人数' },
                    { value: 50, name: '未交人数' },
                ]
                }],
            },
            chartPieB: null,
            } 
        },
        props: {
            params:{
                type:Object,
                default:()=>{} 
            }
        },
        watch: {
            params: {
                handler(newVal, oldVal) {
                    this.taskInfo = newVal;
                },
                deep: true,
                immediate: true
            } 
        },
        mounted() {
            this.initBar();
        },
        methods: {
            /**
             * 初始化 柱状图
             */
            initBar() {
                this.chartPieB = echarts.init(document.getElementById("chartPie"));
                this.chartPieB.setOption(this.barOption);
            },
            // 补全模板中使用的方法
            selectionChange(selection) {
                console.log('selection changed:', selection);
            },
            handleSelectionChange(selection) {
                console.log('handle selection changed:', selection);
            },
            handleSearch(params) {
                console.log('search params:', params);
                // 这里可以添加搜索逻辑
            },
            isShowOperateBtn(scope, item) {
                // 这里可以添加操作按钮显示逻辑
                return true;
            },
            changeSelection(selection) {
                this.$emit("changeSelection", selection);
            },
            formatDataSourceBtns(btn) {
                if (btn.type === "detail") {
                    btn.enable = this.tableConfig.btnList.detail.enable;
                }
                return btn;
            },
            handlePlus(btn) {
                this.$emit("handlePlus", btn); 
            },
            btnClick(row, btn) {
                console.log('button clicked:', row, btn);
                // 这里可以添加按钮点击逻辑
                switch (btn.name) {
                    case '批阅':
                    break;
                    case '查看':
                    break;
                }
            },
        }
    }
</script>   
  
<style lang="scss" scoped>
.task-statistics-page {
    height: 100%;
    background: #FFF;
    .task-top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .top-left{
            width: 55%;
            padding: 0 20px 20px;
            .title{
                line-height: 60px;
                margin: 0;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
                .label{
                    display: inline-block;
                    width: 51px;
                    height: 24px;
                    line-height: 24px;
                    margin-left: 10px;
                    text-align: center;
                    color: #FFFFFF;
                    font-size: 13px;
                    background: #0070FC;
                    border-radius: 2px;
                }
            }
            .task-info{
                height: 80px;
                display: flex;
                justify-content: space-around;
                align-items: center;
                background: #F6FAFF;
                border-radius: 4px;
                .info-item{
                    width: 64px;
                    text-align: center;
                    font-size: 14px;
                    .val{
                        font-size: 18px;
                        color: #333;
                    }
                .lab{
                    font-size: 14px;
                    color: #5C6075;
                }
                }
            }
            .task-time{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                line-height: 20px;
                margin-top: 18px;
            }
        }
        .top-right{
            width: 45%;
            background: #fff;
            border-radius: 4px;
       }
    }

    .task-detail{
        padding: 20px;
        .task-search-top{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 20px;
        }
        .search-input{
            width: 300px;
            margin-right: 10px;
        }
    }
}
</style>