<template>
    <div class="home-work">
        <!-- 使用动态组件渲染 -->
        <component :is="currentComponent" @handleEvent="handleEvent" :params="params"></component>
    </div>
</template>
  
<script>
    // 引入需要动态渲染的组件，这里假设你有多个组件，根据实际情况修改
    import singleTask from './singleTask.vue'; 
    import taskList from './taskList.vue'; 
    import taskStatistics from './taskStatistics.vue';
    import taskReview from './taskReview.vue';
    import selecttiku from './selecttiku.vue';

    export default {
        name: 'homeWork',
        data() {
            return {
                currentComponent: 'taskList', // 默认渲染的组件
                params:{},
            } 
        },
        components: {
            singleTask,
            taskList,
            taskStatistics,
            taskReview,
            selecttiku,
        },
        mounted() {
            
        },
        methods: {
            handleEvent(type,data){
                switch (type) {
                    case 'singleTask': // 切换到 singleTask 组件
                        if(data){
                            this.params = data; 
                        }
                        this.currentComponent = 'singleTask';
                        break; 
                    case 'taskList': // 切换到 taskList 组件
                        this.currentComponent = 'taskList';
                        break; 
                    case 'taskStatistics': // 切换到 taskStatistics 组件
                        if(data){
                            this.params = data; 
                        }
                        this.currentComponent = 'taskStatistics';
                        break;
                    case 'taskReview': // 切换到 taskReview 组件
                        this.currentComponent = 'taskReview';
                        break;
                    case 'selecttiku': // 切换到 taskReview 组件
                        this.currentComponent = 'selecttiku';
                        break;
                }
            },
        }
    }
</script>   
  
<style lang="scss" scoped>
.home-work {
    height: calc(100% - 50px);
    margin-right:10px;
}
</style>