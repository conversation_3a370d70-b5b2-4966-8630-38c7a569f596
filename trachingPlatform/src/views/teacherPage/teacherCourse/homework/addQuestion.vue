<template>
  <div class="add-question-page">
    <div class="question-operate-btns">
      <el-button @click="cancle" class="cancel-btn">取消</el-button>
      <el-button @click="saveClose" class="save-qs-btn">保存</el-button>
      <el-button @click="saveNext" class="save-next-btn">保存并创建下一题</el-button>
    </div>
    <addQquestionContent 
    ref="addQuestionContentRef" 
    :defaultData="defaultData"
    :course-id="$route.query.courseId"
     />
  </div>
</template>
<script>
import addQquestionContent from '@/components/question/add-question-content.vue'
import {getCorrectAnswer, questionTypeMenu, getInitAnswerValue} from '@/components/base/question/util.js';
import {initApiQuestion} from "@/components/question/utils";
import {checkEmpty} from "@/utils/utils";
import eventBus from "@/utils/eventBus";
export default {
  data() {
    return {
      defaultData: { // 传递给子组件的默认数据
        type: 'SingleChoice', // 问题类型
        cateId: 0, // 分类id
      },
      saveLoading: false, // 保存按钮的loading状态
      courseId: 0, // 课程id
      currentId: 0, // 当前题目id
      status: 1, // 题目状态
      editDataLoading: false, // 编辑数据的loading状态
      data:{},
    }
  },
  components: {
    addQquestionContent, // 导入组件
  },
  mounted() {
    // this.initData()
  },
  methods: {
    cancle() { // 取消按钮的点击事件
      this.$emit('cancleAdd') // 触发父组件的cancle事件
    },
    getParams () {
      return this.$refs.addQuestionContentRef.getParams()
    },
    async initData (val) {
      if (val) {
        if (this.data && this.data.id) { // 题目编辑
          // 编辑
          this.currentId = this.data.id
          this.status = this.data.status||0  // 题目状态
          this.editDataLoading = true
          const { errCode, data } = await this.$api.GetById({ id: this.data.id })
          this.editDataLoading = false
          console.log('题目编辑的信息-------',data)
          if(errCode==0){
            this.commandEdit(data)
          }
        } else if (this.data) {
          // 自带默认值
          this.commandEdit(this.data)
        } else {
          // 新建
          this.commandAdd(this.type)
        }
      }
    },
    async saveClose () {
      // const data = this.getParams()
      await this.save()
     
    },
    async saveNext () { // 保存并创建下一题按钮的点击事件
      // const data = this.getParams()
      await this.save(false)
      this.commandAdd(this.defaultData.type, this.defaultData.cateId)
      this.editDataLoading = true
      await this.$nextTick()
      this.editDataLoading = false
    },
    async save (isClose = true) {
      if (this.saveLoading) return
      this.saveLoading = true
      const data = this.getParams()
      // console.log(data,'保存总数据')
      // 填空题取缓存数据
      if (data.type === questionTypeMenu.content) {
        const courseContents = sessionStorage.getItem('courseContents')
        data.optionConfig.settingArr = courseContents ? JSON.parse(courseContents) : []
      }
      const answer = getCorrectAnswer(data.type, data.optionConfig) || ''
      try {
        // 保存值判断
        if (checkEmpty(data.title)) throw '标题不能为空'
        if ([questionTypeMenu.radio, questionTypeMenu.checkbox, questionTypeMenu.isTrue].includes(data.type)) {
          if (data.optionConfig.options.length < 2) {
            throw '选项最少需要设置两个'
          }
          const labelArr = [] // 选项值重复判断用
          data.optionConfig.options.forEach((item) => {
            if (checkEmpty(item.label)) {
              throw '选项值不能为空'
            }
            if (labelArr.includes(item.label)) {
              throw '选项值不能重复'
            }
            labelArr.push(item.label)
          })
          // 判断答案个数是否匹配
          if (data.type === questionTypeMenu.checkbox && answer.length < 2) {
            throw '多选题答案最少需要设置两个'
          } else if (answer.length === 0) {
            throw '未设置答案'
          }
        } else if (data.type === questionTypeMenu.content) {
          if (checkEmpty(data.optionConfig.settingArr)) {
            throw '未设置答案'
          }
          let index = 1
          for (const key in answer) {
            if (checkEmpty(answer[key])) {
              throw `第${index}个未设置答案`
            }
            index++
          }
        } 
        if (checkEmpty(data.answerDetail) || data.answerDetail === '<p><br></p>') {
          throw '答案解析不能为空'
        }
        // 构建保存题目的参数
        let questionParams =  this.handleQuestionParams({
          ...data,
          id: this.currentId,
          courseId: this.getCourseId(),
          status: this.status,
          answer: answer,
        })

        // console.log("保存的题目参数--------------",questionParams)
        this.$emit('saveQuestion',questionParams,isClose);
        return 
        const res= {}
        if(questionParams.id){
          res = await this.$api.UpdateQuestion(questionParams)
        }else {
          res = await this.$api.CreateQuestion(questionParams)
        }
        // if (res.errCode==0) {
          this.$message.success('保存成功！')
          this.saveLoading = false
          this.currentId = 0
          // this.defaultData = {
          //   type: data.type,
          //   cateId: this.defaultData.cateId
          // }
        // } else {
          // this.$message.success(msg)
          // this.saveLoading = false
        // }
      } catch (e) {
        this.$message.warning(e)
        this.saveLoading = false
        // return Promise.reject()
      }
    },
    // 构建题目保存的参数
    handleQuestionParams(qObject){
      // 题目判分类型
      const handleScoringType = (qObject)=>{
          return qObject.optionConfig.scoreRadio?qObject.optionConfig.scoreRadio:2
      }
      // 题目答案
      const handleAnswer = (qObject)=>{
        if(JSON.stringify(qObject.answer).indexOf('longArray')!=-1){
          return JSON.stringify({ longArray: qObject.answer.longArray });
        }else{
          return JSON.stringify({ longArray: qObject.answer });
        }
      }
     // 处理 contentData 字段 // 绑定 this 上下文
      const handleQsContentData = (qObject)=>{
          return this.handleContentData(qObject.optionConfig);
      } 
      const handleParams = (qObject)=>{
        return {
          "id": qObject.id, // 题目id
          "title": qObject.title,
          "courseId": qObject.courseId || 0,
          "questionType": qObject.type,
          "scoringType":handleScoringType(qObject), // 计分规则
          "sort": qObject.sort||0,
          "answer": handleAnswer(qObject),
          "contentData": handleQsContentData(qObject), //  扩展字段  前端使用配置信息
          "description": qObject.desc,
          "answerAnalysis": qObject.answerDetail, // 答案解析
          "complexity": qObject.difficulty, // 难易
          "status": qObject.status,
        }
      }
      let params =  handleParams(qObject);
      return params
    },
    commandAdd (type, cateId) {
      this.currentId = 0  
      this.defaultData = {
        type: type,
        cateId: cateId || this.questionCategoryId || 0
      }
    },
    commandEdit (data) {
      this.defaultData = initApiQuestion(data)

      console.log("this.defaultData",this.defaultData)
    },
    getCourseId (){
      const val = this.courseId || this.$route.query.courseId || 0
      return Number(val)
    },
      // 处理前端配置信息
      handleContentData(data){
      return JSON.stringify(data)
    },
  }
}
</script>
<style lang="scss" scoped>
  .add-question-page{
    position: relative;
    background: #fff;
    .question-operate-btns{
      display: flex;
      justify-content: flex-end;
      position: absolute;
      right: 0;

     .cancel-btn{
      width: 80px;
      height: 38px;
      padding:0;
      color:#333;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #E7E7E7;
      &:hover{
       background: #E7E7E7; 
      }
     }
    .save-qs-btn{
      margin-left: 10px;
      color: #fff;
      width: 80px;
      height: 38px;
      padding:0;
      background: #07C392;
      border-color:#07C392;
      border-radius: 4px;
      &:hover{
        background: #10D3A0;
      }
    }
   .save-next-btn{
      margin-left: 10px;
      width: 130px;
      padding:0;
      color:#fff;
      height: 38px;
      background: #07C392;
      border-color:#07C392;
      border-radius: 4px;
      &:hover{
        background: #10D3A0;
      }
    } 
   }
  }
</style>