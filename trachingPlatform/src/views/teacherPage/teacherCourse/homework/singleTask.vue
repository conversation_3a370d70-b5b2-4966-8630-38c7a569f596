<template>
    <div class="single-task-page">
      <div class="top-tool">
        <el-button class="btn preview" @click="preview">预览</el-button>
        <el-button class="btn cancle" @click="cancle">取消</el-button>
        <el-button class="btn save" @click="save">保存</el-button>
      </div>
      <div class="single-task-info">
        <div class="left">
          <h4 class="title">新建作业</h4>
          <el-form ref="homeForm" label-width="80px" class="task-form-info">
            <!-- <el-form-item label="作业类型" prop="taskType">
              <el-radio-group v-model="form.taskType" @change="initList">
                <el-radio label="1">课前预习</el-radio>
                <el-radio label="2">课中授课</el-radio>
                <el-radio label="3">课后巩固</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="所属章节" prop="chapterId">
              <el-select v-model="form.chapterId" placeholder="请选择">
                <el-option v-for="item in chapterList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="名 称" prop="name">
              <el-input class="name" v-model="form.name" maxLength="20" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="发布范围">
              <el-button @click="selectStudentDialog = true;" class="add-student"><i class="iconfont icon-xinjian"></i> 添加班级</el-button>
              <div class="class-list">
                <el-tag class="student-tag" v-for="item in selectClassList" :key="item.id" closable @close="selectClassList.splice(selectClassList.indexOf(item), 1)">
                  <span>{{item.name}}</span>
                </el-tag>
              </div>
            </el-form-item>
            <el-form-item label="总分">
              <el-input :disabled="true" class="total-score" v-model="form.totalScore" placeholder="系统自动统计"></el-input>
            </el-form-item>
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                v-model="form.beginTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择开始时间"
                default-time="12:00:00">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择结束时间"
                default-time="12:00:00">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="描 述">
              <el-input type="textarea" class="desc" v-model="form.description" placeholder="请输入" maxlength="200"
              show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="right">
          <div class="question-list">
            <empty v-if="selectedQuestionList.length==0" msg="暂无数据" size="middle" />
            <div v-else>
              <div class="topic-item content" v-for="(item, index) in selectedQuestionList" :key="index">
                <question-preview-content  :showSort="true" :sort="(index + 1)" :data="item" />
                <div class="question-other-info">
                  <p class="difficult-info">难易程度：{{complexityType[item.complexity]}}</p>
                  <div class="score-info">
                    <p>分值 <el-input type="number" @input="item.score<0?0:item.score>100?100:item.score" placeholder="请输入" @change="calculateTotalScore();" v-model="item.score"></el-input></p>
                    <p>排序 <el-input type="number" @input="item.sort<0?0:item.sort" placeholder="请输入" v-model="item.sort"></el-input></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="question-operate">
            <el-button class="select-question" @click="selectQuestion"><i class="iconfont icon-xinjian"></i> 题库选题</el-button>
            <el-button class="add-question" @click="isShowAdd = true"><i class="iconfont icon-xinjian"></i> 创建题目</el-button>
          </div>
        </div>
        
        <transition name="el-zoom-in-top">
          <div class="select-tiku" v-show="isShowTiKu" >
            <selecttiku @handleSelectQuestion="handleSelectQuestion" />
          </div>
          
        </transition>
        <transition name="el-zoom-in-top">
          <div class="add-question-content" v-show="isShowAdd" >
            <div style="width:900px;padding:20px;margin:0 auto;background:#fff;">
              <addQuestion v-if="isShowAdd" @cancleAdd="cancleAdd" @saveQuestion="saveQuestion"/>
            </div>
          </div>
          
        </transition>

        <!-- 选择学生弹窗 -->
        <base-dialog :visible.sync="selectStudentDialog"
                 :close-on-click-modal="false"
                 :no-footer="true"
                 class="select-student-dialog"
                 width="500px"
                 title="选择班级" >
                 <el-checkbox-group style="min-height:140px;" v-model="owners">
                  <el-checkbox v-for="item in classList" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox>
                </el-checkbox-group>

                <div class="footer-btns" style="text-align:right;">
                  <el-button class="cancel-btn" @click="selectStudentDialog = false;">取消</el-button>
                  <el-button class="save-btn" @click="selectClass()">确定</el-button>
                </div>
        </base-dialog>
        
      </div>
    </div>
</template>
  
<script>
import {initApiQuestion} from "@/components/question/utils";
import selecttiku from './selecttiku.vue';
// 难易度
const complexityType ={
    Unknown: '未知',
    Easy: '简单',
    Moderation: '一般',
    Difficult: '困难',
}
export default {
    name: 'singleTaskPage',
    components: {
      empty:()=>import("@/components/base/empty.vue"), 
      addQuestion:()=>import("./addQuestion.vue"), 
      selecttiku,
      'question-preview-content': () => import('@/components/question/question-preview-content.vue'),
      
    },
    props:{
      params:{
        type:Object,
        default:()=>{} 
      }
    },
    watch:{
      params:{
        deep:true,
        immediate:true,
        handler(newVal){
          if(newVal){
            console.log('newVal',newVal); // 新值
            if(newVal.id){ // 编辑
              this.$api.TaskGetById({id:newVal.id}).then(res=>{ // 获取作业详情
                console.log('res',res); // 作业详情
                this.form = res.data; // 作业详情
                this.selectClassList = res.data.owners.map(v=>{
                  return {id:v.ownerId,name:v.ownerName}
                });// 选择的班级列表
                this.selectedQuestionList = res.data.questions.map(v => {
                  return {
                    ...initApiQuestion(v.question),
                    score: v.score, // 分值,
                    sort: v.sort,
                  }
                }); // 处理题目列表
              })
            }
          } 
        } 
      }
    },
    data() {
        return {
            form: {
                courseId:this.$route.query.courseId, // 课程id
                name: '',
                totalScore: 0,
                beginTime:'',
                endTime:'',
                description: '',
                // taskType:1,
                owners: [{
                  "ownerId": 0,
                  "ownerType": "Class"
                }],  //发布范围
                questions: []  //题目列表
            },
            owners:[], //选择的班级列表
            selectStudentDialog:false, //选择学生弹窗
            classList:[], //班级列表
            selectClassList:[], //选择的班级列表
            complexityType,
            isShowTiKu:false, //是否显示题库
            isShowAdd:false, //是否显示添加题目
            selectedQuestionList:[], //选择的题目列表
            chapterList:[], // 章节列表


        } 
    },
    mounted() {
        this.getClassList(); // 获取班级列表
    },
    methods: {
      // 获取班级列表
      getClassList(){
        let courseId = this.$route.query.courseId; // 课程id
        this.$api.GetMyTeachClassList({courseId,}).then(res=>{ // 获取我的班级列表
            this.classList = res.data;
        })
      },
      selectClass(){ // 选择班级
        this.selectClassList = this.classList.filter(classItem => 
          this.owners.includes(classItem.id)
        );
        this.selectStudentDialog = false;
        this.form.owners = this.selectClassList.map(item => ({ownerId:item.id,ownerType:'Class'}));
      },
      preview(){
          console.log('预览'); 
      },
      cancle(){
          console.log('取消'); 
          this.$emit('handleEvent','taskList');
      },
      selectQuestion(){
          // this.$emit('handleEvent','selecttiku');
          this.isShowTiKu = true;
      },
      addQuestion(){
          console.log('创建题目'); 
      },
      save(){
          console.log('保存',this.form);
          this.form.questions = this.handleParamsQuestion(); // 处理题目列表
          if(this.form.id){
            this.$api.UpdateTask(this.form).then(res=>{ // 修改作业
              if(res.errCode==0){
                this.$emit('handleEvent','taskList'); // 跳转到作业列表页
              }
            })
          }else{
            this.$api.CreateTask(this.form).then(res=>{ // 创建作业
              if(res.errCode==0){
                this.$emit('handleEvent','taskList'); // 跳转到作业列表页
              }
            })
          }
      },
      handleParamsQuestion(){
        let dtos = this.selectedQuestionList.map((item,index) => { // 遍历选择的题目列表
          return {
                  "questionId": item.id,
                  "newQuestion": {
                    "questionType": item.questionType,
                    "courseId": this.$route.query.courseId,
                    "sectionId": 0,
                    "title": item.title,
                    "description": item.desc,
                    "contentData": JSON.stringify(item.optionConfig),
                    "answer": JSON.stringify(item.answer),
                    "answerAnalysis": item.answerDetail,
                    "complexity": item.difficulty,
                  },
                  "score": item.score,
                  "sort": item.sort,
                }
        });

        return dtos;
      },
      handleSelectQuestion(data){ // 选择题目
        let newQuestion = data.map(v => initApiQuestion(v));
        // 去重逻辑
        newQuestion.forEach((question) => {
          question.score = 0; // 初始化分数为0
          question.sort = question.sort||0; // 初始化排序为0
          const exists = this.selectedQuestionList.some((item) => {
            return item.id === question.id; 
          });
          if (!exists) {
            this.selectedQuestionList.push(question);
          }
        });
        console.log("this.selectedQuestionList",this.selectedQuestionList)
        this.isShowTiKu = false;
      },
      cancleAdd(){ // 取消
        this.isShowAdd = false;
      },
      // 单个题目保存
      saveQuestion(item,isClose){
        console.log('单个题目保存',item);
        if(!isClose){
          this.isShowAdd = false;
        }
        this.selectedQuestionList.push({
          ...initApiQuestion(item),
          score: 0, // 初始化分数为0
          sort: item.sort||0, // 初始化排序为0
        }); // 新增题目
      },
      // 新增题目
      handleAddQuestion(){
        this.isShowAdd = false;
      },
      calculateTotalScore(){
        let total = 0;
        this.selectedQuestionList.forEach(item => {
          total += Number(item.score) || 0;
        });
        this.form.totalScore = total;
      }
    }
}
</script>   
  
<style lang="scss" scoped>
.single-task-page {
    height: 100%;
    background: #fff;
    position: relative;
    .top-tool {
        height: 58px;
        line-height: 58px;
        background: #F6F8FA;
        text-align: right;
        .btn {
          width: 80px;
          height: 38px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #E7E7E7;
        }
        .preview {
          color:#333;
        }
        .cancle {
          color:#07C392;
          border: 1px solid #07C392;
          &:hover{
           border-color: #10D3A0;
          }
        }
        .save {
         color:#fff;
         background: #07C392;
         &:hover{
            background: #10D3A0;
         }
       }
    }
    .single-task-info{
      display: flex;
      justify-content: space-between;
      height: calc(100% - 48px);

      .left {
          width:48%;
          border-right: 1px solid #F2F3F5;

          .title{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 60px;
            margin: 0;
            padding-left: 20px;
            border-bottom: 1px solid #F2F3F5;
          }
          .task-form-info{
            padding: 20px; 
            ::v-deep .el-form-item__label{
              line-height: 36px;
              text-align: justify;
              text-align-last: justify;
            }
            ::v-deep .el-form-item{
              margin-bottom:10px!important;
            }

            .student-tag{
              min-width: 120px;
              max-width: 200px;
              height: 38px;
              background: #F9FAFC;
              color: #5C6075;
              border-radius: 4px;
              border: 1px solid #E7E7E7;
              text-align: center;
              line-height: 38px;
              font-size: 14px;
              margin-right: 10px;
              margin-top:10px;
              text-overflow: ellipsis;
              overflow: hidden; 
              white-space: nowrap;
              span{
                display: inline-block;
                width: 80%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
          }
          
          .add-student{
            width: 120px;
            height: 38px;
            color: #07C392;
            background-color: #fff;
          }
          ::v-deep .el-textarea__inner{
            height: 120px;
          }
      }
     .right {
         width:calc(100% - 48%);
         .question-list {
           height: calc(100% - 48px - 60px);
           overflow-y: auto;
        }
        .topic-item{
          padding: 20px 20px 0 20px;
        }
        .question-other-info{
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 20px;
          border-bottom: 1px solid #F0F0F0;
          .difficult-info{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #5C6075;
         }
         .score-info{

            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 280px;
            height: 38px;
            border-radius: 4px;
            padding: 0 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;

            ::v-deep .el-input{
              width: 80px;
              height: 30px;
              background: #FFFFFF;
              border-radius: 4px;
              .el-input__inner{
                height: 30px;
                line-height: 30px;
                padding-right: 10px;  
              }
            }
         }
        }
        .question-operate {
          border-top: 1px solid #F2F3F5;
          height: 59px;
          line-height: 59px;
          text-align: right;
          background: #FFFFFF;
          padding-right: 10px;
          .select-question, .add-question {
            width: 120px;
            height: 38px;
            color: #fff;
            background-color: #fff;
         }
         .select-question {
            background-color: #207FF5;
          }
          .add-question {
            background-color: #07C392;
          }
        }
     }
    }
}

.select-tiku{
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 999;
  left: 0;
  top: 0;
}
.add-question-content{
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 999;
  left: 0;
  top: 0;
  background: #F6F8FA;
}

</style>
<style lang="scss">
  .select-student-dialog{
    .footer-btns{
      display: flex;
      justify-content: flex-end;
      padding: 10px 0; 
      .cancel-btn{
        margin-right: 10px;
        width: 80px;
        height: 38px;
        color: #333;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E7E7E7;
      }
    .save-btn{
        width: 80px;
        height: 38px;
        color: #fff;
        background: #07C392;
        border-radius: 4px;
        border: 1px solid #E7E7E7;
    }
    }
  }
  .student-tag{
    .el-icon-close{
      top: -14px;
    }
  }
</style>