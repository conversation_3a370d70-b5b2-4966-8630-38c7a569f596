<template>
  <div class="course-analysis-wrapper">
    <div class="course-analysis-header">
      <div class="title">
        班级学情总览  <span>更新于2025年08月10日 23:50</span>
      </div>
      <div class="class-info-content">
        <div class="class-info">
          <div :class="['class-name',{'active':item.rank == 1}]" v-for="item in classList" :key="item.name">
            {{item.name}}
          </div>
        </div>
        <el-row style="margin-top: 10px;" :gutter="17">
          <el-col  :span="8">
            <div class="part part-1">
              <p class="p1">班级人数</p>
              <div class="part-info">
                <p class="value-tip">45</p>
                <p>男<span>20</span>&nbsp;&nbsp;&nbsp;女<span>25</span></p>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="part part-2" >
              <p class="p1">平均作业完成率</p>
              <div class="part-info">
                <p class="value-tip">87%</p>
                <p><span>↑3%</span>&nbsp;较上月</p>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="part part-3">
              <p class="p1">平均考试完成率</p>
              <div class="part-info">
                <p class="value-tip">78.5%</p>
                <p><span>↓1.2% </span>&nbsp;较上次</p>
              </div>
            </div>
          </el-col>
        </el-row>

      </div>
    </div>
    <div class="data-static">
        <div class="textBook-data">
            <h4 class="part-label">班级考试成绩趋势</h4>
            <div id="bar1" style="height:340px;">
            </div>
        </div>
        <div class="user-data">
            <h4 class="part-label">平均分分布</h4>
            <div id="bar2" style="height:340px;">
            </div>
        </div>
    </div>

    <div class="table1">
      <h4 class="part-label">近期作业完成情况</h4>
      <el-table
        :data="tableData1"
        stripe
        :header-cell-style="{ background: '#EFF2F5', color: '#5C6075' }"
        style="width: 100%">
        <el-table-column
          prop="name"
          label="作业名称"
          >
        </el-table-column>
        <el-table-column
          prop="startTime"
          label="发布日期"
          >
        </el-table-column>
        <el-table-column
          prop="endTime"
          label="截止日期">
        </el-table-column>
        <el-table-column
          prop="percentage"
          width="200"
          label="完成率">
          <template slot-scope="scope">
            <el-progress :percentage="scope.row.percentage"></el-progress>
          </template>
        </el-table-column>
        <el-table-column
          prop="averageScore"
          label="平均分">
        </el-table-column>
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini">分析</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="table2">
      <h4 class="part-label">学生表现</h4>
      <el-table
        :data="tableData2"
        stripe
        :header-cell-style="{ background: '#EFF2F5', color: '#5C6075' }"
        style="width: 100%">
        <el-table-column
          prop="date"
          label="排名">
        </el-table-column>
        <el-table-column
          prop="name"
          label="学生">
        </el-table-column>
        <el-table-column
          prop="address"
          label="最近考试得分">
        </el-table-column>
        <el-table-column
          prop="address"
          label="作业完成率">
        </el-table-column>
        <el-table-column
          prop="address"
          label="登录平台次数">
        </el-table-column>
        <el-table-column
          prop="address"
          label="在线时长">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
    data() {
        return {
          classList:[
            {
              name:'班级1',
              number:100,
              score:80,
              rank:1
            },
            {
              name:'班级2',
              number:100,
              score:80,
              rank:2
            },
            {
              name:'班级3',
              number:100,
              score:80,
              rank:3
            }
          ],
          chartBar1:null,
          bar1Option: {
              tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                  type: 'cross',
                  crossStyle: {
                      color: '#999'
                  }
                  }
              },
            
              legend: {
                  data: ['申请额度', '使用额度'],
                  left: 'right', // 设置图例水平居中
                  top: '5%', // 设置图例距离顶部 5%
                  textStyle: {
                      color: '#666' // 设置图例文字颜色
                  },
                  itemWidth: 12,
                  itemHeight: 12,
                  // 可以单独为每个图例项设置颜色
                  pageIconColor: '#409EFF', // 分页图标颜色
                  pageTextStyle: {
                      color: '#666' // 分页文字颜色
                  },
                  // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                  // 如果要单独设置每个图例标记颜色，可结合 series 数据
                  // 下面这种方式在实际使用中可以结合 series 对应数据
                  data: [
                      {
                          name: '申请额度',
                          icon: 'roundRect',
                          textStyle: {
                              color: '#759FF8' // 单独设置 '申请额度' 文字颜色
                          }
                      },
                      {
                          name: '使用额度',
                          icon: 'roundRect',
                          textStyle: {
                              color: '#6FDEB4' // 单独设置 '使用额度' 文字颜色
                          }
                      }
                  ]
              },
              xAxis: [
                  {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                  axisPointer: {
                      type: 'shadow'
                  }
                  }
              ],
              yAxis: [
                  {
                  type: 'value',
                  name: '额度',
                  min: 0,
                  max: 250,
                  interval: 50,
                  axisLabel: {
                      formatter: '{value} '
                  }
                  },
              ],
              series: [
                  {
                  name: '申请额度',
                  type: 'bar',
                  barWidth: '20px',
                  tooltip: {
                      valueFormatter: function (value) {
                      return value;
                      }
                  },
                    // 设置申请额度柱子颜色
                    itemStyle: {
                      color: '#759FF8'
                  },
                  data: [
                      2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                  ]
                  },
                  {
                  name: '使用额度',
                  type: 'bar',
                  barWidth: '20px',
                  tooltip: {
                      valueFormatter: function (value) {
                      return value;
                      }
                  },
                    // 设置使用额度柱子颜色
                    itemStyle: {
                      color: '#6FDEB4'
                  },
                  data: [
                      2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                  ]
                  },
              ]
          },
          chartBar2:null,
          bar2Option: {
              tooltip: {
                  trigger: 'axis',
                  axisPointer: {
                  type: 'cross',
                  crossStyle: {
                      color: '#999'
                  }
                  }
              },
              legend: {
                  data: ['申请额度', '使用额度'],
                  left: 'right', // 设置图例水平居中
                  top: '5%', // 设置图例距离顶部 5%
                  textStyle: {
                      color: '#666' // 设置图例文字颜色
                  },
                  itemWidth: 12,
                  itemHeight: 12,
                  // 可以单独为每个图例项设置颜色
                  pageIconColor: '#409EFF', // 分页图标颜色
                  pageTextStyle: {
                      color: '#666' // 分页文字颜色
                  },
                  // 图例标记颜色，可通过修改 series 里的 itemStyle 来控制，这里模拟设置
                  // 如果要单独设置每个图例标记颜色，可结合 series 数据
                  // 下面这种方式在实际使用中可以结合 series 对应数据
                  data: [
                      {
                          name: '申请额度',
                          icon: 'roundRect',
                          textStyle: {
                              color: '#FFB573' // 单独设置 '申请额度' 文字颜色
                          }
                      },
                      {
                          name: '使用额度',
                          icon: 'roundRect',
                          textStyle: {
                              color: '#DA6FDC' // 单独设置 '使用额度' 文字颜色
                          }
                      }
                  ]
              },
              xAxis: [
                  {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                  axisPointer: {
                      type: 'shadow'
                  }
                  }
              ],
              yAxis: [
                  {
                  type: 'value',
                  name: '额度',
                  min: 0,
                  max: 250,
                  interval: 50,
                  axisLabel: {
                      formatter: '{value} '
                  }
                  },
              ],
              series: [
                  {
                  name: '申请额度',
                  type: 'bar',
                  barWidth: '20px',
                  tooltip: {
                      valueFormatter: function (value) {
                      return value;
                      }
                  },
                  // 设置申请额度柱子颜色
                  itemStyle: {
                      color: '#FFB573'
                  },
                  data: [
                      2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3
                  ]
                  },
                  {
                  name: '使用额度',
                  type: 'bar',
                  barWidth: '20px',
                  tooltip: {
                      valueFormatter: function (value) {
                      return value;
                      }
                  },
                  // 设置使用额度柱子颜色
                  itemStyle: {
                      color: '#DA6FDC'
                  },
                  data: [
                      2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3
                  ]
                  },
              ]
          },

          tableData1: [{
              name: '三角函数综合测试(数学)',
              percentage:50,
              averageScore:85.5,
              endTime: ' 2025.08.08 ',
              startTime:' 2025.08.06 '
            }, {
              name: '函数单调性练习(数学)',
              percentage:50,
              averageScore:85.5,
              endTime: ' 2025.08.08 ',
              startTime:' 2025.08.06 '
            }, {
              name: '函数单调性练习(数学)',
              percentage:50,
              averageScore:85.5,
              endTime: ' 2025.08.08 ',
              startTime:' 2025.08.06 '
            }, {
              name: '三角函数综合测试(数学)',
              percentage:50,
              averageScore:85.5,
              endTime: ' 2025.08.08 ',
              startTime:' 2025.08.06 '
          }],
          tableData2: [{
              date: '2016-05-02',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1518 弄'
            }, {
              date: '2016-05-04',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1517 弄'
            }, {
              date: '2016-05-01',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1519 弄'
            }, {
              date: '2016-05-03',
              name: '王小虎',
              address: '上海市普陀区金沙江路 1516 弄'
          }]
        }
    },
    mounted(){      
      this.initBar();
      // 添加窗口大小改变事件监听
      window.addEventListener('resize', this.handleResize);
    },
    beforeDestroy() {
      // 组件销毁前移除事件监听
      window.removeEventListener('resize', this.handleResize);
    },
    methods:{      
      initBar(){  
        // 使用nextTick确保DOM完全渲染后再计算宽度和初始化图表
          // 先计算并设置容器宽度
          
          // 再初始化图表
          this.chartBar1 = echarts.init(document.getElementById("bar1"));
          this.chartBar1.setOption(this.bar1Option);
          
          this.chartBar2 = echarts.init(document.getElementById("bar2"));
          this.chartBar2.setOption(this.bar2Option);

          setTimeout(() => {
            this.handleResize();
          }, 0);
      },
      
      // 动态计算图表宽度
      calculateChartWidths() {
        const bar1Container = document.getElementById('bar1');
        const bar2Container = document.getElementById('bar2');
        
        if (bar1Container && bar2Container) {
          // 获取父容器的宽度
          const textBookData = document.querySelector('.textBook-data');
          const userData = document.querySelector('.user-data');
          console.dir(textBookData);
          console.dir(userData);
          console.log(textBookData.offsetWidth);
          console.log(userData.offsetWidth);
          if (textBookData && userData) {
            // 计算图表宽度（减去内边距）
            const bar1Width = textBookData.clientWidth - 40; // 40 = 20px padding * 2
            const bar2Width = userData.clientWidth - 40;
            // 设置图表宽度
            bar1Container.style.width = bar1Width + 'px';
            bar2Container.style.width = bar2Width + 'px';
          }
        }
      },
      
      // 处理窗口大小改变
      handleResize() {
        this.calculateChartWidths();
        // 重置图表尺寸以适应新的容器大小
        if (this.chartBar1) {
          this.chartBar1.resize();
        }
        if (this.chartBar2) {
          this.chartBar2.resize();
        }
      }
    }

}
</script>

<style lang="scss">
.course-analysis-wrapper {
    height: 100%;
    background-color: #f5f5f5;
    padding: 20px;
}
.course-analysis-header {
    background-color: #fff;
    .title{
      padding-left: 30px;
      height: 50px;
      line-height: 50px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      border-bottom: 1px solid #F2F3F5;
      position: relative;
      &::after{
        content: '';
        position: absolute;
        left: 20px;
        bottom: 18px;
        width: 3px;
        height: 14px;
        background-color: #0070FC;
      }
      span{
        margin-left: 10px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 18px;
        text-align: justify;
        font-style: normal;
      }
    }
}

.class-info-content{
  padding: 20px;
  background-color: #fff;
  .class-info{
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  .class-name{
    width: 120px;
    height: 38px;
    background: #FFFFFF;
    border-radius: 19px;
    border: 1px solid #E7E7E7;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 38px;
    text-align: center;
    font-style: normal;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    &.active{
      background: #0070FC;
      color: #fff;
    }
  }

  .part{
    height: 100px;
    border-radius: 10px;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    padding-bottom: 12px;
    box-sizing: border-box;
    .p1{
      padding-left: 27px;
       font-weight: 600;
    }
    .part-info{
      display: flex;
      justify-content: space-between;
      padding-left: 27px;
      padding-right:10px;
      .value-tip{
        font-size: 26px;
        font-weight: 600;
      }
    }
  }
  .part-1{
    background: linear-gradient( 178deg, #FFF3DA 0%, #FCFCFB 100%);
    .p1{
      color: #592F06;
    }
  }
  .part-2{
    background: linear-gradient( 180deg, #E4EDFF 0%, #F6F9FF 100%);
    .p1{
      color: #092661;
    }
  }
  .part-3{
    background: linear-gradient( 180deg, #F8EBFF 0%, #FDF9FF 100%);
    .p1{
      color: #4F114A;
    }
  }
}
.part-label{
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #333333;
      position: relative;
      padding-left: 12px;
      margin: 0px;
      &::before{
          content: "";
          display: inline-block;
          position: absolute;
          width: 3px;
          height: 12px;
          background: #0070FC;
          border-radius: 2px;
          left: 0;
          top: 3px;
      }
}
.data-static{
display: flex;
justify-content: space-between;
margin-top: 10px;
.textBook-data{
    width: 50%;
    height: 392px;
    padding: 20px;
    background: #FFFFFF;
}
.user-data{
    width: calc(50% - 10px);
    height: 392px;
    padding: 20px;
    background: #FFFFFF;
}
}

.table1{
  margin-top:10px;
  background: #fff;
  overflow: hidden;
  padding:0 20px 20px;
  .part-label{
    margin-top: 22px;
    margin-bottom: 19px;
  }
}
.table2{
  margin-top:10px;
  background: #fff;
  overflow: hidden;
  padding:0 20px 20px;
  .part-label{
    margin-top: 22px;
    margin-bottom: 19px;
  }
}

</style>