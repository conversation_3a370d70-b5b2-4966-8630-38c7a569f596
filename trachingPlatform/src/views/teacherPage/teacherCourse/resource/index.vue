<template>
  <div class="resource-wrapper">
    <div class="resource-header">
        <div>
            <el-button class="add-btn" type="primary" @click="addFolder"> <i class="iconfont icon-xinjian"></i> 新建文件夹</el-button>
            <el-button class="upload-btn" type="primary" @click="showUploadDialog"> <i class="iconfont icon-shangchuan"></i> 上传</el-button>
            <!-- <el-button class="del-btn" type="primary"> <i></i> 删除</el-button> -->
        </div>
        <el-input class="search-input" placeholder="请搜索文件名"  suffix-icon="el-icon-search" v-model="searchText" clearable></el-input>

    </div>
    <div class="resource-content">
      <el-table
        :data="tableData"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ background: '#EFF2F5', color: '#5C6075' }"
        style="width: 100%">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column type="expand">
          <template slot-scope="props">
             <p v-if="props.row.children && props.row.children.length > 0">
               <span class="file-item" v-for="child in props.row.children" :key="child.id">
                 <i :style="{color: getFileIconColor(child.name)}" :class="['iconfont', getFileIcon(child.name)]"></i>
                 {{ child.name }}
                 <br v-if="!child.isFolder"/>
               </span>
             </p>
             <p v-else style="text-align: center; color: #909399; padding: 20px 0;">
               该文件夹为空
             </p>
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          prop="name">
          <template slot-scope="scope">
            <div v-if="editingRowId === scope.row.id">
              <el-input
                v-model="editingName"
                ref="renameInput"
                @blur="saveName(scope.row.id)"
                @keyup.enter.native="saveName(scope.row.id)"
                @keyup.esc.native="cancelRename"
                size="small"
                style="width: 200px;"
                v-focus
                >
              </el-input>
            </div>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="大小"
          prop="size">
        </el-table-column>
        <el-table-column
          label="日期"
          prop="createTime">
        </el-table-column>
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button style="font-size: 14px;" @click="rename(scope)"  type="text" size="mini">重命名</el-button>
            <!-- <el-button style="font-size: 14px;" @click="remove(scope)"  type="text" size="mini">移动到</el-button> -->
            <el-button style="font-size: 14px;" @click="del(scope)"  type="text" size="mini">删除</el-button>
          </template>

        </el-table-column>
      </el-table>
        
    </div>
    <el-dialog
      title="新建文件夹"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-input v-model="folderName" placeholder="请输入文件夹名称"></el-input>
      <template slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createFolder">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 上传文件弹窗 -->
    <el-dialog
      title="上传文件"
      :visible.sync="uploadDialogVisible"
      width="50%"
    >
      <div class="upload-dialog-content">
        <div class="upload-section">
          <h3 style="margin-bottom: 15px;">文件上传</h3>
          <el-upload
            ref="upload"
            :action="actionUrl"
            :headers="uploadHeaders"
            :data="uploadData"
            :on-success="handleFileSuccess"
            :before-upload="handleBeforeUpload"
            :auto-upload="true"
            :multiple="true"
            :file-list="uploadFiles"
            class="upload-demo"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png/gif/pdf/doc/docx/ppt/pptx/excel等文件</div>
          </el-upload>
        </div>
        
        <div class="folder-section" style="margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h3>选择文件夹</h3>
            <el-button size="small" @click="handleFolderSelect">选择现有文件夹</el-button>
          </div>
          <div style="padding: 10px; border: 1px solid #E7E7E7; border-radius: 4px; background-color: #F8F8F8;">
            当前文件夹：{{ selectedFolderId === 0 ? '根目录' : '文件夹' + selectedFolderId }}
          </div>
        </div>
      </div>
      
      <template slot="footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpload">上传</el-button>
      </template>
      </el-dialog>
      
      <!-- 选择文件夹弹窗 -->
      <el-dialog
        title="选择文件夹"
        :visible.sync="folderSelectDialogVisible"
        width="40%"
      >
        <el-tree
          v-if="folderList.length > 0"
          :data="folderList"
          :props="{label: 'name', children: 'children'}"
          show-checkbox
          check-strictly
          node-key="id"
          ref="folderTree"
          default-expand-all
        ></el-tree>
        <div v-else style="text-align: center; padding: 30px; color: #909399;">
          暂无文件夹
        </div>
        <template slot="footer">
          <el-button @click="folderSelectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmFolderSelection">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>

<script>
// import scene from '@/store/modules/scene';
import token from "@/utils/token.js";
import { mapGetters } from 'vuex'
export default {
    data() {
        return {
           tableData: [
            {
              id: 1,
              name: '文件夹1',
              isFolder: true,
              size: '100MB',
              createTime: '2023-08-01',
            },
           ],
          selection: [],
          searchText: '',
          dialogVisible: false,
          folderName: '',
          editingRowId: null,
          editingName: '',
          uploadDialogVisible: false,
          selectedFolderId: 0,
          uploadFiles: [],
          folderSelectDialogVisible: false,
          folderList: [],
          fileForm:{
            "isFolder": false,
            "courseId": this.$route.query.courseId,
            "size": 0,
            "fileUrl": "",
            "fileType": "Image",
            "name": "",
            "parentId": 0
          },
          actionUrl: window.FILEIP,
          uploadData:{
            schoolId:0,
            uploadFileType:'',
            uploadBusinessType:'TeacherCourseResource',
            courseId:0,
            withPreSigned:false,
          },
          uploadHeaders: {
            schoolId:0,
            Authorization: token.getToken(),
          },
        }
    },
    computed: {
      ...mapGetters(["userInfo"]) // 从vuex中获取用户信息
    },
    mounted() {
      this.initTableData();
      this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
      this.uploadData.courseId = this.$route.query.courseId; // 上传资源时的课程id;
      this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    },
    methods: {
      initTableData(){
        this.$api.TeacherCourseResourceTree({
          courseId: this.$route.query.courseId,
          onlyFolder:false,
          keyWord:'',
        }).then(res => {
          if(res.errCode == 0){
            this.tableData = res.data;
          }
        })
      },
      handleSelectionChange(val) {
        this.selection = val;
      },
      rename(scope){
        // 设置当前编辑的行ID和名称
        this.editingRowId = scope.row.id;
        this.editingName = scope.row.name;
        
        // 等待DOM更新，v-focus指令会自动聚焦到输入框
        this.$nextTick(() => {
          // v-focus指令已经处理了聚焦逻辑
        });
      },
      
      saveName(id) {
        // 查找对应的行
        const row = this.tableData.find(item => item.id === id);
        if (row && this.editingName !== '') {
          // 保存新名称
          row.name = this.editingName;
          
          // 这里可以添加API调用保存到服务器
          // 例如: 
          this.$api.TeacherCourseResourceRename({ id, name: this.editingName }).then(res => {
            if(res.errCode == 0){
              this.initTableData();
            }
          })
        }
        
        // 重置编辑状态
        this.editingRowId = null;
        this.editingName = '';
      },
      
      cancelRename() {
        // 取消编辑
        this.editingRowId = null;
        this.editingName = '';
      },
      remove(scope){
        console.log(scope);
        
      },
      del(scope){
         this.$confirm('确认删除该文件夹吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.$api.TeacherCourseResourceDelete({id:scope.id}).then(res => {
               if(res.errCode == 0){
                this.initTableData();
               }
            }).catch(err => {
              this.$message.error('删除失败');
            });
          }).catch(() => {
          });
      },
      addFolder(){
        this.dialogVisible = true;
      },
      createFolder(){
        if(this.folderName){
          let params = {
             name: this.folderName,
            isFolder: true,
            parentId: 0,
            courseId: this.$route.query.courseId,
          }
          this.$api.TeacherCourseResourceCreate(params).then(res => {
            if(res.errCode == 0){
              this.initTableData();
            }
          })
          this.dialogVisible = false;
          this.folderName = '';
        }
      },
      showUploadDialog() {
        this.uploadDialogVisible = true;
        this.selectedFolderId = 0;
        this.uploadFiles = [];
      },
      handleUpload() {
        // 如果有文件需要上传
        if (this.fileForm.fileUrl) {
          // 显示加载中状态
          this.$loading({ text: '文件上传中...' });
          
          // 遍历选择的文件进行上传
          // const uploadPromises = this.uploadFiles.map(file => {
          //   // 创建FormData对象
          //   const formData = new FormData();
          //   formData.append('file', file.raw);
          //   formData.append('name', file.name);
          //   formData.append('courseId', this.$route.query.courseId);
          //   formData.append('parentId', this.selectedFolderId);
            
          //   // 创建文件表单数据对象
          //   const currentFileForm = {
          //     ...this.fileForm,
          //     name: file.name,
          //     size: file.size
          //   };
            
          //   // 添加fileForm信息到formData
          //   formData.append('fileForm', JSON.stringify(currentFileForm));
            
          //   // 调用上传API
          //   return this.$api.TeacherCourseResourceUpload(formData).then(res => {
          //     if (res.errCode !== 0) {
          //       this.$message.error(`${file.name} 上传失败`);
          //     }
          //   }).catch(err => {
          //     this.$message.error(`${file.name} 上传失败`);
          //     console.error('文件上传错误:', err);
          //   });
          // });
          
          // 等待所有文件上传完成
          // 确保uploadData参数完整
          const uploadParams = {
            ...this.uploadData,
            courseId: this.$route.query.courseId || this.uploadData.courseId,
            schoolId: this.userInfo.schools[0].id || this.uploadData.schoolId
          };
          
          // 在fileForm中添加必要的上传参数
          const uploadFileForm = {
            ...this.fileForm,
            courseId: uploadParams.courseId,
            schoolId: uploadParams.schoolId
          };
          
          this.$api.TeacherCourseResourceCreate(uploadFileForm).then(res=>{
            if(res.errCode == 0){
              this.$loading().close();
              this.initTableData();
              this.uploadDialogVisible = false;
              this.$message.success('文件上传成功');
            }
          })
        } else {
          this.$message.warning('请选择要上传的文件');
        }
      },
      handleFileSuccess(file, fileList) {
        this.uploadFiles = fileList;
        this.fileForm.fileUrl = fileList.response.data
        console.log(fileList);
      },
      handleBeforeUpload(file){
        // console.log(file);
        // 判断文件类型并设置相应的变量
        const fileName = file.name.toLowerCase();
        if (fileName.match(/\.(jpg|jpeg|png|gif|bmp)$/)) {
          this.uploadData.uploadFileType = 'Image';
          this.fileForm.fileType = 'Image';
        } else if (fileName.match(/\.(mp4|avi|mov|wmv|flv)$/)) {
          this.uploadData.uploadFileType = 'Video';
          this.fileForm.fileType = 'Video';
        } else {
          this.uploadData.uploadFileType = 'File';
          this.fileForm.fileType = 'File';
        }
        // 更新fileForm中的名称和大小
        this.fileForm.name = file.name;
        this.fileForm.size = file.size;
        
        // 从原始的FILEIP开始构建URL，确保所有uploadData参数都被拼接
        this.actionUrl = `${window.FILEIP}?` +
                        `schoolId=${this.uploadData.schoolId}&` +
                        `uploadFileType=${this.uploadData.uploadFileType}&` +
                        `uploadBusinessType=${this.uploadData.uploadBusinessType}&` +
                        `courseId=${this.uploadData.courseId}&` +
                        `withPreSigned=${this.uploadData.withPreSigned}`;
        return true;
      },
      handleFolderSelect() {
        // 获取文件夹列表
        this.$api.TeacherCourseResourceTree({
          courseId: this.$route.query.courseId,
          onlyFolder: true,
          keyWord: ''
        }).then(res => {
          if(res.errCode == 0){
            this.folderList = res.data;
            this.folderSelectDialogVisible = true;
          }
        }).catch(err => {
          this.$message.error('获取文件夹列表失败');
        });
      },
      selectFolder(folder) {
        this.selectedFolderId = folder.id;
        this.fileForm.parentId = folder.id;
        this.folderSelectDialogVisible = false;
      },
      confirmFolderSelection() {
        const selectedNodes = this.$refs.folderTree.getCheckedNodes();
        if (selectedNodes.length > 0) {
          this.selectedFolderId = selectedNodes[0].id;
          this.fileForm.parentId = selectedNodes[0].id;
          this.folderSelectDialogVisible = false;
        } else {
          this.$message.warning('请选择一个文件夹');
        }
      },
      
      // 根据文件名获取文件图标
      getFileIcon(fileName) {
        // 如果没有文件名，返回默认图标
        if (!fileName) return 'icon-file';
        
        // 转换为小写以进行比较
        const lowerFileName = fileName.toLowerCase();
        
        // 根据文件扩展名返回相应的图标类名
        if (lowerFileName.endsWith('.ppt') || lowerFileName.endsWith('.pptx')) {
          return 'icon-ppt';
        } else if (lowerFileName.endsWith('.xls') || lowerFileName.endsWith('.xlsx')) {
          return 'icon-excel';
        } else if (lowerFileName.endsWith('.doc') || lowerFileName.endsWith('.docx')) {
          return 'icon-word';
        } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg') || 
                   lowerFileName.endsWith('.png') || lowerFileName.endsWith('.gif') || 
                   lowerFileName.endsWith('.bmp')) {
          return 'icon-jpg-1';
        } else if (lowerFileName.endsWith('.pdf')) {
          return 'icon-pdf';
        } else if (lowerFileName.endsWith('.mp4') || lowerFileName.endsWith('.avi') || 
                   lowerFileName.endsWith('.mov') || lowerFileName.endsWith('.wmv') || 
                   lowerFileName.endsWith('.flv')) {
          return 'icon-video';
        } else if (lowerFileName.endsWith('.mp3') || lowerFileName.endsWith('.wav') || 
                   lowerFileName.endsWith('.flac') || lowerFileName.endsWith('.m4a')) {
          return 'icon-audio';
        } else if (lowerFileName.endsWith('.zip') || lowerFileName.endsWith('.rar') || 
                   lowerFileName.endsWith('.7z') || lowerFileName.endsWith('.tar')) {
          return 'icon-zip';
        } else if (lowerFileName.endsWith('.txt') || lowerFileName.endsWith('.md')) {
          return 'icon-text';
        } else {
          return 'icon-file'; // 默认文件图标
        }
      },
      
      // 根据文件名获取图标颜色
      getFileIconColor(fileName) {
        // 如果没有文件名，返回默认颜色
        if (!fileName) return '#606266';
        
        // 转换为小写以进行比较
        const lowerFileName = fileName.toLowerCase();
        
        // 根据文件扩展名返回相应的颜色
        if (lowerFileName.endsWith('.ppt') || lowerFileName.endsWith('.pptx')) {
          return '#FF7861'; // 橙色 - PPT
        } else if (lowerFileName.endsWith('.xls') || lowerFileName.endsWith('.xlsx')) {
          return '#00C090'; // 绿色 - Excel
        } else if (lowerFileName.endsWith('.doc') || lowerFileName.endsWith('.docx')) {
          return '#4A8DFF'; // 蓝色 - Word
        } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg') || 
                   lowerFileName.endsWith('.png') || lowerFileName.endsWith('.gif') || 
                   lowerFileName.endsWith('.bmp')) {
          return '#FF7861'; // 橙色 - 图片
        } else if (lowerFileName.endsWith('.pdf')) {
          return '#FF4867'; // 红色 - PDF
        } else if (lowerFileName.endsWith('.mp4') || lowerFileName.endsWith('.avi') || 
                   lowerFileName.endsWith('.mov') || lowerFileName.endsWith('.wmv') || 
                   lowerFileName.endsWith('.flv')) {
          return '#9056E3'; // 紫色 - 视频
        } else if (lowerFileName.endsWith('.mp3') || lowerFileName.endsWith('.wav') || 
                   lowerFileName.endsWith('.flac') || lowerFileName.endsWith('.m4a')) {
          return '#6C9EF8'; // 浅蓝色 - 音频
        } else if (lowerFileName.endsWith('.zip') || lowerFileName.endsWith('.rar') || 
                   lowerFileName.endsWith('.7z') || lowerFileName.endsWith('.tar')) {
          return '#FFB900'; // 黄色 - 压缩包
        } else if (lowerFileName.endsWith('.txt') || lowerFileName.endsWith('.md')) {
          return '#606266'; // 灰色 - 文本文件
        } else {
          return '#606266'; // 默认颜色
        }
      }

    }

}
</script>

<style lang="scss">
  .resource-wrapper{
    height: 100%;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    .resource-header{
      display: flex;
      justify-content: space-between;
      background: #fff;
      .search-input{
        width: 300px;
        height: 38px;
        background: #FFFFFF;
        border-radius: 4px;
        .el-input__inner{
          height: 38px;
          border: 1px solid #E7E7E7;
        }

      }
      .add-btn{
        width: 120px;
        height: 38px;
        background: #07C392;
        border: 1px solid #07C392;
        color: #fff;
        border-radius: 4px;
        padding: 0;
      }
      .upload-btn{
        width: 80px;
        height: 38px;
        color: #333333;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E7E7E7;
        padding: 0;
      }
      .del-btn{
        width: 80px;
        height: 38px;
        color: #333333;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E7E7E7;
        padding: 0;
      }

    }

    .resource-content{
      margin-top: 20px;
      .el-table__expand-icon{
        transform: rotate(0deg)!important;
        background:url("../../../../assets/public/sucaiku.png") no-repeat;
      }
      .el-icon-arrow-right{
        display: none;
      }
      .el-icon-arrow-right:before{
        content: ""
    }
  }
  
  /* 上传弹窗样式 */
  .upload-dialog-content {
    .upload-section,
    .folder-section {
      h3 {
        font-size: 14px;
        color: #333333;
        margin-bottom: 15px;
        font-weight: bold;
      }
    }
    
    .upload-section {
      .el-upload__tip {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .folder-section {
      .el-button {
        font-size: 12px;
        padding: 8px 12px;
      }
      
      .el-tree {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #E7E7E7;
        border-radius: 4px;
        padding: 10px;
      }
    }
  }
  
  /* 文件夹选择弹窗样式 */
  .el-tree {
    .el-tree-node__content {
      font-size: 14px;
      height: 36px;
      line-height: 36px;
    }
    
    .el-tree-node__expand-icon {
       font-size: 14px;
     }
   }
    //   }
    // }

    .file-item{
      height: 28px;
      line-height: 28px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: justify;
      font-style: normal;
      padding-left:66px;
    }
  }

</style>