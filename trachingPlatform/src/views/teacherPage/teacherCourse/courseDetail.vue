<template>
    <div class="course-detail-page">
        <div class="top">
            <span class="back-icon" @click="goPath()">
              <i class="iconfont icon-fanhui"></i> 
            </span>
            <el-breadcrumb>
                <el-breadcrumb-item :to="{path: type==1?'/student':'/teacher/teacherCourse'}">我的课程</el-breadcrumb-item>
                <el-breadcrumb-item>{{currentTitle}}</el-breadcrumb-item>
                <el-breadcrumb-item v-if="subTitle">{{subTitle}}</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="course-container">
            <div class="left">
                <div class="aside-item"
                    v-for="item in asideList" 
                    :key="item.id"
                    :class="{ active: currentComponent === item.componentName }" 
                    @click="changeComponent(item)">
                    <p><i :class="['iconfont',item.icon]"></i> <span :class="`${item.icon}-span`">{{item.title}}</span></p>
                </div>
            </div>
            <div class="main">
                <!-- 动态渲染组件 -->
                <component :is="currentComponent"></component>
            </div>
        </div>
    </div>
</template>
<script>
import CourseManagement from '@/views/teacherPage/teacherCourse/course/index.vue'
import AssignmentExam from '@/views/teacherPage/teacherCourse/homework/index.vue';
import CourseQuestionBank from '@/views/teacherPage/teacherCourse/tiku/index.vue';
import MyClass from '@/views/teacherPage/teacherCourse/myClass/index.vue';
import CourseAnalysis from '@/views/teacherPage/teacherCourse/courseAnalysis/index.vue';
import Resource from '@/views/teacherPage/teacherCourse/resource/index.vue';
import Homework from '@/views/teacherPage/teacherCourse/homework/index.vue';
import Exam from '@/views/teacherPage/teacherCourse/exam/index.vue';
export default {
    data() {
        return {
            asideList:[
                {
                    title: '资源',
                    icon: 'icon-fenpei', 
                    id: 6,
                    componentName: 'Resource'

                },
                
                {
                    title: '作业',
                    icon: 'icon-Gc_59_face-Information', 
                    id: 7,
                    componentName: 'Homework'

                },
                {
                    title: '考试',
                    icon: 'icon-ic_zuoye', 
                    id: 8,
                    componentName: 'Exam'
                },
                {
                    id:1,
                    title:'课程信息',
                    icon:'icon-lunwendagang',
                    componentName: 'CourseManagement',
                },
                // {
                //     title: '作业考试',
                //     icon: 'icon-assignment', 
                //     id: 2,
                //     componentName: 'AssignmentExam'
                // },
                {
                    title: '课程题库',
                    icon: 'icon-tiku', 
                    id: 3,
                    componentName: 'CourseQuestionBank'
                },
                {
                    title: '我的班级',
                    icon: 'icon-zu49655', 
                    id: 4,
                    componentName: 'MyClass'
                },
                {
                    title: '学情分析',
                    icon: 'icon-xueqingfenxi', 
                    id: 5,
                    componentName: 'CourseAnalysis'

                },
                
            ],
            // 当前要渲染的组件名
            currentComponent: 'Resource' ,
            subTitle:'',
            currentTitle:'课程管理'
        };
    },
    components: {
        CourseManagement,
        AssignmentExam,
        CourseQuestionBank,
        MyClass,
        CourseAnalysis,
        Resource,
        Homework,
        Exam

    },
    props:{
        type:{
            type:Number,
            default:0
        }
    },
    mounted() {
        if(this.type==1){
            this.asideList = this.asideList.filter(item => item.id !== 3&&item.id!==4);
        }
    },
    methods: {
        // 切换当前组件的方法
        changeComponent(item) {
            this.currentComponent = item.componentName;
            this.currentTitle = item.title; // 更新当前标题
        },
        goPath(){
            if(this.type==1){
                this.$router.push('/student')
            }else{
                this.$router.push('/teacher/teacherCourse')
            }
        }
    }
}
</script>
<style lang="scss" scoped>
    .course-detail-page{
        background-color: #F6F8FA;
        .top{
            height: 50px;
            background: #FFFFFF;
            display: flex;
            padding: 0 30px;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 10px;
            .back-icon{
                width: 20px;
                height: 20px;
                background: #BBBBBB;
                border-radius: 4px;
                color: #fff;
                line-height: 20px;
                text-align: center;
                display: inline-block;
                margin-right: 20px;
                cursor: pointer;
                .iconfont{
                    font-size: 12px;
                    line-height: 20px;
                }
           }
           ::v-deep .el-breadcrumb__inner a, 
           ::v-deep.el-breadcrumb__inner.is-link{
                color: #bbb;
           }
           
           ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner{
                color: #0070FC;
           }
        }
        .course-container{
            display: flex;
            justify-content: space-between;
            height: calc(100vh - 100px);
            margin-left: 10px;
            .left{
                width: 180px;
                background: #FFFFFF;
                border-radius: 4px;
                padding-top: 10px;
                .aside-item{
                    height: 38px;
                    line-height: 38px;
                    padding-left: 20px;
                    color: #333;
                    cursor: pointer;
                    font-size: 14px;
                    border-right: 2px solid transparent;
                    i{
                        color: #C0C6D6;
                        font-size: 14px;
                        margin-right: 6px;
                    }
                    .icon-ic_zuoye{
                        font-size: 22px;
                        position: relative;
                        left:-4px;
                    }
                    .icon-ic_zuoye-span{
                        position: relative;
                        top: -3px;
                        left: -8px;
                    }
                    &:hover{
                        background: #F1F7FF;
                        color: #0070FC;
                        border-right: 2px solid #0070FC;
                        i{
                            color: #0070FC;
                        }
                    }
                    &.active{
                        background: #F1F7FF;
                        color: #0070FC;
                        border-right: 2px solid #0070FC;
                        i{
                            color: #0070FC;
                        }
                    }
                }

            }
    
            .main{ 
                width: calc(100% - 190px);
                margin-left: 10px;
                background: #F6F8FA;
                border-radius: 4px;
                height: 100%;
                overflow-y:auto;
                // padding: 40px;
            }
        }
    }
</style>