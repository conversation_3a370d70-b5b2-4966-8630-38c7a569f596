<template>
    <div class="tiku-info">
        <div class="top-operate">
            <div class="operate-left">
                <el-select v-model="tableConfig.searchParams.questionType" clearable class="select-type" placeholder="请选择题型" @change="selectType">
                    <el-option label="全部" value=""></el-option>
                    <el-option v-for="item in questionType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-button class="btn add-btn-item" @click="addQuestion()"><i class="iconfont icon-xinjian"></i>新增试题</el-button>
                <el-button class="btn import-btn" @click="importDialogVisible = true;"><i class="iconfont icon-shujudaoru"></i>导入试题</el-button>
                <!-- <el-button class="btn move-btn"><i class="iconfont icon-ziyuan143"></i>移动到分类</el-button> -->
                <el-button class="btn del-btn" @click="batchDelete"><i class="el-icon-delete"></i>删除试题</el-button>
            </div>
            <el-input class="search-ipt" placeholder="请输入关键词搜索" @change="searchKey()" v-model="tableConfig.searchParams.searchWord" suffix-icon="el-icon-search"></el-input>
        </div>
        <table2 
        @selectionChange="selectionChange" 
        :notShowSearch="notShowSearch" 
        ref="tablePreview" 
        @selection-change="handleSelectionChange" 
        :selectable="tableConfig.selectable" 
        :data="tableConfig.tableData" 
        :columns="tableConfig.columns" 
        :queryFormConfig="queryConfig" 
        :total="tableConfig.total"
        :pagination="tableConfig.pagination" 
        :max-height="tableConfig.height" 
        :paginationLayout="tableConfig.paginationLayout" 
        :firstLoad="firstLoad" 
        :searchParams="tableConfig.searchParams" 
        @handleSearch="handleSearch">
        <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
        <template #operate>
            <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
            <template slot-scope="scope">
                <el-button v-for="(item, index) in operateBtns" type="text"
                        v-show="isShowOperateBtn(scope, item)"
                        @click="btnClick(scope.row, item)" :key="index"
                        :class="item.class || ''"
                        :disabled="scope.row.dataLocked">
                {{ item.name }}
                </el-button>
            </template>
            </el-table-column>
        </template>
        </table2>


        <el-dialog 
        :beforeClose="handleDialogClose" 
        title="题目导入" 
        v-if="importDialogVisible" 
        width="1000px" 
        :visible.sync="importDialogVisible" 
        custom-class="import-question-dialog" 
        :close-on-click-modal="false">
        <!-- 导入题目  -->
        <importWord ref="importWord" @syncCourse="syncCourse"></importWord>

        </el-dialog>

        <base-dialog 
            :visible.sync="showPreviewModal"
            v-if="showPreviewModal"
            :close-on-click-modal="false"
            :no-footer="true"
            width="1200px"
            title="题目预览">
            <div style="padding: 0 15px; max-height: calc(100vh - 200px); overflow-y: auto;">
                <question-preview-content
                    v-if="showPreviewModal"
                    :showForm="true"
                    :isAnswerHint="true"
                    :data="previewData">
                </question-preview-content>
            </div>
        </base-dialog>
    </div>
</template>
  
<script>
import {
  getAnswerDispatch,
  questionInitConfig,
  questionTypeLabel
} from '@/components/base/question/util.js'
import cloneDeep from 'lodash/cloneDeep';
// 题目类型
const questionType = [
    { label: '单选题', value: 'SingleChoice' },
    { label: '多选题', value: 'MultipleChoice' },
    { label: '填空题', value: 'FillIn' },
    { label: '简答题', value: 'ShortAnswer' },
];
// 难易度
const ComplexityList = [
    { label: '未知', value: 'Unknown' },
    { label: '简单', value: 'Easy' },
    { label: '一般', value: 'Moderation' },
    { label: '困难', value: 'Difficult' },
    
]
export default {
    name: 'tikuTable',
    data() {
        return {
            questionType, // 题目类型  
            className:'', // 分类名称
            tableConfig: {
                columns: [
                {
                    label: "试题标题",
                    prop: "title",
                },
                {
                    label: "题型",
                    prop: "questionType",
                    formatter(row, column, cellValue) {
                        const questionTypeText = questionType.find(item => item.value === cellValue)?.label || cellValue;
                        return questionTypeText; 
                    }
                },
                {
                    label: "难易",
                    prop: "complexity",
                    formatter(row, column, cellValue) {
                        const complexityText = ComplexityList.find(item => item.value === cellValue)?.label || cellValue;
                        return complexityText; // 假设这是你要显示的文本 
                    }
                },
                // {
                //     label: "知识点",
                //     prop: "school",
                // },
                {
                    label: "创建者",
                    prop: "createByUserName", // 原 `phone` 可能是笔误，这里改为 `department`
                },
            
                {
                    label: "创建时间",
                    prop: "createTime",
                    formatter(row, column, cellValue) {
                        const isoDate = '2025-06-08T03:08:18.062';
                        const date = new Date(isoDate);

                        // 自定义格式
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        const hours = String(date.getHours()).padStart(2, '0');
                        const minutes = String(date.getMinutes()).padStart(2, '0');
                        const seconds = String(date.getSeconds()).padStart(2, '0');

                        const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                        return formattedDate
                    }
                }
                ], // 表格列
                tableData: [], // 表格数据
                pagination: true,
                total: 0,
                selectable: true, // 启用多选
                searchParams: {
                    pageIndex: 1,
                    pageSize: 10,
                    searchWord:'',
                    questionType:'',
                    courseId: this.$route.query.courseId, // 课程id
                    // sectionId:'', // 章节id
                    title:'', // 标题
                    // complexity:'', //难易度
                },
                btnList: {
                detail: {
                    enable: true
                }
                },
                // 补全模板中使用的变量
                notShowSearch: false,
                selectable: true,
                pagination: true,
                paginationLayout: 'total, sizes, prev, pager, next, jumper',
            },
            queryConfig: {},
            firstLoad: true,
            loading: false,
            actionBtns: [],
            operateBtns: [
                { name: '预览',class:'default-btn' },
                { name: '编辑',class:'default-btn' },
                { name: '删除',class:'del-btn' }
            ],
            operateWidth: 200,
            selectQuestionList:[],//选中的列表

            importDialogVisible: false, // 导入弹窗是否显示
            uploadStep : 1, // 上传步骤 1:上传文件 2:导入的题目预览
            actionUrl: `${window.PUBLICHOSTA}/jxz/Wrod/ImportWrodDate`, // 上传文件的接口地址
            importQuestionList:[], // 导入的题目列表
            htmlContent:'',// 模板文档的内容
            parsedText: {},// 导入的内容

            showPreviewModal:false,// 题目预览
            previewData:{},//
        } 
    },
    props:['key'],
    components: {
        'question-preview-content': () => import('@/components/question/question-preview-content.vue'),
        importWord: () => import('./impWord/index.vue'), // 导入的题目预览
    },
    watch: {
        key: {
            handler(val) {
                // this.initTableData();
            },
            immediate: true,
        }, 
    },
    mounted() {
        // this.initTableData();
    },
    methods: {
        initTableData() {
            this.$api.GetList(this.tableConfig.searchParams).then(res=>{
                this.tableConfig.tableData = res.data.items;
                this.tableConfig.total = res.data.total;
            })
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
        selectType(val){
            this.tableConfig.searchParams.pageIndex = 1;
            this.tableConfig.searchParams.questionType = val;
            // this.initTableData();

        },
        searchKey(){
            this.tableConfig.searchParams.pageIndex = 1;
            // this.initTableData();
        },
        // 补全模板中使用的方法
        selectionChange(selection) {
            console.log('Selection changed:', selection);
            
        },
        async handleSelectionChange(selection) {
            console.log('Table selection changed:', selection);
            this.selectQuestionList = selection; // 假设这是你要更新的状态变量
        },
        handleSearch(params) {
            // 可以在这里添加搜索逻辑
            console.log('Search params:', params);
            this.tableConfig.searchParams = params;
            // console.log('Search params:', params);
            this.initTableData();
        },
        handlePlus(button) {
            console.log('Clicked plus button:', button);
            // 可以在这里添加新增逻辑
        },
        isShowOperateBtn(scope, item) {
            // 可以根据实际需求添加显示逻辑
            return true;
        },
        btnClick(row, item) {
            console.log('Clicked operation button:', row, item);
            // 可以在这里添加操作按钮点击逻辑
            switch (item.name) {
                case '预览':
                    console.log(row)
                    this.showPreviewModal = true;
                    this.previewData = this.initParams(row)
                    // this.$emit('changeCom','addQuestion',row,'view')
                    break;
                case '编辑':
                    this.$emit('changeCom','addQuestion',row,'edit')
                    break;
                case '删除':
                   // 这里可以添加删除逻辑
                    this.handleDelete(row)
                    break;
            }
        },
        initParams(data){
            console.log('data',data)
            return {
                type: data.questionType,
                title: data.title,
                desc: data.description,
                optionConfig: JSON.parse(data.contentData),
                answerDetail: data.answerAnalysis,
                difficulty: data.complexity ||  'Easy',
                sort: data.sort,// 排序
                cateId: data.cateId,
                answer:data.answer,// 题目答案
            }
        },
        // 新增题目
        addQuestion(){
            this.$emit('changeCom','addQuestion')
        },
        // 删除单个题目
        async handleDelete(row){
            await this.$zdDialog({
                contImg: '',
                contTitle: '确定删除?',
                contDesc: '确定后，该题目会被删除, 是否继续?',
            })
            this.$api.DeleteQuestion({id:row.id}).then(res=>{
                this.tableConfig.searchParams.pageIndex = 1;
                this.initTableData()
            })
        },
        // 批量删除题目
        async batchDelete(ids){
            if(this.selectQuestionList.length>0){
                await this.$zdDialog({
                    contImg: '',
                    contTitle: '确定删除?',
                    contDesc: '确定后，选中的题目会被删除, 是否继续?',
                })
                let ids = this.selectQuestionList.map(item=>item.id)
                this.$api.BatchDelete(ids).then(res=>{
                    this.tableConfig.searchParams.pageIndex = 1;
                    this.initTableData()
                })
            }else{
                this.$message({
                    type:'warning',
                    message: '请选择要删除的题目',
                    offset: 300
                })
            }
           
        },
        // 导入文件弹窗关闭的回调
        handleDialogClose(){
        if(this.$refs.importWord.inputText.trim()==''){
            this.importDialogVisible = false;
            this.importQuestionList = [];
            this.importFileList = [];
            this.uploadStep = 1;
            return
        }
        this.$zdDialog({
            width:'400px',
            center:true,
            contTitle: '确定要关闭吗?关闭后导入的数据将不会保存',
        }).then(()=>{
            this.importDialogVisible = false;
            this.importQuestionList = [];
            this.importFileList = [];
            this.uploadStep = 1;
        })
        
        },
        // 批量保存到题库
        syncCourse(list){
            console.log("list",list)
            let params = list.map(v=>{
                return this.handleQuestionParams({
                    ...v,
                    answer:v.answer
                })
            })
            this.$api.BatchCreate(params).then(res=>{
                this.importDialogVisible = false;
                this.importQuestionList = [];
                this.importFileList = [];
                this.uploadStep = 1;
                this.$message({
                    type:'success',
                    message: '导入成功',
                    offset: 300
                })
                this.tableConfig.searchParams.pageIndex = 1;
                this.initTableData()
            })
        },
        // 构建题目保存的参数
        handleQuestionParams(qObject){
      // 题目判分类型
      const handleScoringType = (qObject)=>{
          return qObject.optionConfig.scoreRadio?qObject.optionConfig.scoreRadio:2
      }
      // 题目答案
      const handleAnswer = (qObject)=>{
        if(JSON.stringify(qObject.answer).indexOf('longArray')!=-1){
          return JSON.stringify({ longArray: qObject.answer.longArray });
        }else{
          return JSON.stringify({ longArray: qObject.answer });
        }
      }
     // 处理 contentData 字段 // 绑定 this 上下文
      const handleQsContentData = (qObject)=>{
          return this.handleContentData(qObject.optionConfig);
      } 
      const handleParams = (qObject)=>{
        return {
          "id": qObject.id, // 题目id
          "title": qObject.title,
          "courseId": qObject.courseId || 0,
          "questionType": qObject.type,
          "scoringType":handleScoringType(qObject), // 计分规则
          "sort": qObject.sort||0,
          "answer": qObject.answer,
          "contentData": handleQsContentData(qObject), //  扩展字段  前端使用配置信息
          "description": qObject.desc,
          "answerAnalysis": qObject.answerDetail, // 答案解析
          "complexity": qObject.difficulty, // 难易
          "status": qObject.status,
        }
      }
      let params =  handleParams(qObject);
      return params
        },
        // 处理前端配置信息
        handleContentData(data){
        return JSON.stringify(data)
        },
    }
}
</script>

<style lang="scss" scoped>
.tiku-info{
    padding: 20px;

    .top-operate{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .operate-left{
            .select-type{
                width: 220px;
                ::v-deep.el-input--medium .el-input__inner{
                    height: 38px;
                    line-height: 38px;
                }
            }
            .btn{
                width: 120px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                color: #333;
                border: 1px solid #E7E7E7;
                &:hover{
                    background: #E7E7E7; 
                }
                .iconfont{
                    margin-right: 4px;
                }
            }
            .add-btn-item{
                background: #07C392;
                border: 1px solid #07C392;
                color: #FFFFFF;
                margin-left: 10px;
                &:hover{
                    background: #10D3A0;
                }
            }
           .import-btn{}
           .move-btn{}
           .del-btn{}
        }
        .search-ipt{
            width: 300px;
            margin-right: 10px;
        }
    }
}
</style>
<style lang="scss">
.import-question-dialog{
  margin-top: 6vh!important;
    .el-dialog__body{
      padding-top: 0;
    }
}
</style>