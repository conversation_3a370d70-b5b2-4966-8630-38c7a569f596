<template>
    <div class="tiku-info">
        <component 
        :is="currentComponent"
        :params="params" 
        @changeCom="changeCom"
        @cancle="cancle"
        :key="componentKey"
        ></component>
    </div>
</template>
  
<script>
import tikuList from '@/views/teacherPage/teacherCourse/tiku/tikuList.vue'
import addQuestion from '@/views/teacherPage/teacherCourse/tiku/addQuestion.vue'
export default {
    name: 'tikuInfo',
    data() {
        return {
            currentComponent: 'tikuList', // 初始组件ID
            params:{}, // 组件待传的参数
            componentKey: 0 // 用于强制组件重新渲染的 key
        } 
    },
    components: {
        tikuList, // 导入组件
        addQuestion, // 导入组件
    },
    mounted() {
    },
    methods: {
        changeCom(val,data,type){ // 切换组件的方法
            this.currentComponent = val
            if(data){ // 题目修改和编辑
                this.params = {
                    defaultData:data,
                }
                if(type == 'edit'){ // 编辑
                    
                }else{ //  预览

                }
            }
            // 每次切换组件时更新 key 值
            this.componentKey += 1
        },
        cancle(){ // 取消
            this.currentComponent = 'tikuList'
            // 每次取消时更新 key 值
            this.componentKey += 1
        },
       
    }
}
</script>

<style lang="scss" scoped>
.tiku-info{
    padding: 20px;
    background: #fff;
    height: calc(100% - 50px);

    .top-operate{
        display: flex;
        justify-content: space-between;
        align-items: center;
        .operate-left{
            .select-type{
                width: 220px;
                ::v-deep.el-input--medium .el-input__inner{
                    height: 38px;
                    line-height: 38px;
                }
            }
            .btn{
                width: 120px;
                height: 38px;
                background: #FFFFFF;
                border-radius: 4px;
                color: #333;
                border: 1px solid #E7E7E7;
                &:hover{
                    background: #E7E7E7; 
                }
                .iconfont{
                    margin-right: 4px;
                }
            }
            .add-btn-item{
                background: #07C392;
                border: 1px solid #07C392;
                color: #FFFFFF;
                margin-left: 10px;
                &:hover{
                    background: #10D3A0; 
                }
            }
           .import-btn{}
           .move-btn{}
           .del-btn{}
        }
        .search-ipt{
            width: 300px;
            margin-right: 10px;
        }
    }
}
</style>