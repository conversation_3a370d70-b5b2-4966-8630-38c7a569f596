<template>
  <div class="import-word-page">
    <div ref="actionsBox" class="flex-btn-pageTop">
      <!-- <FlexExportButton :title="'下载模板'" :fileName="'导入试题模板'" :url="downWord_url" :downLoadFn="downLoadFile" /> -->
      <!-- <input type="file" @click.native="importTemplate">模板导入</input>
      <button @click.native="exampleVisible = true" type="plain">格式示例</button>
      <button @click.native="clearText" type="plain">清空文本</button> -->
      <div class="upload-wrapper">
          <input 
            class="file-input" 
            ref="file" 
            type="file" 
            @change="handleFileChange" 
            accept=".docx"
          />
          <el-button 
            type="primary" 
            icon="el-icon-upload" 
            @click="$refs.file.click()"
          >
            上传Word文档
          </el-button>
          <el-button class="export-fill" type="text" @click="exportFile">下载模板</el-button>
      </div>
      <el-button type="primary" style="width: 200px;margin-top:5px;margin-bottom:6px;" :loading="btnLoading"
          @click.native="importHandle"><i v-if="!btnLoading" class="el-icon-refresh"></i> 同步到课程</el-button>
    </div>
    <div class="quiz-generator">
      <el-input 
        type="textarea" 
        class="pub-area custom-textarea" 
        v-model="inputText" 
        placeholder="在这里输入试题">
      </el-input>
      <div class="pub-area"  v-loading="questionLoading">
        <div style="text-align:center; border-bottom:1px solid #dcdfe6">
          
        </div>
        <div class="right-area">
          <div class="topic-item content" v-for="(item, index) in questionList" :key="index">
            <div class="shadow-box"></div>
            <question-preview-content :showTitle="item.type!=3" :disabled="true" :isAnswerHint="true" :showSort="true" :sort="(index + 1)" :data="item">
            </question-preview-content>
          </div>
        </div>
        
      </div>
    </div>

    <el-dialog
      title="保存到题库"
      :visible.sync="dialogVisible"
      width="500px"
      :modal="false"
      class="join-question-bank"
      :before-close="handleClose">
      <!-- <div class="question-bank-tree">
        <el-tree 
          :data="synchronousTree" 
          default-expand-all 
          :props="defaultProps" 
          @node-click="handleNodeClick">
        </el-tree>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" class="form-main-button" @click="SynchronizeToCourse">同 步</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mammoth from 'mammoth';
import { downloadFile } from "@/utils/downloadFile.js";
import { createQuestion, handleQuestionTitle, handleQuestionDetails, parseInput } from '@/utils/questionParser.js';
import { mapGetters } from "vuex"
export default {
  data() {
    return {
      inputText: '',
      dtoList: [], // 生成的试题数组
      quesAnswerList: [],
      btnLoading: false,
      questionList: [],
      parsedText: {},
      questionLoading:false,
      dialogVisible: false,
      synchronousTree: [], // 同步到课程的树结构数据
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      selectNode: null, // 选中的节点
    }
  },
  name: 'preview-question',
  components: {
    'question-preview-content': () => import('@/components/question/question-preview-content-ai.vue'),
  },
  computed:{
    ...mapGetters({
      userInfo: ["userInfo"]
    })
  },
  methods: {
    handleFileChange(event) {
      this.questionLoading = true;
      const file = event.target.files[0];
      if (file) {
        // 创建FileReader来读取文件
        const reader = new FileReader();
        // 文件读取完成后触发的事件
        reader.onload = (event) => {
          const arrayBuffer = event.target.result;

          // 提取原始文本
          // mammoth.extractRawText({ arrayBuffer: arrayBuffer }).then(rawTextResult => {
          //   const rawText = rawTextResult.value;

          //   // 转换为 HTML
          //   mammoth.convertToHtml({ arrayBuffer: arrayBuffer }).then(htmlResult => {
          //     let html = htmlResult.value;
          //     // 从原始文本中提取换行信息并添加到 HTML 中
          //     const lines = rawText.split(/\r?\n/);
          //     lines.forEach((line, index) => {
          //       if (line.trim() === '') {
          //         // 在 HTML 中添加换行符 \n
          //         html = html.replace(lines[index + 1] || '', '\n' + (lines[index + 1] || ''));
          //       }
          //     });

          //     // 赋值操作，将 HTML 内容保存在 data 属性中
          //     this.inputText = html.replace(/^\s*[\r\n]/gm, '');
          //   }).catch(error => {
          //     console.error('HTML conversion error: ', error);
          //   });
          // }).catch(error => {
          //   console.error('Raw text extraction error: ', error);
          // });
          // Mammoth.js 用于转换arrayBuffer到文本
          mammoth.extractRawText({ arrayBuffer: arrayBuffer }).then(result => {
            this.inputText = result.value.replace(/^\s*[\r\n]/gm, ''); // 赋值操作，将文本内容保存在data属性中
            if(!this.inputText){
              this.questionLoading = false;
            }
          }).catch(error => {
            this.questionLoading = false;
            console.error('File reading error: ', error);
          });
        };
        // 读取文件为ArrayBuffer
        reader.readAsArrayBuffer(file);
      }
    },
    importTemplate() {
      this.$refs.file.value = ''
      this.$refs.file.click();
    },
    generateQuestions() {
      this.questionList = [];
      let parsedText = parseInput(this.inputText);
      this.parsedText = parsedText;
      this.quesAnswerList.length = 0;
      this.dtoList = parsedText.map((item, ind) => {
        let question = createQuestion(ind);
        const longArray = [];
        item.forEach((it, idx) => {
          if (idx === 0) {
            handleQuestionTitle(it, question, longArray);
          } else {
            handleQuestionDetails(it, question, longArray);
          }
        });
        // console.log("q-", question);
        if (question.type) {
          this.questionList.push(question);
          return question;
        } else {
          return null;
        }
      }).filter(it => it !== null && it !== undefined && it);

      this.questionLoading = false;
      console.log('questionList----', this.questionList);
    },
    //获取所有课程
    async GetMyCourses(){
      const { code, data } = await this.$api.GetQuestionCategoryTree({ courseId: this.$route.query.id });
      if(code == 200){
        // data.forEach(e => {
        //   e.name = e.courseName
        //   e.children = []
        // });
        this.synchronousTree = data
      }
    },
    importHandle() {
      if (this.questionList.length === 0) return this.$message.warning("请先在左边输入试题")
      // let isOk = true
      // // 答案必填校验
      // for (let i = 0; i < this.questionList.length; i++) {
      //   if (this.questionList[i].questionType === 1 && this.questionList[i].answer === "") {
      //     isOk = false
      //     return this.$message.error(`请填写第${i + 1}题选择题的答案`)
      //   } else if (this.questionList[i].questionType === 2 && this.questionList[i].answer.length === 0) {
      //     isOk = false
      //     return this.$message.error(`请填写第${i + 1}题选择题的答案`)
      //   } else if (this.questionList[i].questionType === 4 && this.questionList[i].answer === "") {
      //     isOk = false
      //     return this.$message.error(`请填写第${i + 1}题判断题的答案`)
      //   } else if (this.questionList[i].questionType === 3 && this.questionList[i].answer === "") {
      //     isOk = false
      //     return this.$message.error(`请填写第${i + 1}题填空题的答案`)
      //   } else if (this.questionList[i].questionType === 5 && this.questionList[i].answer === "") {
      //     isOk = false
      //     return this.$message.error(`请填写第${i + 1}题简答题的答案`)
      //   }
      // }

      // for (let i = 0; i < this.questionList.length; i++) {
      //   if (this.questionList[i].questionType === 1 || this.questionList[i].questionType === 2) {
      //     // 选择题判断答案是否有重复的，如有两个选项C
      //     const answerHasDuplicates = this.hasDuplicateProperty(this.questionList[i].answer, "answer")
      //     if (answerHasDuplicates) {
      //       isOk = false
      //       return this.$message.warning(`第${i + 1}题选择题有重复的答案，请检查`)
      //     }
      //     // 选择题判断选项是否有重复的，如有两个选项B
      //     const hasDuplicates = this.hasDuplicateProperty(this.questionList[i].optionConfig.options, "questionOption")
      //     if (hasDuplicates) {
      //       isOk = false
      //       return this.$message.warning(`第${i + 1}题选择题选项中有重复的选项，请检查`)
      //     }

      //     const tempOptionList = this.questionList[i].optionConfig.options.map(it => it.value)
      //     // 选择题判断选项是否按照顺序排列并且以A开始，如选项ABCDE 但是填写了EDCBA、CD、ACD等
      //     if (!this.isValidSequence(tempOptionList)) {
      //       isOk = false
      //       return this.$message.warning(`第${i + 1}题选择题选项没按照字母顺序排列，请检查`)
      //     }

      //     for (let j = 0; j < this.questionList[i].optionConfig.options.length; j++) {
      //       // 选择题判断答案是否不在选项范围，如选项只有ABCD 但是填写了F为正确答案
      //       const hasValue = this.hasSpecificValue(this.questionList[i].optionConfig.options, "questionOption", this.questionList[i].answer);
      //       if (!hasValue) {
      //         isOk = false
      //         return this.$message.warning(`第${i + 1}题选择题答案有不在选项范围的，请检查`)
      //       }
      //     }
      //     if (!isOk) return
      //   }
      // }
      // if (!isOk) return
      this.btnLoading = true

      //同步到课程
      setTimeout(() => {
        this.btnLoading = false
      },1500)

      // this.GetMyCourses();
      this.dialogVisible = true;
      // this.$emit('syncCourse', this.questionList)
    },

    // 判断答案选项是否按照顺序排列
    isValidSequence(arr) {
      const validSequence = ['A', 'B', 'C', 'D', 'E'];
      // 检查数组是否为空或第一个元素不是'A'
      if (arr.length === 0 || arr[0] !== 'A') {
        return false;
      }
      // 遍历数组并检查每个元素是否在有效序列中
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== validSequence[i]) {
          return false; // 如果不匹配，则返回false
        }
      }

      return true; // 如果所有条件都满足，返回true
    },

    // 检查数组中对象某个属性的值是否有重复的
    hasDuplicateProperty(arr, propertyName) {
      let seenValues = new Set();
      for (let i = 0; i < arr.length; i++) {
        if (seenValues.has(arr[i][propertyName])) {
          return true; // 发现重复
        }
        seenValues.add(arr[i][propertyName]);
      }
      return false; // 未发现重复
    },

    // 检查数组中对象的某个属性的值是否存在某个值
    hasSpecificValue(arr, propertyName, valueToFind) {
      return arr.some(obj => obj[propertyName] === valueToFind);
    },
    // 下载模板
    exportFile(){
       let url = "/template/题库导入模板.docx"
      downloadFile(url, '题库导入模板',this)
    },
    //节点点击
    async handleNodeClick(data,node) {
        this.selectNode = data
    },
    SynchronizeToCourse(){
      // if(this.selectNode){
        let questionList = this.questionList.map(item => {
          let newItem = {
            ...item,
            // questionCategoryId: this.selectNode.id, // 题目分类id
            questionType: item.type,
            answerAnalysis: item.answerDetail,
            contentData:JSON.stringify(item.optionConfig),// 题目内容
            // skillTags:item.skill, // 技能点
            // knowledgeTags:item.knowledge, //知识点
            courseId: this.$route.query.courseId ,
            scoringType:2,//计分类型
            // noAutoScore: item.questionType==47?true:false,//0 自动判分 1 不自动判分
            tenantId:this.userInfo.tenantId,
            status:0,//0 未发布 1 已发布
            answer:  typeof item.answer== 'object'? JSON.stringify(item.answer) : item.answer, // 答案
          }
          delete newItem.knowledge // 技能/知识点
          delete newItem.skill // 技能/知识点
          return newItem
        })
        // console.log("questionList",questionList)
        if(!this.topicVerification(questionList)){
          this.$message.error('题目信息缺失/错误,无法加入题库!')
          return
        }
        this.$emit('syncCourse', questionList)
      // }else{
      //   this.$message.warning('请选择目标节点')
      // }
    },
    // 题目校验
    topicVerification(arr){
      let boon = true
      // 校验题目选项完整
      let optionVerification = (option)=>{
        let a = true
        option.forEach(e => {
          if(!(e.label && e.value)){
            a = false
          }
        })
        return a
      }
      arr.forEach(e => {
        //简答题,填空题
        if(e.type == 3 || e.type == 47){
          if(!(e.title && e.answer)){
            boon = false
          }
        }
        //判断题
        if(e.type == 8){
          if(!(e.optionConfig.options.length == 2 && e.optionConfig.options.filter(e=>e.isAnswer).length == 1 && optionVerification(e.optionConfig.options) && e.title && e.answer)){
            boon = false
          }
        }
        //单选题
        if(e.type == 1){
          if(!(e.optionConfig.options.length >= 2 && e.optionConfig.options.filter(e=>e.isAnswer).length == 1 && optionVerification(e.optionConfig.options) && e.title && e.answer)){
            boon = false
          }
        }
        //多选题
        if(e.type == 2){
          if(!(e.optionConfig.options.length >= 2 && e.optionConfig.options.filter(e=>e.isAnswer).length >= 2 && optionVerification(e.optionConfig.options) && e.title && e.answer)){
            boon = false
          }
        }
      })
      return boon
    },
  },
  watch: {
    inputText: {
      handler(newText) {
        if(newText.trim() == ''){
          this.questionLoading = false;
          this.questionList = [];
          return
        }
        setTimeout(() => {
          this.generateQuestions();
        }, 1500);
      },
      immediate: true
    }
  },
}
</script>

<style lang='scss' scoped>

.import-word-page{
  .flex-btn-pageTop{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  .import-btn{
    
  }
  // input#file-upload-button{
  //     width: 100px;
  //     height: 36px;
  //     color: #fff;
  //     background-color: var(--theme_primary_color);
  //     border: 1px solid var(--theme_primary_color);
  //   }
}
.quiz-generator {
  display: flex;
  justify-content: space-between;

  .pub-area {
    width: 49%;
    max-height: 660px;
    min-height: 500px;
    box-sizing: border-box;
    border: 1px solid var(--theme_primary_color);
    // overflow-y: auto;
  }

  .custom-textarea {
    ::v-deep .el-textarea__inner {
      max-height: 660px !important;
      min-height: 660px !important;
      box-sizing: border-box;
      // border: 1px solid var(--theme_primary_color);
      // border-radius: none;
      /* 或者您想要的任何高度 */
      /* 若想限制最小或最大高度，可以用min-height或max-height */
    }
  }


  .left-area,
  .right-area {
    width: 100%;
    height: 100%;
    // border: 1px solid var(--theme_primary_color);
    overflow-y: auto;

    .topic-item {
      border-bottom: 1px dashed #bdbdbd;
      padding: 10px;
      position: relative;

      .shadow-box{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
      &>p {
        padding: 4px;

        &>span {
          color: rgb(203, 94, 94);
        }
      }
    }
  }
}
</style>

<style scoped>
.upload-wrapper {
  position: relative;
}


.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  overflow: hidden;
}

.el-button {
  padding: 12px 20px;
  border-radius: 4px;
  transition: all 0.3s;
}

.el-button:hover {
  background-color: #409EFF;
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64,158,255,.3);
}
.export-fill:hover{
  background-color: #fff;
  border: 1px solid transparent;
  box-shadow:unset;
}
</style>