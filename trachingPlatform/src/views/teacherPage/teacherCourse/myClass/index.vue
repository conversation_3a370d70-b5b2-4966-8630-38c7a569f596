<template>
    <div class="teacher-class">
        <div class="class-list-content">
            <div>
                <el-input placeholder="请搜索班级" v-model="className" clearable @change="searchClass()" suffix-icon="el-icon-search"></el-input>
            </div>
            <div class="class-list">
                <div :class="['class-item',tableConfig.searchParams.classId==item.id?'active':'']" v-for="item in bindClassList" @click="changeClass(item)" :key="item.id">
                <p>{{item.name}}</p>
                <p>{{item.studentCount}} 
                    <el-popover
                        placement="right"
                        trigger="hover"
                        popper-class="del-class-popover"
                        width="60px">
                            <el-button size="mini" type="text" @click="delClass(item)">解绑</el-button>
                            <i slot="reference" class="el-icon-more"></i> 
                        </el-popover>
                    </p>
                </div>
            </div>

            <el-button class="bind-class" @click="handleBindClass"><i class="iconfont icon-a-ziyuan6"></i>绑定班级</el-button>
        </div>
        <div class="student-table">
            <div class="class-tool-bar">
                <!-- <h5 class="page-label">班级管理</h5> -->
                <div>
                    <span class="count-num">共{{total}}名学生</span> <el-input clearable @blur="getStudentList" class="search-ipt" placeholder="请搜索学号、姓名" v-model="tableConfig.searchParams.searchWord" suffix-icon="el-icon-search"></el-input>
                    <!-- <el-button class="add-class"><i class="iconfont icon-xinjian"></i>创建班级</el-button>
                    <el-button class="import-class"><i class="iconfont icon-shujudaoru"></i>行政班导入</el-button>
                    <el-button class="export-class"><i class="iconfont icon-daochu"></i>导出</el-button> -->
                </div>
            </div>
            <table2 
            @selectionChange="selectionChange" 
            :notShowSearch="notShowSearch" 
            ref="tablePreview" 
            @selection-change="handleSelectionChange" 
            :selectable="selectable" 
            :data="tableConfig.tableData" 
            :columns="tableConfig.columns" 
            :queryFormConfig="queryConfig" 
            :total="tableConfig.total"
            :pagination="tableConfig.pagination" 
            :max-height="tableConfig.height" 
            :paginationLayout="tableConfig.paginationLayout" 
            :firstLoad="firstLoad" 
            :searchParams="tableConfig.searchParams" 
            @handleSearch="handleSearch">
            <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
            <template #operate>
                <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
                <template slot-scope="scope">
                    <el-button v-for="(item, index) in operateBtns" type="text"
                            v-show="isShowOperateBtn(scope, item)"
                            :class="item.class || ''"
                            @click="btnClick(scope.row, item)" :key="index"
                            :disabled="scope.row.dataLocked">
                    {{ item.name }}
                    </el-button>
                </template>
                </el-table-column>
            </template>
            </table2>
        </div>
        <baseDialog :noFooter="true" :showToScreen="false" :visible.sync="dialogVisible" width="500px" :title="'绑定班级'">
            <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
                <el-form-item label="所属学院"  prop="collegeId">
                <el-select :disabled="!!user.collegeId" style="width:100%;" v-model="form.collegeId" placeholder="请选择所属学院" @change="selectCollege">
                    <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="所属专业"  prop="majorId">
                <el-select style="width:100%;" v-model="form.majorId"  placeholder="请选择所属专业"  @change="selectMajor">
                    <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                </el-form-item>
                <el-form-item label="班级名称"  prop="classId">
                <el-select style="width:100%;" placeholder="请选择班级" v-model="form.classId">
                    <el-option v-for="item in classList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
                </el-form-item>
                <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
                <el-button type="default" @click="dialogVisible = false;">取消</el-button>
                <el-button type="primary" @click="save">保存</el-button>
                </el-form-item>
            </el-form>
        </baseDialog> 
    </div>
</template>
  
<script>
  export default {
    name: 'CourseInfo',
    data() {
        return {
            // 补全模板中使用的变量
            bindClassList:[],// 班级列表
            className: '',
            notShowSearch: false,
            selectable: true,
            tableConfig: {
                columns: [
                {
                    label: "姓名",
                    prop: "name",
                    width: 80,
                },
                {
                    label: "学号",
                    prop: "studentNo",
                },
                {
                    label: "联系方式",
                    prop: "phone",
                },
                // {
                //     label: "学校",
                //     prop: "collegeName",
                // },
                {
                    label: "院系",
                    prop: "collegeName", // 原 `phone` 可能是笔误，这里改为 `department`
                },
                {
                    label: "专业",
                    prop: "majorName", // 原 `textbook` 可能表意不准，改为 `major`
                },
                {
                    label: "年级",
                    prop: "classGrade", // 原 `textbookNum` 可能表意不准，改为 `class`
                },
                {
                    label: "加入时间",
                    prop: "createTime",
                }
                ], // 表格列
                tableData: [ ], // 表格数据
                pagination: true,
                total: 12,
                searchParams: {
                    pageIndex: 1,
                    pageSize: 12,
                    searchWord:'',
                    classId:0, // 班级id
                    collegeId:'', // 院校id
                    majorId:'', // 专业id
                },
               
                btnList: {
                detail: {
                    enable: true
                }
                },
                // 补全模板中使用的变量
                notShowSearch: false,
                selectable: true,
                pagination: true,
                paginationLayout: 'total, sizes, prev, pager, next, jumper',
            },
            queryConfig: {},
            firstLoad: true,
            loading: false,
            actionBtns: [],
            operateBtns: [
                // { name: '编辑',class:'default-btn' },
                // { name: '移交班级',class:'default-btn' },
                // { name: '删除',class:'del-btn' }
            ],
            operateWidth: 0,
            total:45,// 总人数
            dialogVisible: false, // 新增班级弹窗
            form: { // 新增班级表单数据
                id: '', // 班级id
                name: '', // 班级名称
                collegeId: '', // 所属学院id
                majorId: '', // 所属专业id
                grade: '', // 所属年级
                formTeacherUserId: '', // 班主任id
            },
            user:{ // 用户信息
                collegeId:'', // 院校id
            },
            collegeList: [], // 学院列表
            majorList: [], // 专业列表
            classList: [], // 年级列表
        };
    },
    components: {
      // 补全模板中使用的组件
      baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    },
    mounted() {
        // 可以在这里添加初始化逻辑
        this.getMyTeachClassList(); // 获取我的班级列表
        this.getCollegeList(); // 获取学院列表
    },
    methods: {
        searchClass(){
            this.getMyTeachClassList()
        },
        // 获取我的班级列表
        getMyTeachClassList(){
            let courseId = this.$route.query.courseId; // 课程id
            this.$api.GetMyTeachClassList({courseId,className:this.className}).then(res=>{ // 获取我的班级列表
                this.bindClassList = res.data; // 赋值班级列表
                if(this.bindClassList.length>0){ // 如果有班级
                    this.tableConfig.searchParams.classId = this.bindClassList[0].id; // 赋值班级id
                    this.getStudentList(); // 获取学生列表
                }else{ // 如果没有班级
                    this.tableConfig.searchParams.classId = 0; // 赋值班级id为空
                    this.tableConfig.tableData = []; // 赋值学生列表为空
                }
            })
        },
        getCollegeList(){ // 获取学院列表
            this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
                this.collegeList = res.data.items;
            })
        },
        selectCollege(val){ // 选择学院
            this.$api.GetMajorList({collegeId:val,pageIndex:1,pageSize:100}).then(res=>{
                this.majorList = res.data.items;
            })
        },
        selectMajor(val){ // 选择专业
            // res.data.items.filter(v=> v.enabled) :val,
            this.$api.GetClassList({majorId:val,classType:'Teach',pageIndex:1,pageSize:100}).then(res=>{
                this.classList = res.data.items;
            })
        },
        getStudentList(){ // 获取学生列表
            let params = {...this.tableConfig.searchParams }; // 搜索参数
            this.$api.GetStudentList(params).then(res=>{ // 获取学生列表
                this.tableConfig.tableData = res.data.items; // 赋值学生列表
                this.tableConfig.total = res.data.total; // 赋值总人数
                // 新增代码：更新学生总数
                this.total = res.data.total; 
            })
        },
        handleBindClass(){ // 绑定班级
            this.dialogVisible = true;
            // 获取个人信息
            this.$api.GetUserProfile({}).then(res=>{ // 获取个人信息
                if(res.data.college.id){
                    this.form.collegeId = res.data.college.id; // 赋值院校id
                    this.user.collegeId = res.data.college.id; // 赋值院校id
                    this.selectCollege(this.form.collegeId)
                }
            })
        },
        // 选择班级
        changeClass(item){
            this.tableConfig.searchParams.classId = item.id; // 赋值班级id
            this.tableConfig.searchParams.pageIndex = 1; // 赋值页码
            this.getStudentList(); // 获取学生列表
        },
        // 解绑班级
        delClass(item){
            let params = { 
                courseId:this.$route.query.courseId, // 课程id
                classId:item.id, // 班级id
                isAdd:false, // 解绑班级
             };
             this.$api.BindTeachClassCourse(params).then(res=>{ // 绑定课程
                this.$message.success('解绑成功'); // 提示绑定成功
                this.getMyTeachClassList(); // 获取我的班级列表
             })
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
        save() {
            let params = { 
                courseId:this.$route.query.courseId, // 课程id
                classId:this.form.classId, // 班级id
                isAdd:true, // 是否新增
             };
             this.$api.BindTeachClassCourse(params).then(res=>{ // 绑定课程
                this.$message.success('绑定成功'); // 提示绑定成功
                this.dialogVisible = false; // 关闭弹窗
                this.getMyTeachClassList(); // 获取我的班级列表
                this.form = { // 重置表单
                    id: '', // 班级id
                    name: '', // 班级名称
                    collegeId: '', // 所属学院id
                    majorId: '', // 所属专业id
                    grade: '', // 所属年级
                    formTeacherUserId: '', // 班主任id
                };
             })
        },
        // 补全模板中使用的方法
        selectionChange(selection) {
            console.log('Selection changed:', selection);
        },
        handleSelectionChange(selection) {
            console.log('Table selection changed:', selection);
        },
        handleSearch(params) {
            console.log('Search params:', params);
            // 可以在这里添加搜索逻辑
            this.tableConfig.searchParams.pageIndex = params?.pageIndex||1; // 更新搜索参数
            this.getStudentList(); // 重新获取学生列表
        },
        handlePlus(button) {
            console.log('Clicked plus button:', button);
            // 可以在这里添加新增逻辑
        },
        isShowOperateBtn(scope, item) {
            // 可以根据实际需求添加显示逻辑
            return true;
        },
        btnClick(row, item) {
            console.log('Clicked operation button:', row, item);
            // 可以在这里添加操作按钮点击逻辑
        }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .teacher-class {
    // padding: 0 20px;
    display: flex;
    height: calc(100% - 60px);
    justify-content: space-between;
    background: #fff;
    .class-list-content{
        padding: 20px 10px;
        width: 239px;
        box-sizing: border-box;
        border-right: 1px solid #E7E7E7;
        position: relative;
        padding-bottom:78px;
        .class-list{
            margin-top:10px ;
            .class-item{
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 38px;
                line-height: 38px;
                border-radius: 4px;
                padding: 0 10px;
                cursor: pointer; 
                &:hover{
                    background: #F1F7FF;
                }
                &.active{
                    background: #F1F7FF;
                }
                p{
                    color: #333; 
                    font-size: 14px;
                    font-weight: 400;
                    font-family: PingFangSC, PingFang SC;
                }
            }

            .el-icon-more{
                transform: rotate(90deg);
                &:hover{
                    color:#0070FC;
                }
            }
        }
        .bind-class{
            width: 220px;
            height: 38px;
            color:#333333;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #E7E7E7;
            position: absolute;
            bottom: 20px;
            &:hover{
                color:#0070FC;
                border-color: #0070FC;
            }
            .icon-a-ziyuan6{
                margin-right: 9px;
            }
        }

    }
    .student-table{
        width: calc(100% - 240px);
        padding: 0 20px;
    }
    ::v-deep .base-table{
        height: calc(100% - 140px)!important;
        .top{
            margin: 0;
        }
    } 
    .class-tool-bar{
        display: flex;
        justify-content: flex-end;
        align-items: center; 
        height: 78px;
        line-height: 78px;
        .count-num{
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #5C6075; 
            margin-right: 10px;
        }
        .page-label{
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            margin: 0;
            padding-left: 10px;
            position: relative;
            &::before{
                content: '';
                position: absolute;
                left: 0px;
                top: 32px;
                display: inline-block;
                width: 4px;
                height: 14px;
                background: #0070FC;
                border-radius: 2px;
            }
        }
      .search-ipt{
        width: 300px;
        margin-right: 10px;
      }

    //   .add-class{
    //     background-color: #07C392;
    //     color: #fff;
    //     border: none; 
    //     width: 120px;
    //     padding: 0;
    //     height: 38px;
    //   }
    //   .import-class{
    //     background-color: #fff;
    //     border: 1px solid #E7E7E7;
    //     color: #333333;
    //     width: 120px;
    //     padding: 0;
    //     height: 38px;
    //   }
    //  .export-class{
    //     background-color: #fff;
    //     border: 1px solid #E7E7E7;
    //     color: #333333;
    //     width: 80px;
    //     padding: 0;
    //     height: 38px;
    //   } 
     } 
  }
  </style>
  <style lang="scss">
    .del-class-popover{
        min-width: 60px;
        width: 60px;
        height: 40px;
        padding: 5px 10px;
    }
  </style>