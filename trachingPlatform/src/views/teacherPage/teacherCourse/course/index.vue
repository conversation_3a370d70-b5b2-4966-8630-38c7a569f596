<template>
  <div class="course-info">
    <el-tabs v-model="activeName" class="course-tab" @tab-click="handleClick">
        <el-tab-pane label="课程信息" name="first">
            <div style="height:100%;background:#fff;border:1px solid #fff;">
                <div class="course-info-content">
                    <div class="course-cover">
                        <img :src="courseInfo.coverImage|| require('../../../../assets/public/course-cover.png')" alt="">
                    </div>
                    <div class="course-info-item">
                        <h3 class="title">{{courseInfo.name}}</h3>
                        <p class="p1"><span class="label">学 时：</span> <span>{{courseInfo.hours}}</span> <span style="margin-left:40px;" class="label">专业层次：</span> <span>{{courseInfo.majorCengCi}}</span></p>
                        <p class="p2"><span class="label">所属专业：</span>{{courseInfo.majorCategory}}</p>
                        <p class="bind-class">
                            <span class="label">绑定班级：</span>
                            <span v-for="item in classList" :key="item.id" class="class-name"> {{item.name}} </span>
                        </p>
                    </div>
                </div>
                <div class="course-intro">
                    <p class="label">课程简介</p>
                    <el-input class="intro" type="textarea" readonly v-model="courseInfo.description" maxlength="500"
                    show-word-limit></el-input>
                </div>
            </div>

        </el-tab-pane>
        <el-tab-pane label="课程教材" name="second">
             <div class="top-search-bar">
                <el-input placeholder="请输入教材名称" v-model="jcName" class="input-with-select">
                <el-button slot="append" class="search-btn">搜索</el-button>
                </el-input>
                <!-- <el-button class="add-course-btn" @click="addCourse" type="primary"><i class="iconfont icon-xinjian"></i> 新增教材</el-button>
                <el-button class="del-course-btn" @click="delCourse" type="primary"><i class="el-icon-delete-solid"></i> 批量删除</el-button> -->
             </div>
             <div class="book-list">
                <!-- 添加全选复选框 -->
                <el-checkbox style="margin-bottom:10px;" v-model="allChecked" @change="handleAllCheck">全选</el-checkbox>
                <div class="book-item" v-for="item in bookList" :key="item.id">
                    <el-checkbox class="check-box" v-model="item.check" @change="handleSingleCheck"></el-checkbox>
                    <div class="book-cover">
                        <img :src="item.coverImage||require('../../../../assets/public/book-bg.png')" alt="">
                    </div>
                    <div class="book-info">
                        <p class="title">{{item.name}}</p>
                        <p class="author">
                            <span>主编：{{item.helperCreatorUsers.map(v => v.name).join(',')}}</span>
                            <span>出版社：{{item.publishingHouse}}</span>
                            <span>书号：{{item.publishedBookNumber}}</span>
                        </p>
                        <p class="desc">{{item.description}}</p>
                    </div>
                </div>
             </div>
        </el-tab-pane>
        <el-tab-pane label="课程评价" name="third">
            <div class="evaluation-content">
                <div class="evaluation-top">
                    <p class="label">用户评分</p>  
                    <span class="score">{{value}}.0</span>
                    <el-rate v-model="value" :colors="['#F11B1B','#F11B1B','#F11B1B']" text-color="#F11B1B"></el-rate>
                </div>
                <div class="evaluation-list">
                    <div class="evaluation-item" v-for="(item, index) in evaluationList" :key="index">
                        <div class="user-img">
                            <el-avatar :size="item.size" :src="item.circleUrl"></el-avatar>
                        </div>
                        <div class="user-info">
                            <p class="name"><span> {{ item.name }} </span><el-rate :colors="['#F11B1B','#F11B1B','#F11B1B']" v-model="item.value"></el-rate></p>
                            <p class="time">{{ item.time }}</p>
                            <p class="remak">{{ item.remark }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </el-tab-pane>
    </el-tabs>


    <!-- 新增教材弹窗 -->
  </div>
</template>

<script>
export default {
    name: 'CourseInfo',
    data() {
        const evaluationList = Array.from({ length: 5 }, (_, index) => ({
            id: index + 1,
            name: `用户${index + 1}`,
            size: 40,
            circleUrl: "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
            value: Math.floor(Math.random() * 5) + 1,
            time: `2025-05-${String(index + 1).padStart(2, '0')} 10:10:20`,
            remark: `课程内容丰富，有趣，第 ${index + 1} 条评价。`
        }));
        return {
            courseInfo:{},//课程信息
            classList:[],//班级列表
            activeName: 'first',
            jcName:'',
            // 添加全选控制变量
            allChecked: false ,
            bookList:[],
            value:5,
            evaluationList,
        } 
    },
    mounted() {
        this.getCourseInfo();
    },
    methods: {
        async getCourseInfo(){
            let params = {
                id:this.$route.query.courseId
            }
            let res =  await  this.$api.GetCourseDetail(params)
            this.courseInfo = res.data;
            if(res.data.teachClassIds.length == 0){
                this.classList = [{name:'暂无'}]
            }else{
                this.$api.GetClassList({classIds:res.data.teachClassIds,pageIndex:1,pageSize:100}).then(res=>{
                    this.classList = res.data.items;
                })
            }

        },
        handleClick(tab, event) {
            // console.log(tab, event);
            if(this.activeName == 'second'){
                let params = {
                    pageIndex:1,
                    pageSize:100,
                    textbookIds:this.courseInfo.textbooks.map(item=>item.id)
                }
                this.$api.GetTextbookList(params).then(res=>{
                    this.bookList = res.data.items;
                    console.log(this.bookList)
                })
            }
        },
        addCourse(){
            console.log('新增课程');
        },
        delCourse(){
            console.log('批量删除');
        },
        // 全选方法
        handleAllCheck(checked) {
            this.bookList.forEach(item => {
                item.check = checked;
            });
        },
        // 单个复选框变化时检查是否全选
        handleSingleCheck() {
            const allChecked = this.bookList.every(item => item.check);
            this.allChecked = allChecked;
        }
    }
}
</script>

<style lang="scss" scoped>
.course-info{
    height:100%;
    background:#fff;

    ::v-deep .el-tabs__content{
        height: calc(100% - 50px);
        overflow: auto;
    }
    .course-tab{
        height: 100%;
        ::v-deep.el-tabs--top .el-tabs__item{
            height: 50px;
            line-height: 50px;
            font-size: 14px;
            font-weight: 500;
        }
        
        ::v-deep.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
            padding-left: 20px;
        }
        .el-tabs__nav-wrap{
            &::after{
                height: 1px;
            }
        }

        .course-info-content{
            width: 900px;
            margin: 104px auto 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .course-cover{
                height: 160px;
                img{
                    width: 284px;
                    height: 100%;
                }
            }
            .course-info-item{
                height: 160px;
                margin: 20px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                // align-items: flex-start;
                .title{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    margin: 0;
                }
                .label{
                    color: #999;
                }
               .p1{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px; 
                    
               }
               .p2{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px; 
               }
              .bind-class{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                .class-name{
                    text-decoration: underline;
                    cursor: pointer;
                    margin-right: 6px;
                    &:hover{
                        color: #409EFF;
                    }
                }
               } 
            }
        }
        .course-intro{
            width: 900px;
            margin: 0 auto;
            .label{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 40px;
            }
            .intro{
                background: #FFFFFF;
                border-radius: 4px;
                // border: 1px solid #BBBBBB;
                .el-textarea__inner{
                    height: 140px;
                    // border: none;
                    
                }
            }
        }


        .top-search-bar{
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-top: 20px;
            .input-with-select{
                width: 460px;
                ::v-deep .el-input__inner{
                    height: 38px;
                }
            }
            .search-btn{
                width: 60px;
                height: 38px;
                line-height: 38px;
                background: #3274FE;
                border-radius: 0px 4px 4px 0px;
                padding: 0;
                color: #fff;
                &:hover{
                    background: #0086FC;
                }
            }
            .add-course-btn{
                position: absolute;
                width: 120px;
                height: 40px;
                background: #07C392;
                border-radius: 4px;
                border: none;
                right: 130px;
                color: #fff;
            }
            .del-course-btn{
                position: absolute;
                width: 120px;
                height: 40px;
                background: #fff;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                right: 0;
                color: #333;
            }
        }
        .book-list{
            margin-top: 20px;
            .book-item{
                padding: 10px 30px 20px 10px;
                margin-bottom: 20px;
                display: flex;
                background-color: #fff;
                .check-box{
                    width: 30px;
                }
                .book-cover{
                    width: 120px;
                    height: 160px;
                    margin-right: 20px;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                .book-info{
                    width: calc(100% - 170px);
                    height: 160px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    .title{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 16px;
                        color: #333333;
                    }
                    .author{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #5C6075;
                        line-height: 22px;
                        span{
                            margin-right: 32px;
                        }
                   }
                   .desc{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #333333;
                        line-height: 20px;
                   }
                }
            } 
        }
    }

    .evaluation-content{
        width: 1000px;
        margin: 10px auto;
        background: #FFF;
        .evaluation-top{
            height: 60px;
            line-height: 60px;
            padding-left: 30px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border-bottom: 1px solid #F2F3F5;

            ::v-deep .el-rate__icon{
                font-size: 24px;
            }
            .label{
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #333333;
            }
            .score{
                margin-left: 20px;
                margin-right: 20px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 500;
                font-size: 30px;
                color: #F11B1B;
            }
        }
       .evaluation-list{
            margin-top: 20px;
            padding: 0 30px;
           .evaluation-item{
                display: flex;
                justify-content: flex-start;
                align-items: center; 
                min-height: 104px;
                position: relative;
                // 确保父元素没有影响 sticky 的 overflow 属性
                overflow: visible; 
                padding-bottom: 20px;
                border-bottom: 1px solid #F2F3F5;
                margin-bottom: 20px;
                .user-img{
                    align-self: baseline;
               }
              .user-info{
                margin-left: 10px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                align-items: flex-start;
                .name{
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    margin-bottom: 10px;
                    span{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 14px;
                        color: #333;
                        margin-right: 10px;
                    }
                    ::v-deep .el-rate__icon{
                        font-size: 20px;
                    }
                }
               .time{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #999999;
                    margin-bottom: 16px;
                }
              .remak{
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
              }
           } 
       }
        }
    }
}
</style>
<style lang="scss">
.course-info{
    .el-tabs__nav-wrap{
        &::after{
            height: 1px;
        }
    }
    .el-tabs__header{
        margin: 0;
    }
    .el-tabs__content{
        background-color: #F6F8FA;
    }
    .el-tab-pane{
        height: calc(100% - 50px);
    }
}
.intro{
    background: #FFFFFF;
    border-radius: 4px;
    // border: 1px solid #BBBBBB;
        .el-textarea__inner{
            height: 140px;
            // border: none;
            resize: none;
        }
    }
</style>