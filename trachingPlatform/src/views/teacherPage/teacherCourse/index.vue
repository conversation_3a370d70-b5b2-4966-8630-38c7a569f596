<template>
  <div class="teacher-course-page">
    <div class="top-bar">
      <el-input placeholder="请搜索课程名称" clearable v-model="paramSearch.searchWord" class="input-with-select">
      <el-button slot="append" @click="searchCourse()" class="search-btn">搜索</el-button>
      </el-input>
      <el-button v-if="false" class="add-course-btn" @click="addCourse" type="primary"><i class="iconfont icon-xinjian"></i> 新增课程</el-button>
    </div>
    <div class="course-list">
      <div class="course-item" v-for="item in courseList" :key="item.id" >
          <div class="course-info-left">
            <img @click="courseDetail(item)" class="course-cover" :src="`${item.coverImage}?x-oss-process=image/resize,p_25`||require('../../../assets/public/course-cover.png')" alt="">
            <div class="course-info">
              <h3 class="course-title" @click="courseDetail(item)">{{item.name}}</h3>
              <p class="course-label">
                <span class="label-one"><i class="iconfont icon-jiaoshi"></i> 主持教师:{{(item.subjectTeachers.map(v=>v.name)).join(',')}}</span>
                <span class="label-two"><i class="iconfont icon-zhuanye"></i> 所属专业:{{item.majorCategory}}</span>
                </p>
              <div class="course-label-info">
                <div class="label" style="width:60px;">
                  <p>选班数</p>
                  <p>{{item.teachClassIds.length}}</p>
                </div>
                <div class="label" style="width:60px;">
                  <p>学生数</p>
                  <p>{{item.teachClassStudentCount}}</p>
                </div>
                <div class="label" style="width:60px;">
                  <p>学时</p>
                  <p>{{item.hours}}</p>
                </div>
                <div class="label" style="width:140px;">
                  <p>创建时间</p>
                  <p>{{formatDate(item.createTime)}}</p>
                </div>
              </div>
            </div>
          </div>
          <div  class="course-info-right">
            <!-- <el-button type="text" @click="editCourse(item)" class="btn edit">编辑</el-button> -->
            <el-button type="text" @click="setTop(item,false)" v-if="item.orderIndex!=0" class="btn cancle-top">取消置顶</el-button>
            <el-button type="text" @click="setTop(item,true)" v-if="item.orderIndex==0" class="btn set-top">置顶</el-button>
            <!-- <el-button type="text" @click="delCourse(item)" class="btn del">删除</el-button> -->
            <el-button class="btn entry-course" @click="courseDetail(item)">进入课程</el-button>
          </div>
      </div>
      <empty v-if="courseList.length == 0"  msg="暂无数据" size="middle" />
    </div>
    <div class="page-content">
      <zdPagination class="classes-page" :paginationConfig="paginationConfig" @handleSizeChange="handleSizeChange"
      @handleCurrentChange="handleCurrentChange"></zdPagination>
    </div>
  </div>

</template>

<script>
import ZdPagination from "@/components/zd-pagination/index.vue";
// import formatDate from "@/utils/format-date.js";
export default {
    data() {
        return {
          courseName:'',  // 课程名称
          paginationConfig: {
            total: 0,
            currentPage: 1,
            pageSize:3,
            layout: 'total, prev, pager, next, jumper',
          },
          courseList:[], // 课程列表
          paramSearch: {
            pageIndex: 1,
            pageSize: 3,
            searchWord:'',// 关键字
          },
        }; 
    },
    components: { 
      ZdPagination,
      empty:()=>import("@/components/base/empty.vue"),
    },
    mounted() {
      this.getCourseList();
    },
    methods:{
      formatDate(isoDate) {
        if (!isoDate) {
          return '';
        }
        const date = new Date(isoDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      getCourseList(){
        this.$api.MyCourses(this.paramSearch).then(res=>{
          console.log(res);
          this.courseList = res.data.items;
          this.paginationConfig.total = res.data.total;
            
        })
      },
      // 搜索
      searchCourse(){
        this.paramSearch.pageIndex = 1; // 重置页码
        this.getCourseList()
      },
      courseDetail(item){
        this.$router.push({
          path: '/teacher/teacherCourseDetail',
          query: {
            courseId: item.id, // 将课程ID作为参数传递给详情页面
          }
        })
      },
      //每页数
      handleSizeChange(val) {
        this.paramSearch.pageSize = val;
        this.getCourseList();
      },
      //分页数
      handleCurrentChange(val) {
        this.paramSearch.pageIndex = val;
        this.getCourseList();
      },
      // 删除课程
      delCourse(item){

      },
      // 编辑课程
      editCourse(item){
        
      },
      // 置顶
      setTop(item,type){
        this.$api.SetMyCourseToTop({courseId:item.id,isTop:type}).then(res=>{
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.paramSearch.pageIndex = 1; // 重置页码
          this.getCourseList()
        })
      }
    }
    
}
</script>

<style lang="scss" scoped>
.teacher-course-page{
  background-color: #F6F8FA;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  .top-bar{
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-top: 40px;
    .input-with-select{
      width: 460px;
      ::v-deep .el-input__inner{
        height: 38px;
      }
    }
    .search-btn{
      width: 60px;
      height: 38px;
      line-height: 38px;
      background: #0070FC;
      border-radius: 0px 4px 4px 0px;
      padding: 0;
      color: #fff;
      &:hover{
        background: #0086FC;
      }
    }
    .add-course-btn{
      position: absolute;
      width: 120px;
      height: 40px;
      background: #07C392;
      border-radius: 4px;
      border: none;
      right: 0;
      color: #fff;
    }
  }

  .course-list{
    background-color: #F6F8FA;
    height: calc(100% - 186px);
    overflow-y: auto;
    .course-item{
      padding: 15px 20px;
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background:url("../../../assets/public/course-item-bg.png") no-repeat top left/cover;
      background-color: #fff;
      // cursor:pointer;
      &:hover{
        .course-title{
          color: #0070FC;
        }
      }
    }
    .course-info-left{
        display: flex;
        align-items: center;
        .course-cover{
          width: 285px;
          height: 160px;
          border-radius: 4px;
          margin-right: 20px;
          cursor: pointer;
        }
        .course-info{
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          height: 160px;
          width: 600px;
        }
        .course-title{
          color: #333;
          margin: 0;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 15px;
          cursor: pointer;
        }
        .course-label{
          font-size: 13px;
          .label-one{
            display: inline-block;
            padding: 4px 10px;
            height: 26px;
            color:#5583F2;
            background: #EDF2FF;
            border-radius: 4px;
            margin-right: 10px;
          }
          .label-two{
            display: inline-block;
            padding: 4px 10px;
            height: 26px;
            color:#07C392;
            background: #E5FCF5;
            border-radius: 4px;
          }
        }
        .course-label-info{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-top: 10px;
        }
        .label{
          color:#5C6075;
          width:100px;
          margin-right: 30px;
          p{
            font-size: 13px;
            line-height: 18px;
          }
        }
    }
    .course-info-right{
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn{
        height: 38px
      }
      .edit{}
      .cancle-top{}

      .set-top{}

      .del{
        color: #F11B1B;
      }
      .entry-course{
        color:#0070FC;
        background-color: #fff;
        border: 1px solid #0070FC;
        &:hover{
          background-color: #0070FC;
          color:#fff;
        }
        width: 120px;
      }
    }
  }
  .page-content{
    margin-top: 20px;
    text-align: right;
    .zd-pagination{
      justify-content: flex-end!important;
    }
  }
}
</style>