<template>
  <div class="teacher-Ai-page">
    <div class="top">
      <span class="back-icon" @click="goPath()">
        <i class="iconfont icon-fanhui"></i> 
      </span>
      <el-breadcrumb>
          <el-breadcrumb-item :to="{path: type==1?'/student':'/teacher/teacherCourse'}">我的课程</el-breadcrumb-item>
          <el-breadcrumb-item> AI 助教</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="Ai-content">
        <div class="ai-tip-box">
          <img src="@/assets/public/ai-icon.png" alt="">
          <div class="tip-label">
            <h4>老师，您好！我是您的课程AI小助手</h4>
            <p>我可以帮您做这些事情  <span>换一换 <i class="iconfont icon-a-ziyuan16"></i></span></p>
          </div>
        </div>
        <div class="ai-default-tip">
          <el-card class="box-card card-1">
            <div slot="header" class="clearfix1">
              <p>教师常问  <span>为您推荐课程相关的问题</span></p>
            </div>
            <div v-for="o in 4" :key="o" class="text card-item">
              <p class="item-tip">{{'如果学习应付股利这个知识点 ' + o }}</p>
            </div>
          </el-card>

          <el-card class="box-card card-2">
            <div slot="header" class="clearfix2">
              <p>效率工具  <span>快来试试您可能会常用的AI工具吧</span></p>
            </div>
            <div v-for="o in 2" :key="o" class="text card-item">
              <span class="item-icon"> <i class="iconfont icon-ai-assistant-fill"></i></span>
              <div class="right-content">
                <p class="up-tip">AI答疑</p>
                <p class="sub-tip">根据关键词答疑解惑…</p>
              </div>
            </div>
          </el-card>
        </div>


        <div class="chat-box">
          <el-input class="chat-input" type="textarea" placeholder="试着输入您想了解的问题吧。输入shift+enter 换行" v-model="inputValue" @keyup.enter="handleSend"></el-input>
          <div class="chat-btn-box">
            <el-button :class="['deep-think-btn', { 'thinking': isDeepThinking }]" @click="handleDeepThink">
              <i class="iconfont icon-sikaodian"></i>&nbsp;深度思考
            </el-button>
            <el-button class="send-btn "><i class="iconfont icon-send-s"></i></el-button>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
export default {
    data() {
        return {
          isDeepThinking: false
        }
    },
    mounted(){

    },
    methods: {
      handleDeepThink() {
        this.isDeepThinking = !this.isDeepThinking;
        // 这里可以添加深度思考的相关逻辑
      }
    }
}
</script>

<style lang="scss" scoped>
.teacher-Ai-page{
    height: 100%;
    background-color: #F5F7FA;
    .top{
      height: 50px;
      background: #FFFFFF;
      display: flex;
      padding: 0 30px;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      .back-icon{
          width: 20px;
          height: 20px;
          background: #BBBBBB;
          border-radius: 4px;
          color: #fff;
          line-height: 20px;
          text-align: center;
          display: inline-block;
          margin-right: 20px;
          cursor: pointer;
          .iconfont{
              font-size: 12px;
              line-height: 20px;
          }
      }
      ::v-deep .el-breadcrumb__inner a, 
      ::v-deep.el-breadcrumb__inner.is-link{
          color: #bbb;
      }
      
      ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner{
          color: #0070FC;
      }
    }

    .Ai-content{
     border-top: 1px solid transparent;
      height: calc(100% - 50px);
      background: url('../../../assets/public/AI-bg.png') no-repeat center/cover;
    }

    .ai-tip-box{
      width: 800px;
      margin: 40px auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .tip-label{
        margin-left:6px;
        height: 70px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        p,
        h4{
          margin: 0;
        }
        h4{
          font-size: 18px;
          color: #333;
        }
        p{
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 13px;
          color: #666666;
          line-height: 18px;
          text-align: justify;
          font-style: normal;
          margin-top: 8px;
          span{
            margin-left: 14px;
            color: #0070FC;
          }
        }
      }
    }

    .ai-default-tip{
      width: 800px;
      margin: 0 auto;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      
      ::v-deep .el-card__header{
        padding: 13px 2px;
        border: none;
      }

      ::v-deep .el-card__body{
        height: 234px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 4px;
        padding:10px;
      }
     
      .card-1{
        width: 430px;
        background-color: #B9E9FE;
        .clearfix1{
          p{
            color: #023247;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .item-tip{
          height: 53px;
          line-height: 53px;
          border-bottom: 1px solid #D3EAF4;
          font-size: 14px;
          position: relative;
          padding-left: 26px;
          &:before{
            content: '';
            position: absolute;
            left: 10px;
            top: 24px;
            width: 5px;
            height: 5px;
            border-radius: 5px;
            background: #666666;
          }
        }
       
      }
      .card-2{
        width: 350px;
        background-color: #E5DBFE;
        .clearfix2{
          p{
            color: #4720A8;
            font-size: 15px;
            span{
              color: #666666;
              font-size: 13px;
            }
          }
        }

        .card-item{
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height:45px;
          border-bottom: 1px solid #ECE5FF;

          .item-icon{
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #D4E0FF;
            color: #2B66FF;
            text-align: center;
            line-height: 36px;
            font-size: 20px;
            margin-right: 10px;
          }
          .right-content{
            display:flex;
            justify-content: space-around;
            flex-direction: column;
            align-items: flex-start;
          }
          .up-tip{
            height: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            line-height: 20px;
            text-align: justify;
            font-style: normal;
          }
          .sub-tip{
            height: 18px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #666666;
            line-height: 18px;
            text-align: justify;
            font-style: normal;
          }
        }
        

      }
      .box-card{
        height: 300px;
        border-radius: 4px;
        padding: 10px;
        box-sizing: border-box;
        margin-right: 2%;
        &:last-child{
          margin-right: 0;
        }
      }
    }

    .chat-box{
      width: 800px;
      height: 120px;
      background: #FFFFFF;
      box-shadow: 1px 1px 10px 0px rgba(153,153,153,0.1);
      border-radius: 10px;
      margin: 110px auto 20px;
      padding: 15px 15px 12px 15px;
      box-sizing: border-box;
      .chat-input{
        ::v-deep .el-textarea__inner{
          height: 64px;
          border: none;
          resize: none;
        }
      }
      .chat-btn-box{
        display: flex;
        justify-content: space-between;
        align-items: center;

        .deep-think-btn{
          width: 100px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 8px;
          color:#333;
          padding:0;
          transition: all 0.3s ease;

        }
        .deep-think-btn.thinking {
          background: #0070FC;
          color: #fff;
        }
        .send-btn{
          width: 36px;
          height: 36px;
          background: linear-gradient( 90deg, #0070FC 0%, #E38EFC 100%);
          border-radius: 18px;
          color:#fff;
          padding:0;
        }
      }
    }
}
</style>