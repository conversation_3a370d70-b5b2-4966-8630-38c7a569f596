<template>
  <div class="teacher-class-page">
    <div class="class-aside">
      <div>
        <el-input placeholder="请搜索班级" v-model="className" clearable @change="searchClass()" suffix-icon="el-icon-search"></el-input>
        <!-- <el-button type="text" @click="dialogVisible = true;"><i class="iconfont icon-xinjian"></i> 添加班级</el-button> -->
      </div>
      <div class="class-list">
        <div :class="['class-item',tableConfig.searchParams.classId==item.id?'active':'']" v-for="item in bindClassList" @click="changeClass(item)" :key="item.id">
          <p>{{item.name}}</p>
          <p>{{item.studentCount}}</p>
        </div>
      </div>
    </div>
    <div class="class-right">
      <div class="class-tool-bar">
        <div>
          <el-input @blur="searchStudent()" @change="searchStudent()" clearable class="search-ipt" placeholder="请搜索姓名" v-model="tableConfig.searchParams.searchWord" suffix-icon="el-icon-search"></el-input>
          <span class="count-num">共{{total}}名学生</span>
        </div>
        <div>
          <!-- <el-button class="add-student"><i class="iconfont icon-xinjian"></i>添加学生</el-button>
          <el-button class="import-student"><i class="iconfont icon-shujudaoru"></i>EXCEL批量导入</el-button> -->
          <el-button class="export-student"><i class="iconfont icon-daochu"></i>导出学生</el-button>
        </div>
      </div>
      <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="queryConfig" 
      :total="tableConfig.total"
      :pagination="pagination" 
      :max-height="height" 
      :paginationLayout="paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="item.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
      </table2>
    </div>


    <baseDialog :noFooter="true" :showToScreen="false" :visible.sync="dialogVisible" width="500px" :title="!form.id?'新增班级':'更新班级'">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-form-item label="班级名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入班级名称" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="所属学院"  prop="collegeId">
          <el-select style="width:100%;" v-model="form.collegeId" placeholder="请选择所属学院" @change="selectCollege">
            <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属专业"  prop="majorId">
          <el-select style="width:100%;" v-model="form.majorId"  placeholder="请选择所属专业">
            <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班级名称"  prop="grade">
          <el-select style="width:100%;" placeholder="请选择班级" v-model="form.grade">
            <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="班主任" prop="formTeacherUserId">
          <el-select style="width:100%;"  placeholder="请选择班主任" v-model="form.formTeacherUserId">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
  </div>

</template>

<script>
export default {
    data() {
        return {
          className:'',// 班级名称
          bindClassList:[ ],// 班级列表
          total:0, // 学生总数
          tableConfig: {
            columns: [
              {
                    label: "姓名",
                    prop: "name",
                    width: 80,
                },
                {
                    label: "学号",
                    prop: "studentNo",
                },
                {
                    label: "联系方式",
                    prop: "phone",
                },
                // {
                //     label: "学校",
                //     prop: "collegeName",
                // },
                {
                    label: "院系",
                    prop: "collegeName", // 原 `phone` 可能是笔误，这里改为 `department`
                },
                {
                    label: "专业",
                    prop: "majorName", // 原 `textbook` 可能表意不准，改为 `major`
                },
                {
                    label: "年级",
                    prop: "classGrade", // 原 `textbookNum` 可能表意不准，改为 `class`
                },
                {
                    label: "加入时间",
                    prop: "createTime",
                }
            ], // 表格列
            tableData: [ ], // 表格数据
            pagination: true,
            total: 12,
            searchParams: {
              pageIndex: 1,
              pageSize: 12,
              classId:0, // 班级id
              majorId:'', // 专业id
              collegeId:'', // 院校id
              searchWord:'', // 学生姓名
            },
            btnList: {
              detail: {
                enable: true
              }
            },
            // 补全模板中使用的变量
            notShowSearch: false,
            selectable: true,
            pagination: true,
            paginationLayout: 'total, sizes, prev, pager, next, jumper',
          },
          // 补全模板中使用的变量
          queryConfig: {},
          total: 0,
          firstLoad: true,
          loading: false,
          actionBtns: [
            // { id: 1, name: '新增' }
          ],
          operateBtns: [
            { name: '重置密码',class:'default-btn',dataLocked:true },
            { name: '移除',class:'del-btn',dataLocked:true },
            { name: '调班',class:'default-btn',dataLocked:true }
          ],
          operateWidth: 180,
          dialogVisible: false, // 新增班级弹窗
          form: { // 新增班级表单数据
            id: '', // 班级id
            name: '', // 班级名称
            collegeId: '', // 所属学院id
            majorId: '', // 所属专业id
            grade: '', // 所属年级
            formTeacherUserId: '', // 班主任id
          },
        }; 
    },
    components: {
      // 补全模板中使用的组件
      baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    },
    mounted(){
      // 可以在这里添加初始化逻辑
      this.getMyTeachClassList(); // 获取我的班级列表
    },
    methods: {
      // 获取我的班级列表
      getMyTeachClassList(){
            this.$api.GetMyTeachClassList({className:this.className}).then(res=>{ // 获取我的班级列表
                this.bindClassList = res.data; // 赋值班级列表
                if(this.bindClassList.length>0){ // 如果有班级
                    this.tableConfig.searchParams.classId = this.bindClassList[0].id; // 赋值班级id
                    this.initTableData(); // 获取学生列表
                }else{ // 如果没有班级
                    this.tableConfig.searchParams.classId = 0; // 赋值班级id为空
                    this.tableConfig.tableData = []; // 赋值学生列表为空
                }
            })
        },
      // 获取学生列表
      initTableData(){
        let params = {...this.tableConfig.searchParams };
        this.$api.GetStudentList(params).then(res=>{
          this.tableConfig.tableData = res.data.items; // 赋值学生列表
          this.tableConfig.total = res.data.total; // 赋值总人数
          // 新增代码：更新学生总数
          this.total = res.data.total; 
        }) 
      },
      // 选择班级
      changeClass(item){
          this.tableConfig.searchParams.classId = item.id; // 赋值班级id
          this.tableConfig.searchParams.pageIndex = 1; // 赋值页码
          this.initTableData(); // 获取学生列表
      },
      searchStudent(){ // 搜索学生
        this.tableConfig.searchParams.pageIndex = 1; // 重置页码
        this.initTableData(); // 获取学生列表
      },
      searchClass(){ // 搜索班级
        this.getMyTeachClassList(); // 获取我的班级列表

      },
      // 新增班级
      save(){

      },
      // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      // 这里可以添加搜索逻辑
      console.log('handle search:', params);
      this.tableConfig.searchParams.pageIndex = params?.pageIndex||1; // 更新搜索参数
      this.initTableData(); // 重新获取学生列表
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '重置密码':
          break;
        case '移除':
          break;
        case '调班':
          break; 
      }
    },
    }
}
</script>

<style lang="scss" scoped>
.teacher-class-page{
  display: flex;
  height: calc(100% - 40px);
  margin: 20px 20px 0 20px;
  box-sizing: border-box;
  background-color: #F6F8FA;
  .class-aside{
    width: 239px;
    height: 100%;
    padding:20px 10px;
    border-right: 1px solid #E7E7E7;
    background-color: #fff;

    .class-list{
      margin-top: 20px;
      .class-item{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 38px;
        line-height: 38px;
        border-radius: 4px;
        padding: 0 10px;
        cursor: pointer; 
        &:hover{
          background: #F1F7FF;
        }
        &.active{
          background: #F1F7FF;
        }
        p{
          color: #333; 
          font-size: 14px;
          font-weight: 400;
          font-family: PingFangSC, PingFang SC;
        }
      }
    }
  }
  .class-right{
    flex: 1;
    width: calc(100% - 240px);
    box-sizing: border-box;
    padding: 20px;
    height: 100%;
    background-color: #fff;

    .class-tool-bar{
      display: flex;
      justify-content: space-between;
      align-items: center; 

      .search-ipt{
        width: 300px;
        margin-right: 10px;
      }
      .count-num{
        font-size: 14px;
        font-weight: 400;
        font-family: PingFangSC, PingFang SC;
        color: #5C6075;
      }

      .add-student{
        background-color: #07C392;
        color: #fff;
        border: none; 
        width: 120px;
        padding: 0;
        height: 38px;
      }
      .import-student{
        background-color: #fff;
        border: 1px solid #E7E7E7;
        color: #333333;
        width: 150px;
        padding: 0;
        height: 38px;
      }
     .export-student{
        background-color: #fff;
        border: 1px solid #E7E7E7;
        color: #333333;
        width: 120px;
        padding: 0;
        height: 38px;
        &:hover{
            background: #E7E7E7;
        }
      } 
     }
  }
}
</style>