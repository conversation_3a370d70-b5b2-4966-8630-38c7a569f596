<template>
  <transition name="el-zoom-in-center">
    <div class="layout-box">
      <teacherHeader />
      <div class="layout-main">
        <teacherAside />
        <div class="layout-content">
          <!-- <tabView /> -->
          <div style="background:#F6F8FA;height:100%;">
            <router-view></router-view>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "schoolLayout",
  components: {
    teacherHeader: () => import('./components/header.vue'), // 导入组件
    teacherAside: () => import('./components/aside.vue'), // 导入组件
    tabView: () => import('./components/tabView.vue'), // 导入组件
  },
  data() {
    return {
      msg: 'Welcome'
    }; 
  }
};
</script>

<style lang="scss" scoped>
.el-zoom-in-center-enter-active,
.el-zoom-in-center-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

.el-zoom-in-center-enter,
.el-zoom-in-center-leave-to {
  opacity: 0;
  transform: scaleX(0);
}

.layout-box {
  width: 100%;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.layout-main {
  flex: 1;
  display: flex;
  min-height: 0;
  height: 100%;
  flex-direction: row;
}

.schoolAside {
  width: 200px;
  height: 100%;
}

.layout-content {
  flex: 1;
  padding: 0px;
  overflow-y: auto;
  // padding:20px;
  background: #F6F8FA;
  min-height: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>