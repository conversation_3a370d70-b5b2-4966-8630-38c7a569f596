<template>
  <div class="chapter-manage-page">
    <!-- 顶部面包屑 -->
    <div class="breadcrumb-bar">
      <span class="back-btn" @click="$router.go(-1)"
        ><i class="el-icon-arrow-left"></i
      ></span>
      <span class="breadcrumb-path">
        <template v-for="(item, idx) in breadcrumbList[activeTab]">
          <span
            v-if="item.to && idx !== breadcrumbList[activeTab].length - 1"
            class="breadcrumb-link"
            @click="handleBreadcrumbClick(item)"
            >{{ item.label }}</span
          >
          <span v-else>{{ item.label }}</span>
          <span v-if="idx !== breadcrumbList[activeTab].length - 1"> &nbsp;&nbsp;/&nbsp;&nbsp;</span>
        </template>
      </span>
    </div>
    <div class="main-content">
      <!-- 左侧教材列表 -->
      <div class="side-menu">
        <div
          v-for="item in textbookList"
          :key="item.id"
          :class="['menu-item', { active: item.id === currentTextbookId }]"
          @click="handleTextbookChange(item)"
        >
          <i :class="getTextbookIcon(item)"></i>
          {{ item.name }}
        </div>
      </div>
      <!-- 右侧章节树 -->
      <div v-loading="loading" class="chapter-tree-area lanhu-tree-area">
        <div v-if="chapterList.length === 0" class="empty-data lanhu-empty-data">
          <p class="m-b-10">暂无章节数据</p>
          <el-button type="primary" @click="openCreateDrawer">创建章节</el-button>
        </div>
        <div v-else class="chapter-tree lanhu-chapter-tree">
          <!-- 递归组件渲染章节树 -->
          <TreeNode
            v-for="(item,index) in chapterList"
            :key="item.id"
            :data="item"
            :level="1"
            :index="index" 
            @submitApprove="handleSubmitApprove"
            @delete="handleDelete"
            @coop="openCoopDrawer"
            @edit="openEdit"
          />
        </div>
      </div>
    </div>

    <!-- 协作管理抽屉 -->
    <el-drawer
      :visible.sync="coopDrawerVisible"
      :with-header="false"
      size="520px"
      :before-close="closeCoopDrawer"
    >
      <div class="coop-drawer">
        <div class="drawer-header">
          <span class="drawer-title">协作管理</span>
          <i class="el-icon-close close-icon" @click="closeCoopDrawer"></i>
        </div>

        <div class="drawer-content">
          <!-- 章节名称 -->
          <div class="chapter-info">
            <div class="chapter-name">
              {{ currentChapter ? currentChapter.title : "" }}
            </div>
            <div class="add-member" @click="handleAddMember">
              <span class="add-icon">+</span>
              <span class="add-text">添加成员</span>
            </div>
          </div>

          <!-- 权限区域 -->
          <div class="permission-area" v-loading="editorLoading">
            <div class="permission-title">教材权限</div>

            <!-- 成员列表 -->
            <div class="member-list">
              <!-- 所有者 -->
              <!-- <div class="member-item">
                <div class="member-avatar">
                  <img
                    :src="ownerMember.profilePhoto || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
                    alt="avatar"
                  />
                </div>
                <div class="member-info">
                  <div class="member-name">
                    {{ ownerMember.name }}
                  </div>
                  <div class="member-role">所有者</div>
                </div>
              </div> -->
              <!-- 已选择的协作者 -->
              <div
                v-for="editor in editorsList.selected"
                :key="editor.id"
                class="member-item"
              >
                <div class="member-avatar">
                  <img
                    :src="editor.profilePhoto || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"
                    alt="avatar"
                  />
                </div>
                <div class="member-info">
                  <div class="member-name">
                    {{ editor.name }}
                    <span v-if="editor.isMe" class="member-tag">(我)</span>
                  </div>
                  <div v-if="editor.isTextbookOwner" class="member-role">所有者</div>
                  <div v-else class="member-actions">
                    <el-dropdown @command="handleEditorAction($event, editor)">
                      <span class="el-dropdown-link">
                        可编辑<i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="edit">可编辑</el-dropdown-item>
                        <el-dropdown-item command="remove">移除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 添加成员弹窗 -->
    <el-dialog
      :visible.sync="addMemberVisible"
      :before-close="closeAddMemberDialog"
      title="选择协作人"
      width="780px"
      class="coop-member-dialog"
    >
      <div class="dialog-content">
        <div class="coop-title-area">
          <div class="coop-title">
            协作章节:
            <span class="section-name blue-text">{{
              currentChapter ? currentChapter.title : ""
            }}</span>
          </div>
          <div class="coop-title">
            以下成员将获得本章节<span class="blue-text">可编辑</span>权限
          </div>
        </div>

        <div class="transfer-container" v-loading="editorLoading">
          <custom-transfer
            v-model="selectedTransferIds"
            :data="transferData"
            :titles="['未选择', '已选择']"
            @change="handleTransferChange"
            :filterable="true"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeAddMemberDialog">取消</el-button>
        <el-button type="primary" @click="confirmAddMembers">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import textbookSection from "@/api/module/textbookSection.js";
import { mapGetters } from "vuex";
import TreeNode from "./TreeNode.vue";
import CustomTransfer from "@/components/CustomTransfer/index.vue";

// 教材列表接口
const getTextbookListApi = function ($api, params) {
  return $api.GetMyTextbookList(params);
};

export default {
  name: "ChapterManage",
  components: {
    TreeNode,
    CustomTransfer,
  },
  data() {
    return {
      textbookList: [], // 教材列表
      currentTextbookId: null, // 当前选中教材id
      chapterList: [], // 章节树
      loading: false,
      // 协作管理相关
      coopDrawerVisible: false, // 协作抽屉是否可见
      currentChapter: null, // 当前操作的章节
      memberRole: "member", // 成员角色
      // 协作者列表
      editorsList: {
        unselected: [], // 未选择的协作者
        selected: [], // 已选择的协作者
      },
      editorLoading: false, // 协作者加载状态
      // 添加成员弹窗
      addMemberVisible: false,
      selectedTransferIds: [], // 新选择的成员ID列表
      transferData: [], // 转移数据
      breadcrumbList: {
        my:[
          { label: "我的教材", to: { name: "TeacherTextbookChapterManage" } },
          { label: "我创建的", to: { name: "TeacherTextbookMine" },path:'/teacher/teacherTextBook?type=1' },
          { label: "章节管理" },
        ],
        other:[
          { label: "我的教材", to: { name: "TeacherTextbookChapterManage" } },
          { label: "我协作的", to: { name: "TeacherTextbookOther" },path:'/teacher/teacherTextBook?type=2' },
          { label: "章节管理" },
        ]
      },
      activeTab: 'my',
      createByUserName: '', // 新增字段
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ownerMember() {
      return {
        id: 'owner',
        name: this.createByUserName,
        profilePhoto: '', // 默认头像
        isTextbookOwner: true
      };
    }
  },
  created() {
    this.createByUserName = this.$route.query.createByUserName || '';
    this.activeTab = this.$route.query.type
    console.log('query createByUserName:', this.$route.query.createByUserName);
    console.log('data createByUserName:', this.createByUserName);
    this.initTextbookList();
  },
  methods: {
    // 获取教材图标
    getTextbookIcon(item) {
      const icons = {
        财务管理: "el-icon-notebook-2",
        会计基础: "el-icon-notebook-1",
        电子技术: "el-icon-cpu",
        世界名著: "el-icon-collection",
        中国历史: "el-icon-reading",
      };
      // 根据教材名称或类别选择图标
      return icons[item.name] || "el-icon-notebook-2";
    },
    // 获取教材列表
    async initTextbookList() {
      const params = {
        pageIndex: 1,
        pageSize: 1000,
        // schoolId: this.userInfo.schools[0].id,
        editorType: this.activeTab === 'my' ? 'ChiefEditor' : 'HelperCreator',
      };
      try {
        this.loading = true;
        const res = await getTextbookListApi(this.$api, params);
        this.textbookList = Array.isArray(res.data.items) ? res.data.items : [];
        // 默认选中路由id或第一个教材
        const routeId = Number(this.$route.params.id);
        if (routeId && this.textbookList.some((tb) => tb.id === routeId)) {
          this.currentTextbookId = routeId;
        } else if (this.textbookList.length > 0) {
          this.currentTextbookId = this.textbookList[0].id;
        }
        if (this.currentTextbookId) {
          this.fetchChapterTree();
        }
      } catch (e) {
        this.textbookList = [];
        this.loading = false;
      }
    },
    // 切换教材
    handleTextbookChange(item) {
      if (item.id === this.currentTextbookId) return;
      this.currentTextBook = item
      console.log('currentTextBook:', this.currentTextBook);
      this.currentTextbookId = item.id;
      // 切换路由参数，保持刷新可用
      this.$router.replace({ 
        name: "TeacherTextbookChapterManage",
        params: { id:item.id },
        query:{
          ...this.$route.query,
        }
      });
      this.fetchChapterTree();
    },
    // 获取章节树
    async fetchChapterTree() {
      if (!this.currentTextbookId) return;
      this.loading = true;
      try {
        const res = await textbookSection.GetSectionTree({
          textbookId: this.currentTextbookId,
        });
        // 递归处理章节状态
        const statusMap = {
          PENDING: '待审核',
          APPROVED: '已通过',
          REJECTED: '已驳回',
          WRITING: '编写中',
          CANCELLED: '编写中',
        };
        function mapStatus(list) {
          return (list || []).map(item => {
            let status = '编写中';
            if (item.status) {
              status = statusMap[item.status] || item.status;
            } else if (item.wfApproveStatus) {
              status = statusMap[item.wfApproveStatus] || item.wfApproveStatus;
            }
            return {
              ...item,
              status,
              children: item.children ? mapStatus(item.children) : [],
            };
          });
        }
        this.chapterList = Array.isArray(res.data) ? mapStatus(res.data) : [];
      } catch (e) {
        this.chapterList = [];
      }
      this.loading = false;
    },
    // 删除章节
    handleDelete(chapter) {
      this.$confirm(`确定要删除章节"${chapter.title}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 这里调用删除接口
          this.$api.DeleteTextbookSection({
            id:chapter.id
          }).then(res=>{
            if(res.errCode==0){
              this.$message.success("删除成功");
              this.fetchChapterTree(); // 重新加载
            }
          })
        })
        .catch(() => {});
    },
    // 章节提交审批
    handleSubmitApprove(chapter) {
      this.$confirm(`确定要提交章节"${chapter.title}"的审批吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
          let jsonInfo = {
            textBookName:this.currentTextBook.name, // 教材名称
            submitName:this.userInfo.user.name, // 提交人
            category:this.currentTextBook.majorCategory, // 教材类别
          }
          // 这里调用提交审批接口
          let params = {
            "templateId": 1,
            "jsonInfo": JSON.stringify(jsonInfo),
            "description": `${this.userInfo.user.name} ,提交 教材【${this.currentTextBook.name}】 【${chapter.title}】的审核`,
            "bizId": chapter.id, // 章节id
            "bizTableName": 'textbook_section'
          };
          this.$api.StartWorkflow(params).then(res=>{
            console.log(res)
            this.$message.success("提交审批");
          })
       })

    },
    // 打开协作管理抽屉
    openCoopDrawer(chapter) {
      this.currentChapter = chapter;
      this.coopDrawerVisible = true;
      // 获取协作者列表
      this.getEditorsList(chapter.id);
    },
    openEdit(data) {
      const params = {
        textbookId: data.textbookId,
        textbookName: this.textbookList.find((e) => e.id === data.textbookId).name,
        id: data.id,
        schoolId: this.userInfo.schools[0].id,
        type:this.$route.query.type,
      };
      this.$router.push({
        path: `/jc?data=${encodeURIComponent(JSON.stringify(params))}`,
      });
    },
    // 打开创建章节抽屉
    openCreateDrawer() {
      if (!this.currentTextbookId) {
        this.$message.warning("请先选择教材");
        return;
      }
      const params = {
        textbookId: this.currentTextbookId,
        textbookName: this.textbookList.find((e) => e.id === this.currentTextbookId).name,
        id: 0,
        schoolId: this.userInfo.schools[0].id,
      };
      this.$router.push({
        path: `/jc?data=${encodeURIComponent(JSON.stringify(params))}`,
      });
    },
    // 获取协作者列表
    async getEditorsList(sectionId) {
      if (!sectionId) return;

      this.editorLoading = true;
      try {
        const params = {
          textbookId: this.currentTextbookId,
          sectionId: sectionId,
          schoolId: this.userInfo.schools[0].id,
        };

        const res = await textbookSection.GetEditors(params);
        if (res && res.data) {
          this.editorsList.unselected = res.data.unselected || [];
          this.editorsList.selected = res.data.selected || [];

          // 初始化transfer数据
          this.initTransferData();
        }
      } catch (e) {
        this.$message.error("获取协作者列表失败");
        console.error("获取协作者列表失败", e);
      }
      this.editorLoading = false;
    },
    // 初始化transfer数据
    initTransferData() {
      // 构建transfer数据
      this.transferData = [
        ...this.editorsList.unselected.map((item) => ({
          key: item.id,
          label: item.name,
          disabled: false,
        })),
        ...this.editorsList.selected.map((item) => ({
          key: item.id,
          label: item.name,
          disabled: false,
        })),
      ];

      // 设置已选中的ID
      this.selectedTransferIds = this.editorsList.selected.map((item) => item.id);
    },
    // 关闭协作管理抽屉
    closeCoopDrawer() {
      this.coopDrawerVisible = false;
      // 关闭时自动刷新章节树
      this.fetchChapterTree();
    },
    // 处理添加成员
    handleAddMember() {
      this.addMemberVisible = true;
      // 初始化transfer数据
      this.initTransferData();
    },
    // 处理角色变化
    handleRoleChange(editor) {
      // 保存变更
      this.saveEditors();
    },
    // 关闭添加成员弹窗
    closeAddMemberDialog() {
      this.addMemberVisible = false;
    },
    // 确认添加成员
    confirmAddMembers() {
      if (this.selectedTransferIds.length === 0) {
        this.$message.warning("请选择要添加的成员");
        return;
      }

      this.closeAddMemberDialog();
      // 直接保存所选ID列表
      this.saveEditors();
    },
    // 保存协作者
    async saveEditors() {
      if (!this.currentChapter) return;

      this.editorLoading = true;
      try {
        // 根据接口要求格式化参数
        const params = {
          textbookId: this.currentTextbookId,
          sectionId: this.currentChapter.id,
          userIds: this.selectedTransferIds,
        };

        // schoolId 在请求头中自动添加，不需要手动传递
        await textbookSection.SaveEditors(params);
        // this.$message.success("保存成功");

        // 重新获取协作者列表
        this.getEditorsList(this.currentChapter.id);
      } catch (e) {
        this.$message.error("保存失败");
        console.error("保存协作者失败", e);
      }
      this.editorLoading = false;
    },
    // 处理转移变化
    handleTransferChange(value, direction) {
      if (direction === "left") {
        // 处理左侧到右侧的转移
        this.selectedTransferIds = value;
      } else {
        // 处理右侧到左侧的转移
        this.selectedTransferIds = value;
      }
    },
    // 处理删除协作者
    async handleRemoveEditor(editor) {
      this.$confirm(`确定要删除协作者"${editor.name}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            this.editorLoading = true;

            // 构建删除参数
            const params = {
              textbookId: this.currentTextbookId,
              sectionId: this.currentChapter.id,
              userId: editor.id,
            };

            // 调用删除接口
           const res =  await this.$api.RemoveEditor(params);
            // console.log(res);
            if(res.errCode === 0) {
              this.getEditorsList(this.currentChapter.id);
            }
            // 更新协作者列表，从已选列表中移除该成员
            // this.editorsList.selected = this.editorsList.selected.filter(
            //   (item) => item.id !== editor.id
            // );

            // 更新选中的ID列表
            // this.selectedTransferIds = this.selectedTransferIds.filter(
            //   (id) => id !== editor.id
            // );

            // 保存更新后的协作者列表
            // await this.saveEditors();

            this.$message.success("移除成功");
          } catch (e) {
            this.$message.error("删除失败");
            console.error("删除协作者失败", e);
          } finally {
            this.editorLoading = false;
          }
        })
        .catch(() => {});
    },
    handleBreadcrumbClick(item) {
      // if (item.to) {
      //   this.$router.push(item.to);
      // }
      // this.$router.go(-1)
      this.$router.push({path:item.path})
    },
    handleEditorAction(command, editor) {
      if (command === 'remove') {
        this.handleRemoveEditor(editor);
      }
      // "可编辑"暂时不需要处理，因为默认就是可编辑
    },
    handleRemoveFromSelected(option) {
      const index = this.selectedTransferIds.indexOf(option.key);
      if (index > -1) {
        this.selectedTransferIds.splice(index, 1);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.chapter-manage-page {
  display: flex;
  flex-direction: column;
  background: #f6f8fa;
  height: 100%;
  min-height: 0;

  .breadcrumb-bar {
    height: 48px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #bfbfbf;
    padding-left: 24px;
    background: #fff;
    margin-bottom: 10px;

    .back-btn {
      color: #bfbfbf;
      margin-right: 8px;
      font-size: 18px;
      cursor: pointer;
    }

    .breadcrumb-path {
      color: #bfbfbf;
    }
  }

  .main-content {
    display: flex;
    align-items: flex-start;
    height: 100%;
    min-height: 0;
    flex: 1;
    flex-direction: row;

    .side-menu {
      height: 100%;
      min-height: 0;
      overflow: auto;
      flex-shrink: 0;
      width: 230px;
      background: #fff;
      border-radius: 4px;
      margin-left: 10px;
      margin-right: 15px;
      padding: 16px 0;

      .menu-item {
        display: flex;
        align-items: center;
        height: 48px;
        padding-left: 20px;
        padding-right: 20px;
        font-size: 14px;
        color: #333;
        // margin: 0 8px 4px 8px;
        cursor: pointer;
        position: relative;
        transition: background 0.2s, color 0.2s;

        i {
          font-size: 18px;
          margin-right: 10px;
          color: #bfbfbf;
          transition: color 0.2s;
        }
        &:hover {
          background: #f2f8ff;
          color: #0070fc;
          i {
            color: #0070fc;
          }
        }
        &.active {
          background: #f2f8ff;
          color: #0070fc;
          font-weight: 500;
          i {
            color: #0070fc;
          }
        }
        &.active::after {
          content: "";
          position: absolute;
          right: 0;
          top: 0;
          width: 2px;
          height: 100%;
          background: #0070fc;
        }
      }
    }

    .chapter-tree-area {
      flex: 1;
      height: 100%;
      min-height: 0;
      overflow: auto;
      background: #fff;
      border-radius: 4px;
      padding: 0;

      .empty-data {
        padding: 100px 0;
        text-align: center;
        color: #999;
      }

      .chapter-tree {
        width: 100%;
      }
    }
  }
}

// 协作抽屉样式
.coop-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 20px;
    border-bottom: 1px solid #f0f0f0;

    .drawer-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .close-icon {
      font-size: 20px;
      color: #999;
      cursor: pointer;
    }
  }

  .drawer-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .chapter-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 20px;

      .chapter-name {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .add-member {
        display: flex;
        align-items: center;
        color: #0070fc;
        cursor: pointer;

        .add-icon {
          font-size: 16px;
          margin-right: 4px;
        }

        .add-text {
          font-size: 14px;
        }
      }
    }

    .permission-area {
      .creator-row {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
      .permission-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 16px;
      }

      .member-list {
        .member-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          .member-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 12px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .member-info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .member-name {
              font-size: 14px;
              color: #333;

              .member-tag {
                font-size: 12px;
                color: #666;
              }
            }

            .member-role {
              color: #666;
              font-size: 14px;
            }

            .member-actions {
              .el-dropdown-link {
                color: #0070fc;
                cursor: pointer;
                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }
}

.lanhu-tree-area {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 10px 0 rgba(153, 153, 153, 0.1);
  padding: 0;
  overflow: auto;
}
.lanhu-chapter-tree {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
  width: 100%;
  padding: 20px;
}
.lanhu-empty-data {
  padding: 200px 0;
  text-align: center;
  color: #999;
}

// 添加成员弹窗样式
.coop-member-dialog {
  .dialog-content {
    .coop-title-area {
      display: flex;
      justify-content: space-between;
    }

    .coop-title {
      flex:1;
      font-size: 14px;
      color: #333;
      margin-bottom: 16px;
    }

    .section-name {
      font-size: 14px;
    }

    .transfer-container {
      .member-count {
        font-size: 14px;
        color: #666;
        margin-bottom: 16px;
        padding: 8px 15px;
      }
    }
  }
}



::v-deep .el-checkbox-group {
  width: 100%;
}


// 自定义 transfer 组件样式
::v-deep .el-transfer {
  display: flex;
  justify-content: space-between;

  .el-transfer-panel {
    width: 340px;

    .el-transfer-panel__header {
      padding: 10px 15px;
      background: #f5f7fa;

      .el-checkbox {
        display: none; // 隐藏全选框
      }

      .el-transfer-panel__header-title {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .el-transfer-panel__body {
      height: 300px;
    }

    .el-transfer-panel__list {
      height: 270px;
    }

    .el-transfer-panel__filter {
      margin: 10px;

      .el-input__inner {
        height: 32px;
        border-radius: 4px;
      }
    }
  }

  .el-transfer__buttons {
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .el-button {
      border-radius: 4px;
      padding: 8px 16px;
      margin: 5px 0;
      font-size: 13px;

      &:first-child {
        margin-bottom: 10px;
      }
    }
  }
}

.transfer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  width: 100%;

  .member-name {
    margin-left: 8px;
  }

  .delete-icon {
    color: #999;
    cursor: pointer;
    margin-right: 8px;
    &:hover {
      color: #f56c6c;
    }
  }
}

.breadcrumb-link {
  color: #0070fc;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}

.blue-text {
  color: #0070fc;
  margin:0 10px;
}

::v-deep .el-transfer-panel__list {
  .el-checkbox {
    display: none;
  }
}

::v-deep .el-transfer-panel__body {
  .el-transfer-panel__list {
    .el-transfer-panel__item {
      padding: 0 15px;
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.creator-row {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
</style>

