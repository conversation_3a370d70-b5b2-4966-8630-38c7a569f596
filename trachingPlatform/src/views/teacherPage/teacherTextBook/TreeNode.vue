<template>
  <div>
    <template v-if="level === 2">
      <div>
        <div :class="['chapter-row', 'level-' + level, { active: data.id === $parent.currentChapter?.id }]" @mouseenter="hovered = true" @mouseleave="hovered = false" @click.stop="toggle"> 
          <i v-if="hasChildren" :class="['tree-arrow', expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right']" @click.stop="toggle" />
          <i v-else class="tree-arrow tree-arrow-placeholder" />
          <img v-if="data.icon" :src="data.icon" class="chapter-icon" />
          <span class="chapter-title" @click="isMe?$emit('edit', data):''">{{ data.title }}</span>
          <div class="right-area">
            <span class="author">{{ authors }}</span>
            <span v-if="isMe && hovered && data.status!=='待审核'" class="action coop" @click="$emit('edit', data)">编辑</span>
            <span v-if="isMe && hovered" class="action coop" @click="$emit('coop', data)">协作管理</span>
            <span v-if="isMe" class="action me">我</span>
            <span v-if="isMe && hovered && data.status!=='待审核'" class="action delete" @click="$emit('delete', data)">删除</span>
            <span v-if="!isMe" class="editor-tag"><i class="iconfont icon-suo"></i></span>
            <span v-if="isMe" class="status-tag"
              :class="[
                data.status === '编写中' ? 'green' :
                (data.status === '待审核' ? 'blue' :
                (data.status === '已通过' ? 'gray' :
                (data.status === '已驳回' ? 'red' : 'gray')))
              ]">{{ data.status }}</span>
          </div>
        </div>
        <div v-if="expanded && hasChildren">
          <TreeNode
            v-for="(item, idx) in data.children"
            :key="item.id"
            :data="item"
            :level="level + 1"
            @delete="$emit('delete', $event)"
            @coop="$emit('coop', $event)"
            @edit="$emit('edit', $event)"
          />
        </div>
        <div class="level2-divider"></div>
      </div>
    </template>
    <template v-else>
      <div :class="['chapter-row 1', 'level-' + level, { active: data.id === $parent.currentChapter?.id }]" @mouseenter="hovered = true" @mouseleave="hovered = false" @click.stop="toggle">
        <i v-if="hasChildren" :class="['tree-arrow', expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right']" @click.stop="toggle" />
        <i v-else class="tree-arrow tree-arrow-placeholder" />
        <img v-if="data.icon" :src="data.icon" class="chapter-icon" />
        <span class="chapter-title" @click="isMe?$emit('edit', data):''">{{ data.title }}</span>
        <div class="right-area">
          <span class="author">{{ authors }}</span>
          <span v-if="data.wfApproveStatus=='PENDING'" class="author">审批中</span>
          <span v-if="isMe && hovered && data.status!=='待审核'" class="action coop" @click="$emit('edit', data)">编辑</span>
          <span v-if="isMe && hovered" class="action coop" @click="$emit('coop', data)">协作管理</span>
          <span v-if="isMe" class="action me">我</span>
          <span v-if="isMe && hovered && data.status!=='待审核'" class="action delete" @click="$emit('delete', data)">删除</span>
          <span v-if="isApprove && hovered" class="action delete" @click="$emit('submitApprove', data)">提交审批</span>
          <span v-if="!isMe" class="editor-tag"><i class="iconfont icon-suo"></i></span>
          <span v-if="isMe" class="status-tag"
            :class="[
              data.status === '编写中' ? 'green' :
              (data.status === '待审核' ? 'blue' :
              (data.status === '已通过' ? 'gray' :
              (data.status === '已驳回' ? 'red' : 'gray')))
            ]">{{ data.status }}</span>
        </div>
      </div>
      <div v-if="expanded && hasChildren">
        <TreeNode
          v-for="(item, idx) in data.children"
          :key="item.id"
          :data="item"
          :index="idx"
          :level="level + 1"
          @delete="$emit('delete', $event)"
          @coop="$emit('coop', $event)"
          @edit="$emit('edit', $event)"
        />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'TreeNode',
  props: {
    data: { type: Object, required: true },
    index: { type: Number, required: false,default:0 }, // 新增 index 属性，用于标识每个节点的位置
    level: { type: Number, default: 1 },
    isLastChild: { type: Boolean, default: false }
  },
  data() {
    return {
      expanded: this.index<2?true:false,
      hovered: false
    }
  },
  computed: {
    hasChildren() {
      return this.data.children && Array.isArray(this.data.children) && this.data.children.length > 0;
    },
    authors() {
      // return (this.data.editors || []).map(e => e.name).join(' ');
      const arr = (this.data.editors || []).map(e => e.name)
      return arr.length > 1 ? arr[0] + '等人' : arr[0];
    },
    isMe() {
      return (this.data.editors || []).some(e => e.isMe)||this.$route.query.type=='my';
    },
    isApprove(){
      return this.data?.editors.some(p => p.isMe) && this.data?.wfApproveStatus != 'PENDING';
    }
  },
  methods: {
    toggle() {
      this.expanded = !this.expanded;
    }
  }
}
</script>

<style lang="scss" scoped>
.chapter-row {
  display: flex;
  align-items: center;
  min-height: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #333;
  box-sizing: border-box;
  transition: background 0.2s;
  margin-bottom: 0;
  background: transparent;
  border-radius: 0;
  font-weight: normal;
  box-shadow: none;
  border-bottom: none;
  padding-right: 12px;
  cursor: pointer;
  &:hover {
    background: #f3f8ff;
  }
  &.active {
    background: #f1f7ff;
    color: #0070fc;
  }
}
.chapter-row.level-1 {
  background: #f7f7f7;
  border-radius: 4px;
  font-weight: 500;
  transition: box-shadow 0.2s, background 0.2s;
}
.chapter-row.level-1:hover {
  background: #f3f8ff;
  box-shadow: 0 2px 12px 0 rgba(153,153,153,0.10);
}
.chapter-row.level-2 {
  background: #fff;
  border-radius: 0;
  font-weight: normal;
  color: #333;
}
.chapter-row.level-3, .chapter-row.level-4 {
  background: #fff;
  border-radius: 0;
  margin-bottom: 4px;
  font-weight: normal;
  color: #666;
}
.chapter-row.level-1 { padding-left: 24px; }
.chapter-row.level-2 { padding-left: 48px; }
.chapter-row.level-3 { padding-left: 72px; }
.chapter-row.level-4 { padding-left: 96px; }
.chapter-row.level-5 { padding-left: 120px; }
.chapter-row.level-6 { padding-left: 144px; }
.chapter-row.level-7 { padding-left: 168px; }
.chapter-row.level-8 { padding-left: 192px; }
.chapter-row.level-9 { padding-left: 216px; }
.chapter-row.level-10 { padding-left: 240px; }

.chapter-title {
  font-weight: inherit;
  color: inherit;
}
.chapter-icon {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}
.right-area {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding-right: 20px;
  .author {
    margin-right: 40px;
    width: 150px;
    font-size: 14px;
  }
  .action {
    margin-right: 20px;
    font-size: 14px;
    display: none;
    &.coop, &.delete {
      color: #0070fc;
      cursor: pointer;
      &:hover { text-decoration: underline; }
    }
    &.me {
      color: #0070fc;
      display: inline-block !important;
    }
  }
  .action.me {
    display: inline-block !important;
  }
  .status-tag {
    flex-shrink: 0;
    margin-left: 20px;
  }
  .editor-tag{
    color:#C0C6D6;
    font-size:16px;
  }
}
.chapter-row:hover .right-area .action {
  display: inline-block;
}
.status-tag {
  display: inline-block;
  min-width: 42px;
  height: 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 20px;
  margin-left: 20px;
  &.blue { color: #0070fc; background: none; }
  &.green { color: #07c392; background: none; }
  &.gray { color: #bfbfbf; background: none; }
  &.red { color: #f11b1b; background: none; }
}
.tree-arrow {
  font-size: 16px;
  color: #0070fc;
  margin-right: 8px;
  cursor: pointer;
}
.tree-arrow-placeholder {
  visibility: hidden;
  width: 16px;
  margin-right: 8px;
  display: inline-block;
}
.level2-divider {
  height: 1px;
  background: #f5f5f5;
  margin: 0 0 8px 48px;
  width: calc(100% - 48px);
}
</style> 