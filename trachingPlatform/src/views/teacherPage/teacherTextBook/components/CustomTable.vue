<template>
  <div class="custom-table">
    <!-- 表头 -->
    <div class="table-header">
      <div class="header-cell checkbox-cell">
        <el-checkbox 
          v-model="allSelected" 
          @change="handleSelectAll"
          :indeterminate="isIndeterminate">
        </el-checkbox>
      </div>
      <div class="header-cell name-cell">教材名称</div>
      <div class="header-cell desc-cell">简介</div>
      <div class="header-cell version-cell">版本号</div>
      <div class="header-cell status-cell">审核状态</div>
      <div class="header-cell publish-cell">发布状态</div>
      <div class="header-cell time-cell">创建时间</div>
      <div class="header-cell action-cell">操作</div>
    </div>

    <!-- 表格内容 -->
    <div class="table-body">
      <div 
        v-for="(item, index) in data" 
        :key="item.id || index"
        class="table-row"
        @mouseenter="hoveredRow = index"
        @mouseleave="hoveredRow = -1"
        :class="{ 'hovered': hoveredRow === index }"
      >
        <!-- 选择框 -->
        <div class="table-cell checkbox-cell">
          <el-checkbox 
            v-model="item.selected"
            @change="handleRowSelect">
          </el-checkbox>
        </div>

        <!-- 教材名称 -->
        <div class="table-cell name-cell">
          <div class="textbook-content">
            <div class="textbook-cover">
              <i class="el-icon-folder"></i>
              <span>key</span>
            </div>
            <div class="textbook-info">
              <div class="textbook-title">{{ item.name || '财务管理' }}</div>
              <div class="textbook-meta">
                <div>专业：{{ item.majorCategory || '财经商贸类' }}</div>
                <div>主编：{{ item.publishedChiefEditor || '夏问' }}</div>
                <div>协作者：{{ item.publishedCreator || '李利、学灵、李灵' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 简介 -->
        <div class="table-cell desc-cell">
          <div class="description-text">
            {{ item.description || '本书是编者在多年高等职业教育教学改革与实践的基础上，结合高职院校专门培训工分及高等技能人才培养的特点需求等而编成的。' }}
          </div>
        </div>

        <!-- 版本号 -->
        <div class="table-cell version-cell">
          {{ item.currentOnlineVersion || '1.0' }}
        </div>

        <!-- 审核状态 -->
        <div class="table-cell status-cell">
          <span class="status-text">{{ getApproveStatusText(item.wFApproveStatus) }}</span>
        </div>

        <!-- 发布状态 -->
        <div class="table-cell publish-cell">
          {{ getPublishStatusText(item.textbookOnLineStatus) }}
        </div>

        <!-- 创建时间 -->
        <div class="table-cell time-cell">
          {{ item.createTime || '2025.05.05 10:20' }}
        </div>

        <!-- 操作 -->
        <div class="table-cell action-cell">
          <span class="action-link" @click="$emit('chapter-manage', item)">章节管理</span>
          <el-dropdown @command="command => $emit('more-action', command, item)" trigger="click">
            <span class="action-link">
              更多操作 <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="edit">编辑</el-dropdown-item>
              <el-dropdown-item command="submit">提交审核</el-dropdown-item>
              <el-dropdown-item command="delete">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTable',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      hoveredRow: -1,
      allSelected: false,
      isIndeterminate: false
    }
  },
  watch: {
    data: {
      handler() {
        this.updateSelectState();
      },
      deep: true
    }
  },
  methods: {
    // 获取审核状态文本
    getApproveStatusText(status) {
      const statusMap = {
        'PENDING': '编辑中',
        'APPROVED': '已通过',
        'REJECTED': '已驳回'
      };
      return statusMap[status] || '编辑中';
    },
    
    // 获取发布状态文本
    getPublishStatusText(status) {
      const statusMap = {
        'OnLine': '已发布',
        'OffLine': '未发布'
      };
      return statusMap[status] || '未发布';
    },

    // 全选处理
    handleSelectAll(val) {
      this.data.forEach(item => {
        this.$set(item, 'selected', val);
      });
      this.updateSelectState();
      this.$emit('selection-change', this.data.filter(item => item.selected));
    },

    // 单行选择处理
    handleRowSelect() {
      this.updateSelectState();
      this.$emit('selection-change', this.data.filter(item => item.selected));
    },

    // 更新选择状态
    updateSelectState() {
      const selectedCount = this.data.filter(item => item.selected).length;
      const totalCount = this.data.length;
      
      if (selectedCount === 0) {
        this.allSelected = false;
        this.isIndeterminate = false;
      } else if (selectedCount === totalCount) {
        this.allSelected = true;
        this.isIndeterminate = false;
      } else {
        this.allSelected = false;
        this.isIndeterminate = true;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-table {
  background: white;
  border: 1px solid #EBEEF5;
  
  // 表头样式
  .table-header {
    display: flex;
    background: #FAFAFA;
    border-bottom: 1px solid #EBEEF5;
    height: 50px;
    
    .header-cell {
      display: flex;
      align-items: center;
      padding: 0 15px;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      border-right: 1px solid #EBEEF5;
      
      &:last-child {
        border-right: none;
      }
      
      &.checkbox-cell {
        width: 50px;
        justify-content: center;
      }
      
      &.name-cell {
        width: 300px;
      }
      
      &.desc-cell {
        flex: 1;
        min-width: 250px;
      }
      
      &.version-cell {
        width: 80px;
        justify-content: center;
      }
      
      &.status-cell {
        width: 100px;
        justify-content: center;
      }
      
      &.publish-cell {
        width: 100px;
        justify-content: center;
      }
      
      &.time-cell {
        width: 140px;
        justify-content: center;
      }
      
      &.action-cell {
        width: 200px;
        justify-content: center;
      }
    }
  }
  
  // 表格内容
  .table-body {
    .table-row {
      display: flex;
      min-height: 80px;
      border-bottom: 1px solid #EBEEF5;
      transition: background-color 0.2s;
      
      &.hovered {
        background-color: #F5F7FA;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .table-cell {
        display: flex;
        align-items: center;
        padding: 15px;
        font-size: 14px;
        color: #666666;
        border-right: 1px solid #EBEEF5;
        
        &:last-child {
          border-right: none;
        }
        
        &.checkbox-cell {
          width: 50px;
          justify-content: center;
          padding: 0;
        }
        
        &.name-cell {
          width: 300px;
          padding: 15px;
          align-items: flex-start;
        }
        
        &.desc-cell {
          flex: 1;
          min-width: 250px;
          align-items: flex-start;
        }
        
        &.version-cell {
          width: 80px;
          justify-content: center;
        }
        
        &.status-cell {
          width: 100px;
          justify-content: center;
        }
        
        &.publish-cell {
          width: 100px;
          justify-content: center;
        }
        
        &.time-cell {
          width: 140px;
          justify-content: center;
        }
        
        &.action-cell {
          width: 200px;
          justify-content: center;
        }
      }
    }
  }
  
  // 教材内容样式
  .textbook-content {
    display: flex;
    align-items: flex-start;
    width: 100%;
    
    .textbook-cover {
      width: 50px;
      height: 67px;
      border-radius: 4px;
      background: #2C2C2C;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      flex-shrink: 0;
      
      .el-icon-folder {
        color: #FFD700;
        font-size: 18px;
        margin-bottom: 2px;
      }
      
      span {
        color: #FFD700;
        font-size: 10px;
      }
    }
    
    .textbook-info {
      flex: 1;
      min-width: 0;
      
      .textbook-title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 8px;
        line-height: 1.3;
      }
      
      .textbook-meta {
        font-size: 12px;
        color: #666666;
        line-height: 1.6;
        
        div {
          margin-bottom: 2px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  // 简介文本
  .description-text {
    font-size: 14px;
    color: #666666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  // 状态文本
  .status-text {
    color: #409EFF;
    font-size: 14px;
  }
  
  // 操作链接
  .action-link {
    color: #409EFF;
    font-size: 14px;
    cursor: pointer;
    margin-right: 16px;
    text-decoration: none;
    
    &:hover {
      color: #0070FC;
    }
    
    &:last-child {
      margin-right: 0;
    }
    
    .el-icon-arrow-down {
      margin-left: 4px;
      font-size: 12px;
    }
  }
}

// 下拉菜单样式
::v-deep .el-dropdown-menu {
  .el-dropdown-menu__item {
    font-size: 14px;
    
    &:hover {
      background-color: #F5F7FA;
      color: #0070FC;
    }
  }
}
</style> 