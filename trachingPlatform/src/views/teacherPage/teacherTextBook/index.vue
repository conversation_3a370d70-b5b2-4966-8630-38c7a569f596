<template>
  <div class="textbook-manage-page">
    <div class="status-tabs">
      <span
        v-for="item in statusList"
        :key="item.id"
        :class="[
          'status-tab',
          { active: tableConfig.searchParams.editorType === item.value },
        ]"
        @click="changeType(item)"
      >
        {{ item.label }}({{ item.count || 0 }})
      </span>
    </div>

    <!-- 搜索工具栏 -->
    <div class="search-toolbar">
      <el-input
        class="search-input"
        v-model="tableConfig.searchParams.searchWord"
        placeholder="请搜索教材名称"
        clearable
      />

      <el-select
        class="select-box"
        v-model="tableConfig.searchParams.majorCategory"
        placeholder="请选择专业大类"
        clearable
      >
        <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>

      <el-select
        class="select-box"
        v-model="tableConfig.searchParams.wFApproveStatus"
        placeholder="请选择审核状态"
        clearable
      >
        <el-option
          v-for="item in wFApproveStatus"
          :key="item.id"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>

      <el-select
        class="select-box"
        v-model="tableConfig.searchParams.textbookOnLineStatus"
        placeholder="请选择发布状态"
        clearable
      >
        <el-option
          v-for="item in textbookOnLineStatus"
          :key="item.id"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button class="query-btn" type="primary" @click="initTableData()">查询</el-button>
      <el-button class="reset-btn" type="default" @click="resetSearch">重置</el-button>

      <div class="toolbar-spacer"></div>
      <!-- <el-button
        class="create-btn"
        type="success"
        icon="el-icon-plus"
        @click="addTextbook"
        >创建教材</el-button
      > -->
    </div>

    <!-- 表格 -->
    <div class="table-wrapper">
      <table2
        ref="table2"
        :data="tableConfig.tableData"
        :columns="columns"
        :rowKey="'id'"
        :searchParams="tableConfig.searchParams"
        :pagination="false"
        :stripe="false"

        @handleSearch="initTableData"
      >
        <!-- 教材名称自定义渲染 -->
        <template #name="{ row }">
          <div class="textbook-content">
            <div class="textbook-cover" @click="handleChapterManage(row, tableConfig.searchParams.editorType === 'ChiefEditor'?'my':'other')">
              <template v-if="row.coverImage">
                <img :src="`${row.coverImage}?x-oss-process=image/resize,p_25`" alt="封面" />
              </template>
              <template v-else>
                <div class="default-cover">默认封面</div>
              </template>
            </div>
            <div class="textbook-info">
              <div class="textbook-title" @click="handleChapterManage(row, tableConfig.searchParams.editorType === 'ChiefEditor'?'my':'other')">{{ row.name || "教材名称" }}</div>
              <div class="textbook-meta">
                <div>专业：{{ row.majorCategory || "-" }}</div>
                <div>主编：{{ row.chiefEditorUsers ? row.chiefEditorUsers.map(user => user.name).join(' ') : '-' }}</div>
                <div>协作者：
                  <!-- {{ row.helperCreatorUsers ? row.helperCreatorUsers.map(user => user.name).join(' ') : '-' }} -->
                  <template v-if="row.helperCreatorUsers && row.helperCreatorUsers.length > 0">
                    <!-- 始终显示前三个协作者 -->
                    {{ row.helperCreatorUsers.slice(0, 3).map(user => user.name).join(' ') }}
                    <!-- 当协作者数量超过三个时显示“更多”按钮和提示 -->
                    <template v-if="row.helperCreatorUsers.length > 3">
                      <el-tooltip
                        placement="right"
                        effect="light"
                      > 
                        <template #content>
                          <div class="collaborator-list">
                            <div v-for="(user, index) in row.helperCreatorUsers" :key="index">
                              <span style="color:#666;line-height:20px;">{{ user.name }}</span> 
                            </div>
                          </div>
                        </template>
                        <span class="toggle-collaborators">更多</span>
                      </el-tooltip>
                    </template>
                  </template>
                  <template v-else>
                    -
                  </template>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- 操作列自定义渲染 -->
        <template #operate="{ row }">
          <template v-if="tableConfig.searchParams.editorType === 'ChiefEditor'">
            <span class="action-link" @click="handleChapterManage(row, 'my')">章节管理</span>
            <el-dropdown
              @command="(command) => handleMoreAction(command, row)"
              trigger="click"
            >
              <span class="action-link"> 更多操作 <i class="el-icon-arrow-down"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="edit">编辑</el-dropdown-item>
                <el-dropdown-item command="submitCheck">提交审批</el-dropdown-item>
                <el-dropdown-item v-if="row?.wfApproveStatus" command="approvalDetail">审批明细</el-dropdown-item>
                <!-- <el-dropdown-item command="delete">删除</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <span class="action-link" @click="handleChapterManage(row, 'other')">编辑教材</span>
            <span class="action-link" @click="handleSubmit(row)">提交审核</span>
          </template>
        </template>
        <!-- 简介自定义渲染 -->
        <template #description="{ row }">
          <el-tooltip
            v-if="row.description && row.description.length > 60"
            placement="right"
            effect="light"
            :content="row.description">
            <div class="description-text" >{{ handleDesc(row)}}</div>
          </el-tooltip>
          <div v-else class="description-text" >{{ handleDesc(row)}}</div>
        </template>
        <!-- 审核状态自定义渲染 -->
        <template #wfApproveStatus="{ row }">
          <span class="status-highlight">
            {{
              row.wfApproveStatus === "PENDING"
                ? "审批中"
                : row.wfApproveStatus === "APPROVED"
                ? "已通过"
                : row.wfApproveStatus === "REJECTED"
                ? "已驳回"
                : "编辑中"
            }}
          </span>
        </template>
      </table2>
    </div>

    <!-- 创建教材弹窗 -->
    <baseDialog
      :noFooter="true"
      :showToScreen="false"
      :visible.sync="dialogVisible"
      width="720px"
      :title="form.id ? '编辑教材' : '创建教材'"
    >
      <el-form
        ref="form"
        :model="form"
        label-width="80px"
        label-position="top"
        :rules="rules"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材名称" prop="name">
              <el-input v-model="form.name" maxLength="20" placeholder="请输入教材名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属专业大类" prop="majorCategory">
              <el-select
                style="width: 100%"
                v-model="form.majorCategory"
                placeholder="请选择所属专业大类"
              >
                <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="教材封面" prop="coverImage">
          <el-upload
            class="course-cover-uploader"
            :action="actionUrl"
            :show-file-list="false"
            :data="uploadData"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材主编" prop="chiefEditorUserIds">
              <el-select
                v-model="form.chiefEditorUserIds"
                multiple
                filterable
                placeholder="请选择教材主编"
                style="width: 100%"
                @change="handleChiefEditorChange"
              >
                <el-option
                  v-for="item in teacherList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :disabled="form.creatorUserIds.includes(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教材创作者" prop="creatorUserIds">
              <el-select
                v-model="form.creatorUserIds"
                multiple
                filterable
                placeholder="请选择教材创作者"
                style="width: 100%"
                @change="handleCreatorChange"
              >
                <el-option
                  v-for="item in teacherList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :disabled="form.chiefEditorUserIds.includes(item.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="教材简介" prop="description">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入教材简介"
          ></el-input>
        </el-form-item>
        <el-form-item style="text-align: right; margin-bottom: 0; margin-top: 40px">
          <el-button type="default" @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog>

    <!-- 其他弹窗保持不变 -->
    <el-dialog :visible.sync="passDialog" custom-class="pass-dialog" title="提示">
      <p>确定要通过该申请吗？</p>
      <div style="text-align: right; margin-bottom: 0; margin-top: 40px">
        <el-button style="width: 80px" type="default" @click="passDialog = false"
          >取消</el-button
        >
        <el-button style="width: 80px" type="primary" @click="handlePass">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="rejectDialog" custom-class="reject-dialog" title="提示">
      <p style="margin-bottom: 10px">确定要驳回该申请吗？</p>
      <el-input v-model="rejectText" placeholder="请填写驳回原因"></el-input>
      <div style="text-align: right; margin-bottom: 0; margin-top: 40px">
        <el-button style="width: 80px" type="default" @click="rejectDialog = false"
          >取消</el-button
        >
        <el-button style="width: 80px" type="danger" @click="handleReject"
          >拒绝</el-button
        >
      </div>
    </el-dialog>

    <baseDialog
      :noFooter="true"
      title="详情"
      width="720px"
      :visible.sync="detailDialogVisible"
    >
      <detail :detailInfo="detailInfo" />
      <div style="text-align: right; margin-bottom: 0; margin-top: 40px">
        <el-button type="default" @click="detailDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>

    <el-dialog
            title="审批流信息"
            v-if="flowVisible"
            :visible.sync="flowVisible"
            custom-class="flowing-dialog"
            width="700px">
            <!-- <h4 class="flow-title">{{approvalObj.instance.description}}</h4> -->
            <el-descriptions title="基础信息" :column="2" border size="middle" >
                <el-descriptions-item label="工作流ID">{{approvalObj.instance.id}}</el-descriptions-item>
                <el-descriptions-item label="审批状态"> <el-tag size="small">{{WFApproveStatusLabel[approvalObj.instance.approveStatus]}}</el-tag></el-descriptions-item>
                <el-descriptions-item label="提交时间">{{approvalObj.instance.createTime}}</el-descriptions-item>
                <el-descriptions-item label="描述">
                    {{approvalObj.instance.description}}
                </el-descriptions-item>
            </el-descriptions>
            <div style="margin-top:20px;">
                <el-descriptions title="审批节点" :column="2"></el-descriptions>
                <el-steps direction="vertical" :active="-1">
                    <el-step v-for="item in approvalObj.nodes" :key="item.id" :title="item.nodeName" icon="iconfont icon-shenpi1">
                        <template #description>
                            <div class="custom-desc">
                              <el-tag>{{WFApproveStatusLabel[item.status]}}</el-tag>
                              <p class="status">审批人类型:{{WFApproverTypeLabel[item.approverType]}}</p>
                              <p class="status">审批结果:{{item.comment}}</p>
                              <!-- <span class="status">审批人:{{item.comment}}</span> -->
                              <p class="detail">类型:{{item.signType=='AND'?'会签':'或签'}}</p>
                              <p class="detail">{{item.createTime}}</p>
                            </div>
                          </template>
                    </el-step>
                  </el-steps>
            </div>
            <div style="margin-top:20px;">
                <el-descriptions title="审批记录" :column="2"></el-descriptions>
                <el-table
                    :data="approvalObj.records||[]"
                    border
                    style="width: 100%">
                    <el-table-column
                    prop="approverUserId"
                    label="审批人"
                    width="180">
                    </el-table-column>
                    <el-table-column
                    prop="comment"
                    label="审批理由"
                    width="180">
                    </el-table-column>
                    <el-table-column
                    prop="action"
                    label="审批动作">
                    <template slot-scope="scope">
                            <el-tag>{{WFApproveStatusLabel[scope.row.action]}}</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
  </div>
</template>

<script>
// import { categories } from "@/constants/majoeCategories.js";
import { mapGetters } from "vuex";
import token from "@/utils/token.js";
import Table2 from "@/components/base/table2/index.vue";
import { WFApproveStatusLabel,WFApproverTypeLabel } from "@/views/schoolPage/workflow/flowType.js"

//  textbook_section 章节,  textbook 教材
const bizTableType= {
  ChiefEditor: "textbook", //主编
  HelperCreator: "textbook_section", //协作者
}
export default {
  name: "TextbookPage",
  data() {
    return {
      WFApproveStatusLabel,
      WFApproverTypeLabel,
      chiefEditorTotal: 0,
      helperCreatorTotal: 0,
      statusList: [
        { id: 1, label: "我管理的", value: "ChiefEditor", count: 0 },
        { id: 2, label: "我协作的", value: "HelperCreator", count: 0 },
      ],
      wFApproveStatus: [
        { id: 0, label: "全部", value: "" },
        { id: 1, label: "待审核", value: "PENDING" },
        { id: 2, label: "驳回", value: "REJECTED" },
        { id: 3, label: "通过", value: "APPROVED" },
      ],
      textbookOnLineStatus: [
        { id: 0, label: "全部", value: "" },
        { id: 4, label: "已下架", value: "OffLine" },
        { id: 5, label: "已上架", value: "OnLine" },
      ],
      tableConfig: {
        tableData: [], // 表格数据
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 1000,
          majorCategory: "", // 专业id
          editorType: "ChiefEditor", // 主编或协作者 ChiefEditor, HelperCreator
          textbookOnLineStatus: "", // 教材上下架状态 OnLine 上架 OffLine 下架
          wfApproveStatus: "", // 教材审核状态 PENDING, APPROVED, REJECTED, CANCELLED 1 待审核 2 驳回 3 通过
          meAsChiefEditor: "", // 教材主编
          meAsHelperCreator: "", // 教材创作者
          searchWord: "", // 搜索关键字,
          schoolId: "",
        },
      },
      // 补全模板中使用的变量
      firstLoad: true,
      loading: false,
      dialogVisible: false, // 新增教材弹窗
      actionUrl: window.FILEIP, // 上传图片地址
      uploadData: {
        schoolId: 0,
        uploadFileType: "Image",
        withPreSigned: false,
      },
      uploadHeaders: {
        schoolId: 0,
        Authorization: token.getToken(),
      },
      form: {
        // 新增教材表单
        id: "", // id
        name: "", // 教材名称
        majorCategory: "", // 所属专业大类
        publishedChiefEditor: [], // 教材主编
        publishedCreator: [], // 教材创作者
        coverImage: "", // 教材封面
        description: "", // 教材简介
        chiefEditorUserIds: [], // 教材主编
        creatorUserIds: [], // 教材创作者
      },
      imageUrl: "",
      rules: {
        name: [{ required: true, message: "请输入教材名称", trigger: "blur" }],
        majorCategory: [
          { required: true, message: "请选择所属专业大类", trigger: "blur" },
        ],
        chiefEditorUserIds: [
          {
            required: true,
            type: "array",
            min: 1,
            message: "请选择教材主编",
            trigger: "change",
          },
        ],
        creatorUserIds: [
          {
            required: true,
            type: "array",
            min: 1,
            message: "请选择教材创作者",
            trigger: "change",
          },
        ],
        coverImage: [{ required: true, message: "请上传教材封面", trigger: "blur" }],
      },
      majorList: [], // 专业列表
      gradeList: [], // 教材列表
      categories: [], // 专业大类

      passDialog: false, // 通过弹窗
      rejectText: "", // 驳回原因
      rejectDialog: false, // 驳回弹窗
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
      teacherList: [], // 教师列表
      columns: [
        {
          prop: "name",
          label: "教材名称",
          minWidth: "200px",
          slot: "name",
        },
        {
          prop: "description",
          label: "简介",
          minWidth: "100px",
          slot: "description",
        },
        {
          prop: "currentOnlineVersion",
          label: "版本号",
          width: "80px",
          align: "center",
          formatter: (row) => row.currentOnlineVersion || "--",
        },
        {
          prop: "wfApproveStatus",
          label: "审核状态",
          width: "90px",
          align: "center",
          slot: "wfApproveStatus",
        },
        {
          prop: "textbookOnLineStatus",
          label: "发布状态",
          width: "90px",
          align: "center",
          formatter: (row) => {
            const statusMap = {
              OnLine: "已发布",
              OffLine: "未发布",
            };
            return statusMap[row.textbookOnLineStatus] || "未发布";
          },
        },
        {
          prop: "createTime",
          label: "创建时间",
          width: "auto",
          align: "center",
          sortable: true,
          formatter: (row) => {
            if (!row.createTime) return "";
            const d = new Date(row.createTime);
            if (isNaN(d.getTime())) return row.createTime;
            const pad = (n) => (n < 10 ? "0" + n : n);
            return `${d.getFullYear()}.${pad(d.getMonth() + 1)}.${pad(d.getDate())} ${pad(
              d.getHours()
            )}:${pad(d.getMinutes())}`;
          },
        },
        {
          prop: "operate",
          label: "操作",
          width: "190px",
          align: "center",
          slot: "operate"
        },
      ],

      flowVisible: false, // 审批流程弹窗
      approvalObj: {}, // 审批流程数据

    };
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import("@/components/base/button/addButton.vue"), // 新增按钮
    exportButton: () => import("@/components/base/button/exportButton.vue"), // 导出按钮
    importButton: () => import("@/components/base/button/importButton.vue"), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail: () => import("@/views/schoolPage/textbookManage/components/detail.vue"),
    Table2,
  },
  mounted() {
    // this.fetchAllTabData();
    this.getBigMajorList(); // 获取专业大类


    if(this.$route.query.type==1){
      this.tableConfig.searchParams.editorType = 'ChiefEditor';
    }else if(this.$route.query.type==2) {
      this.tableConfig.searchParams.editorType = 'HelperCreator';
    }
  },
  computed: {
    ...mapGetters(["userInfo"]), // 从vuex中获取用户信息
  },
  methods: {
    formatCollaborators(users) {
      return users.map(user => user.name).join('\n\n');
    },
    // 获取两个tab的真实数据和total
    async fetchAllTabData() {
      const [chiefRes, helperRes] = await Promise.all([
        this.$api.GetMyTextbookList({
          ...this.tableConfig.searchParams,
          editorType: "ChiefEditor",
          pageIndex: 1,
          pageSize: 1000,
        }),
        this.$api.GetMyTextbookList({
          ...this.tableConfig.searchParams,
          editorType: "HelperCreator",
          pageIndex: 1,
          pageSize: 1000,
        }),
      ]);
      this.chiefEditorTotal = chiefRes.data.total;
      this.helperCreatorTotal = helperRes.data.total;
      this.statusList[0].count = this.chiefEditorTotal;
      this.statusList[1].count = this.helperCreatorTotal;
      // 当前tab数据渲染到表格
      if (this.tableConfig.searchParams.editorType === "ChiefEditor") {
        this.tableConfig.tableData = chiefRes.data.items;
        this.tableConfig.total = chiefRes.data.total;
      } else {
        this.tableConfig.tableData = helperRes.data.items;
        this.tableConfig.total = helperRes.data.total;
      }

        // this.$api.GetMyTextbookList(this.tableConfig.searchParams).then((res) => {
        //   this.chiefEditorTotal = res.data.total;
        //   this.statusList[0].count = this.chiefEditorTotal;
        //   this.tableConfig.tableData = res.data.items;
        // })

    },
    // 切换类型
    async changeType(item) {
      // 切换 tab 时重置查询条件，只保留 editorType
      this.tableConfig.searchParams = {
        pageIndex: 1,
        pageSize: 1000,
        majorCategory: "",
        editorType: item.value,
        textbookOnLineStatus: "",
        wfApproveStatus: "",
        meAsChiefEditor: "",
        meAsHelperCreator: "",
        searchWord: "",
        schoolId: this.tableConfig.searchParams.schoolId,
      };
      // await this.fetchAllTabData();
    },
         // 获取专业大类
    getBigMajorList(){ 
      this.$api.GetMajorTree({}).then(res=>{
        this.categories = res.data;
      })
    },
    handleDesc(row) {
      if(row.description?.length>60){
        return row.description.substring(0,60)+"...";
      }
      return row.description || "暂无简介";
    },
    // 查询和重置时也调用fetchAllTabData
    resetSearch() {
      this.tableConfig.searchParams = {
        pageIndex: 1,
        pageSize: 1000,
        majorCategory: "",
        editorType: this.tableConfig.searchParams.editorType,
        textbookOnLineStatus: "",
        wfApproveStatus: "",
        meAsChiefEditor: "",
        meAsHelperCreator: "",
        searchWord: "",
        schoolId: this.tableConfig.searchParams.schoolId,
      };
      // this.fetchAllTabData();
    },
    async initTableData() {
      await this.fetchAllTabData();
    },
    // 章节管理
    handleChapterManage(row, type) {
      console.log("章节管理:", row, type);
      // 跳转到章节管理页面，使用动态参数，createByUserName通过query传递
      this.$router.push({
        name: "TeacherTextbookChapterManage",
        params: { id: row.id },
        query: { createByUserName: row.createByUserName, type }
      });
    },

    // 更多操作下拉菜单
    handleMoreAction(command, row) {
      console.log("更多操作:", command, row);
      switch (command) {
        case "edit":
          this.handleEdit(row);
          break;
        case "submit": // 提交审核
          this.handleSubmit(row);
          break;
        case "submitCheck":  // 提交审核
          this.handleSubmit(row);
          break;
        case "approvalDetail": // 审批明细
          this.$api.GetWorkflowInstanceByInstanceId({instanceId:row.wfInstanceId}).then(res=>{
            if(res.errCode==0){
              this.flowVisible=true;
              this.approvalObj = res.data;
              // 调用合并方法
              this.approvalObj.nodes = this.mergeNodesAndRecords(res.data.nodes, res.data.records);
            } 
          })
          break;
        case "delete":
          this.handleDelete(row);
          break;
      }
    },

    // 编辑教材
    async handleEdit(row) {
      await this.getTeacherList();
      this.dialogVisible = true;

      let chiefEditorUserIds = [];
      let creatorUserIds = [];
      if (row.chiefEditorUsers && Array.isArray(row.chiefEditorUsers)) {
        chiefEditorUserIds = row.chiefEditorUsers.map((u) => u.id);
      }
      if (row.helperCreatorUsers && Array.isArray(row.helperCreatorUsers)) {
        creatorUserIds = row.helperCreatorUsers.map((u) => u.id);
      }

      this.form = {
        id: row.id,
        name: row.name,
        majorCategory: row.majorCategory,
        coverImage: row.coverImage,
        description: row.description,
        chiefEditorUserIds,
        creatorUserIds,
        // 其他字段...
      };
      this.imageUrl = `${row.coverImage}?x-oss-process=image/resize,p_25`;
    },

    // 提交审核
    async handleSubmit(row) {
      const res = await this.$api.GetSectionTree({textbookId: row.id})
      console.log(res.data);
      let target = this.findPendingApprovalIds(res.data);
      // console.log("发起审核的章节id集合",target);
      // wfApproveStatus
      this.$confirm("确定要提交审核吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // 调用提交审核API 
        // console.log("提交审核:", row);
        this.handleStartFlow(row,target)
        // this.$message.success("提交成功");
      });
    },
    findPendingApprovalIds(data) {
      const result = [];
      const traverse = (nodes) => {
        nodes.forEach(node => {
          // if (node?.wfApproveStatus == 'PENDING') {
          // && node?.wfApproveStatus != 'PENDING'
          if (node?.editors.some(p => p.isMe) && node?.wfApproveStatus != 'PENDING') {
            result.push({
              id: node.id,
              title: node.title,
            });
          }
          if (node.children && Array.isArray(node.children)) {
            traverse(node.children);
          }
        });
      };
      traverse(Array.isArray(data) ? data : [data]);
      return result;
    },
    // 删除教材
    async handleDelete(row) {
      try {
        await this.$confirm(
          "确定删除该教材吗？删除后数据将不可恢复，请谨慎操作！",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        );

        await this.$api.DeleteTextbook({ id: row.id });
        this.$message.success("删除成功");
        this.initTableData();
      } catch (error) {
        console.log("取消删除");
      }
    },

    async addTextbook() {
      await this.getTeacherList();
      // 新增课程
      this.dialogVisible = true; // 打开弹窗
      this.form = {
        // 重置表单
        id: "", // id
        name: "", // 教材名称
        majorCategory: "", // 所属专业大类
        publishedChiefEditor: "", // 教材主编
        publishedCreator: "", // 教材创作者
        coverImage: "", // 教材封面
        description: "", // 教材简介
        chiefEditorUserIds: [], // 教材主编
        creatorUserIds: [], // 教材创作者
      };
      this.imageUrl = "";
    },

    async save() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          // 处理主编和创作者名称
          const chiefEditorNames = this.teacherList
            .filter((teacher) => this.form.chiefEditorUserIds.includes(teacher.id))
            .map((teacher) => teacher.name)
            .join("、");
          const creatorNames = this.teacherList
            .filter((teacher) => this.form.creatorUserIds.includes(teacher.id))
            .map((teacher) => teacher.name)
            .join("、");

          // 构造参数，未出版教材只传 chiefEditorUserIds 和 helperCreatorUserIds
          const formData = {
            ...this.form,
            isPublishedTextbook: false,
            chiefEditorUserIds: this.form.chiefEditorUserIds,
            helperCreatorUserIds: this.form.creatorUserIds,
            // 其他字段按需保留
          };
          // 删除不需要的字段
          delete formData.creatorUserIds;
          delete formData.publishedChiefEditor;
          delete formData.publishedCreator;

          if (this.form.id) {
            // 编辑
            const res = await this.$api.UpdateTextbook(formData);
            if (res.errCode === 0) {
              this.$message.success("更新成功");
              this.dialogVisible = false;
              this.initTableData();
            }
            return;
          }
          const res = await this.$api.CreateTextbook(formData);
          if (res.errCode === 0) {
            this.$message.success("创建成功");
            this.dialogVisible = false;
            this.initTableData();
          }
        } else {
          console.log("表单验证失败");
          return false;
        }
      });
    },

    getMajorList() {
      this.$api.GetMajorList({ pageIndex: 1, pageSize: 100 }).then((res) => {
        this.majorList = res.data.items;
      });
    },

    getTeacherList() {
      // 获取教师列表
      this.$api.GetTeacherList({ pageIndex: 1, pageSize: 1000 }).then((res) => {
        if (!res.isError) {
          this.teacherList = res.data.items.filter(v=> v.enabled) || [];
        }
      });
    },

    handleRemove(file, fileList) {
      // 删除图片
      console.log(file, fileList);
    },

    handleAvatarSuccess(file) {
      // 预览图片
      this.imageUrl = `${file.data}?x-oss-process=image/resize,p_25`;; // 图片地址
      this.form.coverImage = file.data; // 图片地址
    },

    // 处理主编选择变化
    handleChiefEditorChange(value) {
      // 如果主编中包含了创作者中的人，则从创作者中移除
      this.form.creatorUserIds = this.form.creatorUserIds.filter(
        (id) => !value.includes(id)
      );
    },

    // 处理创作者选择变化
    handleCreatorChange(value) {
      // 如果创作者中包含了主编中的人，则从主编中移除
      this.form.chiefEditorUserIds = this.form.chiefEditorUserIds.filter(
        (id) => !value.includes(id)
      );
    },

    handlePass() {
      // 通过
      this.passDialog = false;
    },

    handleReject() {
      // 驳回
      this.rejectDialog = false;
    },
    // 发起审批流程
    handleStartFlow(row,chapterList){
      // textbook_section 章节,  textbook 教材
      if(this.tableConfig.searchParams.editorType === "ChiefEditor"){ // 主编的审批
        if(!row.defaultWFTemplateId){ //如果教材没有绑定审批模板
          // 选择一个模板
            this.$api.BizTemplateGetByBizId({
              bizId:row.id,
              bizTableName:"TextbookApproval"
            }).then(res=>{
              if(res.errCode==0){
                console.log(res.data);
                let jsonInfo = {
                  textBookName:row.name, // 教材名称
                  submitName:this.userInfo.user.name, // 提交人
                  category:row.majorCategory, // 教材类别
                }
                let params = {
                  "templateId": res.data,
                  "jsonInfo": JSON.stringify(jsonInfo),
                  "description": `${this.userInfo.user.name} ,提交 教材【${row.name}】的审核`,
                  "bizId": row.id, // 教材id
                  "bizTableName": bizTableType[this.tableConfig.searchParams.editorType]
                };
                this.$api.StartWorkflow(params).then(res=>{
                  if(res.errCode == 0){
                    this.$message.success("发起审批成功") 
                  }
                })
            } 
          })

        }else{

          let jsonInfo = {
            textBookName:row.name, // 教材名称
            submitName:this.userInfo.user.name, // 提交人
            category:row.majorCategory, // 教材类别
          }
          let params = {
            "templateId": row.defaultWFTemplateId,
            "jsonInfo": JSON.stringify(jsonInfo),
            "description": `${this.userInfo.user.name} ,提交 教材【${row.name}】的审核`,
            "bizId": row.id, // 教材id
            "bizTableName": bizTableType[this.tableConfig.searchParams.editorType]
          };
          this.$api.StartWorkflow(params).then(res=>{
            if(res.errCode == 0){
              this.$message.success("发起审批成功") 
            }
          })
        }
      }else{ // 创作者的审批
        if(chapterList.length == 0){
          this.$message.error("请选择章节进行提交")
          return;
        }
        chapterList.forEach((item,index)=>{
          let jsonInfo = {
            textBookName:row.name, // 教材名称
            submitName:this.userInfo.user.name, // 提交人
            category:row.majorCategory, // 教材类别
          }
          let params = {
            "templateId": 1,
            "jsonInfo": JSON.stringify(jsonInfo),
            "description": `${this.userInfo.user.name} ,提交 教材【${row.name}】 【${item.title}】的审核`,
            "bizId": item.id, // 章节id
            "bizTableName": bizTableType[this.tableConfig.searchParams.editorType]
          };
          this.$api.StartWorkflow(params).then(res=>{
            console.log(res)
          })
        })
      }
    },
    // 合并 nodes 和 records 数据的方法
    mergeNodesAndRecords(nodes, records) {
        return nodes.map(node => {
            const matchedRecord = records.find(record => record.instanceNodeId === node.id);
            if (matchedRecord) {
                return {
                    ...node,
                    ...matchedRecord,
                    // 移除不需要的属性
                    instanceId: undefined,
                    toDoTaskId: undefined
                };
            }
            return node;
        });
    }
  },
};
</script>

<style lang="scss" scoped>

.collaborator-list {
  max-width: 340px;
  max-height: 260px;
  overflow-y: auto;
}
.toggle-collaborators {
  color: #409eff;
  font-size: 14px;
  cursor: pointer;
  margin-left: 4px;
  &:hover {
    color: #0070fc;
  }
}
.textbook-manage-page {
  box-sizing: border-box;
  height: 100%;
  overflow: hidden;
  background: #F6F8FA;
  // 顶部标签页 - 简单文本样式
  .status-tabs {
    margin-bottom: 20px;
    height: 50px;
    line-height: 50px;
    background: #FFFFFF;
    .status-tab {
      width: 74px;
      color: #5C6075;
      cursor: pointer;
      display: inline-block;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      margin-right: 20px;
      &:nth-of-type(1){
        margin-left: 20px;
      }
      &.active {
        color: #0070fc;
        font-weight: 500;
        border-bottom: 1px solid #0070fc;
      }

      .tab-count {
        font-weight: bold;
        margin-left: 2px;
        color: inherit;
      }
    }
  }

  // 搜索工具栏
  .search-toolbar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin:0 20px 20px;
    position: relative;

    .search-input {
      width: 200px;
    }

    .select-box {
      width: 200px;
    }

    .toolbar-spacer {
      flex: 1;
    }

    .query-btn {
      width: 60px;
      padding:0;
      font-size: 15px;
      height: 38px;
      border-radius: 4px;
      background: #0070fc;
      border-color: #0070fc;
      color:#fff;

      &:hover {
        background: #005ce6;
        color:#fff;
        border-color: #005ce6;
      }
    }

    .reset-btn {
      width: 60px;
      padding:0;
      font-size: 15px;
      height: 38px;
      border-radius: 4px;
      margin-left: 0px !important;
      background: #fff;
      border-color: #fff;

      &:hover {
        background: #e9e9e9;
        border-color: #d9d9d9;
      }
    }

    .create-btn {
      background: #22c287;
      border-color: #22c287;

      &:hover {
        background: #1ea876;
        border-color: #1ea876;
      }
    }
  }

  // 表格样式
  .table-wrapper {
    background: white;
    max-height: 80vh;
    overflow-y: auto;
    margin: 0 20px;
    ::v-deep .top{
      display: none;
    }
    ::v-deep .el-table{
      background: #F6F8FA!important;
      .el-table__header-wrapper{
        margin-bottom: 10px!important;
        height: auto !important;
      }
      thead{
        margin-bottom: 10px;
      }
      .el-table__row{
        margin-top: 10px;
      }
      th.el-table__cell.is-leaf{
        background: #FFF!important;
        color: #333!important;
      }
    }
  }
}
.base-table .el-table > 

// 弹窗样式保持简洁
.course-cover-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #0070fc;
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
    background: #fafafa;
  }

  .avatar {
    width: 120px;
    height: 120px;
    display: block;
    object-fit: cover;
  }
}

// 下拉菜单
::v-deep .el-dropdown-menu {
  .el-dropdown-menu__item {
    font-size: 14px;

    &:hover {
      background-color: #f5f7fa;
      color: #0070fc;
    }
  }
}

.textbook-content {
  display: flex;
  align-items: center;
  min-height: 100px;
}
.textbook-cover {
  width: 100px;
  height: 130px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24px;
  flex-shrink: 0;
  overflow: hidden;
  cursor: pointer;
  .default-cover {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #4f8aff 0%, #3dcfff 100%);
    color: #fff;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 2px;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
}
.textbook-info {
  flex: 1;
  min-width: 0;
  .textbook-title {
    font-size: 20px;
    font-weight: bold;
    color: #222;
    margin-bottom: 12px;
    cursor: pointer;
  }
  .textbook-meta {
    font-size: 15px;
    color:#5C6075;
    line-height: 1.8;
    div {
      margin-bottom: 2px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.description-text {
  font-size: 14x;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
  white-space: pre-line;
  text-align: left;
  padding: 0 4px;
}
.action-link {
  color: #409eff;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  &:hover {
    color: #0070fc;
  }
  &:last-child {
    margin-right: 0;
  }
  .el-icon-arrow-down {
    margin-left: 4px;
    font-size: 12px;
  }
}
.status-highlight {
  color: #409eff !important;
}
.reject-dialog{
    width:500px;
    height:240px;
}
.pass-dialog{
  width:500px;
  height:190px;
}
</style>
