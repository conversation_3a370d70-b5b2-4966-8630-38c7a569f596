<template>
  <div class="textbook-manage-page">
    <div class="status-list">
      <p :class="['status-item',tableConfig.searchParams.editorType==item.value?'active':'']" @click="changeType(item)" v-for="item in statusList" :key="item.id" >{{item.label}}</p>
    </div>
    <section class="top-tool">
      <el-input class="serach-input" @blur="initTableData" clearable v-model="tableConfig.searchParams.searchWord" placeholder="请搜索教材名称" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select class="w-220 m-r-10" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.majorCategory" placeholder="请选择专业大类">
        <el-option v-for="item in categories" :key="item" :label="item" :value="item"></el-option>
      </el-select>
      <el-select class="w-220 m-r-10" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.wFApproveStatus" placeholder="请选择审核状态">
        <el-option v-for="item in wFApproveStatus" :key="item.id" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-select class="w-220 m-r-10" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.textbookOnLineStatus" placeholder="请选择发布状态">
        <el-option v-for="item in textbookOnLineStatus" :key="item.id" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <addButton @addEvent="addTextbook" />
    </section>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <baseDialog :noFooter="true" :showToScreen="false" :visible.sync="dialogVisible" width="720px" title="创建教材">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材名称" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col> 
          <el-col :span="12">
            <el-form-item label="所属专业大类"  prop="majorCategory">
              <el-select style="width:100%;" v-model="form.majorCategory">
                <el-option v-for="item in categories" :key="item" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="教材封面"  prop="coverImage">
          <el-upload
            class="avatar-uploader"
            :action="actionUrl"
            :show-file-list="false"
            :data="uploadData"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材主编" prop="publishedChiefEditor">
              <el-input v-model="form.publishedChiefEditor"></el-input>
            </el-form-item>
          </el-col> 
          <el-col :span="12">
              <el-form-item label="教材创作者" prop="publishedCreator">
                  <el-input v-model="form.publishedCreator"></el-input>
              </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="教材简介"  prop="description">
          <el-input type="textarea" v-model="form.description"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 

    <el-dialog :visible.sync="passDialog" custom-class="pass-dialog" title="提示">
      <p>确定要通过该申请吗？</p>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button style="width:80px;" type="default" @click="passDialog = false;">取消</el-button>
        <el-button style="width:80px;" type="primary" @click="handlePass">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="rejectDialog" custom-class="reject-dialog" title="提示">
      <p style="margin-bottom:10px;">确定要驳回该申请吗？</p>
      <el-input v-model="rejectText" placeholder="请填写驳回原因"></el-input>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button style="width:80px;" type="default" @click="rejectDialog = false;">取消</el-button>
        <el-button style="width:80px;" type="danger" @click="handleReject">拒绝</el-button>
      </div>
    </el-dialog>

    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
import { categories } from "@/constants/majoeCategories.js"
import { mapGetters } from "vuex";
import token from "@/utils/token.js";
export default {
  name: "TextbookPage",
  data() {
    return {
      statusList: [
        // 未出版教材 出版教材
        { id: 1, label: "我创建的",value:'ChiefEditor' },
        { id: 2, label: "我协作的",value:'HelperCreator' },
      ],
      wFApproveStatus: [
        // 待审核 驳回 通过 
        { id: 0, label: "全部",value: '' },
        { id: 1, label: "待审核",value: 'PENDING' },
        { id: 2, label: "驳回",value: 'REJECTED' },
        { id: 3, label: "通过",value: 'APPROVED'},
        // { id: 3, label: "取消",value: 'CANCELLED'},
      ],
      textbookOnLineStatus:[
        // 已下架 已上架
        { id: 0, label: "全部",value: '' },
        { id: 4, label: "已下架",value:'OffLine' },
        { id: 5, label: "已上架",value:'OnLine' },
      ],
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          {
            label: "教材名称",
            prop: "name",
          },
          {
            label: "简介",
            prop: "description",
          },
          {
            label: "版本号",
            prop: "currentOnlineVersion",
          },
          {
            label: "审核状态",
            prop: "wFApproveStatus",
            formatter: (row, column, cellValue) => {
              return row.wFApproveStatus == 1 ? '待审核' : row.wFApproveStatus == 2 ? '驳回' : '通过';
            }
          },
          {
            label: "发布状态",
            prop: "publishedChiefEditor",
            formatter: (row, column, cellValue) => {
              return row.textbookOnLineStatus == 'Online' ? '已上架' : '已下架';
            }
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
          {
            label: "教材封面",
            prop: "coverImage",
            formatter: (row, column, cellValue) => {
              return `<img src="${row.coverImage}" alt="封面" style="width: 50px; height: 50px;">`;
            }
          },
          
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 12,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          majorCategory: '', // 专业id
          editorType:'ChiefEditor', // 主编或协作者 ChiefEditor, HelperCreator
          textbookOnLineStatus:'', // 教材上下架状态 OnLine 上架 OffLine 下架
          wFApproveStatus: '', // 教材审核状态 PENDING, APPROVED, REJECTED, CANCELLED 1 待审核 2 驳回 3 通过
          meAsChiefEditor:'', // 教材主编
          meAsHelperCreator:'', // 教材创作者
          searchWord:'', // 搜索关键字,
          schoolId: ''
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        paginationLayout: 'total, sizes, prev, pager, next, jumper',
        notShowSearch: false,
        selectable: true,
        queryConfig: {},
      },
      // 补全模板中使用的变量
      firstLoad: true,
      loading: false,
      actionBtns: [
      ],
      operateBtns: [
        { name: '通过',class:'default-btn' },
        { name: '驳回',class:'del-btn' },
        { name: '审批流设置',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '详情',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 330,
      dialogVisible: false, // 新增教材弹窗
      actionUrl:window.FILEIP, // 上传图片地址
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      form: { // 新增教材表单
        id: "", // id
        name: "", // 教材名称
        majorCategory: "", // 所属专业大类
        publishedChiefEditor: "", // 教材主编
        publishedCreator: "", // 教材创作者
        coverImage: "", // 教材封面
        description: "", // 教材简介
        chiefEditorUserIds:[], // 教材主编
        creatorUserIds:[], // 教材创作者
      },
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      imageUrl:'',
      rules: { // 表单验证规则
        name: [
          { required: true, message: "请输入教材名称", trigger: "blur" }
        ],
        majorCategory: [
          { required: true, message: "请选择所属专业大类", trigger: "blur" } 
        ],
        publishedChiefEditor: [
          { required: true, message: "请输入教材主编", trigger: "blur" } 
        ],
        publishedCreator: [
          { required: true, message: "请输入教材创作者", trigger: "blur" }
        ],
        coverImage: [
          { required: true, message: "请上传教材封面", trigger: "blur" }
        ],
      },
      majorList: [], // 专业列表
      gradeList: [], // 教材列表
      categories: categories, // 专业大类


      passDialog:false,// 通过弹窗
      rejectText:'', // 驳回原因
      rejectDialog:false, // 驳回弹窗
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/textbookManage/components/detail.vue')
  },
  mounted() {
    this.getMajorList(); // 获取专业列表
    // this.getGradeList(); // 获取教材列表
    this.tableConfig.searchParams.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.initTableData(); // 初始化表格数据
  },
  computed: {
    ...mapGetters(["userInfo"]) // 从vuex中获取用户信息
  },
  methods: {
    // 初始化表格数据
    async initTableData(){
      console.log(this.tableConfig.searchParams);
      let res = await this.$api.GetMyTextbookList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
    },
    // 切换类型
    changeType(item){
      this.tableConfig.searchParams.editorType = item.value
      this.tableConfig.searchParams.pageIndex = 1; // 重置页码
      this.initTableData() // 初始化表格数据
    },
    addTextbook(){ // 新增课程
      this.dialogVisible = true; // 打开弹窗
      this.form = { // 重置表单
        id: "", // id
        name: "", // 教材名称
        majorCategory: "", // 所属专业大类
        publishedChiefEditor: "", // 教材主编
        publishedCreator: "", // 教材创作者
        coverImage: "", // 教材封面
        description: "", // 教材简介
        chiefEditorUserIds:[], // 教材主编
        creatorUserIds:[], // 教材创作者
      };
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateTextbook(this.form).then(res=>{
              console.log("课程详情",res)
              this.dialogVisible = false;
              this.initTableData()
            })
            return;
          }
          this.$api.CreateTextbook({
            coverImage: this.form.coverImage,
            name: this.form.name,
            majorCategory: this.form.majorCategory,
            description: this.form.description,
            isPublishedTextbook: false,
            chiefEditorUserIds:[683200065367941],
            helperCreatorUserIds:[683194530131845]
          }).then(res=>{
            console.log("新增课程",res)
            this.dialogVisible = false;
            this.initTableData()
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    getMajorList(){
      this.$api.GetMajorList({pageIndex:1,pageSize:100}).then(res=>{
        this.majorList = res.data.items;
      })
    },
    handleRemove(file, fileList) { // 删除图片
      console.log(file, fileList);
    },
    handleAvatarSuccess(file) { // 预览图片
      console.log(file);
      this.imageUrl = file.data; // 图片地址
      this.form.coverImage = file.data; // 图片地址
    },
    through(){ // 审核通过
      this.passDialog = true;
    },
    reject(){ // 驳回
      this.rejectDialog = true;
    },
    put(){ // 上架
    },
    off(){ // 下架
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case "通过":
          this.through();
          break;
        case "驳回":
          this.reject();
          break;
        case "审批流设置":
          this.handleSetFlow();
          break;
        case "详情":
          this.detailDialogVisible = true;
          this.detailInfo ={
            ...row,
            children:[
              {
                title:'历史版本',
                list:row.currentOnlineVersion||[],
                name:'first',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'版本号',prop:'version'},
                  {label:'版本描述',prop:'description'},
                ]
              }]
            }
          break;
        case "编辑":
          this.dialogVisible = true; // 打开弹窗
          // 给表单赋默认值
          this.form = {
            id: row.id,
            name: row.name,
            majorCategory: row.majorCategory,
            publishedChiefEditor: row.publishedChiefEditor,
            publishedCreator: row.publishedCreator,
            coverImage: row.coverImage,
            description: row.description,
            chiefEditorUserIds: row.chiefEditorUserIds || [],
            creatorUserIds: row.creatorUserIds || []
          };
          this.imageUrl = row.coverImage; // 设置封面图片预览
          break;
        case "删除":
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '删除后数据将不可恢复，请谨慎操作！',
          });
          this.$api.DeleteTextbook({id:row.id}).then(res=>{
            console.log("删除教材",res);
            this.initTableData();
          });
          break;
      }
    },
    handlePass(){ // 通过
      this.passDialog = false;
    },
    handleReject(){ // 驳回
      this.rejectDialog = false;
    },
    handleSetFlow(){ // 审批流设置
     
    },
  },
};
</script>

<style lang="scss" scoped>
.textbook-manage-page {
  padding: 20px;
  
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
    .btn{
      width: 80px;
      height: 38px;
      border-radius: 4px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      padding: 0;
      color: #333333;
      border-color: #E7E7E7;
    }
    .through-btn{
      margin-left: 10px;
      color: #07C392;
      border: 1px solid #07C392;
    }
   .reject-btn{}
   .put-btn{}
   .off-btn{}
  }

  .status-list{
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F2F3F5;
    height: 50px;
    line-height: 50px;
    .status-item{
      width: 90px;
      line-height: 46px;
      margin-right: 20px;
      font-size: 14px;
      text-align: center;
      cursor: pointer;
      color: #5C6075;
      &.active{
        color: #0070FC;
        font-weight: 500;
        border-bottom: 2px solid #0070FC;
      }
    }

  }
  .book-status{
    display: flex;
    align-items: center;
    height: 50px;
    margin-top: 10px;
   .status-item{
    width: 80px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    margin-right: 10px;
    background: #FFFFFF;
    border-radius: 15px;
    border: 1px solid #E7E7E7; 
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    cursor: pointer;
    &:hover{
      color: #0070FC;
      background: #F2F4FC;
      border-color: transparent;

    }
    &.active{
      color: #0070FC;
      background: #F2F4FC;
      border-color: transparent;
    }
   }
  }
}


</style>
<style lang="scss">

.pass-dialog{
  width:500px;
  height:190px;
}
.reject-dialog{
  width:500px;
  height:240px;
}
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
</style>