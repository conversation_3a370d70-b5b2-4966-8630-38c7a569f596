
<template>
  <div class="main">
    <!-- <header-primary ></header-primary> -->
    <router-view></router-view>
    <!-- <serverDialog /> -->
  </div>
</template>
<script>
// const HeaderPrimary = () => import('@/layout/components/header-primary.vue');
// import serverDialog from "@/components/server-dialog/serve-btn.vue"
export default {
  name: 'Home',
  components: {
    // HeaderPrimary,
    // serverDialog
  },
  data() {
    return {

    };
  },
  computed: {
  }
};
</script>

<style lang="scss" scoped>
.main{
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>