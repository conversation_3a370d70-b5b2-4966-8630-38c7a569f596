<!--
 * @Author: song <EMAIL>
 * @Date: 2024-02-20 14:41:22
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2025-04-12 15:07:30
 * @FilePath: \fusion_front\src\views\student\components\left-menu.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="student-left-menu">
      <div class="course-info" slot="reference">
        <span class="course-name">{{ courseInfo.name||courseInfo.courseName }}</span>
      </div>

    <div class="menu-list">
      <ul>
        <li class="course" :class="{ active: activeMenuItem === 'course' }" @click="redirectTo('course')">
          <i class="menu-icon iconfont icon-kecheng1"></i>
          <span>课程</span>
        </li>
        <li class="task" :class="{ active: activeMenuItem === 'task' }" @click="redirectTo('task')">
          <i class="menu-icon iconfont icon-zuoye"></i>
          <span>作业</span>
        </li>
        <li class="exam" :class="{ active: activeMenuItem === 'exam' }" @click="redirectTo('exam')">
          <i class="menu-icon iconfont icon-renwu"></i>
          <span>考试</span>
        </li>
        <li class="resource" :class="{ active: activeMenuItem === 'resource' }" @click="redirectTo('resource')">
          <i class="menu-icon iconfont icon-shuji"></i>
          <span>资源</span>
        </li>
        <li class="sign" :class="{ active: activeMenuItem === 'sign' }" @click="redirectTo('sign')">
          <i class="menu-icon iconfont icon-shuji"></i>
          <span>签到</span>
        </li>
      </ul>
    </div>

    <div :class="{'coursedtails':true ,'show-course':isShow || showBox}" @mouseover="showBox = true" @mouseout="showBox = false">
      <div class="courseIcon">
        <div>
          <img :src="courseDtailsInfo.images || 'static/img/cover.png'" alt="">
        </div>
      </div>
      <h4>{{ courseDtailsInfo.name }}</h4>
      <div class="courseIntroduce">
        <span :class="showAll?'introduce':''">课程简介: {{ courseDtailsInfo.intro }}</span>
      </div>
      <div class="but" v-if="showAll">
        <el-button type="text" @click="showAll = false">展开更多<i class="el-icon-d-arrow-right el-icon--right" style="transform: rotate(90deg)" ></i></el-button>
      </div>
    </div>

  </div>
</template>

<script>
import { setCurrentCaseId} from "@/utils/token.js" 
import {mapGetters} from 'vuex'
export default {
  name: "student-left-menu",
  props: {
    activeMenu: {
      type: String
    },
  },
  components: {},
  
  created() {
  },
  mounted() {
  },
  data() {
    return {
      currentCourseId: null,
      currentCourseName: "",

      activeMenuItem: "course",

      courses: [],
      courseDtailsInfo:'',
      showBox:false,
      isShow: false,
      showAll: true
    };
  },
  watch: {
  activeMenu: {
    handler(val) {
      if (val) {
        this.activeMenuItem = val;
      }
    },
    immediate: true
  }
  },
  computed: {
    ...mapGetters({
      courseInfo:['getCourseInfo']
    })
  },
  methods: {
    CourseInfo(obj){
      this.courseDtailsInfo = obj
      this.isShow = true
      this.showAll = true
    },
    redirectTo(routerName) {
      this.activeMenuItem = routerName;
      let path = "";
      switch (routerName) {
        case "course":
          path = "/studentCourse";
          break;
        case "task":
          path = "/studentTask";
          break;
        case "exam":
          path = "/studentExam";
          break;
        case "resource":
          // path = "/studentResource";
          path = "/studentMaterial";
          break;
        case "sign":
          path = "/StudentSign";
          break
      }

      this.$router.push({
        path: path
      });
    }
  }
};
</script>

<style lang="scss">
.el-popover.my-popovers{
  padding: 0;
  left: 10px !important;
  width: 251px !important;
  ul li {
    height: 34px;
    line-height: 34px;
    text-align: center;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: #467bfb;
      text-decoration: underline;
    }

    &.active {
      color: #467bfb;
      text-decoration: underline;
    }
  }
}
.student-left-menu {
  width: 260px;
  background: #ffffff;
  box-shadow: 0px 0px 13px 1px rgba(0, 0, 0, 0.11);
  opacity: 1;
  height: 100%;
  position: relative;
  .coursedtails{
    position: absolute;
    width: 478px;
    // height: 344px;
    background: #FFFFFF;
    border-radius: 0px 10px 10px 10px;
    border: 1px solid #D3DFFF;
    top:33px;
    left: 260px;
    display: none;
    z-index: 1000;
    box-shadow: 2px 2px 5px 3px #e9efff;
    padding: 30px 30px 20px 30px;
    .courseIcon{
      width: 265px;
      height: 149px;
      background: linear-gradient( 75deg, #FFFFFF 0%, #3D73FB 55%, #D4E4FE 100%);
      margin: auto;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      >div{
        width: 257px;
        height: 141px;
        background-color: white;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        >img{
          height: 90%;
        }
      }
    }
    h4{
      text-align: center;
      margin: 20px 0;
    }
    .courseIntroduce{
      font-size: 14px;
      color: #333333;
      line-height: 28px;
      .title{
        color: #979797;
        // display: inline-block;
      }
      .introduce{
        overflow:hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
    }
    .but{
      display: flex;
      align-items: center;
      justify-content: center;
      .el-button{
        color: #F56A37;
        :hover{
          color: #f59977;
        }
      }
    }
  }
  .show-course{
    display: block;
  }
  .course-info {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    margin: 33px 0 0 9px;
    padding: 35px 15px 0 36px;
    width: 251px;
    height: 108px;
    border-radius: 10px 0px 0px 10px;
    opacity: 1;
    background: url(../../../assets/scene/title.png) no-repeat center/cover;

    .course-name {
      font-size: 18px;
      font-weight: 800;
      color: #ffffff;
      line-height: 24px;
    }

    .course-change {
      color: #fff;
      transform: rotateZ(90deg);
      border: 1px solid #fff;
      border-radius: 50%;
      width: 23px;
      height: 23px;
      padding: 2px;
      cursor: pointer;
    }
  }

  .menu-list {
    margin-top: 64px;

    li {
      display: flex;
      align-items: center;
      padding-left: 51px;
      height: 50px;
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      cursor: pointer;
      caret-color: transparent;

      &:hover {
        background: #f3f8ff;
      }

      .menu-icon {
        display: flex;
        align-items: center;
        width: 20px;
        height: 21px;
        font-size: 20px;
        margin-right: 10px;
      }

      &.active {
        background: #f3f8ff;
        color: #2b61fa;

        .menu-icon {
          font-weight: 500;
        }


      }

      img {
        width: 15px;
        height: 15px;
      }

      span {
        margin-left: 16px;
        line-height: 50px;
      }
    }
  }
}
</style>
