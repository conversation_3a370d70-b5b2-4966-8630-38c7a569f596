<template>
  <div class="Index-box">
    <div class="greetings">{{ userInfo.realName }}同学 ,{{ greeting }} ^_^</div>
    <div class="top-background">
      <div></div>
      <div></div>
    </div>
    <div class="index-content">

      <div class="content-top">
        <div class="wodekech">
          <div class="middle" @click="Jump('middle')">
            <p>我的课程</p>
            <p>查看课程</p>
            <img src="../../assets/public/home/<USER>" alt="">
          </div>
          <div class="sign-box" v-if="selectCourse.courseName">
            <!-- <span>最近课程</span> -->
            <div class="select-courses">
              <!-- <el-select v-model="selectCourse" placeholder="">
                <el-option
                  v-for="item in courseList"
                  :key="item.courseId"
                  :label="item.courseName"
                  :value="item.courseId">
                </el-option>
              </el-select> -->
              <el-input v-model="selectCourse.courseName" :disabled="true" placeholder=""></el-input>
              <el-button plain @click="goStudentSign">立即签到<i class="el-icon-arrow-right"></i></el-button>
            </div>
          </div>
          <div @click="Jump('discuss')" class="discuss">
            <p>讨论交流</p>
            <p>查看讨论</p>
            <img src="../../assets/public/home/<USER>" alt="">
          </div>
          <!-- <div @click="Jump('questionnaire')" class="discuss">
            <p>问卷</p>
            <p>查看问卷</p>
            <img src="../../assets/public/home/<USER>" alt="">
          </div> -->
        </div>
        <!-- <div class="homework">作业错题</div> -->
        <div class="cneter">
          <div @click="caseDetails" class="case">
            <!-- <router-link to="/StudentCase" class="case"> -->
            <p>企业案例</p>
            <p>查看案例</p>
            <img src="../../assets/public/home/<USER>" alt="" />
            <!-- </router-link> -->
          </div>
          <router-link to="/studentTask" class="right">
            <p>
              <el-badge :value="tasksCount" class="item" v-if="tasksCount != 0">
                我的任务
              </el-badge>
              <span v-else>我的任务</span>
            </p>
            <p>查看任务</p>
            <img src="../../assets/public/home/<USER>" alt="" />
          </router-link>
        </div>
        <div class="one">
          <div class="one-Course" @click="Jump('one-Course')">
          <!-- <div class="one-Course" @click="Jump('questionnaire')"> -->
            <!-- <router-link to="/studentResource"> -->
            <p>课程资源</p>
            <p>查看资源</p>
            <!-- <p>课程问卷</p>
            <p>查看问卷</p> -->
            <div class="studet-resource"></div>
            <!-- </router-link> -->
          </div>
          <div class="one-Message" @click="uncultivated">
            <!-- <router-link to="/studentMessage"> -->
            <p>我的消息</p>
            <p>查看消息</p>
            <div class="studet-message"></div>
            <!-- </router-link> -->
          </div>
        </div>

      </div>
      <div class="content-bottom">
        <div class="middle-d"></div>
        <div class="right-d"></div>
        <div class="one-d"></div>
      </div>
    </div>
    <div class="bottom-background"></div>
    <!-- <div class="footer-text">
      湖南中德安普大数据网络科技有限公司版权所有@2023
    </div> -->
    <!-- 讨论列表 -->
    <discussionList v-if="isShowDiscussionList" :isShowDiscussionList.sync="isShowDiscussionList"></discussionList>
  </div>
</template>

<script>
import discussionList from '@/components/discussion/discussion-list.vue' // 主题列表
import questionnaireList from '@/views/questionnaires/list.vue' // 主题列表
import { mapGetters } from "vuex";
import { setCurrentCaseId} from "@/utils/token.js" 
export default {
  name: "studnetHome",
  props: {},
  components: {
    discussionList,// 主题列表
    questionnaireList,// 问卷列表
  },
  data() {
    return {
      courseList: [],
      selectCourse: {},
      tasksCount: 0,
      isShowDiscussionList: false,// 是否展示讨论列表
      currentTime: new Date().getHours(), // 获取当前小时数
      haveExam: false
    };
  },
  watch: {
    // "$route": {
    //   handler(val) {
    //     debugger
    //     console.log(val, 'route:路由');
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"]
    }),
    greeting() {
      if (this.currentTime >= 5 && this.currentTime <= 9) {
        return "早上好";
      } else if (this.currentTime > 9 && this.currentTime <= 11) {
        return "上午好";
      } else if (this.currentTime > 11 && this.currentTime <= 14) {
        return "中午好";
      } else if (this.currentTime > 14 && this.currentTime <= 18) {
        return "下午好";
      } else {
        return "晚上好";
      }
    },
  },

  mounted() {
    sessionStorage.removeItem("courseId");
    this.studentCheckIn()
    this.InProgressTasks()
    this.StudentTaskExamCount()
    // console.log("userInfo",this.userInfo);
  },

  methods: {
    // 学生查看主题讨论
    openMessage() {
      this.isShowDiscussionList = true;
    },
    async Jump(val) {
      console.log('haveExam', this.haveExam);
      if (this.haveExam) {
        this.$message('当前为考核任务模式，请进入【我的任务】完成指定考核任务。')
      } else {
        if (val == 'middle') { // 学生课程
          // this.$router.push('/studentCourse')
          this.$router.push('/studentCourseList')
        } else if (val == 'discuss') { // 讨论
          this.isShowDiscussionList = true;
        } else if (val == 'questionnaire') { // 问卷调查
          this.$router.push('/studentQuestionnaire')
        } else if (val == 'one-Course') { // 课程资源
          // 获取课程列表 默认取第一门课程 然后再跳转到课程资源页面
          let { data, code } = await this.$api.getCourseList();
          if(data.length!=0){
            let courseInfo = data[0];
            sessionStorage.setItem('courseId',courseInfo.id);
            sessionStorage.setItem('courseInfoId',courseInfo.id);
            this.$store.commit("setCourseInfo", courseInfo); // 存储课程信息
            setCurrentCaseId(courseInfo.caseId);// 存储课程绑定的案例
            this.$router.push('/studentMaterial')
          }else{
            this.$message('暂无课程信息')
          }
          // this.$router.push('/studentResource')
        }
      }
    },

    caseDetails() {
      this.$router.push({ path: '/caseList', query: {} })
      // let routeUrl = this.$router.resolve({ path: '/case-list', query: {} });
      // window.open(routeUrl.href, true);
      setTimeout(()=>{
        this.$router.go(0)
      },100)
    },
    uncultivated() {
      this.$message('该功能暂未开发!')
    },
    async StudentTaskExamCount() {
      let res = await this.$api.StudentTaskExamCount({})
      if (res.code == 200) {
        if (res.data >= 1) {
          this.haveExam = true
        }
      }
    },
    async studentCheckIn() {
      let res = await this.$api.GetStudentCourseSignin({})
      // console.log(res);
      if (res.code == 200) {
        this.courseList = res.data
        if (this.courseList[0]) {
          this.selectCourse = this.courseList[0]
        }
      }
    },
    goStudentSign() {
      if (this.selectCourse) {
        sessionStorage.setItem('selectCourse', JSON.stringify(this.selectCourse))
        this.$router.push({ path: '/StudentSign', query: { courseId: this.selectCourse.courseId } })
      } else {
        this.$message('无签到任务!')
      }
    },
    async InProgressTasks() {
      let res = await this.$api.StudentTaskNotCount({})
      if (res.code == 200) {
        this.tasksCount = res.data
      }
    }
  }
};
</script>

<style lang="scss" scoped>
$topBackWidth: 1394px;

.Index-box {
  width: 100vw;
  height: calc(100vh - 50px);
  position: relative;

  .greetings {
    position: absolute;
    top: 60px;
    left: 90px;
    z-index: 1000;
    color: #979797;
    font-size: 16px;
  }

  .top-background {
    position: fixed;
    width: 1393px;
    height: 330px;
    background: #f8f9ff;
    border-radius: 0 0 30px 30px;
    left: calc(50vw - 700px);
    overflow: hidden;

    >div {
      width: 446px;
      height: 446px;
      background: #dde2ff;
      border-radius: 225px;
      position: absolute;

      &:nth-child(1) {
        left: 168px;
        top: -320px;
        z-index: 100;
      }

      &:nth-child(2) {
        background: #ebeeff;
        left: 446px;
        top: -340px;
      }
    }
  }

  .index-content {
    width: 1190px;
    margin-top: 217px;
    height: 910px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: auto;
    // background: #6670ac;
    position: relative;
    top: 168px;
    z-index: 1000;

    .content-top {
      width: 100%;
      height: 450px;
      display: flex;
      justify-content: space-between;

      .wodekech {
        position: relative;
        height: 450px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .sign-box {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          position: absolute;
          top: 200px;
          left: 66px;
          z-index: 1000;
          left: 50%;
          transform: translateX(-50%);

          >span {
            color: #333333;
            font-size: 14px;
            display: block;
            margin-left: 20px;
            margin-bottom: 10px;
          }

          .select-courses {
            display: flex;

            ::v-deep .el-input__suffix {
              display: none;
            }

            ::v-deep .el-input__inner {
              width: 270px;
              border: 1px solid #3266FA;
              border-radius: 7px 0 0 7px;
            }

            .el-button {
              height: 36px;
              border: none;
              border-radius: 0 7px 7px 0;
            }

            .el-select {
              width: 270px;
              border: none;

              .el-input__suffix {
                display: none;
              }
            }
          }

          .el-button {
            padding: 0 10px;
            background: linear-gradient(96deg, #3266FA 0%, #7891FB 100%);
            color: white
          }
        }
      }

      .cneter {
        height: 450px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .right,
        .case {
          width: 340px;
          height: 165px;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          position: relative;
          cursor: pointer;

          &:hover p {
            text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
          }

          p {
            color: white;
            -webkit-background-clip: text;
            cursor: pointer;

            &:nth-child(1) {
              font-size: 26px;
              margin: 30px 0 0 40px;
            }

            &:nth-child(2) {
              font-size: 16px;
              margin: 8px 0 0 40px;
            }
          }

          >img {
            width: 135px;
            height: 144px;
            position: absolute;
            right: 42px;
            bottom: 10px;
            transform: translateX(0);
            transition: transform 0.5s ease;
          }
        }

        .right {
          background: url("../../assets/public/home/<USER>");

          ::v-deep .el-badge__content.is-fixed {
            top: 0;
            right: 10px;
            background: white;
            color: #ad7048;
            border: #ad7048;
          }
        }

        .case {
          height: 270px;
          background: url("../../assets/public/home/<USER>");

          >img {
            width: 150px;
            height: 160px;
            right: 27px;
            bottom: 47px;
            transform: translateX(0);
            transition: transform 0.5s ease;
          }

          p {
            color: white;
            -webkit-background-clip: text;

            &:nth-child(1) {
              font-size: 26px;
              margin: 70px 0 0 40px;
              transform: translateX(0);
              transition: transform 0.5s ease;
            }

            &:nth-child(2) {
              font-size: 16px;
              margin: 8px 0 0 40px;
              transform: translateY(0);
              transition: transform 0.5s ease;
            }
          }
        }

        .case:hover>img {
          transition: transform 0.5s ease;
          transform: translate(6%, -5%);
        }

        .right:hover>img {
          transition: transform 0.5s ease;
          transform: translate(6%, -5%);
        }

        .right:hover p,
        .case:hover p {
          &:nth-child(1) {
            transform: translateX(6%);
            transition: transform 0.5s ease;
          }

          &:nth-child(2) {
            transform: translateY(15%);
            transition: transform 0.3s ease;
          }
        }

        .right:hover .el-badge__content.is-fixed {
          transform: translateX(6%);
          transition: transform 0.5s ease;
        }
      }

      .one {
        width: 340px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .one-Course {
          height: 217px;
          cursor: pointer;
          background: url("../../assets/public/home/<USER>") no-repeat 0px 0px;

          .studet-resource {
            width: 230px;
            height: 136px;
            position: relative;
            top: -28px;
            left: 90px;
            background: url("../../assets/public/home/<USER>") no-repeat 0px 0px;
            transition: transform 0.5s ease;
            transform: translate(0, 0);
          }
        }

        .one-Message {
          height: 217px;
          cursor: pointer;
          background: url("../../assets/public/home/<USER>") no-repeat 0px 0px;

          .studet-message {
            width: 152px;
            height: 172px;
            position: relative;
            top: -65px;
            left: 157px;
            background: url("../../assets/public/home/<USER>") no-repeat 0px 0px;
            transition: transform 0.5s ease;
            transform: translate(0, 0);
          }
        }

        &:hover p {
          text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        p {
          color: white;
          margin-left: 40px;
          -webkit-background-clip: text;

          &:nth-child(1) {
            font-size: 26px;
            margin-top: 40px;
            transform: translateX(0);
            transition: transform 0.5s ease;
          }

          &:nth-child(2) {
            font-size: 16px;
            margin-top: 8px;
            transform: translateY(0);
            transition: transform 0.3s ease;
          }
        }

      }

      .one-Course:hover .studet-resource {
        transition: transform 0.5s ease;
        transform: translate(5%, -5%);

      }

      .one-Course:hover p {
        &:nth-child(1) {
          transform: translateX(6%);
          transition: transform 0.5s ease;

        }

        &:nth-child(2) {
          transform: translateY(15%);
          transition: transform 0.3s ease;
        }
      }

      .one-Message:hover .studet-message {
        transition: transform 0.5s ease;
        transform: translate(5%, -5%);

      }

      .one-Message:hover p {
        &:nth-child(1) {
          transform: translateX(6%);
          transition: transform 0.5s ease;

        }

        &:nth-child(2) {
          transform: translateY(15%);
          transition: transform 0.3s ease;
        }
      }

      .middle,
      .discuss {
        width: 456px;
        height: 269px;
        background: url("../../assets/public/home/<USER>")no-repeat;
        display: flex;
        flex-direction: column;
        position: relative;
        cursor: pointer;

        &:hover p {
          text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        p {
          color: white;
          -webkit-background-clip: text;
          cursor: pointer;

          &:nth-child(1) {
            font-size: 26px;
            margin: 70px 0 0 40px;
            transform: translateX(0);
            transition: transform 0.5s ease;
          }

          &:nth-child(2) {
            font-size: 16px;
            margin: 8px 0 0 40px;
            transform: translateY(0);
            transition: transform 0.3s ease;
          }
        }

        >img {
          width: 267px;
          height: 144px;
          position: absolute;
          top: 42px;
          right: 20px;
          transform: translateY(0);
          transition: transform 0.3s ease;
        }

      }

      .middle:hover>img,
      .discuss:hover>img {
        transition: transform 0.5s ease;
        transform: translate(5%, -5%);
      }

      .discuss {
        width: 100%;
        height: 165px;
        background: url('../../assets/public/home/<USER>');
        position: relative;

        p {
          color: white;
          -webkit-background-clip: text;

          &:nth-child(1) {
            font-size: 26px;
            margin: 30px 0 0 40px;
          }

          &:nth-child(2) {
            font-size: 16px;
            margin: 8px 0 0 40px;
          }
        }

        img {
          width: 166px;
          height: 130px;
          position: absolute;
          top: 25px;
          right: 52px;
        }
      }

      .middle:hover p,
      .discuss:hover p {
        &:nth-child(1) {
          transform: translateX(6%);
          transition: transform 0.5s ease;

        }

        &:nth-child(2) {
          transform: translateY(15%);
          transition: transform 0.3s ease;
        }
      }

      .right {
        width: 340px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        background: url("../../assets/public/home/<USER>");

        &:hover p {
          text-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        p {
          color: white;
          -webkit-background-clip: text;
          transform: translateX(0);
          transition: transform 0.5s ease;

          &:nth-child(1) {
            font-size: 26px;
            margin: 75px 0 0 40px;
            transform: translateX(0);
            transition: transform 0.5s ease;
          }

          &:nth-child(2) {
            font-size: 16px;
            margin: 8px 0 0 40px;
            transform: translateY(0);
            transition: transform 0.3s ease;
          }
        }

        .studet-quest {
          width: 100%;
          height: 100%;
          top: -4%;
          position: relative;
          left: 24%;
          background: url("../../assets/public/home/<USER>") no-repeat;
          transition: transform 0.5s ease;
          transform: translate(0, 0);
        }
      }

      .right:hover .studet-quest {
        transition: transform 0.5s ease;
        transform: translate(5%, -5%);
      }

      .right:hover p {
        &:nth-child(1) {
          transform: translateX(6%);
          transition: transform 0.5s ease;

        }

        &:nth-child(2) {
          transform: translateY(15%);
          transition: transform 0.3s ease;
        }
      }
    }

    .content-bottom {
      width: 100%;
      height: 450px;
      display: flex;
      justify-content: space-between;

      .one-d {
        width: 340px;
        height: 217px;
        border-radius: 20px;
        background: linear-gradient(180deg,
            #c4c4ee 0%,
            rgba(196, 196, 238, 0) 100%);
        opacity: 0.19;
      }

      .middle-d {
        width: 457px;
        height: 100%;
        background: url("../../assets/public/home/<USER>");
      }

      .right-d {
        width: 340px;
        height: 100%;
        background: url("../../assets/public/home/<USER>");
      }
    }
  }

  .bottom-background {
    width: 100vw;
    height: 256px;
    background: url("../../assets/public/home/<USER>") no-repeat center bottom/cover;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .footer-text {
    position: fixed;
    bottom: 70px;
    width: 100vw;
    display: flex;
    justify-content: center;
    color: #666666;
  }
}
</style>
