<!--
 * @Author: ztong <EMAIL>
 * @Date: 2024-05-10 13:35:18
 * @LastEditors: lihongwang <EMAIL>
 * @LastEditTime: 2024-09-11 15:57:01
 * @FilePath: \fusion_front\src\views\student\exam\index.vue
 * @Description: 学生任务列表
-->
<template>
  <div class="content-page student-exam-page">
    <LeftMenu :activeMenu="'exam'" @handleExamData="handleExamData"></LeftMenu>
   
    <div class="content-box" style="width:calc(100vw - 260px)">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/studentCourseList' }">课程</el-breadcrumb-item>
        <el-breadcrumb-item>课程详情</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="content-wrapper exam-content-wrapper">
        <div class="content exam-content">
          <studentsTask :taskCategory="3" ref="examchild"></studentsTask>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LeftMenu from '../components/left-menu.vue';
import studentsTask from '@/components/students-task/index';
export default {
  name: 'studentExam',
  mixins: [],
  components: {
    LeftMenu, studentsTask
  },
  props: {},
  data() {
    return {
    }
  },
  computed: {},
  watch: {},
  created() { },
  mounted() { },
  methods: {
    handleExamData(){
      this.$refs.examchild.loadList()
    }
  }
}
</script>

<style scoped lang="scss">
.student-exam-page {
  .content {
    flex: 1 1 auto;
  }
  .content-box,
  .content,
  .content-wrapper{
    display:block!important;
  }
    
  .content-wrapper{
    height: 100%;
  }
}
</style>
