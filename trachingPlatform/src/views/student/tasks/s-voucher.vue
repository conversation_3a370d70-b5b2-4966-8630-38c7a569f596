<!--
 * @Author: song <EMAIL>
 * @Date: 2025-01-08 10:43:51
 * @LastEditors: song <EMAIL>
 * @LastEditTime: 2025-01-14 14:18:38
 * @FilePath: \fusion_front\src\views\student\tasks\s-voucher.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>

    <div  class="q-question-comps">
      <questionpreview :data="infoContent" ref="vocherFr"  :sort="sort" :config="config" ></questionpreview>
    </div>

</template>

<script>
import questionpreview from './components/vochersort/question-preview-content.vue'
import { initApiQuestion } from '@/components/question/utils.js';
export default {
  components: {
    questionpreview
  },
  props: {
    config:{
      type: Object,
      default: () => {}
    },
    info: {
      type: Object,
      default: () => {}
    },
    sort: {
      type: Number,
      default: 0
    },
    // 是否只读
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    info: {
      handler(val) {
        this.infoContent=val
      },
      immediate: true,
    }
  },
  computed: {
    // infoContent: function () {
    //   return initApiQuestion(this.info)
    // }
  },
  data () {
    return {
      checkStatus:1,
      infoContent:{}
  
    }
  },
  mounted () {
  },
  methods: {
    getinit(){
      alert(1)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.common-info) {
  display: none !important;
}
:deep(.readOnly) {
  display: none !important;
}
:deep(.divider) {
  display: none !important;
}
:deep(.add-scene) {
  display: none !important;
}
.q-question-comps {
  padding: 20px 50px;
  :deep(.type-text) {
    display: none;
  }
}
</style>