<!--
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-27 14:48:11
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-12-16 16:45:32
 * @FilePath: \fusion_front\src\views\student-page\tasks\component\questionDetail.vue
 * @Description: 
-->
<template>
  <div>
    <!-- 动态表单 -->
    <outerForm
      v-if="!loading && questionInfo.contentData?.content?.sceneType === 1 && questionInfo.contentData?.content?.formType == 1"
      :dataId="dataId"
      :type="this.questionInfo.isReadonly?'detail':'edit'"
      :moduleId="questionInfo.contentData?.content?.id"
      :getSingleData="getSingleData"
      ref="dfsForm"
      :isHideSubmit="true"
      :submitData="submitData"
      />

    <!-- 复杂表单 -->
    <preview-spread
      v-if="questionInfo.contentData?.content?.sceneType === 1 && questionInfo.contentData?.content?.formType == 2"
      ref="spreadQuestion"
      :isDoTask='true'
      :templateObj="templateObj"
      :templateIdData="templateIdData"
      :templateData="templateData">
    </preview-spread>

    <!-- 流程 -->
    <section
      style="height: 600px"
      v-if="questionInfo.contentData?.type === 1 && isShowFlow">
      <PagePreview
        :data="flowConfig"
        @nodeClick="previewNodeClick">
      </PagePreview>
    </section>

    <!-- 知识点 -->
    <section
      v-if="questionInfo.contentData?.type === 5"
      class="known-info">
      <section class="info-container">
        <p v-html="questionInfo?.contentData?.content.description"></p>
      </section>
    </section>
    <!-- 素材 -->
    <section
      v-if="questionInfo.contentData?.type === 6"
      class="material-info">
      <section
        v-if="isTransition"
        class="info-container"
        :style="{ height: isFullScreen ? screenHeight : '500px' }">
        <video
          v-if="handleIsVideo(questionInfo.contentData.content.file) == 'video'"
          controls
          :height="isFullScreen ? screenHeight : 500"
          width="100%">
          <source :src="questionInfo.contentData.content.file" />
        </video>
        <section
          v-else-if="handleIsVideo(questionInfo.contentData.content.file) == 'image'"
          class="img-box"
          :style="{ height: isFullScreen ? screenHeight : '500px' }">
          <img
            :src="questionInfo.contentData.content.file"
            alt="" />
        </section>
        <iframe
          v-else
          width="100%"
          title="文件预览"
          :height="isFullScreen ? screenHeight : '500px'"
          :src="showPreview(questionInfo.contentData.content.file)"></iframe>
      </section>
    </section>
    <!-- 附件场景 -->
    <section class="file-scene" v-if="questionInfo?.type === 3">
      <showAttachment :attachmentId="questionInfo.contentData.content.id"></showAttachment>
    </section>
    <!-- 核算 -->
    <!-- 文档 -->
    <section
      v-if="questionInfo?.type === 10"
      class="known-info">
      <section class="info-container">
        <p v-html="questionInfo.contentData.content.content"></p>
      </section>
    </section>

    <!-- 只读题的讨论框 -->
    <div class="discussion" v-if="questionInfo.isReadonly==1&&questionInfo.contentData">
      <el-input type="textarea" placeholder="请输入自己的见解" v-model="discussion">
      </el-input>
    </div>
  </div>
</template>

<script>
const previewSpread = () => import("@/components/spread-js/preview-spread.vue");
const PagePreview = () => import("@/components/designer/antv-g6-editor/page-preview.vue");
const outerForm = ()=>import("@/components/designer/form-designer/outer-usage.vue");
const showAttachment = () => import("@/components/attachment/show-attachment.vue"); // 附件展示

import { showPreview } from "@/tool/filePreview.js";
import {getBindingPathKeys} from "@/tool/spreadTool.js";
import { fromOADate,isOADate, isISO8601, convertToBeijingTime,isBeijingTimeFormat} from "@/tool/spread-common.js";
import { arrayToObject } from "@/utils/base";
import { validateNumber2 } from '@/utils/validate';
export default {
  name: "",
  components: {
    previewSpread,
    PagePreview,
    outerForm,
    showAttachment,// 附件展示
  },
  props: {
    questionInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false, //
      formVal: "", // 动态表单
      formCode: "", // 动态表单
      templateObj: {}, // spreadJS 模版
      templateData: {}, // spreadJS 数据
      templateIdData: {}, // 模板的数据idkey

      flowConfig: {
        //流程图数据
      },
      isShowFlow: false, // 显示流程的标识

      isTransition: true,
      isFullScreen: false, // 是否全屏
      screenHeight: document.body.clientHeight - 90 + "px", // 全屏高度'
      taskAnswerInfo:{},//答题的权限信息（答案得分提示）
      dfsFormAnswer:{},// 动态表单答案

      discussion:'',// 讨论的答案 
      checkKeyList:[],// 设置的考核点
    };
  },
  computed: {},
  watch: {
    questionInfo: {
      handler(val) {
        if(val.resourceType==2||(val.resourceType==0&&val.isReadonly==1)){
          this.dataId = this.questionInfo.contentData?.content?.dataId|| this.questionInfo.contentData?.content?.formVal?.id;
        }
        if(val.isReadonly==1&&val.gradeDetails&&val.gradeDetails.length>0){  // 只读题的答案
          console.log('val.gradeDetails',val.gradeDetails[0]);
          this.discussion = val.gradeDetails[0].answer;
          if(this.questionInfo.unAnswerData){ // 设置考核点后的数据模版
            let {answerTemplate,checkKeyList} = JSON.parse(this.questionInfo.unAnswerData);
            this.templateData = answerTemplate;
            this.checkKeyList = checkKeyList; // 设置的考核点
          }
        }

        if(val.type === 1){ // 流程
          this.renderFlow(val.contentData?.content.id);
        }else { // 动态表单和spread
          this.renderForm(val.contentData?.content);
        }
      },
      immediate: true,
      deep: true
    }
  },
  created(){
    this.taskAnswerInfo = JSON.parse(sessionStorage.getItem('taskAnswerInfo')||'{}');
  },
  methods: {
    /**
     * 查看文件
     */
    showPreview,
    // 流程
    previewNodeClick(obj) {
      console.log("obj", obj);
    },
    async renderForm(formObj) {
      if (!formObj) return;
      if (formObj.sceneType === 1 && formObj.formType === 1) {
        // 动态表单
        // const formId = formObj.id;
        // const res = await this.$api.GetDynamicById({
        //   id: formId
        // });

      } else if (formObj.sceneType === 1 && formObj.formType === 2) {
        // spreadJS
        const { data } = await this.$api.GetDynamicById({ id: formObj.id });
        this.templateObj = data;

        // 判断如果当前是否是只读题  默认显示数据
        if(this.questionInfo.isReadonly){ 
          // this.templateData = JSON.parse(this.questionInfo.answer);
          if(this.questionInfo.unAnswerData){ // 设置考核点后的数据模版
            let {answerTemplate,checkKeyList} = JSON.parse(this.questionInfo.unAnswerData);
            this.templateData = answerTemplate;
            console.log('this.templateData',this.templateData);
            this.checkKeyList = checkKeyList; // 设置的考核点
          }
        }else{
          // spread 题目 构建初始化数据
          this.templateData = await this.initSpreadData(formObj.erId);
          if (this.questionInfo.gradeDetails && this.questionInfo.gradeDetails?.length != 0) {       // 作答记录
            // const templateData = this.questionInfo.gradeDetails[0]?.answer;
            // if(this.questionInfo.unAnswerData){
              let {answerTemplate,checkKeyList} = JSON.parse(this.questionInfo.unAnswerData);
              this.checkKeyList = checkKeyList; // 设置的考核点
              // 合并答题记录和 answerTemplate
              const templateData = this.handleConcatAnswer(answerTemplate,this.questionInfo.gradeDetails[0]?.answer);
              if (templateData) {
                // 存在作答记录则渲染
                // this.templateData = JSON.parse(templateData);
                this.templateData = templateData;
                setTimeout(()=>{
                  // 等 spraed 渲染完后 初始化处理题目标红权限
                  this.handleQuestionCompare(this.questionInfo.questionType,this.questionInfo.showError,this.questionInfo.gradeDetails[0]);
                },1000)
              }
            // }
          }else{
            if(this.questionInfo.unAnswerData){ // 设置考核点后的数据模版
              let {answerTemplate,checkKeyList} = JSON.parse(this.questionInfo.unAnswerData);
              this.templateData = answerTemplate;
              this.checkKeyList = checkKeyList; // 设置的考核点
            }
             // 设置考核的字段 添加边框
            setTimeout(() => {
              let sheetList = this.$refs.spreadQuestion.sheetList;
              let spread = this.$refs.spreadQuestion.spread;
              sheetList.forEach(v => {
                let sheet = spread.getSheetFromName(v);
                this.resetCompare(this.checkKeyList,sheet,spread);
              })
            },1000)
          }
         
        }
      }
    },
    // 流程图的渲染
    async renderFlow(id) {
      const res = await this.$api.businessFlowQueryById({ id: id });
      if (res.code === 200) {
        this.flowConfig = res.data;
        this.isShowFlow = true;
      }
    },
    // 合并 作答记录和answerTemplate作为完整作答记录
    handleConcatAnswer(answerTemplate,answer){
      let answerObj = JSON.parse(answer)
      for(let key in answerObj){
        let tableCode = key.split('__')[0];
        let columnCode = key.split('__')[1];
        let index = key.split('__')[2]-1;
        if(answerTemplate){
          if(Array.isArray(answerTemplate[tableCode])){ // 表格
            answerTemplate[tableCode] = answerTemplate[tableCode].map((v,i)=>{
              if(i==index){
                v[columnCode] = answerObj[key];
              }
              return v
            })
          }else{ // 表单
            answerTemplate[tableCode][columnCode] = answerObj[key];
          }
        }
      }
      return answerTemplate
    },
    /**
     * 处理题目标红问题
     * @param {*题目类型} qsType 
     * @param {*任务权限信息} showError 
     * @param {*答案提交后的信息} data 
     */
     handleQuestionCompare(qsType,showError,data){
      if(showError){ // 显示题目标红
          // 判断题型  葡萄城类型题目的标红
          let answerCompareInfo = data.answerCompareInfo;
          if(qsType==4 && data.answerCompareInfo){
            this.handleSpreadCompare(JSON.parse(answerCompareInfo),data.answerResult);
          }
        }
    },
    // 获取题目答案
    getAnswer() {
      const formObj = this.questionInfo.contentData.content;
      // 判断如果是只读题 则答案为讨论框的信息 -> discussion 的值
      if(this.questionInfo.isReadonly==1){
        return this.discussion
      }else{
      if (formObj.sceneType === 1 && formObj.formType === 1) {
        // 动态表单获取答案
        this.$refs.dfsForm?.handleSubmitForm();
        return this.dfsFormAnswer
      } else if (formObj.sceneType === 1 && formObj.formType === 2) {
        // spreadJS
        let spread = this.$refs.spreadQuestion.spread;
        let activeSheet = spread.getActiveSheet();
        // 设置考核点的答案构建
        let answer = {};
        this.checkKeyList.forEach(val=>{
            let itemKey = JSON.parse(val);
            let tableCode = itemKey.bindingPath.split('.')[0]
            let columnCode = itemKey.bindingPath.split('.')[1]
            let subIndex = itemKey.subIndex;
            if(subIndex!=-1){ //表格格式 带下标
              answer[`${tableCode}__${columnCode}__${itemKey.subIndex}`] = this.handleDataValue(activeSheet.getValue(itemKey.row,itemKey.col));
            }else{ //表单模式 
              answer[`${tableCode}__${columnCode}`] = this.handleDataValue(activeSheet.getValue(itemKey.row,itemKey.col));
            }
        });
        // const obj = activeSheet.getDataSource();
        // // 处理葡萄城 提交答案格式问题
        // let answer =  this.handleSpreadAnswer(obj?.xf);
        return answer;
      }
      }
    },
    // 处理表单时间格式的值
    handleDataValue(val){
      let targetValue='';
      if(isISO8601(val)||String(val).indexOf('(中国标准时间)')>-1 ||isBeijingTimeFormat(val)){ // 判断单元格是否是国际标准时间格式
        targetValue = convertToBeijingTime(val)
      }else if(isOADate(val)){ //判断是否是OADate 格式
        targetValue = fromOADate(val)
      } else{// 非时间格式
        targetValue =  val;
      }
      return targetValue;
    },
    // 表单的数据
    submitData(data){
      let newData = data.data.map(val=>{
        return {
          ...val, 
            data: val.data.filter(child=>{
            return !child.deleted
          })
        }
      })
      this.dfsFormAnswer = {
        ...data,
        data:newData
      }
    },
    // spread题目的答案标红   answerCompareInfo -> Object 类型
    handleSpreadCompare(answerCompareInfo,answerResult){
      this.$nextTick(() => {
          let sheetList = this.$refs.spreadQuestion.sheetList;
          let spread = this.$refs.spreadQuestion.spread;
          sheetList.forEach(v => {
            let sheet = spread.getSheetFromName(v);
            // this.handleAnswerCompareInfo(answerCompareInfo, getBindingPathKeys(sheet, spread), sheet,spread);
            if(answerResult!=2){
              this.handleCheckAnswerCompareInfo(answerCompareInfo,this.checkKeyList,sheet,spread)
            }else{
              this.resetCompare(this.checkKeyList,sheet,spread);
            }
          })
        });
    },
     /**
     * 处理设置考核后的题目标红  
     * @param {*} compare 答案对比信息
     * @param {*} checkKeyList 设置考核的坐标信息
     */
    handleCheckAnswerCompareInfo(compare,checkKeyList,sheet,spread){
      if (!compare || !checkKeyList) {
        console.log("对比信息有误");
        return;
      }
      this.resetCompare(checkKeyList,sheet,spread);// 重置颜色
      spread.suspendPaint();
      spread.suspendEvent();
      if (compare.length > 0) {
        let rangeCell = sheet.getSpans(); // 所有合并单元格的位置信息
        compare.forEach(info=>{
          let keys = info.path.split('__');
          let bindingPath = `${keys[0]}.${keys[1]}`
          checkKeyList.find(checkKey=>{
            let checkKeyObj = JSON.parse(checkKey);
            if(keys[2]&&keys[2]==checkKeyObj.subIndex&&checkKeyObj.bindingPath==bindingPath){ // 包含下标 表格
              sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, 1).setBorder(new GC.Spread.Sheets.LineBorder('rgb(233,104,104)', GC.Spread.Sheets.LineStyle.double), { all: true });
            }else if(!keys[2]&&checkKeyObj.bindingPath==bindingPath){
              let rangeItem = rangeCell.find(v=>(v.row==checkKeyObj.row&&v.col==checkKeyObj.col));
              if(rangeItem){ // 存在单元格合并问题
                sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, rangeItem.colCount).setBorder(new GC.Spread.Sheets.LineBorder('rgb(233,104,104)', GC.Spread.Sheets.LineStyle.double), { all: true });
              }else{
                sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, 1).setBorder(new GC.Spread.Sheets.LineBorder('rgb(233,104,104)', GC.Spread.Sheets.LineStyle.double), { all: true });
              }
            
            }
          })
        });
      }
      spread.resumeEvent();
      spread.resumePaint();
    },
    // 重置颜色
    resetCompare(checkKeyList,sheet,spread){
      spread.suspendPaint();
      spread.suspendEvent();
      let rangeCell = sheet.getSpans(); // 所有合并单元格的位置信息
      //重置样式
      if(checkKeyList.length>0){
        checkKeyList.find(checkKey=>{
          let checkKeyObj = JSON.parse(checkKey);
          // let range = sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, 1);
          let rangeItem = rangeCell.find(v=>(v.row==checkKeyObj.row&&v.col==checkKeyObj.col));
          let range;
          if(rangeItem){ // 存在单元格合并问题
            range = sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, rangeItem.colCount);
          }else{
            range = sheet.getRange(checkKeyObj.row, checkKeyObj.col, 1, 1);
          }
          range.setBorder(new GC.Spread.Sheets.LineBorder('rgb(51,51,51)', GC.Spread.Sheets.LineStyle.mediumDashed), { all: true })
        })
      }
      spread.resumeEvent();
      spread.resumePaint();
    },
    /**
     * 处理答案 做出标识
     * @param {*} compare 答案对比信息
     * @param {*} position 表格中绑定的标识以及坐标
     */
     handleAnswerCompareInfo(compare, position, sheet,spread) {
      if (!compare || !position) {
        console.log("对比信息有误");
        return;
      }
      let positionObj = [];
      spread.suspendPaint();
      spread.suspendEvent();

      //重置样式
      if (position.length > 0) {
        position.forEach((v) => {
          if (v.range) {
            // 数组
            positionObj.push({
              bindingPath: v.bindingPath,
              ...v.range
            })
            for (let r = 1; r < v.range.rowCount; r++) {
              for (let i = 0; i < v.range.colCount; i++) {
                // sheet.getRange(v.range.row + r, v.range.col + i, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(224,234,255)");
                sheet.getRange(v.range.row + r, v.range.col + i, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(255,255,255)");
              }
            }
          } else {
            // 键值
            positionObj.push({
              bindingPath: v.bindingPath,
              col: v.col,
              row: v.row,
            });
            // sheet.getRange(v.row, v.col, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(224,234,255)");
            sheet.getRange(v.row, v.col, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(255,255,255)");
          }
        });
      }

      if (compare.length > 0) {
        // 然后再追加标红
        positionObj.forEach((v, index) => {
          // 对比判断
          compare.forEach(val => {
            if (val.path == v.bindingPath) {
              let po = v;
              // 数组对象
              if (po.colCount) {
                let rowIndex = val.row + v.row;
                //console.log('rowIndex', rowIndex);
                for (let i = 0; i < po.colCount; i++) {
                  sheet.getRange(rowIndex, v.col + i, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(233,104,104)");
                }
              } else {
                sheet.getRange(po.row, po.col, 1, 1, GC.Spread.Sheets.SheetArea.viewport).backColor("rgb(233,104,104)");
              }
            }
          })
        })

      }
      spread.resumeEvent();
      spread.resumePaint();
    },
    
    // 初始化spread数据结构
    async initSpreadData(erId) {
      const { data, code } = await this.$api.GetTableMapByERId({ erId });
      if (code === 200) {
        const initData = {};
        data.forEach(val => {
          const colObj = {};
          if (val.type === "oneToOne" || val.isMain) {
            // 主表 'main' 或 "oneToOne"  一对一
            val.columns.forEach(col => {
              colObj[col.columnCode] = "";
            });
            initData[val.objectName] = colObj;
          } else if (val.type === "oneToMany") {
            // "oneToMany" 一对多
            val.columns.forEach(col => {
              colObj[col.columnCode] = "";
            });
            initData[val.objectName] = [colObj];
          }
        });
        return initData;
      }
    },
    /**
     * 区分 视频 文件 图片
     * @param {*} file
     */
    handleIsVideo(file) {
      const suffix = file.substring(file.lastIndexOf(".") + 1);
      const videoSuffixs = ["avi", "rmvb", "rm", "asf", "divx", "mpg", "mpeg", "mpe", "wmv", "mp4", "mkv", "vob"];
      const imgSuffixs = ["jpg", "jpeg", "png", "xbm", "tif", "pjp", "svgz", "ico", "tiff", "gif", "svg", "jfif", "webp", "bmp", "pjpeg", "avif"];
      if (videoSuffixs.includes(suffix)) {
        return "video";
      } else if (imgSuffixs.includes(suffix)) {
        return "image";
      } else {
        return "file";
      }
    },
    // 动态表单 获取单条数据
    async getSingleData(params) {
      let res = null
      // if(this.questionInfo.isReadonly==1){ // 只读题  默认展示答案
        res = await this.$api.FetchDispatchSingleData({
        id: this.dataId,
        erId:this.questionInfo.contentData?.content?.erId,
      });
      
      const mapping = arrayToObject(res.data.tableMap, "tableId")
      let answer = null;
      if(this.questionInfo.gradeDetails){
        answer = this.questionInfo.gradeDetails[0]?.answer
      }
      if(answer&&this.questionInfo.isReadonly!=1) {
        const answerResult  = JSON.parse(answer)
        if(answerResult.data) {
          answerResult.data.forEach(aData => {
            // 通过tableMap的objectName取出table数据
            const mappingItem = mapping[aData.tableId]
            if(mappingItem&&res.data[mappingItem.objectName]) {
              if(mappingItem.isMain) {
                // 主表
                if(aData.data[0]) {
                  Object.keys(aData.data[0]).forEach(key => {
                    // 覆盖原来主表数据对应的字段
                    res.data[mappingItem.objectName][key] = aData.data[0][key]
                  })
                }
              } else {
                // 子表数据全部替换
                res.data[mappingItem.objectName] = aData.data
              }
            }
          })
        }
      }
      
      console.log('res',res);
      
      console.log('res-----------',this.questionInfo);
      
      return new Promise((resolve, reject) => {
        resolve(res);
      });
    },
    // 构建表格部分动态新增数据的字段统一问题
    handleSpreadAnswer(obj){
      for(let key in obj){
        if( Array.isArray(obj[key])){ // 表格形式的数据
          let originRow = obj[key].find(v=>v.hasOwnProperty('id'))
          let row;
          if(originRow){ // 重置所有字段的值
            row = JSON.parse(JSON.stringify(originRow))
            for(let k in row){
              row[k]='';
            }
          }
          let newList = obj[key].map(val=>{
              return {
                ...row,
                ...val,
              };
          });
          obj[key] = newList
        }
      }
      console.log('obj====',obj);
      
      return obj
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .gc-designer-container {
  height: 600px!important;
}

.discussion{
  margin: 16px 50px;
  ::v-deep .el-textarea__inner{
    height: 200px;
  }
}
.known-info {
  .info-container {
    font-size: 14px;
    color: #333;
    line-height: 28px;
    text-indent: 2em;
    text-align: left;
    resize: none;
  }
}
.file-scene{
  height: 600px;
}
</style>
