<template>
  <div v-if="type" class="question-preview-content">
    <div v-if="showForm" class="type-text">{{ questionTypeLabel[type] }}</div>
    <!-- :editanwser="answerValue" -->
    <!-- :answerValue="answerValue" -->
   <voucher-preview 
                  :type="type"
                  :config="optionConfig"
                  :title="title"
                  :desc="desc"
                  :sort="sort"
                  :configdata="config"
                  :configno="configNo"
                  :showAnswer="false"
                  :showError="showError"
                 >
    </voucher-preview> 

    <!-- <div class="row" v-if="type==5?false:true">
      <div class="row-label">答案</div>
      <div :class="answerText ? '' : 'null-data'">{{ answerText }}</div>
    </div>
    <div class="row">
      <div class="row-label">答案解析</div>
      <div style="line-height: 1.5;" :class="answerDetail ? '' : 'null-data'" v-html="answerDetail" v-if="isAnswer"></div>
    </div>
    <div class="row">
      <div class="row-label">知识点</div>
      <div :class="knowledge.length ? '' : 'null-data'">
        <knowledge title="知识点"
                   :type="1"
                   :default-data.sync="knowledge"
                   :can-add="false"
                   :can-delete="false">
        </knowledge>
      </div>
    </div>
    <div class="row">
      <div class="row-label">技能点</div>
      <div :class="skill.length ? '' : 'null-data'">
        <knowledge title="技能点"
                   :type="0"
                   :default-data.sync="skill"
                   :can-add="false"
                   :can-delete="false">
        </knowledge>
      </div>
    </div>
    <div class="row">
      <div class="row-label">难易度</div>
      <div :class="difficulty === '' ? 'null-data' : ''">{{ difficultyText }}</div>
    </div> -->
  </div>
</template>

<script>
import {questionTypeLabel, getAnswerDispatch, getDifficultyDispatch} from '@/components/base/question/util.js';
import cloneDeep from "lodash/cloneDeep";

export default {
  name: 'question-preview-content',
  props: {
    sort: {
      type: Number,
      default:0
    },
    data: {
      type: Object,
      default: () => null
    },
    config: {
      type: Object,
      default: () => null
    },
    showForm: {
      type: Boolean,
      default: true
    },
  },
  components: {
    'voucher-preview': () => import('./main.vue'),
    'knowledge': () => import('@/components/question/knowledge/index.vue')
  },
  data () {
    return {
      questionTypeLabel: questionTypeLabel,
      type: '',
      title: '',
      desc: '',
      optionConfig: {},
      answerText: '',
      answerDetail: '',
      knowledge: [],
      skill: [],
      difficulty: '',
      difficultyText: '',
      answerValue:'',// 答案的值
      showError:false,
      isAnswer:false,
      configNo:''
    }
  },
  watch: {
    data: {
      handler (val) {
        this.initData()
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData () {
      if (this.data) {
        this.type = this.data.type || ''
        this.title = this.data.title || ''
        this.showError = this.data.showError || ''
 
        this.desc = this.data.contentData.content.description || ''
        this.optionConfig = this.data.contentData.content.contentData
        let dataheadIem=this.data.contentData.content.answer?JSON.parse(this.data.contentData.content.answer):null
        this.configNo = this.data.contentData.answerheadItem?this.data.contentData.answerheadItem:JSON.stringify(dataheadIem.data.headItem)
        this.answerDetail = this.data.contentData.content.answerAnalysis || ''
        this.skill = this.data.contentData.content.skillTags || []
        this.knowledge = this.data.contentData.content.knowledgeTags || []
        this.difficultyText = this.data.contentData.content.difficultyText

        this.difficulty = this.data.difficulty || this.data.difficulty === 0 ? this.data.difficulty : ''
       // this.answerText = getAnswerDispatch(this.type, this.data.optionConfig)
        this.answerValue = this.data.contentData.content.answer || '' // 题目答案
        this.isAnswer=this.data.showAnswer
      }
    }
  }
}
</script>

<style scoped lang="scss">
.question-preview-content {
  display: grid;
  grid-gap: 20px;
  color: #222;
  width: 100%;
  .type-text {
    border-left: 3px solid var(--theme_primary_color);
    padding-left: 10px;
    font-weight: 700;
  }
  .row {
    .row-label {
      color: #999;
      padding-bottom: 10px;
    }
    .null-data {
      &:before {
        content: '--';
        color: #CCCCCC;
        font-size: 13px;
      }
    }
  }
  :deep(img) {
    max-width: 100%;
  }
}
</style>