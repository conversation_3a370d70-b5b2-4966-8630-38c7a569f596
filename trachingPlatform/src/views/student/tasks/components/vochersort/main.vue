<template>
  <div class="entries-previewe">
     <div class="entries-contanier">
       <div class="entries-title">{{title}}</div>
       <div class="entries-btn">
        <el-row>
           <!-- <el-button class="writesave" @click="handleOpsave" v-if="!isOther">保存</el-button>  -->
           <el-button :class="[isOther?'writevocher':'writeback']" @click="handleOpvocher">{{isOther?'填写凭证':'返回'}}</el-button>
        </el-row>
      </div>
     </div>
     <div class="entries-input">
       <div class="question-desc" v-if="desc" style="" v-html="desc"></div>
     </div>
    <div class="kindtab" v-if="isOther">
        <ul class="nav1" v-if="isfilenum !== 0">
              <li class="navitem" @click="tabClick('first')">
                  <div :class="[activeName == 'first'?'deactive':'defaultclass' ]">单据 {{billticketnum}}</div>
              </li>
              <li class="navitem" @click="tabClick('second')">
                  <div :class="[activeName == 'second'?'deactive':'defaultclass' ]">附件 {{filenum}}</div>
              </li>
        </ul>
    </div>
    <div class="entriescontent" v-if="isOther">
        <div class="entriesticket">
        <!-- 票据 -->
         <template v-if="activeName == 'first'">
          <div class="entrieItem" v-for="(item,index) in fileList" :key="index">
             <div class="ticketTitle"> <i class="el-icon-d-arrow-right transfom"></i> {{getfilefrontname(item.name)}}</div>
             <div class="ticketpic"  @click="handpriview(item)">
               <img :src="item.response?item.response.data:item.url"/>
             </div>
          </div>
        </template>
        <!-- 附件 -->
          <!-- <div class="upload-listfile" v-else>
           <div class="uploadjolp">文件：共 <span class="uploadnum">{{filenum}}</span> 个</div>
            <div class="uploadshow">
                <div class="uploadel">
                   <div class="uploadel-l">
                      <div class="uppic"><img src="../../../../assets/account/doc.png"/></div>
                      <div class="upwor">
                         <div class="upworfilename">wdc.Doc</div>
                         <div class="upworsize">1024kb</div>
                      </div>
                   </div>
                   <div class="uploadel-ricon">
                     <el-row>
                      <i class="el-icon-view iconsiez"></i>
                      <i class="el-icon-download iconsiez"></i>
                      <i class="el-icon-circle-close iconsiez1" @click="handleDeletefile(item)"></i>
                     </el-row>
                   </div>
                </div>
            </div>
        </div> -->
        <div class="upload-listfile" v-else>
           <div class="uploadjolp" v-if="fileList.length !== 0">文件：共 <span class="uploadnum">{{filenum}}</span> 个</div>
            <div class="uploadshow">
                <div class="uploadel" v-for="(item,index) in fileList">
                   <div class="uploadel-l">
                      <!-- <div class="uppic" v-if="getfilename(item.name) ==='jpg'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='png'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='jpeg'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='docx'"><img src="../../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='doc'"><img src="../../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='ppt'"><img src="../../../../../assets/material/ppt.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='pdf'"><img src="../../../../../assets/material/pdf.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xlsx'"><img src="../../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xls'"><img src="../../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else><img src="../../../../../assets/material/video.png"/></div> -->
                      <div class="upwor">
                         <div class="upworfilename">{{item.name}}</div>
                         <div class="upworsize">{{item.size}}kb</div>
                      </div>
                   </div>
                   <div class="uploadel-ricon">
                     <el-row>
                      <i class="el-icon-view iconsiez" v-if="(getfilename(item.name) ==='png') || (getfilename(item.name) ==='jpg') || (getfilename(item.name) ==='jpeg')" @click="handpriview(item)"></i>
                      <i class="el-icon-download iconsiez" @click="handledownload(item)"></i>
                      <!-- <i class="el-icon-circle-close iconsiez1" @click="handleDeletefile(item)"></i> -->
                     </el-row>
                   </div>
                </div>
            </div>
        </div>

        </div>
        <!-- <div class="question-entries">
          <el-button class="writevocher" @click="handleOpvocher">填写凭证</el-button>
        </div> -->
    </div>
    <div class="entriesmain" v-show="!isOther">
       <div class="entriesmain-l">
         <div class="entriesAnswer">
            <!-- <el-row>
               <el-button class="entriesbtn">参考答案</el-button>
               <el-button class="entriesbhn">提交答案</el-button>
            </el-row> -->
         </div>
         <vocherPreview ref="vocherinit" :editanwser="answerValue" :billnum="billnum" :confignoValue="configno" :filenum="filesnum" :showError="showError" :sort="sort"></vocherPreview>
       </div>
       <div class="entriesmain-r">
       <!-- <div class="ticketword">【分录题】{{title}}</div> -->
       <div class="kindtab" >
        <ul class="nav1"  v-if="isfilenum !==0">
              <li class="navitem" @click="handletabClick('third')">
                  <div :class="[activetab == 'third'?'deactive':'defaultclass' ]">单据 {{billticketnum}}</div>
              </li>
              <li class="navitem" @click="handletabClick('fouth')">
                  <div :class="[activetab == 'fouth'?'deactive':'defaultclass' ]">附件 {{filenum}}</div>
              </li>
        </ul>
        </div>

        <template v-if="activetab == 'third'">
          <div class="entrieItem"v-for="(item,index) in filesubList" :key="index" @click="handpriview(item)">
             <div class="ticketTitle" :title="item.name" style="max-width: 280px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">{{getfilefrontname(item.name)}}</div>
             <div class="ticketpic" >
               <img :src="item.response?item.response.data:item.url"/>
             </div>
          </div>
        </template>

        <div class="entriestpic" v-else>
          <div class="upload-listfile" v-if="filesubList.length !== 0">
           <div class="uploadjolp" v-if="filesubList.length !== 0">文件：共 <span class="uploadnum">{{filenum}}</span> 个</div>
            <div class="uploadshow">
                <div class="uploadel" v-for="(item,index) in filesubList">
                   <div class="uploadel-l">
                      <!-- <div class="uppic" v-if="getfilename(item.name) ==='jpg'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='png'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='jpeg'"><img src="../../../../../assets/material/img.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='docx'"><img src="../../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='doc'"><img src="../../../../../assets/material/doc.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='ppt'"><img src="../../../../../assets/material/ppt.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='pdf'"><img src="../../../../../assets/material/pdf.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xlsx'"><img src="../../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else-if="getfilename(item.name) ==='xls'"><img src="../../../../../assets/material/excel-icon.png"/></div>
                      <div class="uppic" v-else><img src="../../../../../assets/material/video.png"/></div> -->
                      <div class="upwor">
                         <div class="upworfilename" :title="item.name">{{item.name}}</div>
                         <div class="upworsize">{{item.size}}kb</div>
                      </div>
                   </div>
                   <div class="uploadel-ricon">
                     <el-row>
                      <i class="el-icon-view iconsiez" v-if="(getfilename(item.name) ==='png') || (getfilename(item.name) ==='jpg') || (getfilename(item.name) ==='jpeg')" @click.prevent="handpriview(item)"></i>
                      <i class="el-icon-download iconsiez" @click.prevent="handledownload(item)"></i>
                      <!-- <i class="el-icon-circle-close iconsiez1" @click.prevent="handleDeletefile(item)" v-if="ispreviewstatus !==1"></i>-->
                     </el-row> 
                   </div>
                </div>
            </div>
        </div>
        </div> 
 

       </div>
    </div>
      <div class="preview-img-box"  v-show="isPreview" v-vocherDrag>
        <div class="img-box">
          <img :src="imgurl" />
        </div>
        <div class="icon-box" @click="isPreview = false">
          <i class="el-icon-circle-close icon"></i>
        </div>
      </div>
  </div>
</template>

<script>
//import {getCorrectAnswer, questionTypeMenu} from "@/components/base/question/util";
import vocherPreview from './voucher.vue'
export default {
  name: 'entries-preview',
  components: {
    vocherPreview,
  },
  props: {
    config: {
      type: Object,
      default: () => {}
    },
    configno: {
      type: String,
      default: ''
    },
    configdata: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    sort: {
      type: Number,
      default: 0
    },
    desc: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showError: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    showAnswer: { // 是否显示答案
      type: Boolean,
      default: true
    },
    errorInfo: { // 错误信息
      type: Array,
      default: () => []
    },
    clearStorage: {
      type: Boolean,
      default: true
    },
    answerValue: { // 答题值
      type: Array | Object | String | Number,
      default: false
    },
  },
  data () {
    return {
      loading: true,
      isOther:true,
      isPreview:false,
      activeName:'first',
      activetab:'third',
      fileList:[],
      filesubList:[],
      fileTypeList:[],
      billticketnum:0,
      filenum:0,
      isfilenum:0,
      ispreviewstatus:0,
      confignoValue:''
    }
  },
  mounted () {
    //this.init()
  },
  watch:{
    config: {
      handler(val) {
        if(val){
          this.fileTypeList=JSON.parse(val)
          let datas=JSON.parse(val)
           this.fileList=datas.questionfiles?.filter(item => item.type === 1)
           this.filesubList=datas.questionfiles?.filter(item => item.type === 1)
           let fileList=datas.questionfiles?.filter(item => item.type === 1)
           this.billticketnum=fileList?.length
           let fileDataList=datas.questionfiles?.filter(item => item.type === 0)
           this.filenum=fileDataList?.length 
           const count =  datas.questionfiles?.filter(item => item.type === 1).length;
           const num =  datas.questionfiles?.filter(item => item.type === 0).length;
           this.isfilenum=num
           this.billnum=count
           this.filesnum=num
        }
      },
      immediate: true,
    },
    configdata: {
      handler(val) {
        if(val){
         this.answerValue=val
        }else{
          this.answerValue=null
        }
      },
      immediate: true,
    }
  },
  methods: {
    tabClick(val){
      this.activeName=val
      if(this.activeName=='first'){
          this.fileList=this.fileTypeList.questionfiles.filter(item => item.type === 1)
          this.billticketnum=this.fileList?.length       
      }else{
          this.fileList=this.fileTypeList.questionfiles.filter(item => item.type === 0)
          this.filenum=this.fileList?.length
      }
    },
    getfilename(filename){
     let laststr=filename.split('.').pop()
      return laststr
    },
    getfilefrontname(filename){
      const nameWithoutExtension = filename.split('.').slice(0, -1).join('.');
      return nameWithoutExtension
    },
    handpriview(item){
     this.imgurl=item.response?item.response.data:item.url
     this.isPreview=true
    },
    handledownload(item){
      let fileUrl = item.response?item.response.data:item.url;
      let fileName =item.name;
          fetch(fileUrl)
              .then((response) => response.blob())
              .then((blob) => {
                // 创建一个临时的URL对象
                const url = URL.createObjectURL(blob);
                // 创建一个隐藏的<a>标签，并设置其href属性为临时URL
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName; // 设置下载的文件名
                a.style.display = 'none';
                // 将<a>标签添加到文档中，并模拟点击下载
                document.body.appendChild(a);
                a.click();
                // 下载完成后，移除<a>标签和临时URL对象
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
              })
              .catch((error) => {
                console.error('下载文件时出错:', error);
          });
    },
    handletabClick(val){
      this.activetab=val
      if(this.activetab=='third'){
          this.filesubList=this.fileTypeList.questionfiles.filter(item => item.type === 1)
          this.billticketnum=this.filesubList?.length       
      }else{
          this.filesubList=this.fileTypeList.questionfiles.filter(item => item.type === 0)
          this.filenum=this.filesubList?.length
      }
    },
    handleDeletefile(task){
      this.fileList = this.fileTypeList.questionfiles.filter(item => item.uid !== task.uid);
      const count =  this.fileTypeList.questionfiles.filter(item => item.type === 1).length;
      const num =  this.fileTypeList.questionfiles.filter(item => item.type === 0).length;
      this.filesnum=num
      this.billnum=count
    
    },
    handleClick(){
       console.log(this.activeName,'SOPP----')
    },
    handleOpsave(){
       let sorts=this.sort - 1
       this.$refs.vocherinit.getcheckList(sorts)
       this.$message.success("保存成功");
    },
    handleOpvocher(){
      this.isOther=!this.isOther
    },
    init () {
      // 避免多个填空题时,答案被替换
      this.loading = true
      const courseContents = sessionStorage.getItem('courseContents')
      let settingArr = courseContents ? JSON.parse(courseContents) : []
      const answerIds = settingArr.map(item => item.answerId)

      if (this.config.settingArr && this.showAnswer) {
        settingArr = settingArr.concat(this.config.settingArr.filter(item => !answerIds.includes(item.answerId)))
      } else if (this.config.settingArr && !this.showAnswer) {
        settingArr = settingArr.concat(this.config.settingArr
            .filter(item => !answerIds.includes(item.answerId))
            .map((item) => ({
              ...item,
              answerCont: this.value[item.answerId] || ''
            }))
        )
      }
      settingArr.forEach((item) => {
        item.isCorrect = true
        if (this.errorInfo.includes(item.answerId)) {
          item.isCorrect = !this.errorInfo.includes(item.answerId)
        }
      })
      sessionStorage.setItem('courseContents', JSON.stringify(settingArr))
      this.$nextTick(() => {
        this.loading = false
      })
    },
    /**
     * 初始化图片
     */
  },
  beforeDestroy() {
    //取消监听

  },
 
}
</script>

<style lang="scss">
.entries-previewe {
  .entries-contanier{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .entries-title{
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #222222;
    }
    .entries-btn{
      .writesave{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        background: #2B66FA;
        color: #fff;
        padding: 7px 10px;
      }
      .writevocher{
        width: 96px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
      .writeback{
        width: 70px;
        height: 30px;
        border: 1px solid #2B66FA;
        color: #2B66FA;
        padding: 7px 10px;
      }
    }
  }
  .entries-input{
    padding: 20px 0 8px 0;
    .question-desc{
      padding: 10px 10px;
      width: 100%;
      height: 105px;
      background: #F6F8FA;
      border-radius: 3px 3px 3px 3px;
      border: 1px solid #DBDBDB;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
  }
  .kindtab{
    padding: 13px 0 20px 0;
    .nav1 {
            display: flex;
            position: relative;
            padding: 0 5px 1px 0;
            overflow-x: auto;
            white-space: nowrap;
            ::-webkit-scrollbar {
                display: none;
            }
            .navitem {      
              cursor: pointer;
              text-align: center;
              font-size: 16px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              line-height: 40px;
              margin-right: 11px;
             .defaultclass{
                width: 90px;
                height: 28px;
                line-height: 28px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                padding: 0 12px;
                color: #747474;
                background: #E6E6E6;
                border-radius: 3px 3px 3px 3px;  
              }
            .deactive {
                width: 90px;
                height: 28px;
                line-height: 28px;
                border-radius: 4px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;     
                padding: 0 12px;
                color: #2B66FA;
                border-radius: 3px 3px 3px 3px;  
                background: #FCFDFF;
                border: 1px solid #2B66FA;
            }
          }
        }
  }
  .entriescontent{
    .entriesticket{
      width: 60%;
      .entrieItem{
        margin-bottom: 15px;
        border: 1px solid #E7E7E7;
        .ticketTitle{
          width:100% ;
          height: 50px;
          background: #F3F3F3;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #222222;
          line-height: 50px;
          padding-left: 10px;
          .transfom{
            transform: rotate(90deg);
            color: #5A5A5A;
            font-size: 16px;
          }
        }
        .ticketpic{
          // width: 100%;
          padding-top: 2px;
          img{
            width: 100%;
          }
        }
      }
    }
    .question-entries{
      width: 15%;
      display: flex;
      justify-content: flex-end;
    }
  }
  .entriesmain{
     width: 100%;
     display: flex;
     justify-content: space-between;
    .entriesmain-l{
      width: 74%;
      .entriesAnswer{
        display: flex;
        justify-content: flex-end;
        padding: 0 15px 15px 0;
        .entriesbtn{
          width: 100px;
          height: 38px;
          border-color: #2B66FA;
          color: #2B66FA;
        }
        .entriesbhn{
          width: 100px;
          height: 38px;
          background-color:  #2B66FA;
          color: #fff;
        }

      }
    }
    .entriesmain-r{
      width: 24%;
      .entrieItem{
        margin-bottom: 10px;
        .ticketTitle{
          border-left: 3px solid #3886FF;
          padding-left: 10px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 22px;
        }
        .ticketpic{
          width: 100%;
          padding-top: 8px;
        }
      }
      .ticketword{
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
        padding-bottom: 10px;
        
      }
      .entriestpic{
        width: 100%;
        padding-top: 5px;
        cursor: pointer;
        // .image-box {
        //   position: relative;
        //   margin: 0 auto;
        //   width: 1000px;
        //   height: 700px;
        //   border: 1px solid #333;
        //   overflow: hidden;
        // }
        // .image-box img {
        //   position: absolute;
        //   cursor: pointer;
        // }
      }
    }
  }
  .upload-listfile{
        padding-top: 15px;
        .uploadjolp{
          font-family: Source Han Sans SC, Source Han Sans SC;
          font-weight: 400;
          font-size: 14px;
          color: #A7A7A7;
          .uploadnum{
            font-family: Source Han Sans SC, Source Han Sans SC;
            font-weight: 400;
            font-size: 14px;
            color: #2B66FA;
          }
        }
        .uploadshow{
          padding-top: 20px;
          .uploadel{
             display: flex;
             justify-content: space-between;
             align-items: center;
             margin-bottom: 20px;
             .uploadel-l{
              display: flex;
              align-items: center;
              .upwor{
                padding-left: 10px;
                .upworfilename{
                  max-width: 200px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  font-size: 14px;
                  color: #333333;
                  padding-bottom: 1px;
                }
                .upworsize{
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  font-size: 12px;
                  color: #B1B1B1;
                }
              }
              }
             .uploadel-ricon{
               .iconsiez{
                padding-right: 14px;
                font-size: 19px;
                color: #909090;
                cursor: pointer;
               }
               .iconsiez1{
                font-size: 19px;
                color: #909090;
                cursor: pointer;
               }
             }
          }
        }
   }
  .preview-img-box {   
     cursor: grab;
     user-select: none; /* 防止文字被选中 */
      position: absolute;
      left: 22%;
      top: 22%;
      z-index: 10;
      .img-box {
        max-width: 1200px;
        height: auto;

        img {
          width: 100%;
          height: 100%;
        }
      }
      .icon-box {
        width: 26px;
        height: 26px;
        position: absolute;
        // right: -20px;
        // top: -23px;
        right: 0;
        top: 0;
        cursor: pointer;
        .icon {
          font-size: 26px;
          color: #222;
        }
      }
    }
  


}
</style>