<template>
  <div class="charge-containerset" :id="`chargeContainer${sort+'s'}`">
    <div class="chargescrollb">
        <div class="charge">
          <div class="charge-header">
            <div style="padding-right: 10px;">
              凭证字
              <el-select style="width: 80px;" v-model="voucher.word" disabled>
                <el-option v-for="item in wordList" :key="item.name" :label="item.name" :value="item.name">
                </el-option>
              </el-select>
            </div>
            <div style="padding-right: 10px;">
              凭证号
              <el-input readonly style="width: 90px;" min="1" placeholder="请输入" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" v-model="no"></el-input>
            </div>
            <div>
              日期 
              <el-date-picker type="date" style="width: 145px;" placeholder="请选择日期" v-model="date"></el-date-picker>
            </div>
          
            <div class="ticketpadding" style="float: right;position: relative;">
              附件
              <el-input type="text" style="width: 60px;" v-model="filedata" :readonly="true"></el-input>
              个
            </div>
            <div class="ticketpadding" style="float: right;position: relative; padding-right: 10px;">
              单据
              <el-input type="text" style="width: 60px;" v-model="billdata" :readonly="true"></el-input>
              张
            </div>
          </div>
          <table :class="[answerResult && answerResult == 2 ? 'charge-table' : 'debtor-border charge-table']" border="1">
            <tr>
              <td :width="isAuxiliary ? '14%' : '24%'" class="abstracts">摘要</td>
              <td :width="isAuxiliary ? '15%' : '24%'" class="abstracts">会计科目</td>
              <td width="14%" v-if="isAuxiliary">辅助核算</td>
              <td width="58%">
                <table style="height: 50px;">
                  <tr style="border-bottom: 1px solid #bab9b9;">
                    <td width="50%" style="border-right: 1px solid #bab9b9;" class="abstracts">借方金额</td>
                    <td width="50%" class="abstracts">贷方金额</td>
                  </tr>
                  <tr>
                    <td style="border-right: 1px solid #bab9b9;">
                      <table class="debtor-lender-table" style="height: 100%;">
                        <tr class="debtorword">
                          <td>佰</td>
                          <td>十</td>
                          <td>亿</td>
                          <td>千</td>
                          <td>百</td>
                          <td>十</td>
                          <td>万</td>
                          <td>千</td>
                          <td>百</td>
                          <td>十</td>
                          <td>元</td>
                          <td>角</td>
                          <td>分</td>
                        </tr>
                      </table>
                    </td>
                    <td>
                      <table class="debtor-lender-table" style="height: 100%;">
                        <tr class="debtorword">
                          <td>佰</td>
                          <td>十</td>
                          <td>亿</td>
                          <td>千</td>
                          <td>百</td>
                          <td>十</td>
                          <td>万</td>
                          <td>千</td>
                          <td>百</td>
                          <td>十</td>
                          <td>元</td>
                          <td>角</td>
                          <td>分</td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="border-color: transparent;"></td>
            </tr>
            <tr v-for="(item, index) in list" :key="index" style="position: relative;" @mouseover="mouseover(index)"
              @mouseleave="mouseleave">
              <td>
                <div @click="showInput(index, 'main')" v-if="!item.isShowMainInput" class="main-subject">
                  <!--{{摘要}}-->
                  <span>{{ item.main }}</span>
                  <!-- <textarea v-model.trim="item.main"></textarea> -->
                </div>
                <div class="main-subject" v-if="item.isShowMainInput">
                  <textarea v-model.trim="item.main" @blur="hideInput(index, 'main')" v-focus
                    @keyup="keyupEvents(index, $event, 1)" @input="chargeSummary(index)"></textarea>
                  <div class="dropdown-menu suggest-list special-elements" style="top: 60px;">
                    <ul class="item-list">
                      <li v-for="main in summaryList" :class="main.name == item.main ? 'hover' : ''"
                        @click="voluationInput(index, 'main', main.name)">{{ main.name }}
                      </li>
                    </ul>
                  </div>

                </div>
                <i v-if="item.isShowMainInput" class="el-icon-more special-elements"
                  @click="selectionList(index, 'main')"></i>
              </td>
              <td>

                <div @click="showInput(index, 'subject')" v-if="!item.isShowSubjectInput" class="main-subject">
                  <div>
                    {{ item.subject.name }}
                  </div>
                  <!-- <textarea v-model.trim="item.subject.name"></textarea> -->
                </div>
                <div class="main-subject" v-if="item.isShowSubjectInput">
                  <textarea v-model.trim="item.subject.name" @blur="hideInput(index, 'subject')"
                    @keyup="keyupEvents(index, $event, 2)" v-focus @input="chargeSubject(index)"></textarea>

                  <div class="dropdown-menu suggest-list special-elements" style="top: 60px;">
                    <ul class="item-list">
                      <li v-for="subject in subjectList" :class="subject.subjectName == item.subject.name ? 'hover' : ''"
                        @click.prevent="voluationInput(index, 'subject', subject)">
                        {{ subject.subjectName }}
                      </li>
                    </ul>
                  </div>
                </div>
                <i v-if="item.isShowSubjectInput" class="el-icon-more special-elements"
                  @click="selectionList(index, 'subject')"></i>
              </td>
              <td>
                <table>
                  <tr>
                    <td width="50%" style="border-right: 1px solid #bab9b9;">
                      <table class="debtor-tbale debtor-lender-table">
                        <tr @click="showInput(index, 'debtor')" v-if="!item.isShowDebtorInput"
                          :class="item.debtor * 1 < 0 ? 'tr-negative' : ''">
                          <td v-for="debtor in item.debtorList">{{ debtor }}</td>
                        </tr>
                        <tr v-if="item.isShowDebtorInput">
                          <input @blur="hideInput(index, 'debtor')" @keyup="debtorInputKeyUp(index, $event, 3)"
                            v-model="item.debtor" maxlength="11" v-focus
                            onkeypress="if (event.keyCode!=46 && event.keyCode!=45 && (event.keyCode<48 || event.keyCode>57)) event.returnValue=false">
                        </tr>
                        <!-- onkeypress="if((event.keyCode<48 || event.keyCode>57) && event.keyCode!=46 || /\.\d\d$/.test(value))event.returnValue=false" -->
                      </table>
                    </td>
                    <td width="50%">
                      <table class="lender-tbale debtor-lender-table">
                        <tr v-if="!item.isShowLenderInput" @click="showInput(index, 'lender')"
                          :class="item.lender * 1 < 0 ? 'tr-negative' : ''">
                          <td v-for="lender in item.lenderList">{{ lender }}</td>
                        </tr>
                        <tr v-if="item.isShowLenderInput">
                          <input @blur="hideInput(index, 'lender')" @keyup="lenderInputKeyUp(index, $event, 4)"
                            v-model="item.lender" maxlength="11" v-focus
                            onkeypress="if (event.keyCode!=46 && event.keyCode!=45 && (event.keyCode<48 || event.keyCode>57)) event.returnValue=false">
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="position: absolute; border: none; left: -19px;">
                <div class="showbtn1" v-show="showIndex == index ? true : false">
                  <div @click="addList(index)" class="charge-table-icon">
                    <!-- <img src="../../../../../assets/account/add.png" /> -->
                  
                  </div>
                  <div @click="removeList(index)" class="charge-table-icon">
                    <!-- <img src="../../../../../assets/account/des.png" /> -->
               
                  </div>
                </div>
              </td>
            </tr>
            <tr style="position: relative;">
              <td colspan="2" style="text-align: left;padding-left: 10px;" class="abstracts">合计：{{ this.totalamount }}
              </td>
              <td>
                <table>
                  <tr>
                    <td width="50%" style="border-right: 1px solid #bab9b9;">
                      <table class="debtor-tbale debtor-lender-table">
                        <tr :class="[this.debtorTotal * 1 < 0 ? 'tr-negative' : '']">
                          <td v-for="debtor in debtorTotalList">{{ debtor }}</td>
                        </tr>
                      </table>
                    </td>
                    <td width="50%">
                      <table class="lender-tbale debtor-lender-table">
                        <tr :class="this.lenderTotal * 1 < 0 ? 'tr-negative' : ''">
                          <td v-for="lender in lenderTotalList">{{ lender }}</td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
              <td style="border-color: transparent;"></td>
            </tr>
          </table>
          <!-- <div class="accountmain">
            <div class="accountchild">
              <div class="accountchild-el" @click="onInputVoucher">
                <div class="accountshow">制单人: <span>{{ voucherfrom }}</span></div>

              </div>
            </div>
            <div class="accountchild">
              <div class="accountchild-clear" @click="clearExamine">
                清空凭证
              </div>
            </div>
          </div> -->

          <!-- <div class="closeBabelt" v-if="auditAccountType == 1">
            <img src="../../../assets/account/closed.png" />
          </div>
          <div class="closeBabel" v-if="auditType == 1">
            <img
              :src="isToggleTheme ? require('../../../assets/account/yshg.png') : require('../../../assets/account/ysh.png')"
              alt="" />
          </div> -->
        </div>
    </div>
      <!-- <div class="footbtns" v-if="saveVisible">
        <el-button class="btndefault" @click="getAnswerModal(true, questionsId, parentmainId, systemId, systemDataList)"
          v-show="answerShowType == 2 || answerShowType == 0 || isViewRecode ? true : false">答案解析</el-button>
        <el-button class="btnsave" @click="getSaveDataExamine(1)"
          v-show="auditType == 1 || auditAccountType == 1 ? false : true" v-if="!isViewRecode"  v-preventReClick>保存</el-button>
        <el-button class="btnsend" @click="getAllDataSubmitExamine(2)"
          v-show="auditType == 1 || auditAccountType == 1 ? false : true" v-if="!isViewRecode"  v-preventReClick>提交</el-button>
        <el-button @click="automaticSubmit(2)" class="btndefault" v-if="automaticAnswer">自动作答</el-button>
      </div> -->
    </div>
</template>
 
<script>
import Vue from 'vue';
Vue.directive('focus', {
  // 当绑定元素插入到 DOM 中。
  inserted: function (el) {
    // 聚焦元素
    el.focus();
  }
});
import DX from '@/utils/moneyToChinese'
import { mapGetters } from 'vuex';
import eventBus from "@/utils/eventBus";
export default {
  name: "voucherForm",
  props: ['billnum','filenum','editanwser','sort','showError','confignoValue'],
  components: {},
  data() {
    return {
      auditAccountType:0,
      auditType:0,
      studentInfo: this.$local.getCurrentStudentInfo(),
      businessModuleId: 0,
      showIndex: 0,
      questionsId: 0,
      answerResult: 2,
      voucher: {
        word: '记', no: 1, date: '', bill: 0
      },
      no:1,
      date:'',
      voucherCacher: {
        word: '', no: '', date: new Date(), bill: 0
      },
      list: [
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '200',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        }
      ],
      billdata:0,
      debtorTotal: 0,
      debtorTotalList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
      lenderTotal: 0,
      lenderTotalList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
      wordList: [
        {
          id:0,
          name:'记'
        }
      ],
      subjectList: [],
      subjectcopyList: [],
      dialogMainIndex: 0,
      dialogSubjectIndex: 0,
      isAuxiliary: false,
      saveVisible: true,
      billNo: '',
      totalamount: '',
      debtorTotalmax: 0,
      lenderTotalmax: 0,
      timer: null,
      summarydata: [],
      summaryList: [],
      ansewerArray: [],
      filedata:0,
      caseId:sessionStorage.getItem('caseId'),
    
    }
  },
  computed: {
    ...mapGetters({
      userInfo: ["userInfo"],
      voucherWorkInfo: ["voucherWorkInfo"]
    })
  },
  mounted() {
    this.getsubjectData()
    this.getSummaryData()
    // this.getquestionInfo()
    // this.getisAuditstatus()
    document.addEventListener('keydown', this.handleEvent);
     if(!this.editanwser){
      this.init();
     }
    eventBus.$on("handlevoucherData", data => {
      this.getcollectList(data)
    });
  },
  updated() {
    //给特定区域添加ID   绑定onmousedown 事件
    var chargeContainer = document.getElementById(`chargeContainer${this.sort +'s'}`);
    var outDiv = chargeContainer.getElementsByClassName('special-elements');
    for (var i = 0; i < outDiv.length; i++) {
      outDiv[i].onmousedown = function (e) {
        //现代浏览器阻止默认事件
        if (e && e.preventDefault)
          e.preventDefault();
        //IE阻止默认事件
        else
          window.event.returnValue = false;
        return false;
      }
    }
 
  },
  watch: {
    billnum: {
      handler(val) {
         this.billdata=val
      },
      immediate: true,
    },
    filenum: {
      handler(val) {
          this.filedata=val  
      },
      immediate: true,
    },
    confignoValue:{
      handler(val) {
        if(val){
          let dateNo= JSON.parse(val)
          this.no=dateNo.no
          this.date=dateNo.date
        }
      },
      immediate: true
    },
    editanwser: {
      handler(val) {
      if(val){
       let voucherdata=val.answer?JSON.parse(val.answer):JSON.parse(val.answer)
       if(val?.contentData){
       let totalData= val.contentData.questionfiles?val.contentData:JSON.parse(val.contentData.content.contentData)
       const count =  totalData.questionfiles.filter(item => item.type === 1).length;
       const num =  totalData.questionfiles.filter(item => item.type === 0).length;
       this.filedata=num
       this.billdata=count
       }
       if (this.showError) {
          if (val.answerResult == 2) {
              this.answerResult = 2
            } else {
              this.answerResult = 3
            }
       } else {
            this.answerResult = 2
       }
       let cacherData=voucherdata
       this.no=cacherData.data?.headItem.no
       this.date=cacherData.data?.headItem.date
        let list = []
        let debtorTotal = 0
        let lenderTotal = 0
        cacherData.data.bodyItems.forEach((item1, index1) => {
          list.push({
            main: '',
            itemflag: false,
            isShowMainInput: false,
            subject: {
              name: '',
              subjectId: '',
              subjectCode: '',
            },
            isShowSubjectInput: false,
            debtor: '',
            debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
            isShowDebtorInput: false,
            lender: '',
            lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
            isShowLenderInput: false,
          })
          debtorTotal += Number(item1.borrowersAmount)
          lenderTotal += Number(item1.creditorAmount)
        })
        list.forEach((item, index) => {
          cacherData.data.bodyItems.forEach((item1, index1) => {
            if (index == index1) {
              item.main = item1.abstracts
              item.subject.name = item1.subjectCode + '\xa0' + item1.subjectName;
              item.subject.subjectCode = item1.subjectCode;
              item.subject.subjectId = item1.subjectId;
              item.debtor = item1.borrowersAmount || ''
              item.lender = item1.creditorAmount || ''
            }
          })
        });
        list.forEach((item, index) => {
          let debtorList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
          let lenderList = ['', '', '', '', '', '', '', '', '', '', '', '', '']
          item.debtorList = this.collatingData(item.debtor, debtorList)
          item.lenderList = this.collatingData(item.lender, lenderList)
        })
        this.list = list
        if ((debtorTotal != 0 || lenderTotal != 0) && debtorTotal.toFixed(2) != lenderTotal.toFixed(2)) {
          this.totalamount = '凭证借贷不平衡'
        } else {
          this.totalamount = DX(debtorTotal)
        }
        debtorTotal = debtorTotal + '';
        lenderTotal = lenderTotal + '';
        let debtorTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', '']
        let lenderTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', '']
        this.debtorTotalList = this.collatingDataTotal(debtorTotal, debtorTotalList)
        this.lenderTotalList = this.collatingDataTotal(lenderTotal, lenderTotalList)
      }else{
        this.init();
        this.answerResult = 2
        this.totalamount=''
      }
      },
      immediate: true
    },
  },
  methods: {
    getcollectList(data){
      if(data==this.sort){
        var reqObj = this.checkListData();
        let voucherWorkInfo=this.voucherWorkInfo
        voucherWorkInfo.vocherInfo.details.forEach((item,index)=>{
          if(item.type == 49 && index==data){
            item.subAnswer=JSON.stringify(reqObj) || ''
          }
        })
        this.$store.commit("voucherWorkInfo", voucherWorkInfo);
      }   
    },
    isJsonEmpty(json) {
      return JSON.stringify(json) === "{}";
    },
    mouseover(index) {
      this.showIndex = index
    },
    mouseleave() {
      this.showIndex = 10000
    },
    getchargeDargStatus() {
      this.modalRun = false
    },
    chargeSubject(index) {
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        let subject = this.list[index].subject.name
        let newArr4 = this.subjectcopyList.length > 0 ? this.subjectcopyList : this.subjectList;
        let arr = []
        if (subject) {
          newArr4.forEach(function (item) {
            if (item.subjectName.indexOf(subject) != -1) {
              arr.push(item);
            }
          })
          if (arr.length > 0) {
            this.subjectList = arr
          } else {
            this.subjectList = []
          }
        } else {
          this.subjectList = this.subjectcopyList
        }
      }, 500)
    },
    chargeSummary(index) {
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        let main = this.list[index].main
        var newArr5 = JSON.parse(JSON.stringify(this.summaryList));
        let arr = []
        if (main) {
          newArr5.forEach(function (item) {
            if (item.name.indexOf(main) != -1) {
              arr.push(item);
            }
          })
          this.summaryList = arr
        } else {
          this.summaryList = this.summarydata
        }
      }, 500)
    },
    accAdd(arg1, arg2) {
      if (isNaN(arg1)) {
        arg1 = 0;
      }
      if (isNaN(arg2)) {
        arg2 = 0;
      }
      arg1 = Number(arg1);
      arg2 = Number(arg2);
      var r1, r2, m, c;
      try {
        r1 = arg1.toString().split(".")[1].length;
      }
      catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      }
      catch (e) {
        r2 = 0;
      }
      c = Math.abs(r1 - r2);
      m = Math.pow(10, Math.max(r1, r2));
      if (c > 0) {
        var cm = Math.pow(10, c);
        if (r1 > r2) {
          arg1 = Number(arg1.toString().replace(".", ""));
          arg2 = Number(arg2.toString().replace(".", "")) * cm;
        } else {
          arg1 = Number(arg1.toString().replace(".", "")) * cm;
          arg2 = Number(arg2.toString().replace(".", ""));
        }
      } else {
        arg1 = Number(arg1.toString().replace(".", ""));
        arg2 = Number(arg2.toString().replace(".", ""));
      }
      return (arg1 + arg2) / m;
    },
    subTraction(arg1, arg2) {
      if (isNaN(arg1)) {
        arg1 = 0;
      }
      if (isNaN(arg2)) {
        arg2 = 0;
      }
      arg1 = Number(arg1);
      arg2 = Number(arg2);
      var r1, r2, m, c;
      try {
        r1 = arg1.toString().split(".")[1].length;
      }
      catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      }
      catch (e) {
        r2 = 0;
      }
      c = Math.abs(r1 - r2);
      m = Math.pow(10, Math.max(r1, r2));
      if (c > 0) {
        var cm = Math.pow(10, c);
        if (r1 > r2) {
          arg1 = Number(arg1.toString().replace(".", ""));
          arg2 = Number(arg2.toString().replace(".", "")) * cm;
        } else {
          arg1 = Number(arg1.toString().replace(".", "")) * cm;
          arg2 = Number(arg2.toString().replace(".", ""));
        }
      } else {
        arg1 = Number(arg1.toString().replace(".", ""));
        arg2 = Number(arg2.toString().replace(".", ""));
      }
      return (arg1 - arg2) / m;
    },
    getsubjectData() {
      let param = {
        caseId: this.caseId
      };
      this.$api.GetsubjectlistData(param).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.subjectName = item.subjectCode + '\xa0' + item.subjectName
            item.subjectName = item.subjectCode + '\xa0' + item.subjectName
          })
          this.subjectList = res.data
          this.subjectcopyList = res.data
        }
      })
        .catch((err) => { })
        .finally((res) => {
          this.loading = false
        })
    },
    getSummaryData() {
      let param = 
        {      
          params: {
            caseid: this.caseId,
          }
        }
      this.$api.GetabsuctlistData(param).then((res) => {
        if (res.code == 200) {
          this.summarydata = res.data
          this.summaryList = res.data
        }
      })
        .catch((err) => { })
        .finally((res) => {
          this.loading = false
        })
    },
    judgeIsAuxiliary() {
      var flag = false;
      for (var i in this.list) {
        var detailJson = this.list[i].subject.detailJson;
        if (detailJson !== '' && detailJson !== undefined) {
          flag = true;
          this.list[i].isAuxiliary = true;
        } else {
          this.list[i].isAuxiliary = false;
          this.list[i].auxiliary = '';
        }
      }
      this.isAuxiliary = flag;
    },
    closeSubjectDialog(sub) {
      if (!sub.isTrusted) {
        this.list[this.dialogSubjectIndex].subject = sub;
      }
      this.dialogSubjectVisible = false;
      this.judgeIsAuxiliary();
    },
    clearAuxiliary(index, e) {
      this.list[index].auxiliary = '';
    },
    handleEvent(event) {
      //console.log(event);
      if (window.location.hash == '#/general_ledger/voucher_entry') {
        if (event.keyCode === 83 && event.ctrlKey) {
          //console.log('拦截到83+ctrl');
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 115) {
          //console.log('拦截到115');//F4
          this.addList();
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 121) {
          //console.log('拦截到121');//F10
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 122) {
          //console.log('拦截到122');//F11
          event.preventDefault();
          event.returnValue = false;
          return false;
        }
      } else {
        //需要销毁事件 防止全局生效
        //document.removeEventListener('keydown', this.handleEvent);
      }

    },
    showInput(index, type) {
      this.subjectList = this.subjectcopyList
      this.summaryList = this.summarydata
      for (var i in this.list) {
        this.list[i].isShowDebtorInput = false;
        this.list[i].isShowLenderInput = false;
        this.list[i].isShowMainInput = false;
        this.list[i].isShowSubjectInput = false;
        if (i == index && type == 'debtor') {
          if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
            return
          } else {
            this.list[index].isShowDebtorInput = true;
          }
        } else if (i == index && type == 'lender') {
          if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
            return
          } else {
            this.list[index].isShowLenderInput = true;
          }
        } else if (i == index && type == 'main') {
          if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
            return
          } else {
            this.list[index].isShowMainInput = true;
            this.list[index].main = this.subjectname;
            //this.chargeSummary(index)
          }
        } else if (i == index && type == 'subject') {
          if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
            return
          } else {
            this.list[index].isShowSubjectInput = true;
          }
        }
      }
    },
    hideInput(index, type) {
      if (type == 'debtor') {
        //判断是否有小数点
        var inx = this.list[index].debtor.indexOf('.');
        if (inx != -1 && this.list[index].debtor.length - 1 != inx) {
          this.list[index].debtor = (this.list[index].debtor * 1).toFixed(2);
        }
        this.list[index].isShowDebtorInput = false;
      } else if (type == 'lender') {
        //判断是否有小数点
        var inx = this.list[index].lender.indexOf('.');
        if (inx != -1 && this.list[index].lender.length - 1 != inx) {
          this.list[index].lender = (this.list[index].lender * 1).toFixed(2);
        }
        this.list[index].isShowLenderInput = false;
      } else if (type == 'main') {
        this.list[index].isShowMainInput = false;
        this.list[index].main = this.subjectname
      } else if (type == 'subject') {
        this.list[index].isShowSubjectInput = false;
        //this.list[index].subject.name = ''
      }
    },
    voluationInput(index, type, val) {
      if (type == 'main') {
        this.list[index].main = val;
        this.subjectname = val
        this.list[index].isShowMainInput = false;
      } else if (type == 'subject') {
        this.list[index].subject.name = val.subjectName;
        this.list[index].subject.subjectId = val.id;
        this.list[index].subject.subjectCode = val.subjectCode;
        this.list[index].isShowSubjectInput = false;
      }
      this.judgeIsAuxiliary();
    },
    selectionList(index, type) {
      //console.log('弹出选择列表');
      if (type == 'main') {
        this.dialogMainIndex = index;
      } else if (type == 'subject') {
        this.dialogSubjectIndex = index;
      }
    },
    keyupEvents(index, e, remaind) {
      if (e.keyCode == 37) {
        //console.log('拦截到37');//左
        this.keyboardEvents('left', index * 4 + remaind);
        return;
      } else if (e.keyCode == 38) {
        //console.log('拦截到38');//上
        this.keyboardEvents('up', index * 4 + remaind);
        return;
      } else if (e.keyCode == 39) {
        //console.log('拦截到39');//右
        this.keyboardEvents('right', index * 4 + remaind);
        return;
      } else if (e.keyCode == 40) {
        //console.log('拦截到40');//下
        this.keyboardEvents('down', index * 4 + remaind);
        return;
      } else if (e.keyCode == 13) {
        if (remaind == 2) {
          let val = this.subjectList.length > 0 ? this.subjectList[0] : {}
          this.list[index].subject.name = val.subjectName;
          this.list[index].subject.subjectId = val.id;
          this.list[index].subject.subjectCode = val.subjectCode;
        } else {
          let val = this.summaryList.length > 0 ? this.summaryList[0] : {}
          this.list[index].main = val.name;
        }
        this.keyboardEvents('enter', index * 4 + remaind);
        return;
      } else if (e.keyCode == 118) {
        //console.log('拦截到118');//F7
        if (remaind == 1) {
          this.selectionList(index, 'main');
        } else if (remaind == 2) {
          this.selectionList(index, 'subject');
        }
        return;
      }

      var main = this.list[index].main;
      var subject = this.list[index].subject.name;
      if (index - 1 >= 0) {
        if (main.indexOf('//') != -1 || subject.indexOf('//') != -1) {
          this.list[index].main = this.list[index - 1].main;
          this.list[index].subject = this.list[index - 1].subject;
          this.list[index].debtor = this.list[index - 1].debtor;
          this.list[index].debtorList = this.list[index - 1].debtorList;
          this.list[index].lender = this.list[index - 1].lender;
          this.list[index].lenderList = this.list[index - 1].lenderList;
          this.list[index].auxiliary = this.list[index - 1].auxiliary;
          this.calcDebtorTotal();
          this.calcLenderTotal();
        }
        if (main.indexOf('..') != -1) {
          this.list[index].main = this.list[index - 1].main;
        }
      }
      //判断是否显示辅助核算
      this.judgeIsAuxiliary();
    },
    keyboardEvents(type, number) {
      var total = this.list.length * 4;
      if (type == 'enter') {
        number++;
      } else if (type == 'left' && number - 1 > 0) {
        number--;
      } else if (type == 'right' && number + 1 <= total) {
        number++;
      } else if (type == 'up' && number - 4 > 0) {
        number = number - 4;
      } else if (type == 'down' && number + 4 <= total) {
        number = number + 4;
      }
      if (type == 'enter' && number > total) {
        this.addList();
      }
      var index = parseInt(number / 4);
      var remaind = number % 4;
      if (remaind == 1) {
        this.showInput(index, 'main');
      } else if (remaind == 2) {
        this.showInput(index, 'subject');
      } else if (remaind == 3) {
        this.showInput(index, 'debtor');
      } else if (remaind == 0) {
        this.showInput(index - 1, 'lender');
      }
    },
    debtorInputKeyUp(index, e, remaind) {
      if (e.keyCode === 187) {
        this.calcDebtorTotal(index);
        this.calcLenderTotal(index);
        var cha = this.subTraction(this.lenderTotal, this.debtorTotal)
        if (cha == 0) {
          cha = '';
        }
        this.list[index].debtor = cha + '';
      }
      this.list[index].lender = '';
      this.list[index].lenderList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      var debtor = this.list[index].debtor;
      var debtorList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      this.list[index].debtorList = this.collatingData(debtor, debtorList);
      this.calcDebtorTotal();
      this.calcLenderTotal();
    },
    calcDebtorTotal(index) {
      var debtorTotal = 0;
      let DebtorArrayTotal = []
      for (var i in this.list) {
        if (this.list[i].debtor != null && this.list[i].debtor != '') {
          if (!(index && index == i)) {
            //debtorTotal += (this.list[i].debtor) * 1;
            DebtorArrayTotal.push(this.list[i].debtor)
          }
        }
      }
      let sumd = 0;
      DebtorArrayTotal.forEach((i) => {
        sumd = this.accAdd(sumd, i)
      })
      this.debtorTotal = sumd;
      debtorTotal = sumd + '';
      this.debtorTotalmax = sumd
      if ((this.debtorTotalmax !== 0 || this.debtorTotalmax !== 0) && Number(this.debtorTotalmax).toFixed(2) !== Number(this.lenderTotalmax).toFixed(2)) {
        this.totalamount = '凭证借贷不平衡'
      } else {
        this.totalamount = DX(debtorTotal)
      }
      var debtorTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      this.debtorTotalList = this.collatingDataTotal(debtorTotal, debtorTotalList);
      //console.log(this.debtorTotal,'xdfg----')
    },
    formatAmountToChineses(amount) {
      ////console.log('global-formatAmountToChinese')
      amount = parseFloat(amount);
      if (isNaN(amount)) {
        return;
      }
      amount = Math.round(amount * 100);
      var isInt = amount % 100 == 0 ? true : false;
      var numArr = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
      var unitArr = ["分", "角", "元", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", '十亿', '佰亿'];
      var resultStr = '',
        num, unitIdx, len, zeroCount = 0;
      if (amount == 0) {
        return '零元整';
      }
      if (amount < 0) {
        resultStr += '负';
        amount = -amount;
      }
      amount = amount.toString();
      len = amount.length;
      for (var i = 0; i < len; i++) {
        num = parseInt(amount.charAt(i));
        unitIdx = len - 1 - i;
        if (num == 0) {
          //元 万 亿 输出单位
          if (unitIdx == 2 || unitIdx == 6 || unitIdx == 11) {
            resultStr += unitArr[unitIdx];
            zeroCount = 0;
          } else {
            zeroCount++;
          }
        } else {
          if (zeroCount > 0) {
            resultStr += '零';
            zeroCount = 0;
          };
          resultStr = resultStr + numArr[num] + unitArr[unitIdx];
        }
      };

      if (isInt) {
        resultStr += '整';
      };

      return resultStr;
    },
    lenderInputKeyUp(index, e, remaind) {
      if (e.keyCode === 187) {
        this.calcDebtorTotal(index);
        this.calcLenderTotal(index);
        var cha = this.subTraction(this.debtorTotal, this.lenderTotal)
        if (cha == 0) {
          cha = '';
        }
        this.list[index].lender = cha + '';
      } else if (e.keyCode === 32) {
        this.list[index].isShowDebtorInput = true;
        this.list[index].isShowLenderInput = false;
        this.list[index].debtor = this.list[index].lender.trim();
        this.list[index].lender = '';
        this.list[index].lenderList = ['', '', '', '', '', '', '', '', '', '', ''];
        var debtorList = ['', '', '', '', '', '', '', '', '', '', ''];
        this.list[index].debtorList = this.collatingData(this.list[index].debtor, debtorList);
        this.calcLenderTotal();
        this.calcDebtorTotal();
        return;
      } else if ((e.keyCode >= 37 && e.keyCode <= 40) || e.keyCode == 13) {
        this.keyupEvents(index, e, remaind);
        return;
      }
      this.list[index].debtor = '';
      this.list[index].debtorList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      var lender = this.list[index].lender;
      var lenderList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      this.list[index].lenderList = this.collatingData(lender, lenderList);
      this.calcLenderTotal();
      this.calcDebtorTotal();
    },
    calcLenderTotal(index) {
      var lenderTotal = 0;
      let lenderArrayTotal = []
      for (var i in this.list) {
        if (this.list[i].lender != null && this.list[i].lender != '') {
          if (!(index && index == i)) {
            //lenderTotal += (this.list[i].lender) * 1;
            lenderArrayTotal.push(this.list[i].lender)
          }
        }
      }
      let sum = 0;
      lenderArrayTotal.forEach((i) => {
        sum = this.accAdd(sum, i)
      })
      this.lenderTotal = sum;
      lenderTotal = sum + '';
      var lenderTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      this.lenderTotalList = this.collatingDataTotal(lenderTotal, lenderTotalList);
      this.lenderTotalmax = lenderTotal
      if ((this.debtorTotalmax !== 0 || this.debtorTotalmax !== 0) && Number(this.debtorTotalmax).toFixed(2) !== Number(this.lenderTotalmax).toFixed(2)) {
        this.totalamount = '凭证借贷不平衡'
      } else {
        this.totalamount = DX(lenderTotal)
      }
    },
    addList(index) {
      if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
        return
      } else {
        var obj = {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        };
        this.list.splice(index, 0, obj)
      }
    },
    removeList(index) {
      if (this.auditType == 1 || this.auditAccountType == 1 || this.isViewRecode) {
        return
      } else {
        if (this.list.length <= 2) {
          this.$message.error('至少保存两行');
          return;
        }
        this.list.splice(index, 1);
        this.calcDebtorTotal();
        this.calcLenderTotal();
      }
    },
    collatingData(debtor, debtorList) {
      debtor = debtor.replace(/-/g, '');
      //判断是否有小数点
      if (debtor.indexOf('.') != -1) {
        debtor = (debtor * 1).toFixed(2);
        debtor = (debtor + '').replace(/\./g, '');
      } else if (debtor && debtor !== '0') {
        debtor = debtor + '00';
      } else {
        debtor = ''
        debtor = debtor + '';
      }
      if (debtor.length <= 13) {
        var cha = debtorList.length - debtor.length;
        for (var i = 0; i < debtor.length; i++) {
          debtorList[i + cha] = debtor.charAt(i);
        }
      } else {
        for (var i = 0; i < debtor.length; i++) {
          debtorList[i] = debtor.charAt(i);
        }
      }
      return debtorList;
    },
    collatingDataTotal(debtor, debtorList) {
      debtor = debtor.replace(/-/g, '');
      //判断是否有小数点
      if (debtor.indexOf('.') != -1) {
        debtor = (debtor * 1).toFixed(2);
        debtor = (debtor + '').replace(/\./g, '');
      } else if (debtor && debtor !== '0') {
        debtor = debtor + '00';
      } else {
        debtor = 0
        debtor = debtor + '';
      }
      if (debtor.length <= 13) {
        var cha = debtorList.length - debtor.length;
        for (var i = 0; i < debtor.length; i++) {
          debtorList[i + cha] = debtor.charAt(i);
        }
      } else {
        for (var i = 0; i < debtor.length; i++) {
          debtorList[i] = debtor.charAt(i);
        }
      }
      return debtorList;
    },
    checkListData() {
      // if ((this.debtorTotal != 0 || this.lenderTotal != 0) && Number(this.lenderTotal).toFixed(2) != Number(this.debtorTotal).toFixed(2)) {
      //   this.$message.error('凭证借贷不平衡，请检查');
      //   return;
      // }
      //var mainFlag = true;
      // for (var i in this.list) {
      //   if (this.list[i].main != null && this.list[i].main != '') {
      //     mainFlag = false;
      //   }
      //   if (this.list[i].subject.name == null || this.list[i].subject.name == '') {
      //     this.$message.error('第' + (i * 1 + 1) + '行中的会计科目为必填项，请填写了再提交');
      //     return;
      //   }
      //   if ((this.list[i].debtor == null || this.list[i].debtor == '') && (this.list[i].lender == null || this.list[i].lender == '')) {
      //     this.$message.error('第' + (i * 1 + 1) + '行中借方金额、贷方金额必须填一个，请填写了再提交');
      //     return;
      //   }
      // }
      // if (mainFlag) {
      //   this.$message.error('必须填写一个摘要，请填写了再提交');
      //   return;
      // }
      // this.list[a].subject.subjectCode
      var list = [];
      for (var a = 0; a < this.list.length; a++) {
        let currentName = this.list[a].subject.name;
        let kl = currentName.indexOf("\xa0");
        let subjectNew = currentName.slice(kl + 1, currentName.length);
        var obj = {
          subjectId: this.list[a].subject.subjectId?this.list[a].subject.subjectId:'',
          subjectCode: "",
          abstracts: this.list[a].main,
          subjectName: subjectNew,
          borrowersAmount: this.list[a].debtor,
          creditorAmount: this.list[a].lender
        };
        if (obj.abstracts !== '' || obj.borrowersAmount !== '' || obj.creditorAmount !== '' || obj.subjectName !== '') {
          list.push(obj)
        }
      }
      var reqObj = {
        data: {
          headItem: {
            word: this.voucher.word,
            date: this.date!==''?this.formatDate("yyyy-MM-dd hh:mm:ss", this.date):'',
            no: this.no,
            invoicesNum: this.billdata
          },
          bodyItems: list,
          auditItem: {
            accounting: this.accountHeader,
            cashier: this.Debtor,
            audit: this.checker,
            invoices: this.voucherfrom
          }
        }
      }
      return reqObj;
    },
    formatDate(fmt, date) {
      if (typeof date === 'string') {
        date = new Date(date);
      }
      var o = {
        "M+": date.getMonth() + 1,                 //月份
        "d+": date.getDate(),                    //日
        "h+": date.getHours(),                   //小时
        "m+": date.getMinutes(),                 //分
        "s+": date.getSeconds(),                 //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds()             //毫秒
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    },
    isFieldEmpty(arr, field) {
    return arr.every(item => !item[field]);
    },
    cancelback() {
      this.$router.go(-1);
    },
    cancelback() {
      this.$router.go(-1);
    },
    clearExamine() {
      if (this.auditType == 1 || this.auditAccountType == '1' || this.isViewRecode) {
        return
      } else {
        this.dialogClearVisible = true
      }
    },
    init() {
      this.voucher = { word: '记', no: '', date: '', bill: 0 };
      let list = [
        {
          main: '',
          itemflag: false,
          isShowMainInput: false,
          subject: {
            // number: '',
            name: '',
            subjectId: '',
            subjectCode: '',
            // detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
        },
        {
          main: '',
          itemflag: false,
          isShowMainInput: false,
          subject: {
            // number: '',
            name: '',
            // detailJson: ''
            subjectId: '',
            subjectCode: '',
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            // number: '',
            name: '',
            // detailJson: ''
            subjectId: '',
            subjectCode: '',
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
        },
        {
          main: '',
          isShowMainInput: false,
          itemflag: false,
          subject: {
            // number: '',
            name: '',
            // detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', '', '', '', '', ''],
          isShowLenderInput: false,
        }
      ]
      list.forEach((item, index) => {
        var debtorList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
        var lenderList = ['', '', '', '', '', '', '', '', '', '', '', '', '']
        item.debtorList = this.collatingData(item.debtor, debtorList)
        item.lenderList = this.collatingData(item.lender, lenderList)
      })
      this.list = list
      this.showstatus = ''
      this.debtorTotal = 0;
      this.debtorTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
      this.lenderTotal = 0;
      this.lenderTotalList = ['', '', '', '', '', '', '', '', '', '', '', '', ''];
    }
  }
}
</script>
 
<style lang="scss">
.charge-containerset {
  padding: 0px 0px 10px 0px;
  font-size: 14px;
  color: #444;
  font-weight: 400;
  background-color: white;
  height: calc(100% - 50px);
  position: relative;




  .manualTM {
    width: 100%;
    padding-top: 12px;
    padding-bottom: 8px;

    //border-bottom: 1px solid #EDEDED;
    .pointa {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
    }
  }



  .accountmain {
    padding-top: 5px;
    height: 46px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;

    .accountchild {
      display: flex;
      align-items: center;
      font-size: 15px;

      .accountchild-el {
        display: flex;
        align-items: center;

        .accountshow {
          position: relative;
          z-index: 100;
        }
      }

      .accountchild-clear {
        position: relative;
        cursor: pointer;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #D92316;
        z-index: 100;
      }

      .el-input {
        width: 46%;
        padding-left: 8px;
      }

      .el-input__inner {
        height: 28px;
        line-height: 28px;
        cursor: pointer;
        color: red;
        text-align: center;
        border: 4px solid red !important;
      }
    }
  }

  table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    padding: 0;
    margin: 0;
  }

  table td {
    text-align: center;
    table-layout: fixed;
    padding: 0px;
    position: relative;
  }

  .main-subject {
    position: relative;
    height: 60px;
    line-height: 21px;
    text-align: left;
  }

  .chargescrollb {
    width: 100%;
    height: calc(100vh - 480px);
    overflow: auto;

    .charge {
      width:calc(100% - 40px);
       margin: 0 auto;
      // width: 100%;
      position: relative;

      .closeBabelt {
        position: absolute;
        top: 54%;
        left: 50%;
        margin-left: -60px;
        margin-top: -20px;
      }

      .closeBabel {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -60px;
        margin-top: -95px;
      }

      .showbtn1 {
        // position: absolute;
        // top: 0;
        // left: -21px;
        width: auto;
        height: auto;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        .charge-table-icon {
          cursor: pointer;
          display: flex;
          align-items: center;
          background-color: rgb(216, 216, 216, .5);
          color: #666666;
          font-size: 13px;
          margin-bottom: 1px;
          border-radius: 4px;
          padding: 2px 3px 2px 3px;
        }
      }
    }

 
  }

  .footbtns {
    width: 100%;
    text-align: center;
    background-color: #fff;
    position: absolute;
    bottom: 40px;

    .btndefault {
      width: 100px;
      height: 40px;
    }

    .btnsend {
      width: 100px;
      height: 40px;
      background: var(--theme_primary_color);
      color: #fff;
    }

    .btnsave {
      width: 100px;
      height: 40px;
      background: #fff;
      border: 1px solid var(--theme_primary_color);
      color: var(--theme_primary_color);
    }

    .el-button {
      padding: 10px 14px;
    }
  }

  .charge-header {
    margin-bottom: 10px;
  }

  .charge-header>div {
    display: inline-block;
    // margin-right: 15px;
  }


  .tip-box {
    width: 330px;
    padding: 10px;
    position: absolute;
    top: 27px;
    right: -15px;
    z-index: 1005;
    background-color: #fff;
    box-shadow: 0 0 6px rgba(170, 170, 170, .73);
    display: none;
  }

  .tip-box-table tr {
    height: 25px;
  }

  .el-icon-info {
    font-size: 18px;
    margin-left: 30px;
    cursor: pointer;
  }

  .el-icon-info:hover+.tip-box {
    display: inline-block;
  }

  .el-icon-more {
    position: absolute;
    top: 22px;
    right: 10px;
    z-index: 2;
    color: #666;
    cursor: pointer;
    font-size: 16px;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0px;
    float: left;
    padding: 5px 0 0 0;
    margin: 2px 0 0;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    z-index: 1015;
  }

  .suggest-list {
    width: 100%;
    height: auto;
    z-index: 1015;
    min-width: inherit;
    display: block;
    overflow: hidden;
    border: none;
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
  }

  .suggest-list .item-list {
    max-height: 285px;
    overflow: auto;
    list-style: none;
    margin: 0px;
    padding: 0px;
  }

  .item-list li {
    display: flex;
    padding: 0 10px;
    height: auto;
    line-height: 24px;
    width: 210px;
    cursor: pointer;

    // overflow: hidden;
    // white-space: nowrap;
    // text-overflow: ellipsis;
  }

  .item-list li:hover {
    background: var(--theme_font_hover_color);
    font-size: 17px;
    font-weight: bold
  }

  .item-list li.hover {
    background: var(--theme_font_hover_color);
  }

  .charge-table,
  .tip-box-table {
    border: 1px solid #bab9b9;
    border-right: none;
  }

  .debtor-border {
    border: 2px solid red;
  }

  .abstracts {
    font-weight: 600;
  }

  .charge-table,
  .debtor-tbale,
  .lender-tbale>tr {
    height: 60px;
  }

  .charge-table>tr:first-child {
    height: 50px;
  }

  .charge-table>tr {
    // border:1px solid #000;
    border-color: #bab9b9;
  }

  .td-auxiliary-dis {
    background-color: #f7f7f7;
  }

  .auxiliary-accounting {
    height: 60px;
    overflow: auto;
    padding: 15px 0 0 30px;
  }

  .auxiliary-accounting:before {
    content: "+";
    font-size: 30px;
    color: #4a90e2;
    cursor: pointer;
    padding: 0 11px;
    position: absolute;
    top: 0;
    left: 0;
    line-height: 60px;
  }

  .auxiliary-single {
    display: flex;
    float: left;
    height: 28px;
    line-height: 28px;
    margin-right: 5px;
    cursor: pointer;
    background: #eee;
    padding: 0 8px;
    border-radius: 2px;
  }

  .auxiliary-single span {
    max-width: 90px;
    overflow: hidden;
    height: 28px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .auxiliary-single i {
    color: red;
    margin: 8px 0 8px 7px;
    font-size: 10px;
    visibility: hidden;
  }

  .auxiliary-single:hover i {
    visibility: inherit;
  }

  .charge-table-icon {
    cursor: pointer;
    display: inline-block;
  }

  .debtor-lender-table tr td {
    width: 7%;
    border-right: 1px solid #bab9b9;
  }

  .debtorword td {
    color: #939393;
  }

  .debtor-lender-table tr td:nth-child(3) {
    border-right-color: rgba(74, 144, 226, .5);
  }

  .debtor-lender-table tr td:nth-child(6) {
    border-right-color: rgba(74, 144, 226, .5);
  }

  .debtor-lender-table tr td:nth-child(9) {
    border-right-color: rgba(226, 106, 74, .5);
  }

  .debtor-lender-table tr td:last-child {
    border-right: none;
  }

  .tr-negative {
    color: red;
  }

  .charge-table input,
  select {
    width: 100%;
    height: 60px;
  }

  .charge-table textarea {
    width: 100%;
    height: 60px;
    line-height: 21px;
    padding: 9px 14px 9px 10px;
    overflow: auto;
    resize: none;
    border: none;
    border-radius: 0px;
    margin: 0;
    color: #444;
    box-sizing: border-box;
    // border: 2px solid red;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
  }

  .nt-tl {
    margin-left: 10px;

    .el-radio-group {
      .el-radio {
        margin-right: 8px;
      }

      .el-radio__inner {
        border-radius: 0;
        border: 1px solid #c1c1c1;
      }

      .el-radio__input.is-checked .el-radio__inner {
        border-color: #ffffff;
        background: #ffffff;
      }

      .el-radio__input.is-checked .el-radio__inner::after {
        // transform: rotate(45deg) scaleY(1);
      }

      .el-radio__inner::after {
        height: 0px;
      }

      .el-radio__label {
        display: none;
      }
    }

    .isNowItem {
      border: none !important;
      // box-shadow: 0px 1px 2px #E6BF8A;
      background: #E6BF8A !important;
      // .el-radio__inner {
      //   border: none !important;
      //   box-shadow: 0px 4px 4px #999999;
      //   background: #999999;
      //   // border: 4px;
      // }
    }

    .isNowItemb {
      border: none !important;
      background: #E6BF8A !important;
      cursor: pointer;
    }

    .istrue {
      width: 14px;
      height: 14px;
      background: #3886FF;
      margin-right: 10px;
      position: relative;

      .doted {
        position: absolute;
        width: 5px;
        height: 5px;
        bottom: -8px;
        left: 5px;
        border-radius: 50%;
        background-color: #FF554C;
      }
    }

    .pointflex {
      display: flex;
      flex-wrap: wrap;

      .isfalse {
        width: 16px;
        height: 16px;
        background: #cdcdcd;
        margin-right: 10px;
        color: #fff;
        margin-top: 5px;
        cursor: pointer;
        text-align: center;
        line-height: 17px;
        border-radius: 2px;
        font-size: 13px;
        // .el-radio__inner {
        //   background: #cdcdcd !important;
        // }
      }

      .istrue {
        width: 16px;
        height: 16px;
        background: var(--theme_primary_color);
        ;
        margin-right: 10px;
        color: #fff;
        text-align: center;
        line-height: 17px;
        border-radius: 2px;
        font-size: 13px;
        cursor: pointer;
        margin-top: 5px;
        // .el-radio__inner {
        //   background: #3886FF !important;
        // }
      }

      .pointflex {
        display: flex;

        .isfalse {
          width: 14px;
          height: 14px;
          background: #cdcdcd;
          margin-right: 10px;
          font-size: 13px;
          cursor: pointer;
          // .el-radio__inner {
          //   background: #cdcdcd !important;
          // }
        }

        .istrue {
          width: 14px;
          height: 14px;
          background: #3886FF;
          margin-right: 10px;
          font-size: 13px;
          cursor: pointer;
          // .el-radio__inner {
          //   background: #3886FF !important;
          // }
        }
      }

      .isdo {
        background: #E6BF8A !important;
        // .el-radio__inner {
        //    background: #E6BF8A !important;
        // }
      }
    }
  }

  .nt-tlchild {
    padding-top: 10px;

    .el-radio-group {
      .el-radio {
        margin-right: 8px;
      }

      .el-radio__inner {
        border-radius: 0;
        border: 1px solid #c1c1c1;
      }

      .el-radio__input.is-checked .el-radio__inner {
        border-color: #ffffff;
        background: #ffffff;
      }

      .el-radio__input.is-checked .el-radio__inner::after {
        // transform: rotate(45deg) scaleY(1);
      }

      .el-radio__inner::after {
        height: 0px;
      }

      .el-radio__label {
        display: none;
      }
    }


  }

  .el-dialog__body {
    padding: 10px 20px;
  }

  .popperitem {
    background: transparent !important;
  }

}

.fontclass {
  align-items: center;

  .el-notification__content {
    font-size: 14px;
    margin: 0;
  }
}
</style>