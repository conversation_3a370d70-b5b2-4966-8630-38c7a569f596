<template>
  <div class="answer-hint-page">
    <!-- 答案解析 弹窗 -->

    <!-- 题库 -->
    <question-preview-content
        v-if="questionTypeLabel[questionInfo.questionType]"
        :show-form="questionInfo.questionType==5?true:false"
        :isAnswerHint="true"
        :data="getTikuData()">
    </question-preview-content>
  </div>
</template>

<script>
import {questionTypeLabel} from "@/components/base/question/util";
import {initApiQuestion} from "@/components/question/utils";

const questionPreviewContent = () => import('@/components/question/question-preview-content.vue')

export default {
  name: 'AnswerHint',
  props: {

    questionInfo: {
      type: Object,
      default: () => {}
    }
  },
  
  components: {
    questionPreviewContent
  },
  provide() {
    return {
      hintToSpread: this.handleDataFromSpread
    };
  },
  data() {
    return {
      loading: false, //
      formVal: "", // 动态表单
      formCode: "", // 动态表单
      templateObj: {}, // spreadJS 模版
      templateData: {}, // spreadJS 数据
      templateIdData: {}, // 模板的数据idkey
      flowConfig: {
        //流程图数据
      },
      isShowFlow: false, // 显示流程的标识
      isTransition: true,
      screenHeight: document.body.clientHeight - 90 + "px", // 全屏高度'
      dataId:0,
      questionTypeLabel: questionTypeLabel
    }
  },
  watch: {
    questionInfo: {
      handler(val) {

        this.dataId = this.questionInfo.contentData?.content?.dataId||this.questionInfo.contentData?.content?.formVal?.id
        
        this.renderForm(val.contentData?.content);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getTikuData () {
      return initApiQuestion(this.questionInfo.contentData.content)
    },
    async renderForm(formObj) {
      if (!formObj) return;
      if (formObj.sceneType === 1 && formObj.formType === 1) {
        // 动态表单
        const formId = formObj.id;
        const res = await this.$api.GetDynamicById({
          id: formId
        });
        this.formCode = res.data.config;
        this.formVal = formObj.formVal;
      } else if (formObj.sceneType === 1 && formObj.formType === 2) {     // spreadJS
    
        const { data } = await this.$api.GetDynamicById({ id: formObj.id });
        this.templateObj = data;
        // spread 题目 构建初始化数据
        this.templateData = await this.initSpreadData(this.templateObj.erId);
        if (this.questionInfo.answer) {
          let {answerTemplate,checkKeyList} = JSON.parse(this.questionInfo.unAnswerData); // 答案
          this.templateData = this.handleConcatAnswer(answerTemplate,this.questionInfo.answer);
          // 作答记录
          //  const templateData = this.questionInfo.answer;
          // if (templateData) {
          //   // 存在作答记录则渲染
          //   this.templateData = JSON.parse(templateData);
          // }
          
        }
      }
    },
    // 合并 答案 和answerTemplate作为完整记录
    handleConcatAnswer(answerTemplate,answer){
      let answerObj = JSON.parse(answer)
      for(let key in answerObj){
        let tableCode = key.split('__')[0];
        let columnCode = key.split('__')[1];
        let index = key.split('__')[2]-1;
        if(Array.isArray(answerTemplate[tableCode])){ // 表格
          answerTemplate[tableCode] = answerTemplate[tableCode].map((v,i)=>{
            if(i==index){
              v[columnCode] = answerObj[key];
            }
            return v
          })
        }else{ // 表单
          answerTemplate[tableCode][columnCode] = answerObj[key];
        }
      }
      return answerTemplate
    },
     // 初始化spread数据结构
     async initSpreadData(erId) {
      const { data, code } = await this.$api.GetTableMapByERId({ erId });
      if (code === 200) {
        const initData = {};
        data.forEach(val => {
          const colObj = {};
          if (val.type === "oneToOne" || val.isMain) {
            // 主表 'main' 或 "oneToOne"  一对一
            val.columns.forEach(col => {
              colObj[col.columnCode] = "";
            });
            initData[val.objectName] = colObj;
          } else if (val.type === "oneToMany") {
            // "oneToMany" 一对多
            val.columns.forEach(col => {
              colObj[col.columnCode] = "";
            });
            initData[val.objectName] = [colObj];
          }
        });
        return initData;
      }
    },
    async getSingleData(params) {
      const res = await this.$api.FetchDispatchSingleData({
        id: this.dataId,
        erId:this.questionInfo.contentData?.content?.erId,
      });
      return new Promise((resolve, reject) => {
        resolve(res);
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.answer-hint-page{
  height: 700px;
  // ::v-deep .gc-designer-container{
  //   height: 700px!important;
  // }
}

</style>