<!--
 * @Author: song <EMAIL>
 * @Date: 2024-02-20 14:41:22
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2025-03-18 16:49:58
 * @FilePath: \fusion_front\src\views\student\tasks\index.vue
 * @Description:  学生任务列表
 
-->
<template>
  <div class="content-page student-task-page">
    <LeftMenu v-if="showLeftMenu" :activeMenu="'task'" @handleTaskData="handleTaskData"></LeftMenu>

    <div class="content-box" :style="showLeftMenu?'width:calc(100vw - 260px)':'width:100vw'">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item  v-if="showLeftMenu" :to="{ path: '/studentCourseList' }">课程</el-breadcrumb-item>
        <el-breadcrumb-item  v-if="showLeftMenu">课程详情</el-breadcrumb-item>
        <el-breadcrumb-item v-if="!showLeftMenu">我的任务</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="content-wrapper">
        <div class="content">
          <studentsTask :taskCategory="showLeftMenu ? 2 : null" ref="taskchild"></studentsTask>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LeftMenu from '../components/left-menu.vue';
import studentsTask from '@/components/students-task/index.vue';


export default {
  name: 'StudentTask',
  components: {
    LeftMenu,
    studentsTask
  },
  props: {

  },
  data() {
    return {
      showLeftMenu: sessionStorage.getItem('courseId'),
    };
  },
  computed: {

  },
  watch: {
    '$route': {
      handler(val, oldval) {
        // console.log(val);//新路由信息
        // console.log(oldval);//老路由信息
      },
      deep: true,
      immediate:true
    }
  },

  created() {

  },
  mounted() {

  },

  methods: {
    handleTaskData() {
      this.$refs.taskchild.loadList()
    },
  }
};
</script>

<style lang="scss" scoped>
.student-task-page {
  .content-box,
  .content,
  .content-wrapper{
    display:block!important;
    position: relative;
  }
  
  .content-wrapper{
    height: 100%;
  }
}
</style>
