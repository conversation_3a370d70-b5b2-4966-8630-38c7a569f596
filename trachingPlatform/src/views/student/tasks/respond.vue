<template>
  <div class="studentRespond-page" ref="respondBox" @scroll="isViewArea">
    <div
      class="respond-header"
      ref="headerBar">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item @click="returnBack">{{this.$route.query.taskType==3?'考核':'作业'}}</el-breadcrumb-item>
        <el-breadcrumb-item>{{this.$route.query.taskType==3?'课程考核':'课程作业'}}</el-breadcrumb-item>
      </el-breadcrumb>
      <!-- <el-button
        plain
        size="mini"
        class="back-btn"
        :style="isOpenAside ? 'right:500px' : 'right:0'"
        @click="returnBack"
        >返回</el-button
      > -->
    </div>
    <div
      class="respond-main-content"
      :style="isOpenAside ? 'width:calc(100% - 500px)' : 'width:100%'">
      <el-container>
        <el-main>
          <div>
            <div
              v-for="(item, index) in respondList"
              :key="item.id"
              class="main-content-info"
              :ref="`qs_${item.id}`"
              >
              <div class="main-body-header">
                <span class="main-header-info">{{ index + 1 }}.<span class="question-label">[{{questionTypeLabel[item?.questionType]}}]</span> {{ item.title }}</span>
                <div>
                  <span style="display: inline-block; font-size: 14px; margin-right: 10px">
                    <span>分值</span>  
                    <span style="margin-left: 4px; text-decoration: underline"> {{ item.score }} 分</span>
                  </span>
                  <el-button
                    v-if="item.isScene"
                    class="operate-btn info-btn"
                    plain
                    size="mini"
                    @click="lookInformation(item)">参考数据</el-button
                  >
                  <el-button
                    v-if="item.showAnswer && item?.isReadonly != 1&&item?.questionType != 50"
                    class="operate-btn answer-btn"
                    plain
                    size="mini"
                    @click="lookAnswer(item)">参考答案</el-button>
                  <el-button v-if="taskStatus != 2 && item?.questionType != 42&&item?.questionType != 50" class="operate-btn" size="mini"
                    :disabled="!item?.isSubmit" @click="resetAnswer(item, index)">重置</el-button>
                  <el-button v-if="taskStatus != 2 && item?.questionType != 42&&item?.questionType != 50" class="operate-btn submit-btn"
                    size="mini" v-preventReClick="1000" @click="submitAnswer(item, index)">保存</el-button>
                </div>
              </div>
              <div v-if="questionTypeLabel[item?.questionType]" style="padding: 20px;">
                <answer-form-preview
                  :ref="item.id"
                  v-if="item.questionDetail && item.questionType !==49"
                  :type="item.questionType"
                  :config="item.questionDetail.optionConfig"
                  :title="item.questionDetail.title"
                  :desc="item.questionDetail.desc"
                  :answer-value.sync="item.questionDetail.studentAnswer"
                  :error="item.questionDetail.answerCompareInfo"
                  :isDoTask="true"
                  :disable="false"
                  :showAnswer="false"
                  :sort="index"
                  :isshowscore="true"
                  :isshowAnalysis="true"
                  :isshowBtn="true"
                  :isshowDel="false"
                  :gradeId="gradeId"
                  >
                </answer-form-preview>
                <!-- <div v-if="item.questionType==46||item.questionType==47"> -->
                <div v-if="item.questionType==47||item.noAutoScore">
                <!--手动评分 -->
                <manualScore 
                v-if="handleShowManual(item)" 
                :score="item.score" 
                :teacherComment="item.gradeDetails?item.gradeDetails[0].teacherComment:''"
                :reviewed="item.gradeDetails?item.gradeDetails[0].reviewed:false"
                :gradeDetailId="item.gradeDetails?item.gradeDetails[0].id:0"/>
                </div>
                <questionVocherform
                    v-if="item.type == 49"
                    :ref="item.id"
                    :info="item"
                    :sort="index"
                    :config="item.gradeDetails&&item.gradeDetails.length !== 0 ?item.gradeDetails[0]:null"
                  >
                </questionVocherform>
              </div>
              <div v-else >
                <div class="main-body-content">
                  <p v-show="Boolean(item.description)&&item.description!='<p><br></p>'" class="description" v-html="item.description"></p>
                </div>
                <div
                    v-if="item?.questionType == 42"
                    class="accountbtn">
                  <el-button
                      class="btnblue"
                      @click="GetaccountPage(item)"
                  >进入核算作答</el-button
                  >
                </div>
                <div v-if="item?.questionType == 1"></div>

                <!-- :style="item?.questionType == 41 || item?.questionType == 4 ? 'height:600px' : ''" -->
                <div
                    v-if="item?.questionType !== 42"
                    class="question-container">
                  <el-button
                      v-if="!item.contentData"
                      class="open-detail"
                      plain
                      @click="openQuestionDetail(item, index)">
                    展开详情</el-button
                  >
                  <question-detail
                      v-if="item?.questionType !== 42"
                      :ref="item.id"
                      :questionInfo="item"></question-detail>
                </div>
              </div>
            </div>
          </div>
        </el-main>
        <el-aside
          width="500px"
          ref="asideNav"
          :style="isOpenAside ? 'right:0;' : 'right:-500px;'"
          class="aside-nav">
          <span
            class="expend-btn"
            @click="checkNav()"
            ><i :class="isOpenAside ? 'el-icon-arrow-left' : 'el-icon-arrow-right'"></i
          ></span>
          <section>
            <div class="aside-content-info">
              <div class="main-body-header">
                <div class="main-header-info">任务</div>
              </div>
              <section class="aside-main-info">
                <div class="aside-main-quest">
                  <p>
                    作业名称 <span style="margin-left: 30px">{{ $route.query.taskName }}</span>
                  </p>
                  <p>
                    任务时间 <span style="margin-left: 30px">{{ $route.query.taskBeginTime }} <span style="color: #2b66ff">至</span> {{ $route.query.taskEndTime }}</span>
                  </p>
                  <p>
                    任务分值 <span style="margin-left: 30px">{{ accountscore }} 分</span>
                  </p>
                  <p v-if="viewType == 2">
                    任务得分 <span style="margin-left: 30px">{{ $route.query.gradeScore }} 分</span>
                  </p>
                  <p v-if="viewType == 2">作答时长 <span style="margin-left: 30px"></span></p>
                </div>
                <div class="aside-main-time">
                  <div class="drawertime">
                    <div class="drawertimecount">
                      <div class="drawerseconds">
                        <span v-if="day !== 0"
                          ><span class="drawerday">{{ nonReactiveTime.day }}</span
                          >天</span
                        >
                        <span class="drawerday">{{ nonReactiveTime.hr }}</span
                        >小时 <span class="drawerday">{{ nonReactiveTime.min }}</span
                        >分钟 <span class="drawerday">{{ nonReactiveTime.sec }}</span
                        >秒
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="viewType == 1"
                  class="main-content-button">
                  <el-button
                    type="primary"
                    @click="submitExam"
                    >交卷</el-button
                  >
                </div>
              </section>
            </div>
            <div class="aside-content-info info-answer">
              <div class="main-body-header">
                <div class="main-header-info">
                  答题卡 <span style="color: #ff0000">{{ doNum }}</span> / {{ handleTotalNum() }}
                </div>
                <el-progress :percentage="handlePercent()"></el-progress>
              </div>
              <section>
                <div class="main-content-markers">
                  <div class="manualcolorb"></div>
                  <span>已答</span>
                  <div class="manualcolorf"></div>
                  <span>未答</span>
                </div>
                <div class="main-content-topic">
                  <div
                    v-for="(item, index1) in questionList"
                    :key="index1"
                    class="main-topic-list">
                    <div class="main-topic-flex">
                      <div class="main-topic-list-l" @click="openQuestionDetail(item,index1,'skip')">
                        <span :class="['q-item', cachedHandleQStatus(item)?'done':'undo']">{{ index1 + 1 }}</span>
                        <span :title="item?.title" class="title">{{ item?.title }}</span>
                      </div>
                      <div
                        class="main-topic-list-r"
                        v-if="item?.questionType !== 42 && item.showScore">
                        <span
                          v-if="item.showScore"
                          :class="[cachedHandleQStatus(item) ? 'activecolor' : '']"
                          > {{ item?.answerScore }}分</span>
                        <span v-if="item.showScore"> / </span> <span>{{ item?.score }}分</span>
                      </div>
                    </div>
                    <!-- <div class="accountchild" v-if="item.children && item.children.length !==0">
                      <div class="accountchild-l">1</div>
                      <div class="accountchild-r">1</div>
                    </div> -->
                    <!-- 核算下的子题目 -->
                    <div
                      class="accountchild"
                      style="padding-left:16px;"
                      v-if="item?.children && item?.children.length !== 0">
                      <div
                        class="drawcarditems"
                        v-for="(items, indexs) in item?.children"
                        :key="indexs">
                        <div class="drawcarditemflex"  @click="openQuestionDetail(items,indexs,'skip')">
                          <div :class="[items.isSubmit ? 'drawcarditem-l' : 'drawcarditem-fade']">
                            <span>{{ index1 + 1 }}.{{ indexs + 1 }}</span>
                          </div>
                          <div 
                            class="drawcarditem-m"
                            v-if="items?.questionType == 16">
                            {{ items?.title }}-{{ indexs + 1 }}
                          </div>
                          <div :title="items?.title" class="drawcarditem-m" v-else>
                            {{ items?.title }}
                          </div>
                          <!-- <div
                            class="drawcarditem-m"
                            v-else-if="items?.questionType == 43">
                            {{ items?.title }}
                          </div>
                          <div
                            class="drawcarditem-m"
                            v-else-if="items?.questionType == 21">
                            {{ items?.title }}
                          </div>
                          <div
                            class="drawcarditem-m"
                            v-else-if="items?.questionType == 22">
                            {{ items?.title }}
                          </div>
                          <div
                            class="drawcarditem-m"
                            v-else-if="items?.questionType == 49">
                            {{ items?.title }}
                          </div> -->
                        </div>
                        <!--  finishAnswerShowType 1 考后显示成绩 -->
                        <div
                          v-if="taskAnswerInfo.finishScoreShowType == 1"
                          class="drawcarditem-r">
                         <span v-if="items.showScore"> <span :class="[items?.isSubmit ? 'activecolor' : '']">{{ items?.answerScore }}分</span> / </span>{{ items?.score }} 分
                        </div>
                      </div>
                    </div>
                    <!-- <div>自定义场景设计</div> -->
                  </div>
                </div>
              </section>
            </div>
          </section>
        </el-aside>
      </el-container>
    </div>

    <!-- 参考答案弹窗 -->
    <base-dialog
      v-if="hintDialogVisible"
      :visible.sync="hintDialogVisible"
      :hasFull="true"
      v-dialogDrag
      :close-on-click-modal="false"
      :fullscreen="false"
      width="1200px"
      className="answer-hint-content-dialog resize-dialog flex-content-dialog"
      title="查看答案"
      :before-close="handleClose">
      <div>
        <answerHint
          v-if="currentQuestion?.questionType !== 42 && currentQuestion?.questionType !== 49"
          :questionInfo="currentQuestion"></answerHint>
        <answervoucher
         v-else
          :editanwser="currentQuestion"></answervoucher>
      </div>
      <div slot="footer"></div>
    </base-dialog>


    <!-- 查看已关联的场景信息 -->
    <showScene v-if="isShowScene" :visible.sync="isShowScene" :sceneIds="sceneDataInfo"></showScene>
  </div>
</template>

<script>
import {throttle} from "@/utils/throttle";

const questionDetail = () => import("./components/questionDetail.vue");
const answerHint = () => import("./components/answerHint.vue");
const answervoucher = () => import("./components/vochersort/voucher.vue");
const baseDialog = () => import("@/components/base/dialog.vue");

const showScene = () => import('@/components/scene/query-show-choose-scene.vue');// 题目查看场景关联的信息

const answerFormPreview = () => import('@/components/base/question/answer-form-preview.vue')
const questionVocherform =  () => import("./s-voucher.vue")//分录答题

import {getInitAnswerValue, questionTypeLabel, questionTypeMenu, getCorrectAnswer} from "@/components/base/question/util";

import eventBus from "@/utils/eventBus";
import { mapGetters } from "vuex";
import cloneDeep from "lodash/cloneDeep";

import { setCurrentCaseId } from "@/utils/token.js"
import {initApiQuestion} from "@/components/question/utils";
export default {
  name: "StudentIndex",
  components: {
    questionDetail,
    answerHint,
    baseDialog,
    showScene,
    answerFormPreview,
    'manualScore': () => import('@/components/base/question/manual-score/index.vue'),
    questionVocherform,
    answervoucher
  },
  mixins: [],
  props: {},
  provide() {
    return {
      passToSpread: this.handleDataFromSpread
    };
  },
  data() {
    return {
      respondList: [], //题目列表
      questionList: [], //题目答题卡列表
      index1: 1,
      title: "",
      viewType: "",
      questionInfo: {}, // 题目详情
      gradeId: 0, // 作答记录id
      deadline: 0,
      taskAnswerInfo: {}, // 任务答案得分部分的状态控制
      taskStatus: 1, //任务状态 1 进行中 2 已结束/交卷
      hintDialogVisible: false, // 查看答案解析
      currentQuestion: {}, // 查看当前题目答案
      nonReactiveTime: {
        day: 0,
        hr: 0,
        min: 0,
        sec: 0
      },
      _interval: "",
      _animationFrameId: null, // 用于存储 requestAnimationFrame 的 ID
      cachedHandleQStatusCache: {}, // 用于缓存结果
      isOpenAside: true,//是否展示侧边导航栏
      sceneDataInfo:[],//已设置关联的场景信息
      isShowScene:false,
      accountscore:0,
      questionTypeLabel: questionTypeLabel, //题库文本
      spread:null, // spread 表格题
      subAnswer:'',
    };
  },
  computed: {
    ...mapGetters({
      accountdetailInfo: ["accountdetailInfo"],
      voucherWorkInfo: ["voucherWorkInfo"]
    }),
    doNum() {
      let answeredCount = 0;
      this.questionList.forEach(question => {
        if (question.children && question.children.length > 0) {
          // 累加已作答的子题数量
          answeredCount += question.children.filter(child => child.isSubmit).length;
        } else if (question.isSubmit) {
          // 累加已作答的单题数量
          answeredCount++;
        }
      });
      return answeredCount;

      // 已做题数
      // let num = 0;
      // let accountnum = 0;
      // this.questionList.forEach(v => {
      //   if (v?.isSubmit) {
      //     num++;
      //   }
      //   if (v && (v.questionType == 42||v.questionType == 50)) {
      //     if (v.children && v.children.length !== 0) {
      //       v.children.forEach(item => {
      //         if (item.isSubmit) {
      //           accountnum++;
      //         }
      //       });
      //     }
      //     // num++;
      //   }
      // });

      // return num + accountnum;
    },
    // cachedHandleQStatus() {
    //   const cache = {};
    //   return (item) => {
    //     console.log("item------",item)
    //     if (!cache[item.id]) {
    //       cache[item.id] = this.handleQStatus(item);
    //     }
    //     return cache[item.id];
    //   };
    // }
  },
  watch: {
  },
  created() {},
  mounted() {
    this.getRespondList();
    this.viewType = this.$route.query.type;
    this.taskName = this.$route.query.taskName;
    this.taskBeginTime = this.$route.query.taskBeginTime;
    this.taskEndTime = this.$route.query.taskEndTime;
    // 倒计时
    this.deadline = new Date(this.taskEndTime);
    this.score = this.$route.query.score;
    this.type = this.$route.query.type;

    this.taskAnswerInfo = JSON.parse(sessionStorage.getItem("taskAnswerInfo") || "{}");
    this.taskStatus = JSON.parse(sessionStorage.getItem("taskStatus") || 1);

    if (this.taskStatus == 2) {
      // 已结束/交卷
      this.countdown(parseInt(this.$route.query.duration))
    } else {
      this.startAnimationFrame();
      // this._interval = setInterval(() => {
      //   this.countdown();
      // }, 1000);
    }

    // eventBus.$on("handlesinglequestion", data => {
    //   this.submitAnswer(data.item,data.index)
    // });
    eventBus.$on("handleOpenQuestionDetail", data => {
      this.openQuestionDetail(data.qsInfo)
    });

    // 子题目状态

    this.cachedHandleQStatusCache = {};
    eventBus.$off('changeSubQuestionStatus')
    eventBus.$on('changeSubQuestionStatus',data => {
      // 清空缓存
      this.cachedHandleQStatusCache = {};
      // console.log("提交的子题目",data)

      this.questionList.forEach(val=>{
        if(val.children&&val.children.length > 0){
          if(val.id == data.parentId&& val.children[data.index]){
            // console.log("匹配到的题目---",val)
            val.children[data.index].isSubmit = data.isSubmit // 提交状态
            val.children[data.index].answerScore = data.answerScore //
            val.children[data.index].gradeDetails = data.gradeDetails // 作答记录
            // 实时统计综合题的分数
           
            val.answerScore = val.children.reduce((total, item) => {
              return total + item.answerScore;
            }, 0);
            // 实时统计综合题的状态
            val.isSubmit = val.children.every(item => item.isSubmit);
          }
        }
      })
      this.$set(this,'questionList',this.questionList)
    }) 
  },
  destroyed() {
    clearInterval(this._interval);
    if (this._animationFrameId) {
      cancelAnimationFrame(this._animationFrameId);
    }
  },
  methods: {
    // 判断数据是否在可视区,基础题默认请求详情展示
    isViewArea: throttle(function (el = '') {
      const target = el.target || this.$refs.respondBox
      const viewTop = target.scrollTop
      const viewHeight = target.clientHeight
      this.respondList.forEach((item, index) => {
        const top = this.$refs[`qs_${item.id}`][0].offsetTop
        // const height = this.$refs[`qs_${item.id}`][0].clientHeight
        if ((index < 2 || top - viewTop <= viewHeight) && questionTypeLabel[item.questionType] && !item.questionDetail) {
          this.openQuestionDetail(item, index)
        }
      })
    }, 100),
    // 显示和隐藏右侧菜单导航
    checkNav() {
      this.isOpenAside = !this.isOpenAside;
      // 触发spread 重新渲染
      eventBus.$emit('screenChange');
    },
    // 计算完成进度
    handlePercent() {
      if (this.questionList.length > 0) {
        return ((this.doNum / this.handleTotalNum()) * 100).toFixed(0);
      } else {
        return 0;
      }
    },
    // 计算总提数
    handleTotalNum() {
      let totalCount = 0;
      this.questionList.forEach(item => {
        if (item.children && item.children.length > 0) {
          // 如果有子题目，加上子题目的数量
          totalCount += item.children.length;
        }else{
          // 先算上当前题目
          totalCount++;
        }
      });
      return totalCount;
      // if (this.questionList.length > 0) {
      //   let doNum = this.questionList.length - 1;
      //   let accountArr = [];
      //   this.questionList.forEach(item => {
      //     if (item && (item.questionType == 42|| item.questionType == 50)) {
      //       accountArr.push(item);
      //     }
      //   });
      //   if (accountArr.length !== 0) {
      //     return doNum + accountArr[0].children.length;
      //   } else {
      //     return this.questionList.length;
      //   }

      //   //return (this.doNum / this.questionList.length).toFixed(2) * 100;
      // } else {
      //   return 0;
      // }
    },
    // returnBack() {
    //   // 区分考核和作业
    //   if(sessionStorage.getItem('haveExam')){
    //     this.$router.push({path:'/studentTask',query:{haveExam:true}})
    //   }else{
    //     this.$router.push({
    //       path: this.$route.query.taskType==3?'/studentExam':'/studentTask'
    //     })
    //   }

    // },
    // 计算题目状态
    cachedHandleQStatus(item) {
      // if (!this.cachedHandleQStatusCache[item.id]) {
        // this.cachedHandleQStatusCache[item.id] = this.handleQStatus(item);
        // }
        // return this.cachedHandleQStatusCache[item.id];
      return this.handleQStatus(item);
    },
    startAnimationFrame() {
      const loop = () => {
        this.countdown();
        this._animationFrameId = requestAnimationFrame(loop);
      };
      this._animationFrameId = requestAnimationFrame(loop);
    },
    countdown(duration) {
      // 目标日期时间戳
      const end = Date.parse(new Date(this.$route.query.taskEndTime));
      const now = Date.parse(new Date());
      const msec = duration?duration:(end - now);
      if (msec <= 0) return clearInterval(this._interval); // 时间结束 清除定时器
      let day = parseInt(msec / 1000 / 60 / 60 / 24);
      let hr = parseInt((msec / 1000 / 60 / 60) % 24);
      let min = parseInt((msec / 1000 / 60) % 60);
      let sec = parseInt((msec / 1000) % 60);
      this.nonReactiveTime.day = day;
      this.nonReactiveTime.hr = hr > 9 ? hr : "0" + hr;
      this.nonReactiveTime.min = min > 9 ? min : "0" + min;
      this.nonReactiveTime.sec = sec > 9 ? sec : "0" + sec;
    },
    hilarity() {
      // this.$notify({
      //   title: "提示",
      //   message: "时间已到",
      //   duration: 0,
      // });
    },
    async GetaccountPage(item) {
      await this.gettaskdetailData(item.id, item.taskId, item.children, item.gradeId, item.description);

      sessionStorage.setItem("gradeId", item.gradeId);

      let path = "";

      let questionTypes = Array.from(new Set(this.accountdetailInfo.children.map(item => item.questionType || 0)));
      if (questionTypes.length == 0) return this.$message({ type: "warning", message: "当前核算任务未包含任何核算选题,请编辑或重发当前核算任务!" });
      if (questionTypes.includes(16)) {
        path = "/voucherTopic";
      } else if (questionTypes.includes(43)) {
        path = "/Twordaccount";
      } else if (questionTypes.includes(21)) {
        path = "/homework/manual-accounting/balancesheet";
      } else if (questionTypes.includes(22)) {
        path = "/homework/manual-accounting/income";
      }

      this.$router.push({
        path: path,
        query: { currentPath: path,duration: parseInt(this.$route.query.duration),taskEndTime:this.$route.query.taskEndTime }
      });
    },
    async gettaskdetailData(id, taskid, children, gradeId, description) {
      const params = {
        taskId: taskid,
        questionId: id,
        gradeId: gradeId
      };

      let { data, code } = await this.$api.GetAccountTaskData(params);

      if (code === 200) {
        const contentdata = JSON.parse(data.contentData);
        const params = {};
        params.subject = contentdata.subject;
        params.cerBeginDate = contentdata.cerBeginDate;
        params.cerDateType = contentdata.cerDateType;
        params.cerEndDate = contentdata.cerEndDate;
        params.children = children;
        params.gradeId = gradeId;
        params.description = description;
        params.tAccount = contentdata.tAccount;
        params.subject = JSON.parse(contentdata.subject);
        params.id = id;
        params.taskId = taskid;
        params.taskName = this.taskName;
        params.taskBeginTime = this.taskBeginTime;
        params.taskEndTime = this.taskEndTime;
        params.score = this.score;
        params.type = this.type;
        params.duration = this.$route.query.duration;
        params.accountEntrys=contentdata.accountEntrys
        this.$store.commit("accountdetailInfo", params);
      }
    },
    format(percentage) {
      return percentage === 100 ? "满" : `${percentage}%`;
    },
    //获取答案列表
    //StudentGradeDetailGetTaskQuestionList
    async getRespondList() {
      const queryData = {
        caseId: 0,
        gradeId: 0,
        taskId: this.$route.query.id
      };
      const res = await this.$api.StudentGradeDetailGetTaskQuestionList(queryData);
      if (res.code === 200) {
        if (res.data && res.data[0]) {
          setCurrentCaseId( res.data[0].caseId)
          this.gradeId = res.data[0].gradeId; // 作答记录id
        }
        this.questionList = JSON.parse(JSON.stringify(res.data));
        const questionListData =  this.questionList.map(item => ({
          ...item,
          answerScore: (item.children&&item.children.length>0)?item.children.reduce((sum, child) => sum + child.answerScore, 0):item.answerScore
        }));
        this.questionList=questionListData
        // console.log(this.questionList,'SISHG')
        this.respondList = res.data.map(v => {
          if (v && v.questionType !== 42) {
            return {
              ...v
            };
          }
          if (v && v.questionType == 42) {
            return {
              questionType: v.questionType,
              title: v.title,
              score: v.score,
              description: v.description,
              id: v.id,
              taskId: v.taskId,
              children: v.children,
              gradeId: v.gradeId
            };
          }
        });
        let voucherWorkInfo= this.$store.getters.voucherWorkInfo
        voucherWorkInfo.vocherInfo.details=this.respondList
        this.$store.commit("voucherWorkInfo", voucherWorkInfo);
        let respondArray = this.respondList.filter(item => item.questionType == 42)
        if(respondArray.length !== 0){
          this.accountscore=respondArray[0].score
        }else{
          this.accountscore=this.$route.query.score
        }
        await this.$nextTick()
        this.isViewArea() 
      }
    },
    /**
     * 题目状态
     * @param {*} item
     * @param {*} index
     */
    // handleStatus(item, index) {
    //   if (item?.isSubmit) {
    //     return "done";
    //   } else {
    //     return "undo";
    //   }
    // },
    // 查看题目详情
    /**
     * @skip 是否是列表触发的跳转
     */
    async openQuestionDetail(qsInfo, index,skip) {
      // 核算题 需要跳转到核算对应的界面作答
      if(qsInfo.questionType==42)return;
      const params = {
        taskId: qsInfo.taskId?qsInfo.taskId:Number(this.$route.query.id),
        questionId: qsInfo.parentId?qsInfo.parentId:qsInfo.id,
        gradeId: this.gradeId
      };
      const res = await this.$api.GetAccountTaskData(params);
      if (res.code === 200) {
        let contentData = JSON.parse(res.data.contentData);
        let data = {
          // ...qsInfo, // 题目原有的状态
          isSubmit: qsInfo.isSubmit,
          ...res.data,
          contentData: contentData,
          type: contentData.type
        };


        // 题库选题答案
        if (questionTypeLabel[data.questionType]) {
          data.questionDetail = initApiQuestion(contentData.content)
          data = this.handleQuestionInfo(data,'主题目')
          // 子题目中的 答案处理
          if (data.children && data.children.length > 0) {
            data.children = data.children.map((val,index) => {
              val.questionDetail = initApiQuestion(val)
              val = this.handleQuestionInfo(val,'子题目')

              data.questionDetail.optionConfig.children = data.children // 更新综合题下的子题目
              // 合并 配置信息中的子题目信息
              data.questionDetail.optionConfig.children[index].questionDetail = val.questionDetail;
              data.questionDetail.optionConfig.children[index].contentData = JSON.stringify(val); 
              return val;
            });
          }
          // else{
            const initAns = getInitAnswerValue(data.questionType)
            if (data.gradeDetails && data.gradeDetails[0] && data.gradeDetails[0].answer) { // 学生作答记录
              // data.questionType==5  表格题
              let ans = null
              if(data.questionType==5){
                ans = JSON.parse(data.gradeDetails[0].answer)
              }else if(data.questionType==46||data.questionType==47){ // 变成题或简答题
                ans = JSON.parse(data.gradeDetails[0].answer)
              }else{
                ans = JSON.parse(data.gradeDetails[0].answer).longArray
              }
              data.questionDetail.studentAnswer = typeof initAns === 'string' ? ans[0] : ans
              // 判断是否显示答案对比
              data.questionDetail.answerCompareInfo = data.showError?data.gradeDetails[0].answerCompareInfo:null
            } else {
              data.questionDetail.studentAnswer = initAns
            }
            // contentData answer置空
            // data.contentData.answer = getInitAnswerValue(data.questionType)
            // data.answer = getInitAnswerValue(data.questionType)
            // data.questionDetail.answer = getInitAnswerValue(data.questionType)

            // console.log("题目信息---",data);
            // console.log("单个题目信息的详情-----------",data.questionDetail);
          // }
        }
        this.$set(this.respondList, index, data);
        this.respondList.forEach((item, index) => {
          if (!item.isSubmit) {
            item.questionDetail?.optionConfig?.children?.forEach((val, index) => {
              if(val.type==49){
              val.answer = "";
              }
            });
          }
        });
        this.skipPosition(skip,qsInfo,'respondBox'); //列表的快捷跳转
      }
      //  resourceType 资源类型  2 场景题  其他为非场景题目
      //  this.$route.query.type -> 1 作答  2 查看记录
    },
    // 题目答案处理
    handleQuestionInfo(data,type){
      const initAns = getInitAnswerValue(data.questionType)
      if (data.gradeDetails && data.gradeDetails[0] && data.gradeDetails[0].answer) { // 学生作答记录
        // data.questionType==5  表格题
        let ans = null
        let answerResult=null
        let showError=null
        if(data.questionType==5){
          ans = JSON.parse(data.gradeDetails[0].answer)
        }else if(data.questionType==46||data.questionType==47){ // 变成题或简答题
          ans = JSON.parse(data.gradeDetails[0].answer)
        }else if(data.questionType==49){
          ans = data.gradeDetails[0].answer
          answerResult= data.gradeDetails[0]
          showError = data.showError
        }
        else{
          ans = JSON.parse(data.gradeDetails[0].answer).longArray
        }
        data.questionDetail.studentAnswer = typeof initAns === 'string' ? ans[0] : ans
        if(data.questionType==49){ // 区分分录题
          data.questionDetail.vocherResult =answerResult
          data.questionDetail.vocherResult.showError = showError
        }
        // 判断是否显示答案对比
        data.questionDetail.answerCompareInfo = data.showError?data.gradeDetails[0].answerCompareInfo:null
      } else {
        data.questionDetail.studentAnswer = initAns
        // console.log(data.questionDetail.studentAnswer,'ans++++++++222',type)
      }
      return data
    },
    // 题目滚动跳转
    skipPosition(skip,data,parent){
      // 如果不是列表的快捷跳转，直接返回
      if (!skip) return;
      // 获取文档的高度
      const bodyHeight = document.body.offsetHeight;
      // 定义一个滚动到指定位置的函数，减少代码重复
      const scrollToPosition = (offsetTop) => {
        const scrollTop = Math.max(offsetTop - bodyHeight / 2, 0);
        $(this.$refs[parent]).animate({ scrollTop: `${scrollTop}px` }, 800);
      };
      // 如果有父题目 ID，获取父题目内子题目的偏移量并滚动 子题目滚动
      if (data.parentId) {
        console.log('this.$refs[`qs_${data.parentId}`]',this.$refs[`qs_${data.parentId}`])
        const currentOffsetTop = this.$refs[`qs_${data.parentId}`][0].offsetTop;
        scrollToPosition(currentOffsetTop);
        const intervalId = setInterval(() => {
          try {
            // 检查是否存在指定元素
            if (!this.$refs[`qs_${data.parentId}`]) {
              throw new Error("元素不存在");
            }
          } catch (error) {
            // 元素不存在时清除定时器
            clearInterval(intervalId);
            return;
          }
          const targetElement = this.$refs[`qs_${data.parentId}`][0];
          if (targetElement.querySelector(`#qs${data.id}`)) {
            const childOffsetTop = targetElement.querySelector(`#qs${data.id}`).offsetTop;
            scrollToPosition(childOffsetTop);
            // 清除定时器
            clearInterval(intervalId);
         
          }
        }, 100); // 每 100 毫秒检查一次
      }else{
        // 获取当前题目的偏移量并滚动
        if(this.$refs[`qs_${data.id}`]&&this.$refs[`qs_${data.id}`][0]){
          const currentOffsetTop = this.$refs[`qs_${data.id}`][0].offsetTop;
          scrollToPosition(currentOffsetTop);
        }
      }
      
    },
    /**
     * 提交题目答案
     * @param {当前题目信息} qsInfo
     * @param {当前题目的下标} index
     */
    async submitAnswer(qsInfo,index) {
      // console.log(qsInfo,'qsInfo')
      if(qsInfo.type == 49){
        eventBus.$emit('handlevoucherData',index)
        let voucherInfo=this.voucherWorkInfo.vocherInfo.details
        voucherInfo.forEach((item,i)=>{
          if(index == i){
            this.subAnswer=item.subAnswer
            return
          }
        })
      }
      if (!qsInfo.contentData) return this.$message.error("请展开题目详情后作答");
      const answer = this.getQuestionAnswer(qsInfo);
      if(qsInfo.questionType == '47'){
        if(answer=='{"answer":"<p><br></p>"}'||answer=='{"answer":""}'){
          return this.$message({ type: "warning", message: "请填写答案后提交" });
        }
      }
      if (!answer) return this.$message({ type: "warning", message: "请填写答案后提交" });

      const params = {
        id: qsInfo.gradeDetails ? qsInfo?.gradeDetails[0]?.id : qsInfo.gradeDetailsId || 0,
        gradeId: this.gradeId,
        questionsId: qsInfo.id,
        questionType: qsInfo.questionType,
        answer:qsInfo.type == 49?this.subAnswer:answer,
        noAutoScore: qsInfo.questionType==47?1:0,//是否自动判分
        isReadonly: qsInfo.isReadonly
      };
      const { data, code,msg } = await this.$api.GetSaveStudentAnswerData(params);
      if (code === 200) {
        this.$message({
          type: "success",
          message: "提交成功！"
        });
        qsInfo.gradeDetailsId = data.id; // 作答记录id
        // // 更新当前题的作答记录
        if(questionTypeLabel[qsInfo.questionType]){
          qsInfo.gradeDetails = [
            {
              ...data,
              answer:params.answer,
            }
          ]
          //更新填空题缓存数据
          if (qsInfo.questionDetail.optionConfig.settingArr) {
            const ansIds = qsInfo.questionDetail.optionConfig.settingArr.map(op => op.answerId)
            const courseContents = sessionStorage.getItem('courseContents')
            let settingArr = courseContents ? JSON.parse(courseContents) : []
            settingArr.forEach((sa) => {
              if (ansIds.includes(sa.answerId)) {
                sa.answerCont = JSON.parse(params.answer).longArray[sa.answerId]
              }
            })
            // console.log("作答后的题目信息---------",settingArr);
            sessionStorage.setItem('courseContents', JSON.stringify(settingArr))
          }
        }
        // 得分显示
        this.handleQuestionScore(qsInfo.showScore, data, qsInfo.isReadonly,qsInfo.questionType);
        // 处理标红处理
        this.handleQuestionCompare(qsInfo.questionType, qsInfo.showError, data, qsInfo.isReadonly);
        // 更改题目列表状态
        this.changeQuestionStatus(data.questionsId, data.sumScore);
        // 更改题目作答状态
        this.changeSingleQuestionStatus(qsInfo, index);
      } else if (code === 1000) { // 题目已更新 需要刷新当前题目
        // 获取单个题目信息
        // this.$confirm('教师已修改当前题目考核,需要刷新当前题目，是否刷新？').then(_=>{
        this.openQuestionDetail(qsInfo, index);
        // }).catch(_=>{
        // })
      }else if(code==1001){ // 题目答案格式不对的问题
        let msgList = msg.split('__')
        console.log('msg', msgList);
      }
    },
    // 重置作答记录
    async resetAnswer(qsInfo,index){
      this.$confirm('重置后，作答记录将被清空，确认重置？').then(_=>{
        let gradeDetailId = qsInfo.gradeDetails[0].id;//当前题目作答记录id
        this.$api.ResettingAnswer({gradeDetailId,}).then(async res=>{
          if(res.code==200){
            this.$message({type:'success',message:'重置成功'});
            // 获取单个题目信息
            await this.openQuestionDetail(qsInfo, index);
            // 重置更新单个题目的分数
            this.questionList = this.resetSingleQuestion(this.questionList, index);
            // 需要重置答题情况
            qsInfo.gradeDetails= null; //  直接重置作答记录字段
            if (qsInfo.questionDetail.optionConfig) {
              // 填空题需要清空缓存数据
              if (qsInfo.questionDetail.optionConfig.settingArr) {
                const ansIds = qsInfo.questionDetail.optionConfig.settingArr.map(op => op.answerId)
                const courseContents = sessionStorage.getItem('courseContents')
                let settingArr = courseContents ? JSON.parse(courseContents) : []
                settingArr.forEach((sa) => {
                  if (ansIds.includes(sa.answerId)) {
                    sa.answerCont = ''
                  }
                })
                sessionStorage.setItem('courseContents', JSON.stringify(settingArr))
              }
              // 表格题或代码编程题内容重置
              if(qsInfo.questionDetail.type==46||qsInfo.questionDetail.type==47){
                qsInfo.questionDetail.studentAnswer.answer = '';
              }

              this.$nextTick(() => {
                this.$refs[qsInfo.id][0].handleSpreadCompare('');
              })
            }
            // 清空缓存
            this.cachedHandleQStatusCache = {};
          }
        })
      }).catch((err) => {
        console.log('err',err);
        
      });
    },
    // 重置单个题目的分数
    resetSingleQuestion(questionList, index) {
      return questionList.map((v, i) => {
        if (i == index) {
          v.answerScore = 0;
          this.doNum--;
          v.isSubmit = false;
        }
        return v;
      })
    },
    /**
     * // 更改题目状态
     * @questionId 题目id
     * @sumScore 得分
     */
    changeQuestionStatus(questionId, sumScore) {
      this.questionList = this.questionList.map((v, index) => {
        if (v && v.id && v?.id == questionId) {
          v.isSubmit = true;
          v.answerScore = sumScore; // 更新得分
        }
        return v;
      });
      // 清空缓存
      this.cachedHandleQStatusCache = {};
    },
    // 更改题目提交状态
    changeSingleQuestionStatus(qsInfo,index){
      // 延时是未了获取服务端的修改后的数据，如果直接获取会拿不到修改后的数据
      setTimeout(()=>{
        this.openQuestionDetail(qsInfo, index);
      },500)
    },
    // 获取题目的答案
    getQuestionAnswer(qsInfo) {
      // 题库
      if (questionTypeLabel[qsInfo.questionType]) {
        let answer = null;
        // 填空题特殊处理
        if (qsInfo.questionType === questionTypeMenu.content) { // 填空
          const courseContents = sessionStorage.getItem('courseContents')
          answer = getCorrectAnswer(questionTypeMenu.content, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: courseContents ? JSON.parse(courseContents) : []
          })
        } else if(qsInfo.questionType === questionTypeMenu.python){ // 代码填空
          qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.codeSetRef.$refs.pythonSettingRef.innerHTML;
          const courseContents = sessionStorage.getItem('courseContents')
          answer = getCorrectAnswer(questionTypeMenu.python, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: courseContents ? JSON.parse(courseContents) : []
          })
        } else if(qsInfo.questionType === questionTypeMenu.table){ // 表格题
          // 区分富文本表格题 和spread 表格题
          const courseContents = sessionStorage.getItem('courseContents')
          let contentData = JSON.parse(qsInfo.contentData.content.contentData)
          if(contentData.tableType==1){ //spread 表格题
            qsInfo.questionDetail.optionConfig.spread = this.spread;
            answer = getCorrectAnswer(questionTypeMenu.table, {
              ...qsInfo.questionDetail.optionConfig,
              settingArr: courseContents ? JSON.parse(courseContents) : []
            })
          }else{ // 富文本表格题
            qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.questionTablePreview.innerHTML
            answer = getCorrectAnswer(questionTypeMenu.table, {
              ...qsInfo.questionDetail.optionConfig,
              settingArr: courseContents ? JSON.parse(courseContents) : []
            })
          }
          return JSON.stringify(answer) // 表格题答案
        }else if(qsInfo.questionType === questionTypeMenu.pythonCode){ // 编程题
          qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.monaco.getVal();
          answer = getCorrectAnswer(questionTypeMenu.pythonCode, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: []
          })
          return JSON.stringify(answer)
        }else if(qsInfo.questionType === questionTypeMenu.shortAnswer){// 简答题
          qsInfo.questionDetail.optionConfig.html = this.$refs[qsInfo.id][0].$refs.previewCompsRef.$refs.shortEditor.html;
          answer = getCorrectAnswer(questionTypeMenu.shortAnswer, {
            ...qsInfo.questionDetail.optionConfig,
            settingArr: []
          })
          return JSON.stringify(answer)
        } else{
          answer = qsInfo.questionDetail.studentAnswer
        }
        
        return JSON.stringify({
          longArray: typeof getInitAnswerValue(qsInfo.questionType) === 'object' ? answer : [answer]
        })
      }
      let questionId = qsInfo.id;
      const data = this.$refs[questionId][0].getAnswer();
      if (qsInfo.isReadonly == 1) {
        return data;
      } else {
        return JSON.stringify(data);
      }
    },

    // 获取 spread 对象
    handleDataFromSpread(spread){
      this.spread = spread;
    },
    // 处理题目得分显示
    handleQuestionScore(showScore, data, isReadonly,questionType) {
      // 编程题和简答题 不提示得分
      if(questionType==questionTypeMenu.pythonCode||questionType==questionTypeMenu.shortAnswer)return 
      if (showScore && isReadonly != 1) {
        // 只读题不显示得分 显示得分
        this.$notify({
          title: "提示",
          message: `当前题目总分：${data.topicScore}，得分：${data.score}`,
          type: data.answerResult == 4 ? "warning" : "success"
        });
      }
    },
    /**
     * 处理题目标红问题
     * @param {*题目类型} qsType
     * @param {*任务权限信息} taskAnswerInfo
     * @param {*答案提交后的信息} data
     * @param {*只读题} isReadonly
     *
     */
    handleQuestionCompare(qsType, showError, data, isReadonly) {
      if (showError) {
        // 显示题目标红
        // 判断题型  葡萄城类型题目的标红
        let answerCompareInfo = data.answerCompareInfo;
        if (!this.$refs[data.questionsId]) return

        if (questionTypeLabel[qsType] && isReadonly != 1&&questionTypeLabel[qsType]!='分录题') {
          // 题库
          this.$refs[data.questionsId][0].handleSpreadCompare(answerCompareInfo, data.answerResult);
        } else if (qsType == 4 && data.answerCompareInfo && isReadonly != 1) {
          this.$refs[data.questionsId][0].handleSpreadCompare(JSON.parse(answerCompareInfo), data.answerResult);
        } else if (data.answerResult == 2 && isReadonly != 1) {
          this.$refs[data.questionsId][0].handleSpreadCompare({}, data.answerResult);
        }
      }
    },
    // 查看背景资料
    lookInformation(item){
      this.sceneDataInfo = JSON.parse(item.sceneData)
      this.isShowScene = true;
    },
    /**
     * 查看答案
     * @param {*题目信息} item
     */
    async lookAnswer(item) {
      // this.currentQuestion = item;
      // 判断是否包含题目详情
      this.hintDialogVisible = true;
      let { data, code } = await this.$api.GetQuestionAnswer({
        taskId: item.taskId,
        questionId: item.id,
        gradeDetailId: item.gradeDetails && item.gradeDetails[0] ? item.gradeDetails[0].id : 0
      });
      if (code == 200) {
        let contentData = JSON.parse(data.contentData);

        // 深拷贝 题目信息
        this.currentQuestion = cloneDeep({
          ...item,
          ...data,
          contentData: contentData,
          type: contentData.type
        });

        console.log('this.currentQuestion', this.currentQuestion);
        console.log('this.questionList', this.questionList);
      }
    },
    handleClose() {
      this.hintDialogVisible = false;
    },
    // 交卷功能
    submitExam() {
      if (this.doNum == 0) return this.$message({ type: "warning", message: "当前未做题，请做题后交卷" });
      this.$confirm(`交卷后无法继续作答, 确认交卷？`)
        .then(_ => {
          console.log("this.gradeId", this.gradeId);
          this.$api.SubmitExam({ gradeId: [this.gradeId] }).then(res => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                message: "交卷成功！"
              });
              setTimeout(() => {
                this.$router.push("/studentTask");
              }, 500);
            }
          });
        })
        .catch(_ => {});
    },
    // 处理 是否展示教师批语
    handleShowManual(item){
      if(item.gradeDetails&&item.gradeDetails[0]&&item.gradeDetails[0].reviewed){
        return true
      }
      return false
    },
    // 题目作答状态
    handleQStatus(item){
      if(item.children&&item.children.length>0){
        let isSubmit = item.children.every(child=>child.isSubmit);
        return isSubmit
      }else{
        return item.isSubmit;
      }
    }
  }
};
</script>

<style scoped lang="scss">
.studentRespond-page {
  height: calc(100vh - 50px);
  overflow-y: auto;
  background: #f6f8fa;
  transition: all .5s;

  .fixed-header {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
  }
  .respond-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    line-height: 40px;
    background: #f2f2f2;
    opacity: 1;
    padding: 0 58px;
    cursor:pointer;

    .el-breadcrumb {
      line-height: 60px;
    }
    .back-btn{
      position: relative;
      width: 80px;
      height: 30px;
      padding:0;
    }
  }

  .respond-main-content {
    padding: 30px;

    .el-main {
      padding: 0;
      // overflow: unset;
    }

    .el-aside.aside-nav {
      width: 500px;
      height: calc(100vh - 120px);
      position: fixed;
      right: 0;
      top: 60px;
      overflow: unset;
      transition: all 0.5s;
      background: #fff;
      .expend-btn {
        line-height: 78px;
        color: #fff;
        cursor: pointer;
        position: absolute;
        width: 20px;
        height: 78px;
        top: 300px;
        left: -16px;
        background: url("../../../assets/material/top-collapse-bg1.png") no-repeat;
      }
    }
    .main-content-info {
      // width: 98%;
      min-height: 318px;
      background: #ffffff;
      box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.08);
      margin-bottom: 44px;
      padding-bottom: 30px;

      .main-body-header {
        min-height: 40px;
        // line-height: 60px;
        background-color: #f7f7f7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        padding: 0 35px;

        
        .question-label{
          font-size: 12px;
          position: relative;
          top: -2px;
          color: var(--theme_primary_color);
        }
        .main-header-info {
          font-size: 16px;
          font-family: "PingFang SC, PingFang SC";
          font-weight: 600;
          color: #2b66ff;
          max-width: 65%;
          display: block;
          line-height: 24px;
          padding: 10px;
          word-break: break-all;
        }

        .operate-btn {
          width: 80px;
          height: 30px;
          padding: 0;
        }

        .answer-btn {
        }
        .info-btn{
          background-color: #FAECD8;
          bottom: 1px solid #E6A23C;
          color:#333;
        }
        .submit-btn {
          background-color: #2b66ff;
          color: #fff;
        }
      }

      .main-body-content {
        margin: 0 35px;
        .description{
          font-size: 14px;
          line-height: 28px;
          padding-left: 13px;
          margin-top:20px;
          color: #787d83;
          width: 100%;
          min-height: 85px;
          background: #f9f9f9;
          opacity: 1;
          border: 1px dashed #dde2e9;

          
          word-wrap: break-word;
          word-break: break-all;
          
          p{
              word-wrap: break-word;
              word-break: break-all;
          }
        }
      }

      .accountbtn {
        width: 100%;
        height: 40px;
        margin-top: 35px;
        display: flex;
        justify-content: center;

        .btnblue {
          width: 115px;
          height: 40px;
          background: #2b66fa;
          color: #fff;
          padding: 10px;
        }
      }

      .question-container {
        margin: 18px 35px 0;
        overflow: auto;

        text-align: center;

        .open-detail {
        }
      }
    }

    .aside-content-info {
      width: 500px;
      // height: 319px;
      background: #ffffff;
      box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.1);
      padding-bottom: 20px;
      margin-bottom: 20px;

      .main-body-header {
        height: 40px;
        line-height: 40px;
        background-color: #f7f7f7;
        display: flex;
        align-items: center;
        position: relative;

        .main-header-info {
          font-size: 16px;
          color: #333333;

          &::before {
            content: " ";
            border-left: 4px solid #6383fb;
            margin: 0 4px 0 20px;
          }
        }

        .el-progress {
          width: 60%;
          right: 0;
          position: absolute;
        }
      }

      .aside-main-info {
        .aside-main-quest {
          margin-bottom: 20px;
        }

        p {
          font-size: 14px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          color: #787d83;
          margin: 20px 0 0 57px;

          span {
            color: #333333;
          }
        }
        .main-content-button {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 40px;
          margin-top: 20px;
          .el-button{
            width: 80px;
            height: 30px;
            padding: 0;
          }
        }
        .aside-main-time {
          width: 446px;
          height: 60px;
          background: #f7f7f7;
          margin: 0 27px;
          border-radius: 48px;
          display: flex;
          align-items: center;
          justify-content: center;

          .drawertime {
            width: 90%;
            height: 60px;
            border-radius: 28px;
            // background: #F7F7F7;
            // margin: 30px auto 0 auto;
            .drawerseconds {
              text-align: center;
              line-height: 60px;
              font-size: 14px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 400;
              color: #333333;
              .drawerday {
                padding: 7px 10px 6px 10px;
                background: #ffffff;
                border-radius: 5px 5px 5px 5px;
                color: #2b66fa;
                margin-right: 8px;
                margin-left: 8px;
              }
            }
          }
        }
      }
    }

    .info-answer {
      height: 570px;

      .main-content-markers {
        height: 46px;
        line-height: 46px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 20px;
        border-bottom: 1px dashed #ccc;
        margin-bottom: 10px;

        .manualcolorb {
          width: 10px;
          height: 10px;
          border-radius: 2px;
          background: #2b66fa;
          margin-right: 4px;
        }

        .manualcolorf {
          width: 10px;
          height: 10px;
          border-radius: 2px;
          background: #bfbfbf;
          margin: 0 4px 0 30px;
        }
      }

      .main-content-topic {
        height: calc(100vh - 496px);
        overflow-y: auto;
        padding-bottom: 50px;

        .main-topic-list {
          width: 100%;
          padding-left: 20px;
          margin-bottom: 16px;
          //padding-top: 10px;
          // height: 40px;

          cursor: pointer;
          font-size: 14px;

          .title {
            line-height: 24px;
            padding-right: 20px;
            font-size: 15px;
            word-break: break-all;
          }
          .main-topic-flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 18px;
            .main-topic-list-l {
              // width: 82%;
              max-width: 80%;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            .main-topic-list-r {
              // width: 18%;
              margin-left: 16px;
              white-space: nowrap;
              text-align: center;
              .activecolor {
                color: #2b66ff;
              }
            }
          }
          .accountchild {
            .drawcarditems {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 16px;
              padding-right: 20px;
              .drawcarditemflex {
                display: flex;
                align-items: center;
                max-width: 80%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                .drawcarditem-l {
                  background: #2b66fa;
                  color: #fff;
                  border-radius: 3px 3px 3px 3px;
                  padding: 3px 2px 1px 1px;
                  font-size: 13px;
                }
                .drawcarditem-fade {
                  background: #bfbfbf;
                  color: #fff;
                  border-radius: 3px 3px 3px 3px;
                  padding: 3px 2px 1px 1px;
                  font-size: 13px;
                }
                .drawcarditem-m {
                  font-size: 14px;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 400;
                  color: #333333;
                  padding-left: 6px;
                }
              }
              .drawcarditem-r {
                //  padding-right: 18px;
                .activecolor {
                  color: #2b66ff;
                }
              }
            }
          }
        }
        .q-item {
          width: 20px;
          height: 20px;
          background: #cdcdcd;
          border-radius: 2px;
          margin-right: 7px;
          color: #fff;
          display: inline-block;
          text-align: center;
          font-size: 15px;
          line-height: 16px;
          padding: 2px;
        }

        .done {
          background-color: #2b66ff;
        }

        .undo {
          background-color: #cdcdcd;
        }
      }
    }
  }

  .answer-hint-content-dialog {
  }
}
</style>
