<template>
  <div class="student-questionnaire-page">
    <div class="content-box">
      <el-breadcrumb style="margin-bottom: 20px;" separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item v-if="!showLeftMenu">我的问卷</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="content-wrapper">
        <!-- <div class="search-bar">
          <el-form :inline="true" :model="tableConfig.params" class="demo-form-inline">
            <el-form-item label="课程">
              <el-select v-model="tableConfig.params.courseId" placeholder="请选择课程">
                <el-option v-for="item in courseList" :key="item.id" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">查询</el-button>
            </el-form-item>
          </el-form>
        </div> -->
        <div class="content">
          <table2 row-key="id" ref="tablePreview" :data="tableData" :columns="columns" :queryFormConfig="queryConfig" :total="total"
            :pagination="pagination" :paginationLayout="paginationLayout" :searchParams="searchParams" @handleSearch="handleSearch" @handleChangePageIndex="pageCurrentChange">
            <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
            <template #actions v-if="actionBtns?.enable">
              <el-button @click="handlePlus">{{ actionBtns?.name || "新增" }}</el-button>
            </template>
            <template #operate>
              <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
                <template slot-scope="scope">
                  <el-button v-for="(item, index) in operateBtns" type="text" v-if="handleShow(scope.row, item)" @click="btnClick(scope.row, item)" :key="index" :disabled="scope.row.dataLocked">
                    {{ item.name }}
                  </el-button>
                </template>
              </el-table-column>
            </template>
          </table2>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StudentTask',
  components: {
  },
  props: {

  },
  data() {
    return {
      formInline:{
        courseId:'',
      },
      courseList:[],// 课程列表
      pagination:true,
      tableData:[],
      queryConfig:{

      },
      columns:[
        {
          label: '问卷名称',
          prop: 'name',
          align:'left',
        },
        {
          label: '所属课程',
          prop: 'courseName',
          align:'left',
        },
        {
          label: '发布人',
          prop: 'createName',
          align:'center',
          width:120,
        },
        // {
        //   label: '班级',
        //   prop: 'classes',
        //   formatter: (row) => {
        //     return row.classes.map(v=>v.name).join(',');
        //   },
        // },
        {
          label: '开始时间',
          prop: 'beginTime',
          align:'center',
          width: 180,
          formatter: (row) => {
            return row.beginTime.substring(0,10);
          },
        },
        {
          label: '结束时间',
          prop: 'endTime',
          align:'center',
          width: 180,
          formatter: (row) => {
            return row.endTime.substring(0,10);
          },
        },
        {
          label: '状态',
          prop:'status',
          align:'center',
          width:120,
          formatter: (row) => {
            return row.status === 2? '已结束' : '已发布';
          },
        },
      ],
      total:0,
      operateBtns:[{
        name: '填写',
        icon: 'el-icon-edit',
        type: 'doTask',
      },{
        name: '查看',
        icon: 'el-icon-edit',
        type: 'preview',
      }],
      searchParams:{
        pageSize: 10,
      },
      tableConfig:{
        params: {
          // courseId:0,
          isDelete:0,
          classIds:[],
        },
        pageIndex: 1,
        pageSize: 10,
      }
    };
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {
    this.getCourseList();
    this.initPage();
  },
  methods: {
    async initPage(){
      // 获取问卷列表
      // let {data,count} = await this.$api.QuestionnairesTopicsSearch(this.tableConfig);
      let studentClass = await this.$api.StudentClassList({})
      this.tableConfig.params.classIds = studentClass.data;
      let {data,count,code} = await this.$api.SurveyStudentDetailSearchStudentTopics(this.tableConfig);
      if(code==200){
        this.tableData = data
        this.total = count
      }
    },
    // 当前学生的课程列表
    async getCourseList(){
      let { data, code } = await this.$api.getCourseList();
      this.courseList = data.map(v=>{
        return {label:v.courseName,value:v.id};
      });
    },
    onSubmit(){
      this.initPage();
    },
    handleSelectionChange(val){

    },
    pageCurrentChange(val){
      this.tableConfig.pageIndex = val
      this.initPage();
    },

    btnClick(row,item){
      let {id} = row
      switch(item.type){
        case 'doTask':
          // 作答
          this.$router.push({
            path:'/questionnaire',
            query:{
              id,
              type:1,
            }
          })
          break;
        case 'preview':
          // 作答
          this.$router.push({
            path:'/questionnaire',
            query:{
              id,
              type:1,
              preview:1,
            }
          })
          // 查看
          break;
        default:
          break;
      }
    },
    // 是否显示按钮
    handleShow(row,item){
      if(item.type=='doTask'){
        return row.status != 2;
      }else {
        return true
      }
    }
  }
};
</script>

<style lang="scss">
.student-questionnaire-page {
  height: 100%;  
  padding: 30px;
  .content{
    height: 100%;
    width: 100%;
    min-width: 1200px;
  }
  .content-box{
    height: 100%;
  }

  .content-wrapper{
    height: 90%;
    .search-bar{
      height: 50px;
      line-height: 50px;
      .el-form-item{
        margin-bottom: 0px;
      }
      .el-form-item__content{
        margin-top: 10px;
      }
    }
  }
  .table2-pagination{
    text-align: center;
  }
}
</style>
