<template>
  <div class="material-resource-box">
    <LeftMenu :activeMenu="'resource'" @handleResourceData="handleResourceData"></LeftMenu>
    <div class="resource-content">
      <div class="breadcrumb-box" v-show="currentFolderId" style="margin-bottom: 10px;">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item class="all-item"
            @click.native="breadcrumbList = []; getAllFolder()">全部</el-breadcrumb-item>
          <el-breadcrumb-item v-for="(breadcrumb, index) in breadcrumbList" :key="breadcrumb.id"
            :title="breadcrumb.name" class="breadcrumb-item" @click.native="switchClick(breadcrumb, index)">{{
              breadcrumb.name
            }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="material-library-content"
        :style="{ height: currentFolderId ? 'calc(100vh - 120px)' : 'calc(100vh - 100px)' }">
        <div class="condition-dv">
          <div class="left-action" :style="breadcrumbList.length == 0 ? 'width: 635px;' : 'width: 375px'">
            <!-- 考虑学生上传资源不安全问题，暂先隐藏 -->
            <!-- <el-button type="primary" v-if="isTeachPermi || breadcrumbList.length < 3" @click="addFolder()"
              v-preventReClick><i class="el-icon-plus"></i>
              新增文件夹</el-button>
            <upload-btn v-if="isTeachPermi" :course-id="courseId" :folder-id="currentFolderId"
              @success="getAllFolder()"></upload-btn>
            <el-button v-if="isTeachPermi" @click="deleteBidsClick"
              :disabled="multipleSelection.length == 0">删除</el-button> -->
            <el-input v-model="folderName" placeholder="请输入文件名搜索" clearable v-if="breadcrumbList.length == 0">
              <i slot="suffix" style="cursor: pointer;" class="el-input__icon el-icon-search"
                @click="getAllFolder()"></i>
            </el-input>
          </div>
        </div>
        <el-table :data="showList" :header-cell-style="{ backgroundColor: '#F2F3F5' }"
          :height="currentFolderId ? 'calc(100vh - 245px)' : 'calc(100vh - 220px)'" class="table-sty"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" :selectable="selectableStatus"></el-table-column>
          <el-table-column prop="name" label="名称" min-width="150">
            <template slot-scope="{ row }">
              <div v-if="!row.isRename" class="file-name">
                <img :src="getIcon(row,1)" alt=""
                  :style="!row.isFile ? 'width: 22px; height: 22px; margin-right: 4px;' : ''">
                <span :title="row.name" @click="getDetail(row)">{{ row.name }}</span>
              </div>
              <div class="input-box" v-else>
                <el-input v-model="renameValue" :autofocus="true" maxlength="255" clearable></el-input>
                <el-button size="mini" type="primary" @click="changeFolderName(row)">确定</el-button>
                <el-button size="mini" @click="canleClick(row)">取消</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fileSize" label="大小">
            <template slot-scope="{ row }">
              <span v-if="!row.isFile">--</span>
              <span v-else>{{ formatSize(row.fileSize) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="日期"></el-table-column>
          <el-table-column prop="createByName" label="创建人"></el-table-column>
          <el-table-column prop="isDownload" label="下载权限" min-width="130">
            <template slot-scope="{ row }">
              <span v-if="row.createBy == userInfo.id">允许</span>
              <span v-else>{{ row.isDownload ? '允许' : '禁止' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="time" label="操作" min-width="100">
            <template slot-scope="{ row }">
              <div v-if="row.createBy == userInfo.id">
                <!-- <el-button type="text" @click="reNameClick(row)">重命名</el-button> -->
                <!-- <el-button type="text" v-if="row.level != 1" @click="moveToClick(row)">移动到</el-button> -->
                <!-- <el-button type="text" style="color: #F56C6C;"
                  @click="deleteFolder(!row.isFile ? 'floder' : 'file', row.id)">删除</el-button> -->
              </div>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <empty slot="empty" msg="暂无数据" size="middle" />
        </el-table>
      </div>
      <material-module v-if="previewFile.show" :dialogVisible="previewFile.show" :fileObj="previewFile.data"
        @addClose="previewFile.show = $event;">
      </material-module>
    </div>
    <!-- 删除文件夹弹窗 -->
    <el-dialog title="提示" :visible.sync="fileDeteleDialog" class="removeFolder">
      <div class="removePrompt">
        <span>删除数据后不可恢复，确认删除？</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="fileDeteleDialog = false">取 消</el-button>
        <el-button type="primary" @click="removeFolderConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="移动到" :visible.sync="moveToMaterial" custom-class="moveToDialog" width="320px">
      <div class="move-to-dialog-contBox">
        <div class="tree-header">根目录</div>
        <el-tree :data="catalogTree" :props="defaultProps" highlight-current @node-click="handleNodeClick">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <div class="tree-text">
              <img v-if="!data.isFile" src="@/assets/material/wenjianjia.png" alt="">
              <span :title="node.label">{{ node.label }}</span>
            </div>
            <i v-if="!node.isLeaf" :class="['usually', node.expanded ? 'active' : '']"></i>
          </span>
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="moveToMaterial = false">取 消</el-button>
        <el-button type="primary" @click="moveSureClick">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import uploadBtn from '@/components/material-upload-btn/index.vue';
import { getIcon } from "@/utils/utils.js";
import materialModule from "@/components/materialModule/index.vue";
import { fileTypeMenu } from "@/utils/file.js";
import { mapGetters } from "vuex";
import LeftMenu from '../components/left-menu.vue';
import empty from "@/components/base/empty.vue";
export default {
  name: 'FusionFrontMaterial',
  components: {
    LeftMenu,
    uploadBtn,
    materialModule,
    empty
  },
  data() {
    return {
      courseId: '',
      folderList: [],
      breadcrumbList: [],
      fileTypeMenu: fileTypeMenu,
      previewFile: { // 文件预览数据
        data: {},
        show: false
      },
      folderId: "",//文件夹ID
      currentFileId: "",//文件资源ID
      diastatus: "",//记录类型（文件夹或者文件）
      fileDeteleDialog: false,
      folderName: "",//文件名查询
      renameValue: "",
      moveToMaterial: false,
      materialTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      multipleSelection: [],
      isFileState: null,
      moveObj: {},
      nowId: null,
      catalogTree: [],
      isTeachPermi: true,//区分文件是教师还是学生创建
      currentObj: {},//当前移动选中的操作对象
      targetObj: {},//树结构选中的目标对象
      showList: [],//当前表格展示出来的数据列表
      nowTarget: {},//当前操作对象
    };
  },

  computed: {
    // 当前文件夹id
    currentFolderId: function () {
      return this.breadcrumbList?.length ? this.breadcrumbList[this.breadcrumbList.length - 1].id : 0
    },
    ...mapGetters({
      userInfo: ["userInfo"],
      courseInfo: ["getCourseInfo"]
    })
  },
  watch: {
    breadcrumbList: {
      handler(val) {
        if (val.length == 0) {
          this.isTeachPermi = true;//清空展示功能
        }
      },
      immediate: true
    },
  },
  created() {
    this.courseId = sessionStorage.getItem('courseInfoId') || this.courseInfo.id
    this.getAllFolder()
  },

  methods: {
    switchClick(val, index) {
      if (this.currentFolderId && this.currentFolderId != val.id) {
        this.breadcrumbList = this.breadcrumbList.slice(0, index + 1);
        this.getAllFolder(2, val);
      }
    },
    selectableStatus(row, rowIndex) {
      // 如果行数据中有disabled属性且为true，则返回false禁用多选
      return row.disabled = row.createBy == this.userInfo.id;
    },
    handleResourceData() {
      this.getAllFolder()
    },
    //多选删除
    deleteBidsClick() {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择要删除的数据?")
        return
      }
      this.$confirm('此操作将永久删除文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let paramFileData = this.multipleSelection.filter(item => !item.isFile).map(item => item.id);
        let paramResourceData = this.multipleSelection.filter(item => item.isFile).map(item => item.id);
        let isDeleteFnc = false;
        if (paramFileData.length > 0) {
          const resFileData = await this.$api.DeleteByIdsFileTree(paramFileData);
          if (resFileData.code === 200) {
            isDeleteFnc = true;
          }
        }
        if (paramResourceData.length > 0) {
          const resResourceData = await this.$api.DeleteByIdsFileResource(paramResourceData);
          if (resResourceData.code === 200) {
            isDeleteFnc = true;
          }
        }
        if (isDeleteFnc) {
          this.$message.success('删除成功！');
          this.multipleSelection = [];
          this.getLevelList();
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    //table 勾选功能
    handleSelectionChange(val) {
      console.log(val, 'valvalal');
      this.multipleSelection = val;
    },
    //移动到
    moveToClick(val) {
      this.moveToMaterial = true;
      this.isFileState = !val.isFile ? true : false;
      this.nowId = val.id;
      this.getAllFolder(1, val);
      this.currentObj = val;
    },
    //素材库tree点击事件
    handleNodeClick(data) {
      this.targetObj = data;
      this.moveObj = {
        id: this.nowId,
        categoryId: data.id,
        courseId: data.courseId,
      }
    },
    //移动确认
    async moveSureClick() {
      if (Object.keys(this.moveObj).length == 0) {
        this.$message.error("请选择所需要移动的目录，如果没有目录，请先创建目录！")
        return
      }
      if (this.currentObj.level == 2 && this.targetObj.level != 1) {
        this.$message.error("当前第二层级，只能移到第一层级！")
        return
      }
      if (this.currentObj.level == 3 && (this.targetObj.level == 3)) {
        this.$message.error("当前第三层级，只能移到第二层和第一层！")
        return
      }
      if (this.isFileState) {
        const res = await this.$api.fileMove(this.moveObj);
        if (res.code === 200) {
          this.$message.success('移动成功！');
          this.moveToMaterial = false;
          this.breadcrumbList = [];
          this.folderName = "";
          this.getAllFolder();
          this.moveObj = {};
        }
      } else {
        const res = await this.$api.resourceMove(this.moveObj);
        if (res.code === 200) {
          this.$message.success('移动成功！');
          this.moveToMaterial = false;
          this.breadcrumbList = [];
          this.folderName = "";
          this.getAllFolder();
          this.moveObj = {};
        }
      }
    },
    //取消-重命名
    canleClick(row) {
      this.$set(this.showList, this.showList.findIndex(item => item.id === row.id), { ...row, isRename: false })
    },
    //更改name 失去焦点方法
    async changeFolderName(val) {
      if (val.isFile) {
        let data = Object.assign({}, val, { fileName: this.renameValue })
        let res = await this.$api.MaterialAddAndEdi(data);
        if (res.code === 200) {
          this.getLevelList();
          this.renameValue = "";
        }
      } else {
        let data = Object.assign({}, val, { name: this.renameValue })
        let res = await this.$api.CreateANewFileTree(data);
        if (res.code === 200) {
          this.getLevelList();
          this.renameValue = "";
        }
      }
      if (val) {
        this.canleClick(val);
      }
    },
    //判断不同类型的资源Icon
    getIcon,
    //重命名
    reNameClick(row) {
      if (this.renameValue) {
        this.$message.error('一次只能修改一个文件夹名称！请操作完');
        return
      }
      this.renameValue = row.name;
      this.$set(this.showList, this.showList.findIndex(item => item.id === row.id), { ...row, isRename: true })
    },
    //删除弹窗打开
    deleteFolder(data, id) {
      this.diastatus = data;
      if (data == "floder") {
        this.folderId = id;
      } else if (data == "file") {
        this.currentFileId = id;
      }
      this.fileDeteleDialog = true;
    },
    //删除文件夹/文件
    removeFolderConfirm() {
      if (this.diastatus == "floder") {
        this.removeFolders(this.folderId);
      } else if (this.diastatus == "file") {
        this.DeleteMaterialss(this.currentFileId);
      }
      this.fileDeteleDialog = false;
    },
    //删除文件夹API
    async removeFolders(id) {
      let data = {
        id,
      };
      let res = await this.$api.DeleteFileTree(data);
      console.log("删除文件夹", res);
      if (res.code === 200) {
        this.$message.success("删除成功！")
        this.getLevelList();
      }
    },
    //素材资源删除
    async DeleteMaterialss(id) {
      let data = {
        id,
      };
      let res = await this.$api.DeleteMaterial(data);
      // console.log('素材删除',res);
      if (res.code === 200) {
        this.$message.success("删除成功");
        this.getLevelList();
      } else {
        this.$message.success("删除失败");
      }
    },
    getLevelList() {
      if (Object.keys(this.nowTarget).length > 0) {
        this.getAllFolder(2, this.nowTarget)
      } else {
        this.getAllFolder()
      }
    },
    // 获取所有类型文件夹
    async getAllFolder(type, val) {
      const params = {
        courseId: Number(this.courseId),
      }
      if (this.folderName) {
        params.key = this.folderName;
      }
      if (this.currentFolderId) {
        params.categoryId = this.currentFolderId;
      }
      if (type == 1) {
        params.isFile = false;
        params.moveId = val.id;
        params.categoryId = 0;
      } else if (type == 2) {
        params.level = this.breadcrumbList.length + 1;
      }
      const { code, data } = await this.$api.ObtainClassificationTree(params)
      if (code === 200) {
        if (type == 1) {
          this.catalogTree = data;
          return
        }
        this.showList = data.map(item => ({ ...item, isRename: false }));//12-30改版 数据量太大  改成查询接口
      }
    },
    async addFolder() {
      const params = {
        parentId: this.currentFolderId,
        name: `新建文件夹${this.showList.length + 1}`,
        courseId: this.courseId,
        isShare: 1,//0 是不可见 默认1 所有可见 2班级 3老师团队
        isDownload: true
      };
      let res = await this.$api.CreateANewFileTree(params);
      if (res.code === 200) {
        this.$message.success("新增成功！")
        this.getAllFolder()
      }
    },
    // 预览文件/或进入文件夹
    async getDetail(row) {
      if (row.isFile) {
        let data = { id: row.id }
        let res = await this.$api.FavoritesResourceFindById(data)
        if (res.code === 200) {
          this.previewFile.data = res.data
          this.previewFile.show = true
        }
      } else {
        this.isTeachPermi = row.createBy == this.userInfo.id;
        this.breadcrumbList.push({
          id: row.id,
          name: row.name
        })
        this.getAllFolder(2, row)
        if (this.breadcrumbList.length > 0) {
          this.nowTarget = row;
        }
      }
    },
    // 获取文件大小信息
    formatSize(size, pointLength, units = ["B", "K", "M", "G", "TB"]) {
      let unit;
      while ((unit = units.shift()) && size > 1024) {
        size = size / 1024;
      }
      return (unit === "B" ? size : size.toFixed(pointLength === undefined ? 2 : pointLength)) + unit
    }
  },
};
</script>
<style lang="scss" scoped>
.material-resource-box {
  display: flex;
  align-items: center;
  background: #F6F8FA;

  .resource-content {
    width: calc(100% - 260px);
    padding: 30px;

    .breadcrumb-box {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
      display: -webkit-box;
      -webkit-box-orient: vertical;

      ::v-deep .el-breadcrumb {
        line-height: 2;

        .breadcrumb-item,
        .all-item {
          display: flex;
          align-items: center;
        }

        .all-item {

          .el-icon-arrow-right,
          .el-breadcrumb__inner {
            color: #333;
          }

          &:hover {

            .el-icon-arrow-right,
            .el-breadcrumb__inner {
              color: var(--theme_primary_color);
            }
          }
        }

        .el-breadcrumb__inner {
          cursor: pointer !important;
          display: inline-block;
          max-width: 180px;
          height: 16px;
          line-height: 16px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis
        }

        :deep .breadcrumb-item:last-child {
          .el-breadcrumb__inner {
            color: var(--theme_primary_color) !important;
          }
        }

      }
    }

    .material-library-content {
      background-color: #fff;
      border-radius: 10px;
      padding: 25px;
      color: #333333;
      margin-top: 5px;

      .condition-dv {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left-action {
          display: flex;
          gap: 10px;

          :deep(.el-input) {
            width: 260px;
          }
        }
      }

      .table-sty {
        width: 100%;
        border: 1px solid #f0f0f0;
        border-bottom: none;

        .file-name {
          display: flex;
          align-items: center;

          img {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            margin-top: -2px;
          }

          >span {
            cursor: pointer;
            max-width: 180px;
            height: 16px;
            line-height: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis
          }
        }

        .input-box {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 10px;

          ::v-deep .el-button+.el-button {
            margin-left: 0;
          }
        }

        :deep(.el-select) {
          width: 160px;
        }
      }
    }
  }


  .removeFolder {
    ::v-deep .el-dialog {
      border-radius: 10px;
      width: 320px;
      // height: 366px;

      .el-dialog__header {
        height: 60px;
        // background: #f7f7f7;
        border-radius: 10px 10px 0 0;
      }

      .el-dialog__body {
        display: flex;
        justify-content: flex-start;
        padding-top: 0px;

        .removePrompt {
          display: flex;
          flex-direction: column;
          align-items: center;

          >img {
            margin-bottom: 17px;
          }

          >span {
            font-size: 14px;
            color: #606a78;
          }
        }
      }

      .el-dialog__footer {
        width: 100%;
        text-align: right;
        padding: 20px;
      }
    }
  }

  ::v-deep .moveToDialog {
    .el-dialog__title {
      font-weight: 500;
      font-size: 16px;
      color: #222222;
    }

    .el-dialog__body {
      padding: 0 20px;
    }

    .move-to-dialog-contBox {
      .tree-header {
        font-size: 14px;
        color: #666666;
        height: 40px;
        background: #FBFBFB;
        border-radius: 4px 4px 0px 0px;
        padding: 0 10px;
        line-height: 40px;
      }

      .el-tree-node__content {
        height: 32px;
        line-height: 32px;
      }

      .custom-tree-node {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;

        .tree-text {
          display: flex;
          align-items: center;
          height: 32px;
          line-height: 32px;

          >span {
            margin-left: 7px;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            max-width: 185px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .usually {
          display: inline-block;
          width: 9px;
          height: 6px;
          background: url('../../../assets/material/arrow-bottom.png') no-repeat center/cover !important;
          transition: transform .3s ease-in-out;
          transform: rotate(180deg);
          margin-right: 9px;
        }

        .active {
          transform: rotate(0);
          transition: transform .3s ease-in-out;
        }
      }

      .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        .tree-text {
          >span {
            color: #fff !important;
          }
        }
      }

      .el-tree-node__expand-icon {
        display: none;
      }
    }
  }
}
</style>