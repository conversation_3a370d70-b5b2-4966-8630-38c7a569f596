<template>
  <div class="content-page resource-box">
    <LeftMenu :activeMenu="'resource'"   @handleResourceData="handleResourceData"></LeftMenu>

    <div class="content-box">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/studentCourseList' }">课程</el-breadcrumb-item>
        <el-breadcrumb-item>课程详情</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="content-wrapper resource-content-wrapper">
        <div class="content resource-content">

          <div class="material-folder" v-for="(item,index) in folderList" :key="item.id">
            <div class="material-folder-header">
              <div class="folder-name">
                <!-- <img src="@/assets/teaching/folder.png" alt=""> -->
                <span>{{ item.name }}</span>
              </div>
              <i class="arrow-icon el-icon-caret-bottom" @click="Shrink(index,$event)"></i>
            </div>
            <div class="material-content" ref="AccordionBody">
              <div>
                <div class="file-item" v-for="k in item.children" :key="k.id">
                  <div class="file-name">
                    <img v-if="k.filesType == 4" src="@/assets/material/img.png" alt="">
                    <img v-if="k.filesType == 1" src="@/assets/material/doc.png" alt="">
                    <img v-if="k.filesType == 3" src="@/assets/material/ppt.png" alt="">
                    <img v-if="k.filesType == 2" src="@/assets/material/pdf.png" alt="">
                    <img v-if="k.filesType == 6" src="@/assets/material/video.png" alt="">
                    <span>{{ k.name }}</span>
                  </div>
                  <span class="file-size">{{ formatSize(k.fileSize) }}</span>
                  <div class="view">
                    <span @click="opeDetail(k)">查看</span>
                  </div>

                </div>
              </div>

            </div>
          </div>

          <zd-default-page v-if="folderList.length==0" size="large" type="noData" msg="暂无内容"></zd-default-page>
        </div>
        <materialModule v-if="handlefile" :dialogVisible="handlefile" :fileObj="fileObj" @addClose="addClose">
        </materialModule>
      </div>
    </div>
  </div>
</template>

<script>
import LeftMenu from '../components/left-menu.vue'
import materialModule from "@/components/materialModule/index";

export default {
  data() {
    return {
      folderId: '',
      folderList: [],
      handlefile: false,
      fileObj: {},

    }
  },
  components: {
    LeftMenu,
    materialModule,
  },
  mounted() {
    this.getOneCourseId()
  },
  methods: {
    handleResourceData(){
       this.getAllFolder()
    },
    addClose(val) {
      this.handlefile = val;
    },
    //获取默认第一课程id
    async getOneCourseId() {
      let { data, code } = await this.$api.getCourseList();
      if (code === 200) {
        // this.getCourseInfo(data[0].id)
        this.getAllFolder(data[0].id)
      }
    },
    Shrink(index,event) {
      const isShrink = event.target.getAttribute('class').split(' ')[1]
      const  AllHiden = this.$refs.AccordionBody;

      const eleMoreHeight = AllHiden[index].childNodes[0].offsetHeight + 40 + "px";
      AllHiden[index].style.height = eleMoreHeight

      setTimeout(() => {
        if (isShrink == 'el-icon-caret-bottom') {
          event.target.setAttribute('class','arrow-icon el-icon-caret-top')
          AllHiden[index].style.height = "0px";
        } else {
          event.target.setAttribute('class','arrow-icon el-icon-caret-bottom')
          AllHiden[index].style.height = eleMoreHeight;
        }
      }, 1);
    },
    //获取所有类型文件夹
    async getAllFolder(id) {
      let data = {
        courseId: sessionStorage.getItem('courseId') || id
      }
      let res = await this.$api.StudentCourseMaterials(data)
      // console.log('文件树', res);
      if (res.code === 200) {
        this.folderList = res.data
      }
    },
    formatSize(size, pointLength, units) {
      var unit;
      units = units || ["B", "K", "M", "G", "TB"];
      while ((unit = units.shift()) && size > 1024) {
        size = size / 1024;
      }
      return (
        (unit === "B"
          ? size
          : size.toFixed(pointLength === undefined ? 2 : pointLength)) + unit
      );
    },
    async opeDetail(val) {
      let data = { id: val.id };
      let res = await this.$api.FavoritesResourceFindById(data);
      if (res.code === 200) {
        this.fileObj = res.data;
        this.handlefile = true;
      }
    },

  }

}
</script>

<style lang="scss" scoped>
.resource-box {
  .content-box {
    .resource-content {
      flex-direction: column !important;
      height: 100%;
      overflow-y: auto;
      .material-folder {
        border: 1px solid #E7E7E7;
        // overflow: hidden;
        margin-bottom: 30px;

        .material-folder-header {
          height: 48px;
          background: #F9F9F9;
          box-sizing: border-box;
          padding: 0 40px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .folder-name {
            display: flex;
            align-items: center;

            >img {
              margin-right: 20px;
            }

            >span {
              font-size: 14px;
              color: #222222;
            }

            >input {
              outline: none;
              height: 30px;
              padding-left: 10px;
              border-radius: 4px;
            }
          }
        }
        .arrow-icon {
          color: #3886ff;
          font-size: 18px;
          cursor: pointer;
        }

        .material-content {
          box-sizing: border-box;
          padding: 0 41px;
          overflow: hidden;
          transition: all 0.3s ease-in;
          .share {
            height: 60px;
            display: flex;
            align-items: center;
            border-bottom: 1px dashed #BBBBBB;

            >span {
              color: #222222;
              font-size: 14px;
              margin-right: 17px;
            }

            >button {
              // width: 59px;
              height: 20px;
              background: #F7F7F7;
              padding: 0 10px;
              border-radius: 3px 3px 3px 3px;
              font-size: 14px;
              color: #333333;
              line-height: 0px;
              margin-right: 5px;
              border: none;

            }
          }

          .file-item {
            height: 22px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .file-name {
              width: 46%;
              display: flex;
              align-items: center;
              font-size: 16px;
              color: #333333;


              >img {
                margin-right: 10px;
              }

              span {
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .file-size {
              width: 10%;
              font-size: 14px;
              color: #222222;
            }

            .view {
              span {
                font-size: 14px;
                color: #3776ec;
                cursor: pointer;
              }
            }

            .knowledge,
            .scene {
              width: 15%;
              font-size: 14px;
              color: #222222;

              span {
                &:nth-child(2) {
                  color: #3C76FF;
                }
              }
            }

            .scene {
              width: 22%;
            }

          }
        }
      }
    }

  }
}
</style>