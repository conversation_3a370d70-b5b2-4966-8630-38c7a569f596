<!--
 * @Author: xiaochongyang <EMAIL>
 * @Date: 2024-02-20 15:00:58
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-12-17 14:26:05
 * @FilePath: \fusion_front\src\views\student\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="student-work-wrapper">
    <studentHeader></studentHeader>
    <router-view></router-view>
  </div>
</template>

<script>
import studentHeader from '@/components/student-header/index.vue'
export default {
  name: 'studentIndex',
  props: {

  },
  components: {
    studentHeader
  },
  data() {
    return {

    }
  },
  watch: {

  },
  computed: {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.student-work-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

 ::v-deep .content-page {
    flex: 1;
    min-height: 0;
    display: flex;
    background: #F6F8FA;
    .el-breadcrumb{
      height:40px;
      line-height: 40px;
      background:#F6F8FA;
      padding-left: 12px;
      .el-breadcrumb__inner.is-link{
        color: #333;
      }
      .el-breadcrumb__inner{
        color: #2b66fa;
      }
    }
    .content-box {
      flex: 1;
      min-width: 0;
      border-radius: 10px;
      box-sizing: border-box;
      padding: 30px;
      padding-top: 0;
      .content-wrapper {
        flex: 1 1 0%;
        min-height: 0px;
        box-sizing: border-box;
        background-color: rgb(255, 255, 255);
        display: flex;
        flex-direction: column;
        padding: 24px 60px 30px 60px;
        height: 100%;
        &.cource-content-wrapper {
          padding: 54px 0px 0px 112px;
        }
        .content {
          flex: 1;
          display: flex;
        }
      }
    }
  }
}
</style>