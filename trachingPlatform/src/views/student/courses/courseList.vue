
<template>
  <div class="student-course-list">
    <div class="course-list-page">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>课程</el-breadcrumb-item>
        </el-breadcrumb>

        <div @wheel="handleWheel" class="course-content">
          <el-carousel ref="carousel" height="calc(100vh - 170px)" :indicator-position="renderList.length==1?'none':'outside'" :loop="false" direction="vertical" :autoplay="false">
              <el-carousel-item v-for="(item,index) in renderList" :key="index">
                <div class="course-carousel">
                  <courseCover v-for="course in item" :key="course" @skipCourse="skipCourse" :courseInfo="course" />
                </div>
              </el-carousel-item>
            </el-carousel>
        </div>
    </div>
  </div>
</template>
<script>
import { setCurrentCaseId} from "@/utils/token.js" 
export default {
  name: '',
  props: '',
  components: {
    courseCover: () => import('@/views/student/courses/components/courseCover.vue')
  },
  data() {
    return {
      renderList:[],// 待渲染的数组
    }
  },
  created() {
    this.initPage();
  },
  methods:{
    async initPage(){
      let { data, code } = await this.$api.getCourseList();
      let courses = data.map(item => {
        return {
          id: item.id,
          name: item.courseName,
          caseId: item.cases[0].caseId,
          images:item.images,
          intro:item.intro,
          createByName:item.createByName,
          teachCourseStatus:item.teachCourseStatus,
          teachCourseBeginTime:item.teachCourseBeginTime,//  开始时间
        };
      });
      this.renderList =  this.convertTo2DArray(courses,8);
      // console.log(this.renderList);
    },
    skipCourse(courseInfo){
      sessionStorage.setItem('courseId',courseInfo.id);
      sessionStorage.setItem('courseInfoId',courseInfo.id);
      this.$store.commit("setCourseInfo", courseInfo); // 存储课程信息
      setCurrentCaseId(courseInfo.caseId);// 存储课程绑定的案例
      
      this.$router.push({
        path:'/studentCourse',
        query: {
          id: courseInfo.id
        }
      })
    },
    handleWheel(event) {
      // 获取 el-carousel 实例
      const carousel = this.$refs.carousel;
      if (event.deltaY > 0) {
        // 如果滚轮向下滚动，切换到下一张
        carousel.next();
      } else {
        // 如果滚轮向上滚动，切换到上一张
        carousel.prev();
      }
    },
    /**
      将一维数组转成二维
      arr
      subArrayLength  子数组长度
    */
    convertTo2DArray(arr, subArrayLength) {
        const result = [];
        // 遍历数组，按 subArrayLength 拆分
        for (let i = 0; i < arr.length; i += subArrayLength) {
            // 使用 slice 方法获取子数组
            const subArray = arr.slice(i, i + subArrayLength);
            result.push(subArray);
        }
        return result;
    }
  }
}
</script>

<style lang="scss" scoped>
.student-course-list{
  background:#F6F8FA;
  height:calc(100% - 50px)
}
.course-list-page{
width:1200px;
margin:0 auto;

.el-breadcrumb{
  height:50px;
  line-height:50px;
}


.course-content{
  ::v-deep .el-carousel__indicators--outside{
    position: absolute;
    bottom: 0;
    top: auto;
  }
  ::v-deep .el-carousel__button{
    width: 4px;
    height:20px;
    background-color: var(--theme_primary_color)
  }
  ::v-deep .el-carousel__item.is-animating{
    padding-right:26px;
  }
  .course-carousel{
    display:flex;
    // justify-content:space-between;
    justify-content:flex-start;
    flex-wrap:wrap;

  }
}
// .el-carousel__item h3 {
//   color: #475669;
//   font-size: 14px;
//   opacity: 0.75;
//   line-height: 200px;
//   margin: 0;
// }

// .el-carousel__item:nth-child(2n) {
//   background-color;
// }

// .el-carousel__item:nth-child(2n+1) {
//   background-color: #d3dce6;
// }
}
</style>
