<template>
 <div class="course-cover" @click="skipCourse(courseInfo)">
    <div class="teaching-state" v-if="!courseInfo.teachCourseStatus">
      <span class="equalizer">
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
        <div class="bar"></div>
      </span>
      <span class="text">授课中</span> 
    </div>
    <div class="cover">
      <img :src="courseInfo.images||'../../../../assets/course/cover.png'" />
    </div>
    <div class="course-info">
      <p class="title">{{courseInfo.name}}</p>
      <p class="author">{{courseInfo.createByName}}</p>
    </div>
 </div>
</template>
<script>
export default {
  name: 'courseCover',
  props: ['courseInfo'],
  components: {
  },
  data() {
    return {

    }
  },
  methods:{
    skipCourse(info){
      this.$emit('skipCourse',info)
    }
  }
}
</script>
 
<style lang="scss" scoped>

.course-cover:nth-child(4n){
  margin-right:0
}
 .course-cover{
  width: 280px;
  height: 348px;
  background: #FFF;
  border-radius: 4px;
  margin-bottom: 26px;
  cursor:pointer;
  border:2px solid transparent;
  transition:all .5s;
  position: relative;
  margin-right:18px;
  &:hover{
    border-image: linear-gradient(45deg, #4aecff, #2b66ff, #fff, #ff38f7) 1; /* 45度渐变边框 */
    animation: gradientBorder 3s infinite linear;
  }
  .teaching-state{
    position: absolute;
    top: 0;
    right: 0;
    width:60px;
    height:20px;
    background: linear-gradient( 90deg, #FF7274 0%, #FF9A9B 100%);
    border-radius: 0px 0px 0px 12px;
    font-size: 12px;
    color:#fff;
    display:flex;
    justify-content: center;
    .equalizer{
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      width: 14px;
      height: 14px;
      .bar {
        width: 10px;
        height: 10px;
        background-color: #fff;
        margin-left:1px;
        animation: wave 1s infinite ease-in-out;
      }
      /* 每个柱子的不同动画延迟，让波动错开 */
      .bar:nth-child(1) {
        animation-delay: 0s;
      }
      .bar:nth-child(2) {
        animation-delay: 0.2s;
      }
      .bar:nth-child(3) {
        animation-delay: 0.4s;
      }
      .bar:nth-child(4) {
        animation-delay: 0.6s;
      }
      .bar:nth-child(5) {
        animation-delay: 0.8s;
      }
    }
    .text{
      line-height:20px;
      margin-left:5px;
    }
  }
  .cover{
    padding:40px 57px 27px 57px;
    img{
      width: 100%;
      height: 195px;
    }
  }
  .course-info{
    width:90%;
    margin:0 auto;
    height:76px;
    text-align:center;
    border-top:1px dashed #D9D9D9;
    display:flex;
    flex-direction:column;
    justify-content: center;
    .title{
      color:#333;
      font-size: 16px;
      line-height:22px;
      margin-bottom:5px;
      width:100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .author{
      color:#666;
      font-size: 14px;
      width:100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
 }

 /* 波动效果的关键帧动画 */
@keyframes gradientBorder {
  0% {
    border-image-source: linear-gradient(45deg, #4aecff, #2b66ff, #fff, #ff38f7);
  }
  25% {
    border-image-source: linear-gradient(45deg, #2b66ff, #fff, #ff38f7, #4aecff);
  }
  50% {
    border-image-source: linear-gradient(45deg,  #fff, #ff38f7, #4aecff, #2b66ff);
  }
  75% {
    border-image-source: linear-gradient(45deg,  #ff38f7,#4aecff, #2b66ff, #fff);
  }
  100% {
    border-image-source: linear-gradient(45deg, #4aecff, #2b66ff, #fff, #ff38f7);
  }
}

/* 波动动画 */
@keyframes wave {
  0% {
    height: 5px;
  }
  50% {
    height: 10px;
  }
  100% {
    height: 5px;
  }
}
</style>