<template>
  <!-- 学生课程预览 -->
  <div class="content-page student-course-page">
    <!-- @handleChartData="handleChartData" -->
    <LeftMenu ></LeftMenu>
    <div class="content-box">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/studentHome' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/studentCourseList' }">课程</el-breadcrumb-item>
        <el-breadcrumb-item>课程详情</el-breadcrumb-item>
      </el-breadcrumb>
      <!-- 新课程设计 画布缩放 -->
      <menuChange
        @changeScaling="changeScaling"
        @expendTree="expendTree"
       />
      <!-- <div class="content-wrapper cource-content-wrapper">
        <div class="content cource-content">
          <img class="directory" src="@/assets/student/directory.png" alt="">
          <el-tree :data="chapterData" :props="defaultProps" default-expand-all @node-click="handleNodeClick">
            <span class="custom-tree-node" slot-scope="{ node }">
              <span>{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
      </div> -->
      <div v-if="chapterShowType == 1" style="height: calc(100% - 56px);overflow-y: auto;">
        <course-design  :isOpenApi="menuType == '课程设计'" ref="design"></course-design>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import LeftMenu from '../components/left-menu.vue';
import courseDesign from '../../teaching-page/course-design/index.vue'; // 课程设计
import menuChange from '@/views/teaching-page/course-design/components/menuChange.vue' // 课程目录和思维导图的切换
export default {
  name: 'studentCourses',
  props: {

  },
  components: {
    LeftMenu,
    courseDesign,
    menuChange
  },
  data() {
    return {
      courseId: '',
      chapterData: [],
      defaultProps: {
        children: 'children',
        label: 'sectionName'
      },
      // changeType: 1,// 切换目录显示模式
      scaling: 100,//缩放比例
      isOpenTree: true,
    }
  },
  // watch: {
  //   courseId: {
  //     handler(newVal, val) {
  //       //console.log(newVal);
  //       // if (!newVal) {
  //       //   this.getOneCourseId()
  //       // } else {
  //       if (newVal) {
  //         sessionStorage.setItem('courseInfoId', newVal)
  //         this.getCourseInfo(newVal)
  //       }
  //       // }
  //     },
  //     immediate: true
  //   }
  // },
  computed: {
    ...mapGetters({
      chapterShowType: ["getChapterShowType"],
    })
  },
  mounted() {
    // this.getCourseId()
  },
  methods: {
    // 画布的缩放
    scale(val) {
      console.log("val", val);
      this.scaling = (val * 100).toFixed(0);
    },
    
    // map 的缩放事件
    changeScaling(val) {
      this.$refs.courseDesignXMind?.setMapScaling(val)
    },
    handleChartData() {
      // this.getCourseId()
    },
    //获取默认第一课程id
    // async getOneCourseId() {
    //   let { data, code } = await this.$api.getCourseList();
    //   if (code === 200) {
    //     const courseId = this.courseId ? this.courseId : data[0].id;
    //     this.getCourseInfo(courseId)
    //   }
    // },
    //获取当前课程id
    getCourseId() {
      this.courseId = sessionStorage.getItem('courseId')
    },
    handleNodeClick(data) {
      //console.log('当前节点信息', data);
      this.$router.push({
        path: '/courseDetails',
        query: { nodeId: data.id }
      })
    },
    //获取当前课程章节信息
    async getCourseInfo(id) {
      let data = {
        courseId: id
      }
      let res = await this.$api.GetAllChapters(data)
      if (res.code === 200) {
        this.chapterData = res.data
      }
      //console.log('课程章节', res);
    }
  },
  mounted() {
    this.getCourseId()
  }
}
</script>

<style lang="scss" scoped>
.student-course-page {
  .content-box {
    .directory {
      width: 46px;
      height: 171px;
      margin-right: 34px;
    }

    // ::v-deep .cource-content-wrapper {
    //   .el-tree {
    //     height: calc(100vh - 200px);
    //     flex: 1;
    //     padding-right: 112px;
    //     min-height: 0px;
    //     overflow-y: auto;
    //     border-left: 1px solid rgb(221, 221, 221);
    //     padding-left: 40px;
    //     padding-bottom: 20px;
    //   }

    //   .el-tree-node__children {
    //     .el-tree-node__content {
    //       border-bottom: 1px dashed #E3E7F0;
    //     }
    //   }

    //   .el-tree-node__content {
    //     height: auto;
    //     border-bottom: 1px solid #E3E7F0;
    //   }

    //   .custom-tree-node {
    //     flex: 1;
    //     display: flex;
    //     align-items: center;
    //     justify-content: space-between;
    //     font-size: 14px;
    //     padding-right: 8px;
    //     height: 49px;
    //     line-height: 49px;
    //     padding-left: 20px;

    //     &:hover {
    //       background-color: #F4F8FF;
    //     }
    //   }
    // }

  }
}
</style>