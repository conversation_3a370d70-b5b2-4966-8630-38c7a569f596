<template>
  <div class="courseDetails-box">

    <preparaPreview />
    <!-- <div v-if="false" class="directory">
      <div class="directory-icon">
        <img src="../../../assets/scene/list-img.png" alt="">
      </div>
      <el-tree :data="chapterData" :props="defaultProps" ref="sectionTree" node-key="id" :current-node-key="currentNode" default-expand-all @node-click="handleNodeClick">
      </el-tree>
    </div>
    <div v-if="false" class="right-content">
      <div class="headblank">
      </div>
      <el-tabs type="border-card" @tab-click="handleTabClick" v-model="editableTabsValue">
          <el-tab-pane  :label="item.name" :name="item.name" v-for="item in chapterClassList" :key="item.id">
            <section class="lessons-textEditor" >
              <template v-if="courseContents && courseContents.length > 0">
                <div class="container-box" ref="scrollContainer" @scroll="onScroll">
                  <section v-for="(item, index) in courseContents" :key="index" style="margin-bottom: 20px;">
                    <comp-teach-knowledge :pIndex="index" :isEdit="false" :isPrepare="true" :konwnList="item.content"
                      v-if="item.type == 5|| item.type == 4" />
                    <comp-teach-resource :pIndex="index" :isEdit="false" :isPrepare="true" :list="item.content"
                      v-if="item.type == 6|| item.type == 7" />
                    <comp-teach-attachment :pIndex="index" :isEdit="false" :isPrepare="true" :isShowClose="true"
                      @onDeleteFile="handleDeteleFile($event, index)" :list="item.content" v-if="item.type == 8" />
                    <comp-teach-flow v-if="item.type == 1" :pIndex="index" :isEdit="true" :isPrepare="true"
                      :flowInfo="item" />
                    <comp-teach-scene v-if="item.type == 2" :pIndex="index" :isEdit="true" :isPrepare="true"
                      :sceneInfo="item" />
                    <comp-edit-text v-if="item.type == 10" :pIndex="index" :isEdit="true" :isPrepare="true"
                      :isType="item.type" :textInfo="item"/>
                    <CompTeachAccount v-if="item.type === 3" :key="item.id" :obj="item.content" :pIndex="index"
                      :readOnly="false"></CompTeachAccount>
                    <comp-teach-task :key="`${item.id}-edit-task`" v-else-if="item.type == 11" :pIndex="index"
                      :isEdit="true" :isPrepare="false" :isCoursing="true" :isType="item.type" :taskInfo="item" />
                    <comp-teach-discuss :key="`${item.id}-edit-discuss`" v-else-if="item.type == 12" :pIndex="index"
                      :isEdit="true" :isCoursing="true" :isPrepare="false" :isType="item.type"
                      :discussInfo="item" />
    
                  </section>
                  <el-button class="more-btn" type="text" v-if="canLoadMore" >加载更多...</el-button>
                </div>
              </template>
            </section>
          </el-tab-pane>
      </el-tabs>
    </div> -->
  </div>
</template>

<script>
// import CompTeachKnowledge from "../../teaching-page/components/teachKnowledge.vue"
// import CompTeachResource from "../../teaching-page/components/teachResource.vue"
// import CompTeachAttachment from "../../teaching-page/components/teachAttachment.vue"
// import CompTeachFlow from "../../teaching-page/components/teachFlow.vue";
// import CompTeachScene from "../../teaching-page/components/teachScene.vue";
// import CompTeachAccount from "../../teaching-page/components/teach-components/accounts/teachAccount.vue";
// import CompEditText from "../../teaching-page/components/editText.vue";
// import CompTeachTask from "../../teaching-page/components/teachTask.vue";//  已引入的任务、测验
// import CompTeachDiscuss from "../../teaching-page/components/teachDiscuss.vue";// 主题讨论
import {mapGetters} from 'vuex'
import preparaPreview from '@/views/teaching-page/components/preparaPreview.vue'  // 学生预览
export default {
  data() {
    return {
      // defaultProps: {
      //   children: 'children',
      //   label: 'sectionName'
      // },
      // chapterData: [],
      // chapterClassList: [],
      // courseContents: [],// 当前分类下的资源加载
      // currentTypeResources: [],//  当前分类下的所有资源
      // currentIndex: 0, // 当前加载的索引
      // pageSize: 5, // 每次加载的数量
      // currentNode:'',
      // editableTabsValue: "0",
    }
  },
  components: {
    // CompTeachKnowledge,
    // CompTeachResource,
    // CompTeachAttachment,
    // CompTeachFlow,
    // CompTeachScene,
    // CompTeachAccount,
    // CompEditText,
    // CompTeachTask,
    // CompTeachDiscuss,

    preparaPreview,// 学生预览课程
  },
  computed:{
    ...mapGetters({
      userInfo: ["userInfo"],
    }),
    // canLoadMore() { //是否加载更多
    //   return this.currentIndex < this.currentTypeResources.length;
    // },
  },
  mounted() {
    // this.getCourseInfo(sessionStorage.getItem('courseId'))
    
  },
  created(){
    // this.getCurentNode()
  },
  methods: {
    // getCurentNode(){
    //   this.currentNode = this.$route.query.nodeId
    //   this.$nextTick(() => {
    //     this.$refs['sectionTree'].setCurrentKey(this.currentNode);
    //   });
    //   this.ChapterAllClass(this.currentNode)
    // },
    // handleTabClick(e) {
    //   this.currentIndex = 0;
    //   this.CoursePrepareSearchBy(this.chapterClassList[e.index].id)
    // },
    // //获取课程树
    // async getCourseInfo(id) {
    //   let data = {
    //     courseId: id
    //   }
    //   let res = await this.$api.GetAllChapters(data)
    //   if (res.code === 200) {
    //     this.chapterData = res.data
    //   }
    //   console.log('课程章节', res);
    // },
    // //章节节点选择
    // handleNodeClick(data) {
    //   this.currentIndex = 0;
    //   this.ChapterAllClass(data.id)
    // },
    // //获取章节下所有分类
    // async ChapterAllClass(id) {
    //   let data = {
    //     params: {
    //       sectionId: id
    //     }
    //   }
    //   let res = await this.$api.ClassificationUnderChapters(data)
    //   if (res.code === 200 && res.data) {
    //     this.chapterClassList = res.data
    //     if (res.data.length > 0) {
    //       this.editableTabsValue = res.data[0]?.name;
    //       this.CoursePrepareSearchBy(res.data[0]?.id)
    //     }
    //   }
    // },
    // //分类内容
    // CoursePrepareSearchBy(categoryId) {
    //   this.courseContents = [];
    //   this.currentTypeResources = [];
    //   let param = {
    //     params: {
    //       categoryId
    //     },
    //     orderBy: 'sort asc'
    //   }
    //   this.$api.CoursePrepareSearchBy(param).then(res => {
    //     if (res.code === 200) {
         
    //       res.data?.forEach(item => {
    //         this.currentTypeResources.push({
    //           type: item.type,
    //           id: item.id,
    //           content: JSON.parse(item.content)
    //         })
    //       })
    //       this.loadMore();
    //       // this.loadBatch(this.currentTypeResources, this.editableTabsValue)
    //       // res.data?.forEach(item => {
    //       //   this.courseContents.push({
    //       //     type: item.type,
    //       //     id: item.id,
    //       //     content: JSON.parse(item.content)
    //       //   })
    //       // })

    //     }
    //   })
    // },

    // // 加载更多
    // loadMore() {
    //   if (!this.canLoadMore) return;
    //   const newData = this.currentTypeResources.slice(
    //     this.currentIndex,
    //     this.currentIndex + this.pageSize
    //   );
    //   this.courseContents = [...this.courseContents, ...newData];
    //   this.currentIndex += this.pageSize;
    // },
    // onScroll() {
    //   const container = this.$refs.scrollContainer[0];
    //   if (
    //     container.scrollTop + container.clientHeight >=
    //     container.scrollHeight
    //   ) {
    //     this.loadMore();
    //   }
    // },
    // // 分批加载场景
    // loadBatch(allData, editableTabsValue) {
    //   const batchSize = 1; // 每批加载的数据量
    //   let currentIndex = 0;
    //   // 单个题目加载
    //   let self = this;
    //   const loadQuestion = () => {
    //     if (editableTabsValue != self.editableTabsValue) return // 如果tab 切换 则停止加载
    //     const nextIndex = currentIndex + batchSize;
    //     const batchData = allData.slice(currentIndex, nextIndex);
    //     self.courseContents = self.courseContents.concat(batchData);
    //     currentIndex = nextIndex;
    //     if (currentIndex < allData.length) {
    //       setTimeout(() => {
    //         loadQuestion()
    //       }, 700); // 每批次间隔700ms加载
    //     } else {
    //       console.log('questionLoad');
    //     }
    //   };
    //   loadQuestion();
    // },
  }
}
</script>

<style lang='scss' scoped>
.courseDetails-box {
  width: 100%;
  height: calc(100vh - 50px);
  background: rgb(255, 255, 255);
  display: flex;
  // .container-box{
  //   height: 100%;
  //   overflow-y: auto;
  // }
  // .directory {
  //   width: 270px;
  //   height: 100%;

  //   // background: goldenrod;
  //   .directory-icon {
  //     height: 120px;
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //   }

  //   ::v-deep .el-tree{
  //     height: calc(100vh - 180px);
  //     overflow-y: auto;
  //     .el-tree-node__content {
  //       background: white;
  //       height: 50px;
  //     }

  //     .el-tree-node__content:hover {
  //       border-left: 2px solid #2B66FF;
  //       background-color: #F3F8FF;

  //       .node {
  //         background-color: #F3F8FF;
  //       }

  //     }

  //     .el-tree-node.is-current>.el-tree-node__content:first-child {
  //       border-left: 2px solid #2B66FF;
  //       background-color: #F3F8FF;

  //       span {
  //         color: #2B66FF;
  //       }
  //     }
  //     .el-tree-node .is-expanded .is-current .is-focusable{
  //       border-left: 2px solid #2B66FF;
  //       background-color: #F3F8FF;

  //       span {
  //         color: #2B66FF;
  //       }
  //     }
  //   }

  // }

  // .right-content {
  //   position: relative;
  //   // min-width: 0;
  //   width: calc(100vw - 270px);
  //   // background: rgb(201, 201, 173);

  //   .lessons-textEditor{
  //     height: calc(100vh - 170px);
  //     overflow-y: auto;
  //   }
  // }

  // .headblank {
  //   height: 40px;
  //   background: white;

  //   >div {
  //     height: 84px;
  //     background: #F6F8FA;
  //   }
  // }

   
  //   ::v-deep .el-tabs {
  //     border: none;

  //     // box-shadow: none;
  //     .el-tabs__header {
  //       background: #F2F2F2;
  //       // height: 36px;
  //       border: none;

  //       .el-tabs__item {
  //         border-radius: 6px;
  //         background: #F8F8F8;
  //         margin-right: 10px;
  //         height: 60%;
  //       }
  //     }

  //     .el-tabs__content {
  //       height: calc(100vh - 140px);
  //       background: #F6F8FA;
  //       box-sizing: border-box;
  //       // padding: 44px 65px 60px 65px;
  //       overflow: auto;
  //     }
  //   }

}
</style>