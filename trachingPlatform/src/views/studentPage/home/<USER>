<template>
  <el-scrollbar class="h-full">
    <div class="student-content">
      <div class="top-title">
        <h3 class="title-label">我的课程</h3>
        <el-input
          v-model="searchWord"
          placeholder="请搜索课程名称"
          class="input-with-btn"
          clearable
        >
          <el-button slot="append" @click="handleSearch">搜索</el-button>
        </el-input>
      </div>
      <div class="student-course-list">
        <div
          v-for="item in courseList"
          :key="item.id"
          class="course-item"
          @click="toCourseDetail(item)"
        >
          <img
            class="course-cover"
            :src="
              item.coverImage
                ? item.coverImage
                : '@/assets/images/course-default.png'
            "
            alt=""
          />
          <h4 class="course-title">{{ item.name }}</h4>
          <div class="flex flex-col gap-10px mt-10px">
            <div class="flex">
              <div class="flex item-center gap-6px label-one overflow-hidden">
                <i class="iconfont icon-jiaoshi text-12px"></i>
                <div class="truncate flex-1-hidden">
                  任课老师:
                  {{ item?.subjectTeachers?.map((v) => v.name).join(", ") }}
                </div>
              </div>
            </div>
            <div class="flex">
              <div class="flex item-center gap-6px label-two overflow-hidden">
                <i class="iconfont icon-zhuanye text-12px"></i>
                <div class="truncate flex-1-hidden">
                  所属专业: {{ item.bigMajorName || "-" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-wrap"></div>
    </div>
  </el-scrollbar>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "StudentHome",
  data() {
    return {
      loading: false,
      originalCourseList: [], //原始课程列表
      courseList: [], //课程列表
      searchWord: null,
      paramSearch: {
        schoolId: null,
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  created() {
    this.paramSearch.schoolId = this.userInfo.schools[0].id;
    this.handleGetCourseList();
  },
  methods: {
    // 获取课程列表
    async handleGetCourseList() {
      try {
        this.loading = true;
        const res = await this.$api.MyCoursesOfStudent(this.paramSearch);
        this.originalCourseList = res.data ?? [];
        this.courseList = this.originalCourseList;
      } finally {
        this.loading = false;
      }
    },
    // 搜索
    handleSearch() {
      this.courseList = this.originalCourseList.filter((item) => {
        return item.name.includes(this.searchWord);
      });
    },
    // 跳转到课程详情
    toCourseDetail(item) {
      this.$router.push({
        path: "/student/course/info",
        query: {
          id: item.id,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.bg-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  background: url("~@/assets/images/students/home-bg.png") no-repeat;
  background-size: 100% 100%;
}

.student-content {
  width: 1200px;
  margin: 20px auto 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  .top-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .title-label {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #021632;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      position: relative;
      padding-left: 10px;
      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 4px;
        width: 3px;
        height: 12px;
        background: #0070fc;
      }
    }
    .input-with-btn {
      width: 460px;
      .el-input-group__append button.el-button {
        background: #0070fc;
        color: #fff;
        border-bottom-left-radius: 0;
        border-top-left-radius: 0;
      }
    }
  }
}
.student-course-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  .course-item {
    width: 285px;
    height: 280px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 10px;
    margin-bottom: 30px;
    cursor: pointer;

    .course-cover {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .course-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      margin: 10px 0 0 0;
    }
    .label-one {
      font-size: 12px;
      font-weight: 400;
      color: #5583f2;
      background: #edf2ff;
      border-radius: 4px;
      padding: 4px 8px;
    }
    .label-two {
      font-size: 12px;
      font-weight: 400;
      color: #07c392;
      background: #e5fcf5;
      border-radius: 4px;
      padding: 4px 8px;
    }
  }
}
</style>
