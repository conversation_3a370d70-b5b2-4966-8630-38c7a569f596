<template>
  <div class="wh-full bg-white flex flex-col rounded-4px p-20px gap-20px">
    <div class="flex item-center justify-between">
      <div class="flex items-center gap-10px">
        <el-button
          type="primary"
          icon="el-icon-plus"
          class="add-btn"
        >
          添加分类
        </el-button>
      </div>
      <el-input
        class="search-input w-300px"
        placeholder="请搜索文件名"
        suffix-icon="el-icon-search"
        v-model="searchText"
        clearable
      ></el-input>
    </div>

    <el-table
      :data="tableData"
      style="width: 100%"
      row-key="id"
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <el-table-column
        prop="date"
        label="名称"
        width="180">
      </el-table-column>
      <el-table-column
        prop="name"
        label="大小"
        width="180">
      </el-table-column>
      <el-table-column
        prop="address"
        label="日期">
      </el-table-column>
      <el-table-column
        prop="address"
        label="操作">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "CourseNote",
  data() {
    return {
      searchText: "",
      tableData: [{
        id: 1,
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        id: 2,
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        id: 3,
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄',
        children: [{
          id: 31,
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }, {
          id: 32,
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }]
      }, {
        id: 4,
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }],
    };
  },
};
</script>

<style lang="scss" scoped>
.add-btn{
  width: 120px;
  height: 38px;
  background: #07C392;
  border: 1px solid #07C392;
  color: #fff;
  border-radius: 4px;
  padding: 0;
}

::v-deep .el-table {
  th {
    background-color: #EFF2F5;

    &.el-table__cell > .cell {
      color: #5C6075;
      font-size: 14px;
      font-weight: 500;
    }
  }
}
</style>
