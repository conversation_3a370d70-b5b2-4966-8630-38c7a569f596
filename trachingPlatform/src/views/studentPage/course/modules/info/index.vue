<template>
  <div class="w-full h-full flex flex-col">
    <div class="w-full bg-white h-50px rounded-4px px-20px">
      <el-tabs v-model="activeName" class="h-full">
        <el-tab-pane label="课程信息" name="first"></el-tab-pane>
        <el-tab-pane label="课程评价" name="second"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="activeName === 'first'" class="w-full flex-1-hidden bg-#fff flex flex-col item-center justify-center" v-loading="loading">
      <div class="flex space-x-20px m-a">
        <img :src="courseInfo.coverImage" alt="" class="w-284px h-160px object-cover rounded-4px">
        <div class="flex flex-col">
          <div class="text-16px font-500 text-#333 pb-20px">{{courseInfo.name}}</div>
          <div class="flex flex-col gap-14px">
            <div class="flex item-center gap-40px">
              <div><span class="label">学&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</span><span class="value">{{courseInfo.hours}}</span></div>
              <div><span class="label">专业层次：</span><span class="value">{{courseInfo.majorCengCi}}</span></div>
            </div>
            <div><span class="label">所属专业：</span><span class="value">{{courseInfo.majorCategory}}</span></div>
            <div class="w-360px"><span class="label">课程简介：</span><span class="value">{{courseInfo.description}}</span></div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeName === 'second'">
      课程评价
    </div>
  </div>
</template>

<script>
export default {
  name: "CourseInfo",
  data() {
    return {
      loading: false,
      activeName: "first",
      courseInfo: {}
    };
  },
  created() {
    this.getCourseInfo();
  },
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        this.loading = true;
        const res = await this.$api.GetCourseDetail({ id: this.$route.query.id })
        this.courseInfo = res.data || {};
      } finally {
        setTimeout(() => {
          this.loading = false;
        }, 300);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__nav-wrap {
  &::after {
    display: none;
  }
}

::v-deep .el-tabs__item {
  height: 50px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 400;
  color: #5C6075;
  &.is-active {
    color: #0070FC;
  }
}

.label {
  font-weight: 400;
  font-size: 14px;
  color: #999;
  line-height: 20px;
}

.value {
  font-weight: 400;
  font-size: 14px;
  color: #333;
  line-height: 20px;
}
</style>
