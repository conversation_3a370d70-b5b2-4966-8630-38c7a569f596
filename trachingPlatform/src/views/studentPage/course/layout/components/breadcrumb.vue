<template>
  <div class="breadcrumb py-20px">
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbList"
        :key="index"
        :to="index === breadcrumbList.length - 1 ? '' : item.path"
      >
        {{ item.meta.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script>
export default {
  name: "CourseBreadcrumb",
  data() {
    return {};
  },
  computed: {
    breadcrumbList() {
      return this.$route.matched.filter((item) => !!item.meta?.title);
    },
  },
  mounted() {},
  methods: {
    // getBreadcrumb(route) {
    //   this.breadcrumbList = route.matched.filter(item => !!item.meta?.title);
    // }
  },
  // watch: {
  //   '$route': {
  //     handler(val) {
  //       this.getBreadcrumb(val)
  //     },
  //     deep: true,
  //     immediate: true
  //   }
  // },
};
</script>

<style lang="scss" scoped></style>
