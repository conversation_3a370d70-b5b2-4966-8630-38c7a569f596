<template>
  <div class="w-180 h-full bg-#fff rounded-4px py-6px flex flex-col gap-10px">
    <div
      class="px-20px flex items-center h-38px gap-10px cursor-pointer  list-item"
      :class="{'active': item.path === $route.path}"
      v-for="(item, index) in list"
      :key="index"
      @click="handleClick(item.path)"
    >
      <div class="w-14px h-14px iconfont text-#C0C6D6 icon mt-[-2px]" :class="{'active': item.path === $route.path, [item.icon]: true}"></div>
      <div class="text-14px font-400">{{item.title}}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CourseSideBar",
  data() {
    return {
      list: [
        {
          title: '课程资源',
          icon: 'icon-fenpei',
          path: '/student/course/resource'
        },
        {
          title: '关联教材',
          icon: 'icon-shuzijiaocai',
          path: '/student/course/textbook'
        },
        {
          title: '课程作业',
          icon: 'icon-assignment',
          path: '/student/course/homework'
        },
        {
          title: '课程考试',
          icon: 'icon-ic_zuoye',
          path: '/student/course/exam'
        },
        {
          title: '课程笔记',
          icon: 'icon-bijiben_B',
          path: '/student/course/note'
        },
        {
          title: '学情分析',
          icon: 'icon-xueqingfenxi',
          path: '/student/course/analysis'
        },
        {
          title: '课程信息',
          icon: 'icon-Gc_59_face-Information',
          path: '/student/course/info'
        }
      ],
    };
  },
  methods: {
    handleClick(path) {
      this.$router.push({
        path,
        query: this.$route.query
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.list-item {
  &.active{
    background: #F1F7FF !important;
    color: #0070FC !important;
    position: relative;
    &::after{
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      width: 2px;
      height: 100%;
      background: #0070FC;
    }
  }

  .icon {
    &.active{
      color: #0070FC !important;
    }
  }

  &:hover{
    background: #F1F7FF !important;
    color: #0070FC !important;
    .icon {
      color: #0070FC !important;
    }
  }
}

</style>
