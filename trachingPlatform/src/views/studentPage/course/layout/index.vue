<template>
  <div class="w-full h-full bg-#F6F8FA">
    <div class="w-1200px m-a flex flex-col h-full">
      <Breadcrumb />
      <div class="w-full flex items-start gap-10px flex-1">
        <sideBar />
        <div class="flex-1-hidden w-full flex-1-hidden h-full">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import sideBar from './components/sideBar.vue'
import Breadcrumb from './components/breadcrumb.vue'

export default {
  name: 'CourseLayout',
  components: {
    sideBar,
    Breadcrumb
  },
  data() {
    return {

    }
  },
}
</script>
