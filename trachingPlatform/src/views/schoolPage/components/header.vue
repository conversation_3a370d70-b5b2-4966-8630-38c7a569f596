<template>
  <div class="school-header">
    <div class="header-left">
      <div class="logo-img">
        <img src="../../../assets/public/logo.png" alt="">
      </div>
      <el-menu :default-active="activeIndex" class="el-menu-sys" mode="horizontal" @select="handleSelect($event, true)">
        <el-menu-item index="admin"><i class="iconfont icon-jichufuwu mr-8"></i> 学校端</el-menu-item>
        <el-menu-item index="teach"><i class="iconfont icon-ziyuan mr-8"></i> 教学端</el-menu-item>
        <!-- <el-menu-item index="account"><i class="iconfont icon-zhanghuxinxi2 mr-8"></i> 账户管理</el-menu-item> -->
      </el-menu>
    </div>
    <div class="header-right">
      <User/>
    </div>
  </div>
</template>

<script>
import eventBus from "@/utils/eventBus.js";
import { mapGetters } from "vuex";
export default {
  name: "schoolHeader",
  data() {
    return {
      activeIndex: "admin",
    }; 
  },
  components:{
   User:()=>import('@/components/common/user.vue')
  },
  created() {
    // 组件创建时根据当前路由触发 handleSelect
    // this.handleRouteChange();
    // 监听路由变化
    // this.$router.afterEach(() => {
    //   this.handleRouteChange();
    // });
  },
  mounted() {
    // this.handleSelect('base', true); // 默认选中第一个选项
  },
  computed: {
    ...mapGetters({
      userInfo:['userInfo']
    }),  
  },
  methods: {
    // isTeacher(){
    //   let userType = this.userInfo.user.userType.split(',');
    //   return userType.includes('Teacher')
    // },
    handleSelect(key, isManual = true) {
      eventBus.$emit("changeMenu", key, isManual);
    },
    handleRouteChange() {
      const metaType = this.$route.meta.type;
      if (metaType) {
        this.activeIndex = metaType;
        this.handleSelect(metaType, false);
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.school-header{ 
    background-color: #fff;
    height: 60px;
    line-height: 60px;
    box-shadow: 0px 0px 13px 0px rgba(0,0,0,0.1);
    padding: 0 20px; // 左右边距，用于留出边距空间，避免内容紧贴边
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    .header-left{
      display: flex;
      align-items: center;
      .logo-img{
        font-size: 20px;
        font-weight: bold;
        color: #333; // 文字颜色
        min-width: 200px;
        img{
          vertical-align: middle;
        }
      }
      .el-menu-sys.el-menu.el-menu--horizontal{
        margin-left: 74px; // 左侧菜单与logo之间的间距;
        border-bottom: none; // 去除底部边框
       
        .el-menu-item{
          height: 50px; // 菜单项的高度
          line-height: 50px; // 菜单项的行高; 
          padding: 0;
          margin-right: 64px; // 菜单项之间的间距
          &:hover{
            color: #0070FC;
            .iconfont{
              color: #0070FC;
            }
          }
          &.is-active{
            color: #0070FC; // 选中项的文字颜色
            font-weight: bold; // 选中项的字体加粗
          }
        }

        .mr-8{
          margin-right: 4px; // 图标和文字之间的间距
        }
      }
    }
}
</style>