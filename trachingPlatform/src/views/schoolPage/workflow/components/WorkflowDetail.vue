<template>
  <div v-loading="loading">
    <template v-if="workflowDetail">
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="工作流ID">
          {{ workflowDetail.instance.id }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
          {{ workflowDetail.instance.description }}
        </el-descriptions-item>
        <el-descriptions-item label="审批状态">
          <el-tag
            :type="getStatusType(workflowDetail.instance.approveStatus)"
          >
            {{ getStatusText(workflowDetail.instance.approveStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(workflowDetail.instance.createTime) }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审批节点 -->
      <div class="mt-4">
        <h3>审批节点</h3>
        <el-timeline>
          <el-timeline-item
            v-for="node in filteredNodes"
            :key="node.id"
            :type="getNodeStatusType(node.status)"
            :timestamp="formatDate(node.createTime)"
          >
            <div class="node-content">
              <div class="node-header">
                <span class="node-name">{{ node.nodeName }}</span>
                <el-tag :type="getNodeStatusType(node.status)" size="small">
                  {{ getStatusText(node.status) }}
                </el-tag>
              </div>
              <div class="node-info">
                <div class="approver-info">
                  <span class="label">审批人类型:</span>
                  <span class="value">{{
                    node.approverType === "USER" ? "用户" : "角色"
                  }}</span>
                </div>
                <div class="approver-info">
                  <span class="label">审批人:</span>
                  <span class="value">{{ node.approverValue }}</span>
                </div>
                <div class="approver-info">
                  <span class="label">会签类型:</span>
                  <span class="value">{{
                    node.signType === "AND" ? "会签" : "或签"
                  }}</span>
                </div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 审批记录 -->
      <div class="mt-4">
        <h3>审批记录</h3>
        <el-table :data="workflowDetail.records" border style="width: 100%">
          <el-table-column
            prop="approverUserId"
            label="审批人ID"
            width="100"
          />
          <el-table-column prop="action" label="操作" width="100">
            <template slot-scope="{ row }">
              <el-tag :type="row.action === 'APPROVE' ? 'success' : 'danger'">
                {{ row.action === "APPROVE" ? "同意" : "拒绝" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="审批意见" />
          <el-table-column prop="createTime" label="审批时间" width="180">
            <template slot-scope="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </div>
</template>

<script>
import formatDate from "@/utils/format-date.js";
export default {
  name: "WorkflowDetailDialog",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    workflowDetail: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    },
    filteredNodes() {
      return this.workflowDetail?.nodes?.filter(
        (n) => n.nodeType === "APPROVAL"
      ) || [];
    }
  },
  methods: {
    getStatusType(status) {
      switch (status) {
        case "PENDING":
          return "warning";
        case "APPROVED":
          return "success";
        case "REJECTED":
          return "danger";
        case "CANCELLED":
          return "info";
        default:
          return "info";
      }
    },
    getStatusText(status) {
      switch (status) {
        case "PENDING":
          return "审批中";
        case "APPROVED":
          return "已通过";
        case "REJECTED":
          return "已拒绝";
        case "CANCELLED":
          return "已取消";
        default:
          return "未知";
      }
    },
    getNodeStatusType(status) {
      return this.getStatusType(status);
    },
    formatDate,
  }
};
</script>

<style lang="scss" scoped>
.mt-4 {
  margin-top: 16px;
}

h3 {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
}

h4 {
  margin: 0 0 8px;
  font-size: 14px;
  font-weight: 500;
}

p {
  margin: 4px 0;
  color: #666;
  font-size: 13px;
}

.node-content {
  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .node-name {
      font-size: 14px;
      font-weight: 500;
    }
  }

  .node-info {
    .approver-info {
      margin: 4px 0;
      color: #666;
      font-size: 13px;

      .label {
        color: #909399;
        margin-right: 8px;
      }
    }
  }
}
</style>
