<template>
  <div class="work-flow-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @blur="initTableData" clearable placeholder="请搜索流程名称" suffix-icon="iconfont icon-sousuo"></el-input>
      <addButton label="新增模版" widths="120px" @addEvent="addEvent" />
      <reflashButton @reflashEvent="reflashEvent" />
    </section>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :height="height" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" 
                       :key="index"
                       type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <!-- 新增流程模板 -->
    <baseDialog :noFooter="true" :showToScreen="false" width="900px" :visible.sync="dialogVisible" className="add-flow-dialog" :title="!form.template.id?'新增工作审批流':'编辑工作审批流'">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" label-position="top">
        <el-form-item label="模板名称" prop="template.name">
          <el-input v-model="form.template.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板描述" prop="template.description">
          <el-input
            v-model="form.template.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        <!-- 审批节点列表 -->
        <div class="nodes-container">
          <div class="nodes-header">
            <h3>审批节点</h3>
            <el-button v-if="type!='detail'" type="text" @click="handleAddNode">
              <i class="el-icon-plus"></i>添加节点
            </el-button>
          </div>
          <el-table :data="form.nodes" border style="width: 100%" :header-cell-style="{ background: '#EFF2F5', color: '#5C6075' }">
            <el-table-column label="节点名称" width="180">
              <template slot-scope="{ row }">
                <el-input v-model="row.nodeName" placeholder="请输入节点名称" />
              </template>
            </el-table-column>
            <el-table-column label="节点类型" >
              <template slot-scope="{ row }">
                <el-select v-model="row.nodeType" placeholder="请选择">
                  <el-option label="审批" value="APPROVAL" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="审批人类型">
              <template slot-scope="{ row }">
                <el-select v-model="row.approverType" placeholder="请选择">
                  <el-option label="用户" value="USER" />
                  <!-- <el-option label="角色" value="ROLE" /> -->
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="审批人">
              <template slot-scope="{ row }">
                <el-select
                  v-if="row.approverType === WFApproverType.USER"
                  v-model="row.approverValue"
                  placeholder="请选择审批人"
                  filterable
                >
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="`${user.name} (ID: ${user.id})`"
                    :value="user.id.toString()"
                  />
                </el-select>
                <el-input
                  v-else
                  v-model="row.approverValue"
                  placeholder="请输入角色ID"
                />
              </template>
            </el-table-column>
            <el-table-column label="会签类型">
              <template slot-scope="{ row }">
                <el-select v-model="row.signType" placeholder="请选择">
                  <el-option label="会签" value="AND" />
                  <el-option label="或签" value="OR" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button type="text" @click="handleRemoveNode(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-form-item style="text-align:right;margin-top:30px">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
    <el-dialog :visible.sync="flowDialogVisible" custom-class="flow-dialog" title="提示">
      <el-select v-model="businessId" clearable filterable multiple>
        <el-option v-for="item in businessTypeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
      </el-select>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;" v-if="type!='detail'">
        <el-button style="width:80px;" type="default" @click="flowDialogVisible = false;">取消</el-button>
        <el-button style="width:80px;" type="primary" @click="handleSetFlow" v-preventReClick>保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { UserType } from "@/constants/common.js"; // 导入常量
import token from "@/utils/token.js";
import { mapGetters } from "vuex";
import { WFNodeType, WFApproverType, WFSignType } from "./flowType.js";
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数

const businessTypeList= [{
  value:'TextbookApproval',
  label:'教材审批'
}]
const businessTypeLable={
  TextbookApproval:'教材审批'
}
export default {
  name: "userManage",
  data() {
    return {
      businessTypeList,//业务类型
      userType: UserType, // 用户类型
      WFApproverType: WFApproverType, // 审批人类型
      WFSignType: WFSignType, // 会签类型
      WFNodeType: WFNodeType, // 节点类型
      userList: [], // 用户列表
      height: '660px', // 表格高度
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          // {
          //   label: "ID",
          //   prop: "id",
          //   formatter: (row, column, cellValue, index) => {
          //     return row.template.id;
          //   }
          // },
          {
            label: "审批流程名称",
            prop: "name",
            formatter: (row, column, cellValue, index) => {
              return row.template.name;
            }
          },
          {
            label: "描述",
            prop: "description",
            formatter: (row, column, cellValue, index) => {
              return row.template.description;
            }
          },
          {
            label: "应用状态",
            prop: "template",
            align: "center", // 居中对齐
            formatters: (row, column, cellValue, index) => {
              return cellValue.createBy?'<span style="display:inline-block;text-align:center;width: 50px;height: 20px;background: #EDF2FF;border-radius: 4px;color:#0070FC;">已应用</span>':'<span style="display:inline-block;text-align:center;width: 50px;height: 20px;background: #FBEEEE;border-radius: 4px;color:#F11B1B;">未应用</span>';
            }
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              return row.template.createTime?formatISOString(row.template.createTime):'';
            }
          },
          {
            label: "绑定的业务",
            prop: "bizBindings",
            formatter: (row, column, cellValue, index) => {
              return row.bizBindings.map(v=>businessTypeLable[v.businessType]).join(',')
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord: "", // 搜索关键字
          schoolId: 0 // 
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
        queryConfig: {},
      },
      firstLoad: true,
      loading: false,
      actionBtns: [
        // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '查看详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '设置业务',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 240,
      collegeList:[],// 学院列表
      classList:[],// 班级列表
      courseList:[],// 课程列表
      dialogVisible:false,// 弹窗是否显示
      type:'add',// 类型
      form:{ // 新增表单
        template: {
          id: 0,
          name: '',
          description: '',
          createBy:0
        },
        nodes: [
          {
            nodeName: '',
            nodeType: 'APPROVAL',
            approverType: 'USER',
            approverValue: '',
            signType: 'AND',
            nodeOrder: 1,
          }
        ]
      },
      // 添加校验规则
      rules: {
       "template.name": [
          { required: true, message: "请输入模板名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" },
        ],
        "template.description": [
          { required: true, message: "请输入模板描述", trigger: "blur" },
          { min: 1, max: 500, message: "长度在 1 到 500 个字符", trigger: "blur" },
        ],
      },
      flowDialogVisible:false,// 弹窗是否显示

      templateId:0,
      businessId:[],
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    workflowDetail:()=>import("@/views/schoolPage/workflow/components/WorkflowDetail.vue"), // 工作流详情

  },
  computed: {
    ...mapGetters({
      userInfo:'userInfo'
    }),
  },
  mounted() {
    // 初始化表格数据

    this.tableConfig.searchParams.schoolId = this.userInfo.schools[0].idid;
    this.initTableData();
    this.getTeacherList(); // 获取用户列表
  },
  methods: {
    // 初始化表格数据
    async initTableData(val){
      this.loading = true;
      val?this.tableConfig.searchParams.collegeId = val:'';
      let res = await this.$api.GetWorkflowTemplates(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data
      this.tableConfig.total = res.data.length;
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    getTeacherList(){ // 获取用户列表
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000}).then(res=>{
        this.userList = res.data.items.filter(v=> v.enabled);
      })
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addEvent(){
      this.dialogVisible = true;
      this.type = 'add';
      this.form = { // 新增表单
        template: {
          id: 0,
          name: '',
          description: '',
          createBy:0
        },
        nodes: [
          
        ]
      };
    },
    async handleSubmit(){
     this.submitForm();
    },
    // 修改保存方法，先进行表单校验
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.save();
        } else {
          console.log('表单校验失败');
          return false;
        }
      });
    },
    // 添加节点
    handleAddNode() {
      const newNode= {
        id: 0,
        templateId: 0,
        nodeName: "",
        nodeType: WFNodeType.APPROVAL,
        approverType: WFApproverType.USER,
        approverValue: "",
        signType: WFSignType.AND,
        nodeOrder: this.form.nodes.length + 1,
        createTime: new Date().toISOString(),
      };
      this.form.nodes.push(newNode);
    },

    // 删除节点
    handleRemoveNode(index){
      this.form.nodes.splice(index, 1);
      // 重新排序
      this.form.nodes.forEach((node, idx) => {
        node.nodeOrder = idx + 1;
      });
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.template.id){ // 编辑
            this.$api.UpdateWorkflowTemplate(this.form).then(res=>{
              if(res.errCode == 0){ // 编辑成功，需要更新账号信息
                this.$message.success("编辑成功")
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateWorkflowTemplate(this.form).then(res=>{
            if(res.errCode == 0){ // 编辑成功，需要更新账号信息
              this.$message.success("新增成功")
              this.dialogVisible = false;
              this.initTableData()
              this.resetForm(); // 重置表单
            }
          })
        } else {
          console.log('表单校验失败');
          return false;
        }
      });
     
    },
    // 新增重置表单方法
    resetForm() {
      this.form = {
        template: {
          id: '',
          name: '',
          description: ''
        },
        nodes: [
          {
            nodeName: '',
            nodeType: 'APPROVAL',
            approverType: 'USER',
            approverValue: '',
            signType: 'AND'
          }
        ]
      }
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    // 工作流详情
    GetWorkflowTemplate(id){
      this.$api.GetWorkflowTemplate({templateId:id}).then(res=>{
        console.log("工作流详情",res)
      })
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '查看详情':
          this.dialogVisible = true;
          this.type = 'detail';
          // this.$api.GetWorkflowTemplate({templateId:row.template.id}).then(res=>{
          //   console.log("工作流详情",res)
          // })
          this.form = JSON.parse(JSON.stringify({
            template: {
              id: row.template.id,
              name: row.template.name,
              description: row.template.description
            },
            nodes: row.nodes.map(node => ({
              ...node,
              nodeOrder: node.nodeOrder || 1
            }))
          }));
          // this.GetWorkflowTemplate(row.template.id)
          break;
        case '设置业务':
          this.flowDialogVisible = true;
          this.templateId = row.template.id;
          break;
        case '编辑':
          this.dialogVisible = true;
          this.type = 'edit';
          this.form = JSON.parse(JSON.stringify({
            template: {
              id: row.template.id,
              name: row.template.name,
              description: row.template.description
            },
            nodes: row.nodes.map(node => ({
              ...node,
              nodeOrder: node.nodeOrder || 1
            }))
          }));
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该模板会被删除, 是否继续?',
      })
      let params = {
        templateId:row.template.id,
        schoolId:this.userInfo.schools[0].id,
      }
      this.$api.DeleteWFTemplate(params).then(res=>{
        this.initTableData()
      })
    },
    // 模板绑定业务
    handleSetFlow(){
      let params = this.businessId.map(v=>{
        return {
          templateId: this.templateId,
          businessType: v
        }
      })
      this.$api.BizTemplateBindSet(params).then(res=>{
          this.flowDialogVisible = false;
          this.$message.success('绑定成功')
          this.initTableData()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.work-flow-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}

::v-deep .add-flow-dialog{
  .el-select{
    width: 100%;
  }
  .el-form-item__label{
    color: #333;
  }
}
.nodes-container{
  .nodes-header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    h3{
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 15px;
      color: #333333;
    }
  }
}
</style>