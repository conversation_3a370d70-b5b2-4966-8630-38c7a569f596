<template>
    <div class="self-work-flow-page">
        <div class="top">
            <span class="back-icon">
              <i class="iconfont icon-fanhui"></i> 
            </span>
            <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/school/workBranch' }">工作台</el-breadcrumb-item>
                <el-breadcrumb-item>我的待办</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="main-content">
            <el-tabs v-model="activeName" @tab-click="handleClick()">
                <el-tab-pane :label="`待审批(${pendingCount})`" name="first">
                    <div class="approval-item" v-for="item in paddingList" :key="item.id">
                        <div class="title">
                            <p class="book-name">《{{extractTextBetweenBrackets(item.message,1)}}》</p>
                            <span class="time">{{formatISOString(item.instance.createTime)}}</span>
                        </div>
                        <div class="info-content">
                            <div class="info-left">
                                <div class="info-item">
                                    <p class="info-label">ID</p>
                                    <p class="info-value">{{item.instance.bizId||'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">所属大类</p>
                                    <p class="info-value">{{item.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).category:'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">发起人</p>
                                    <p class="info-value">{{ item.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).submitName:'-'}}</p>
                                </div>
                            </div>
                            <div class="operate-right">
                                <el-button class="btn1" @click="getApproval(item.instance.id)" type="text">审批流</el-button>
                                <el-button class="btn1" @click="passFlow(item)" type="text">通过</el-button>
                                <el-button class="btn2" @click="rejectFlow(item)" type="text">驳回</el-button>
                            </div>
                        </div>
                    </div>
                    <empty style="margin-top:50px;" v-if="pendingCount==0" msg="暂无数据" size="middle" />
                </el-tab-pane>
                <el-tab-pane :label="`我已审批(${approvedCount})`" name="second">
                    <div class="approval-item" v-for="item in approvedList" :key="item.id">
                        <div class="title">
                            <p class="book-name">《{{extractTextBetweenBrackets(item.instance.description,1)}}》<span :class="item.instance.approveStatus=='APPROVED'?'status-class':'status-class2'">{{WFApproveStatusLabel[item.instance.approveStatus]}}</span></p>
                            <span class="time">{{formatISOString(item.instance.updateTime)}}</span>
                        </div>
                        <div class="info-content">
                            <div class="info-left">
                                <div class="info-item">
                                    <p class="info-label">ID</p>
                                    <p class="info-value">{{item?.instance.id||'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">所属大类</p>
                                    <p class="info-value">{{item.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).category:'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">发起人</p>
                                    <p class="info-value">{{ item?.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).submitName:extractTextBetweenBrackets(item.instance.description,2)}}</p>
                                </div>
                            </div>
                            <div class="operate-right">
                                <el-button class="btn1" @click="getApproval(item.instance.id)" type="text">审批流</el-button>
                                <!-- <el-button class="btn1" type="text">通过</el-button>
                                <el-button class="btn2" type="text">驳回</el-button> -->
                            </div>
                        </div>
                    </div>
                    <empty style="margin-top:50px;" v-if="approvedCount==0" msg="暂无数据" size="middle" />
                </el-tab-pane>
                <el-tab-pane :label="`我发起的(${createCount})`" name="third" v-if="false">
                    <div class="approval-item" v-for="item in createList" :key="item.id">
                        <div class="title">
                            <p class="book-name">《{{extractTextBetweenBrackets(item.instance.description,1)}}》<span :class="item.instance.approveStatus=='APPROVED'?'status-class':item.instance.approveStatus=='PENDING'?'status-class3':'status-class2'">{{WFApproveStatusLabel[item.instance.approveStatus]}}</span></p>
                            <span class="time">{{formatISOString(item.instance.createTime)}}</span>
                        </div>
                        <div class="info-content">
                            <div class="info-left">
                                <div class="info-item">
                                    <p class="info-label">ID</p>
                                    <p class="info-value">{{item.instance.id||'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">所属大类</p>
                                    <p class="info-value">{{item.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).category:'-'}}</p>
                                </div>
                                <div class="info-item">
                                    <p class="info-label">发起人</p>
                                    <p class="info-value">{{ item.instance?.jsonInfo?JSON.parse(item.instance.jsonInfo).submitName:extractTextBetweenBrackets(item.instance.description,2)}}</p>
                                </div>
                            </div>
                            <div class="operate-right">
                                <el-button class="btn1" @click="getApproval(item.instance.id)" type="text">审批流</el-button>
                                <el-button class="btn2" v-if="item.instance.approveStatus=='PENDING'" @click="cancelFlow(item)" type="text">取消审批</el-button>
                                <!-- <el-button class="btn1" type="text">通过</el-button>
                                <el-button class="btn2" type="text">驳回</el-button> -->
                            </div>
                        </div>
                    </div>
                    <empty style="margin-top:50px;" v-if="createCount==0" msg="暂无数据" size="middle" />
                </el-tab-pane>
            </el-tabs>
        </div>

         <!-- 审批通过或取消 -->
         <el-dialog :visible.sync="passDialog" custom-class="pass-dialog" title="提示">
            <p>确定要{{approveAction=='CANCEL'?'取消':'通过'}}该申请吗？</p>
            <div style="text-align: right; margin-bottom: 0; margin-top: 40px">
                <el-button style="width: 80px" type="default" @click="passDialog = false"
                >取消</el-button >
                <el-button style="width: 80px" type="primary" @click="handlePass">保存</el-button>
            </div>
        </el-dialog>
        <!-- 审批驳回 -->
        <el-dialog :visible.sync="rejectDialog" custom-class="reject-dialog" title="提示">
            <p style="margin-bottom: 10px">确定要驳回该申请吗？</p>
            <el-input v-model="rejectText" placeholder="请填写驳回原因"></el-input>
            <div style="text-align: right; margin-bottom: 0; margin-top: 40px">
                <el-button style="width: 80px" type="default" @click="rejectDialog = false"
                >取消</el-button>
                <el-button style="width: 80px" type="danger" @click="handleReject"
                >拒绝</el-button
                >
            </div>
        </el-dialog>


        <el-dialog
            title="审批流信息"
            v-if="flowVisible"
            :visible.sync="flowVisible"
            custom-class="flowing-dialog"
            width="700px">
            <!-- <h4 class="flow-title">{{approvalObj.instance.description}}</h4> -->
            <el-descriptions title="基础信息" :column="2" border size="middle" >
                <el-descriptions-item label="工作流ID">{{approvalObj.instance.id}}</el-descriptions-item>
                <el-descriptions-item label="审批状态"> <el-tag size="small">{{WFApproveStatusLabel[approvalObj.instance.approveStatus]}}</el-tag></el-descriptions-item>
                <el-descriptions-item label="提交时间">{{approvalObj.instance.createTime}}</el-descriptions-item>
                <el-descriptions-item label="描述">
                    {{approvalObj.instance.description}}
                </el-descriptions-item>
            </el-descriptions>
            <div style="margin-top:20px;">
                <el-descriptions title="审批节点" :column="2"></el-descriptions>
                <el-steps direction="vertical" :active="-1">
                    <el-step v-for="item in approvalObj.nodes" :key="item.id" :title="item.nodeName" icon="iconfont icon-shenpi1">
                        <template #description>
                            <div class="custom-desc">
                              <el-tag>{{WFApproveStatusLabel[item.status]}}</el-tag>
                              <p class="status">审批人类型:{{WFApproverTypeLabel[item.approverType]}}</p>
                              <p class="status">审批结果:{{item.comment}}</p>
                              <!-- <span class="status">审批人:{{item.comment}}</span> -->
                              <p class="detail">类型:{{item.signType=='AND'?'会签':'或签'}}</p>
                              <p class="detail">{{formatISOString(item.createTime)}}</p>
                            </div>
                          </template>
                    </el-step>
                  </el-steps>
            </div>
            <div style="margin-top:20px;">
                <el-descriptions title="审批记录" :column="2"></el-descriptions>
                <el-table
                    :data="approvalObj.records||[]"
                    border
                    style="width: 100%">
                    <el-table-column
                    prop="approverUserName"
                    label="审批人"
                    width="180">
                    </el-table-column>
                    <el-table-column
                    prop="comment"
                    label="审批理由"
                    width="180">
                    </el-table-column>
                    <el-table-column
                    prop="action"
                    label="审批动作">
                        <template slot-scope="scope">
                            <el-tag>{{WFApproveStatusLabel[scope.row.action]}}</el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { WFApproveStatusLabel,WFApproverTypeLabel } from "@/views/schoolPage/workflow/flowType.js"
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数 
export default {
    data(){
        return {
            WFApproveStatusLabel,
            WFApproverTypeLabel,
            list:[],
            activeName: 'first',
            flowVisible: false,
            pendingCount: 0, // 待审批
            approvedCount: 0, //已通过
            createCount: 0, // 我发起的
            paddingList:[], //
            approvedList:[],
            createList:[],
            approvalObj:{},//审批流程
            currentFlow:{},//当前审批
            approveAction: 'APPROVE',// 审批动作
            passDialog: false, // 审批通过
            rejectDialog: false, // 审批驳回
            rejectText: '', // 驳回原因

        }
    },
    computed:{
        ...mapGetters([
            'userInfo'
        ])
    },
    watch:{
        '$route.query.type':{
            handler(val){
                if(val==1){
                    this.activeName = 'first'
                }else if(val==2){
                    this.activeName ='second'
                }
                this.GetTodoList()
                this.GetWorkflowInstancesApprovedByMe()
            },
            immediate:true
        },
    },
    components:{
        empty:()=>import("@/components/base/empty.vue"),
    },
    mounted(){
    },
    methods:{
        formatISOString,
        passFlow(item){
            this.currentFlow = item;
            this.approveAction = 'APPROVE';
            this.passDialog = true;
        },
        // 审批驳回
        rejectFlow(item){
            this.currentFlow = item;
            this.approveAction = 'REJECT';
            this.rejectDialog = true;
        },
        // 取消审批
        cancelFlow(item){
            this.currentFlow = item;
            this.approveAction = 'CANCEL';
            this.passDialog = true;
        },
        // 审批操作
        handleApprove(query,msg){
            console.log(this.currentFlow)
            let params = {
                "instanceId": this.currentFlow.instanceId,
                "instanceNodeId": this.currentFlow.instanceNodeId,
                "toDoTaskId": this.currentFlow.id,
                ...query,
                // "action": "CANCEL",
                // "comment": `${this.userInfo.user.name} 已取消审批`
            }
            this.$api.WorkFlowApprove(params).then(res => {
                if(res.errCode==0){
                    this.$message.success(msg)
                    this.passDialog = false;
                    this.rejectDialog = false;
                    this.GetTodoList()
                    this.GetWorkflowInstancesApprovedByMe()
                }
            }) 
        },
        // 取消审批
        handleCancel(){
            // instanceId
            let params = {
                instanceId: this.currentFlow.instance.id,
            }
            this.$api.WithdrawWorkflow(params).then(res => {
                if(res.errCode==0){
                    this.$message.success("取消审批")
                    this.passDialog = false;
                    this.GetTodoList()
                    this.GetWorkflowInstancesApprovedByMe()
                }
            }).catch(err => {
                console.log(err)
                this.passDialog = false;
            })
        },
        // 通过审批
        handlePass(){
            if(this.approveAction =='CANCEL'){
                this.handleCancel()
                return
            }
            this.handleApprove({action: this.approveAction,comment: `${this.userInfo.user.name}已同意`},"通过成功")
        },
        // 驳回审批
        handleReject(){
            this.handleApprove({action: this.approveAction,comment: `${this.rejectText}`},"驳回成功")
        },
        getApproval(id){
            this.$api.GetWorkflowInstanceByInstanceId({instanceId:id}).then(res => {
                this.flowVisible=true;
                console.log(res)
                this.approvalObj = res.data;
                // 调用合并方法
                this.approvalObj.nodes = this.mergeNodesAndRecords(res.data.nodes, res.data.records);
            })
        },
        handleClick(tab, event) {
            if(this.activeName=='first'){
                this.GetTodoList()
                
            }else if(this.activeName=='second'){
                this.GetWorkflowInstancesApprovedByMe()
            }
        },
        // 匹配教材
        extractTextBetweenBrackets(str,type) {
            if(type==1){
                const regex = /教材【(.*?)】/;
                const match = str.match(regex);
                return match && match[1]? match[1] : null; 
            }else if(type==2){
                const match = str.split(' ,');
                return match && match[0]? match[0] : null;
            }
        },
        // 获取待办数量
        GetTodoList() {
            this.$api.GetTodoList({pageSize:1000,pageIndex:1,wFApproveStatus:'PENDING'}).then(res => {
                this.pendingCount = res.data.total;
                this.paddingList = res.data.items;
            })
        },
        // 获取我审批的数量
        GetWorkflowInstancesApprovedByMe(){
            this.$api.GetWorkflowInstancesApprovedByMe({pageSize:1000,pageIndex:1}).then(res => {
                this.approvedCount = res.data.total;
                this.approvedList = res.data.items;
            })
        },
        // 获取我发起的数量
        // GetWorkflowInstanceOfMine(){
        //     this.$api.GetWorkflowInstanceOfMine({pageSize:1000,pageIndex:1}).then(res => {
        //         this.createCount = res.data.total;
        //         this.createList = res.data.items;
        //     })
        // },
         // 合并 nodes 和 records 数据的方法
         mergeNodesAndRecords(nodes, records) {
            return nodes.map(node => {
                const matchedRecord = records.find(record => record.instanceNodeId === node.id);
                if (matchedRecord) {
                    return {
                        ...node,
                        ...matchedRecord,
                        // 移除不需要的属性
                        instanceId: undefined,
                        toDoTaskId: undefined
                    };
                }
                return node;
            });
        }
    }
}
</script>
<style lang="scss">
        .self-work-flow-page{
        height: 100%;
        width: 100%;
        background-color: #F5F6F8;

        .top{
            height: 50px;
            background: #FFFFFF;
            display: flex;
            padding: 0 30px;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 10px;
            .back-icon{
                width: 20px;
                height: 20px;
                background: #BBBBBB;
                border-radius: 4px;
                color: #fff;
                line-height: 20px;
                text-align: center;
                display: inline-block;
                margin-right: 20px;
                .iconfont{
                    font-size: 12px;
                    line-height: 20px;
                }
           }
           ::v-deep .el-breadcrumb__inner a, 
           ::v-deep.el-breadcrumb__inner.is-link{
                color: #bbb;
           }
           
           ::v-deep .el-breadcrumb__item:last-child .el-breadcrumb__inner{
                color: #0070FC;
           }
        }

        .main-content{
            width: 900px;
            margin: 10px auto;
            box-sizing: border-box;
            padding: 0 20px;
            height:calc(100% - 60px);
            background:#fff;
            overflow-y: auto;
            .approval-item{
                width: 100%;
                background-color: #fff;
                border-radius: 4px;
                border: 1px solid #E7E7E7;
                padding-bottom: 10px;
                margin-bottom: 20px;
                .title{
                    height: 40px;
                    background: #FBFBFB;
                    border-radius: 4px 4px 0px 0px;
                    border: 1px solid #F2F3F5;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 10px;
                    .book-name{
                        ont-family: PingFangSC, PingFang SC;
                        font-weight: 500;
                        font-size: 15px;
                        color: #333333;
                        .status-class{
                            display: inline-block;
                            width: 50px;
                            height: 20px;
                            background: #E5FCF5;
                            border-radius: 4px;
                            font-size:12px;
                            text-align: center;
                            line-height: 20px;
                            margin-left: 10px;
                            color: #07C392;
                        }
                        .status-class2{
                            display: inline-block;
                            width: 50px;
                            height: 20px;
                            background: #FBEEEE;
                            border-radius: 4px;
                            font-size:12px;
                            text-align: center;
                            line-height: 20px;
                            margin-left: 10px;
                            color: #F11B1B;
                        }
                        .status-class3{
                            display: inline-block;
                            width: 50px;
                            height: 20px;
                            background: #eee;
                            border-radius: 4px;
                            font-size:12px;
                            text-align: center;
                            line-height: 20px;
                            margin-left: 10px;
                            color: #666;
                        }
                    }
                   .time{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #666666;
                   }
                }

               .info-content{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding:5px;
                    .info-left{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0 10px;
                        width: 60%;
                        .info-item{
                            
                            .info-label{
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 14px;
                                line-height: 30px;
                                color: #999999;
                            }
                           .info-value{
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 30px;
                            color: #333333;
                           }
                        }

                    }

                   .operate-right{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                        color: #333333;
                        padding-right:40px;
                    .btn1{
                        color: #333;
                        &:hover{
                            color: #0070FC;
                        }
                    }
                    .btn2{
                        color: #333;
                        &:hover{
                            color: #F11B1B;
                        }
                    }
                   }
               }
            }
        }
    }

    .flowing-dialog{
        background: #FFFFFF;
        border-radius: 4px;
        .el-dialog__body{
            padding-top: 10px;
            min-height: 420px;
        }
        .flow-title{
            margin-top: 0;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
        }
        .el-step.is-vertical .el-step__line{
            left: 21px;
        }
        .el-step__title{
            font-size: 14px;
            color: #333;
        }
        .el-step__line{
            background-color: #0070FC;
            opacity: 0.4;
        }
        .el-step.is-vertical .el-step__main{
            padding-left: 40px;
            height: 140px;
        }
        .el-step__icon{
            width: 44px!important;
            height: 44px;
            display: inline-block;
            border-radius: 22px;
            background: #0070FC;
            color: #fff;
            font-size: 20px;
            text-align: center;
            line-height: 44px;
        }
    }
    .reject-dialog{
        width:500px;
        height:240px;
    }
    .pass-dialog{
        width:500px;
        height:190px;
    }
    .el-step__description{
        padding-right: 0;
    }
    // .el-step{
    //     margin-bottom: 30px;
    // }
    .custom-desc{
        position: relative;
        width: 100%;
       
        .el-tag{
            position: absolute;
            right: 0;
            top: 0;
        }
    }
</style>