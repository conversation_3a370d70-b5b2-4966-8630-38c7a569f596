<template>
  <div class="teacher-manage-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @blur="initTableData()" clearable placeholder="请搜索教师姓名/联系电话" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select @change="initTableData()" filterable clearable style="width:220px;margin-right:10px;"  v-model="tableConfig.searchParams.collegeId" placeholder="请选择院校">
        <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addEvent" />
      <exportButton @exportEvent="exportEvent" />
      <importButton @importEvent="importEvent" type="Teacher"/>
      <reflashButton @reflashEvent="reflashEvent" />
    </section>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :height="height" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <!-- 新增班级教师 -->
    <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" :title="!form.id?'新增教师':'编辑教师'">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-form-item label="教师姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入教师姓名" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="头像"  prop="coverImage">
          <el-upload
            class="avatar-uploader"
            :action="actionUrl"
            :show-file-list="false"
            :data="uploadData"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="所属类型" prop="userType">
          <el-select style="width:100%;" placeholder="请输入所属类型" v-model="form.userType">
            <el-option value="Teacher" label="教师"></el-option>
            <!-- <el-option value="Teacher" label="教务人员"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="所属学院" prop="collegeId">
          <el-select style="width:100%;" clearable filterable v-model="form.collegeId" placeholder="请选择所属学院">
            <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="任课班级" prop="teachClassIds">
          <el-select style="width:100%;" multiple clearable filterable v-model="form.teachClassIds" placeholder="请选择任课班级">
            <el-option v-for="item in classList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item> -->
        <!-- prop="teachCourseIds" -->
        <el-form-item label="任教课程" >
          <el-select style="width:100%;" multiple  clearable filterable v-model="form.teachCourseIds"  placeholder="请选择任教课程">
            <el-option v-for="item in courseList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 

    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
import token from "@/utils/token.js";
import { mapGetters } from "vuex";
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
import { exportExcel } from '@/utils/common.js' // 导入导出excel
export default {
  name: "schoolPage",
  data() {
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          // },{
          //   label: "ID",
          //   prop: "id",
          },
          {
            label: "教师姓名",
            prop: "name",
          },
          {
            label: "所属类型",
            prop: "userType",
            formatter: (row, column) => {
              return row.userType === 'Teacher' ? '任课教师' : '教务人员'
            }
          },
          {
            label: "联系方式",
            prop: "phone",
          },
          {
            label: "任课班级",
            prop: "classes",
            formatter: (row, column) => {
              return row.classes.map(item => item.name).join(',') 
            }
          },
          {
            label: "任教课程",
            prop: "courses",
            formatter: (row, column) => {
              return row.courses.length
            }
          },
          {
            label: "关联教材",
            prop: "textbooks",
            formatter: (row, column) => {
              return row.textbooks.map(item => item.name).join(',') 
            }
          },
          {
            label: "所属学院",
            prop: "collegeName",
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter: (row, column) => {
              return formatISOString(row.createTime) 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord: "", // 搜索关键字
          userType:'Teacher', // 教师类型
          collegeId: "" // 所属学院ID
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
        queryConfig: {},
      },
      firstLoad: true,
      searchParams: {
        pageIndex: 1,
        pageSize: 10
      },
      loading: false,
      actionBtns: [
        // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 150,
      collegeList:[],// 学院列表
      classList:[],// 班级列表
      courseList:[],// 课程列表
      dialogVisible:false,// 弹窗是否显示
      form:{ // 新增表单
        id:'', // 班级ID
        name:'', // 名称
        headerImage:'', // 头像
        phone:'', // 联系方式
        userType:'Teacher', // 教师类型
        collegeId:'', // 所属学院ID
        teachClassIds:[], // 任课班级ID
        teachCourseIds:[], // 任教课程ID
      },
      actionUrl:window.FILEIP, // 上传图片地址
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      imageUrl:'',
      rules: { // 表单验证规则
        name: [
          { required: true, message: '请输入教师姓名', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('请输入姓名'));
              } else {
                callback();
              }
            } }
        ],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        userType: [
          { required: true, message: '请选择教师类型', trigger: 'change' }
        ],
        collegeId: [
          { required: true, message: '请选择所属学院', trigger: 'change' }
        ],
        teachClassIds: [
          { required: true, message: '请选择任课班级', trigger: 'change' }
        ],
        // teachCourseIds: [
        //   { required: true, message: '请选择任教课程', trigger: 'change' }
        // ]
      },
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
      height:'660px',
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/teacherManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // 初始化表格数据
    this.getClassList();// 获取班级列表
    this.getCollegeList(); // 获取学院列表
    this.getCourseList(); // 获取课程列表
    // this.initTableData();

    this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
  },
  computed: {
    ...mapGetters(["userInfo"]) // 从vuex中获取用户信息
  },
  methods: {
    // 初始化表格数据
    async initTableData(val){
      // val?this.tableConfig.searchParams.collegeId = val:'';
      this.loading = true;
      if(val){
        this.tableConfig.searchParams.collegeId = val;
      }
      let res = await this.$api.GetTeacherList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
      })
    },
    getCourseList(){ // 获取课程列表
      this.$api.GetCourseList({pageIndex:1,pageSize:100}).then(res=>{
        this.courseList = res.data.items;
      })
    },
    getClassList(){ // 获取班级列表
      this.$api.GetClassList({pageIndex:1,pageSize:100}).then(res=>{
        this.classList = res.data.items;
      })
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    // 导出
    exportEvent(){     
      let params={
        type:"Teacher",
      }
      exportExcel('/Excel/DownloadTemplate',params,'教师模板')
    },
    // 导入
    importEvent(file){
     this.initTableData()
    },
    addEvent(){
      this.dialogVisible = true;
      this.form = { // 新增表单
        id:'', // 班级ID
        name:'', // 名称
        headerImage:'', // 头像
        phone:'', // 联系方式
        userType:'', // 教师类型
        collegeId:'', // 所属学院ID
        teachClassIds:[], // 任课班级ID
        teachCourseIds:[], // 任教课程ID
      }
    },
    handleAvatarSuccess(file) { // 预览图片
      console.log(file);
      this.imageUrl = file.data; // 图片地址
      this.form.headerImage = file.data; // 图片地址
    },
    save(){
      this.$refs.form.validate((valid) => { // 表单验证
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateTeacher(this.form).then(res=>{
              if(res.errCode==0){
                this.$message({message:'更新成功',type:'success'})
                console.log("教师详情",res)
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateTeacher(this.form).then(res=>{
            if(res.errCode==0){
              this.$message({message:'新增成功',type:'success'})
              this.dialogVisible = false;
              this.initTableData()
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
     
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
      this.tableConfig.searchParams = params;
      this.initTableData()
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '详情':
          this.detailDialogVisible = true;
          console.log("教师详情",row)
          let courses = []
          if(row.courses.length){ // 如果没有课程，就获取课程列表
            let courseIds = row.courses.map(item => item.id); // 获取课程id
            let res = await this.$api.GetCourseList({pageIndex:1,pageSize:100,courseIds:courseIds})
            courses = res.data.items;
          }

          this.detailInfo ={
            ...row,
            children:[
              {
                title:'任课班级',
                list:row.classes,
                name:'first',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'任课班级',prop:'name'},
                  {
                    label:'学生数量',
                    prop:'studentCount',
                  },
                ]
              },
              {
                title:'任教课程',
                list:courses,
                name:'secened',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'课程名称',prop:'name'},
                  {label:'所属专业大类',prop:'majorCategory'},
                ]
              }
            ]
          }; // 假设详情信息保存在 row 中，根据实际情况修改
          break;
        case '编辑':
          this.dialogVisible = true;
          this.form = { // 编辑表单
            ...row,
            teachClassIds:row.classes.map(item => item.id),// 任课班级ID
            teachCourseIds:row.courses.map(item => item.id),// 任教课程ID
          }
          this.imageUrl = row.coverImage; // 设置封面图片预览
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该教师会被删除, 是否继续?',
      })
      this.$api.DeleteTeacher({id:row.id}).then(res=>{
        this.initTableData()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.teacher-manage-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}
</style>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>