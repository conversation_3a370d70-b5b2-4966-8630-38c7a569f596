<template>
  <div class="user-manage-page">
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @blur="initTableData" clearable placeholder="请搜索名字" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select @change="initTableData" style="width:220px;margin-right:10px;" clearable filterable v-model="tableConfig.searchParams.collegeId" placeholder="请选择类型">
        <el-option v-for="item in userType" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <addButton @addEvent="addEvent" />
      <exportButton />
      <importButton />
    </section>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :height="height" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <!-- 新增账号 -->
    <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" className="add-account-dialog" :title="!form.id?'新增':'编辑'">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="所属角色" prop="userType">
          <el-select v-model="form.userType" placeholder="请输入所属角色">
            <el-option v-for="item in userType" :value="item.value" :key="item.value" :label="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工号" prop="userType">
          <el-input v-model="form.workNumber" placeholder="请输入工号"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
  </div>
</template>

<script>
import { UserType } from "@/constants/common.js"; // 导入常量
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数 
export default {
  name: "userManage",
  data() {
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      userType: UserType, // 用户类型
      height:'560px',
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          {
            label: "工号",
            prop: "workNumber",
          },
          {
            label: "姓名",
            prop: "name",
          },
          {
            label: "联系方式",
            prop: "phone",
          },
          {
            label: "角色",
            prop: "userType",
            formatter: (row, column, cellValue, index) => {
              return UserType.find(item => item.value === cellValue)?.label || '';
            }
          },
          {
            label: "用户状态",
            prop: "enabled",
            width: 100,
            formatter: (row, column, cellValue, index) => {
              return cellValue ? "启用" : "禁用";
            }
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              return formatISOString(cellValue); 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 12,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord: "", // 搜索关键字
          userType: '' // 用户类型
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, sizes, prev, pager, next, jumper',
        queryConfig: {},
      },
      firstLoad: true,
      searchParams: {
        pageIndex: 1,
        pageSize: 12
      },
      loading: false,
      actionBtns: [
        // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '重置密码',class:'default-btn' },
        { name: '停用',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 280,
      collegeList:[],// 学院列表
      classList:[],// 班级列表
      courseList:[],// 课程列表
      dialogVisible:false,// 弹窗是否显示
      form:{ // 新增表单
        id:'', // ID
        name:'', // 名称
        phone:'', // 联系方式
        userType:'', // 账号类型
        workNumber:'', // 工号
      },
      // 添加校验规则
      rules: {
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' } 
        ],
        userType: [
          { required: true, message: '请选择角色', trigger: 'change' } 
        ]
      }
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // 初始化表格数据
    this.initTableData();
    this.getCollegeList(); // 获取学院列表
  },
  methods: {
    // 初始化表格数据
    async initTableData(val){
      val?this.tableConfig.searchParams.collegeId = val:'';
      let res = await this.$api.GetTeacherList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      console.log("院校列表",res.data.items)
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
      })
    },
    addEvent(){
      this.dialogVisible = true;
    },
    // 修改保存方法，先进行表单校验
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.save();
        } else {
          console.log('表单校验失败');
          return false;
        }
      });
    },
    save(){
      // 定义手机号码正则表达式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.form.phone)) {
        this.$message.error('请输入有效的手机号码');
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateTeacher(this.form).then(res=>{
              console.log("账号详情",res)
              if(res.errCode == 0){
                this.$message.success("更新成功");
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateTeacher(this.form).then(res=>{
            if(res.errCode == 0){
                this.$message.success('新增成功');
                this.dialogVisible = false;
                this.initTableData()
                this.resetForm(); // 重置表单
              }
          })
        } else {
          console.log('表单校验失败');
          return false;
        }
      });
     
    },
    // 新增重置表单方法
    resetForm() {
      this.form = { 
        id: '', 
        name: '', 
        phone: '', 
        userType: '', 
        workNumber: '123123123', 
      };
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '详情':
          break;
        case '编辑':
          this.dialogVisible = true;
          this.form = { // 编辑表单
            ...row
          }
          // this.$api.UpdateCollege({id:row.id,...this.form}).then(res=>{
          //   this.dialogVisible = false;
          //   this.initTableData()
          //   console.log("院校详情",res)
          // })
          break;
        case '重置密码':
          // 这里可以添加重置密码逻辑
          break;
        case '停用':
          // 这里可以添加停用逻辑
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该账号会被删除, 是否继续?',
      })
      this.$api.DeleteTeacher({id:row.id}).then(res=>{
        this.initTableData()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.user-manage-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}

::v-deep .add-account-dialog{
  .el-select{
    width: 100%;
  }
}
</style>