<template>
  <div class="textbook-manage-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" @blur="initTableData" clearable v-model="tableConfig.searchParams.searchWord" placeholder="请搜索教材名称" suffix-icon="iconfont icon-sousuo"></el-input>
     
      <el-select style="width:220px;margin-right:10px;" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.bigMajorId" placeholder="请选择专业大类">
         <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addTextbook" />
      <reflashButton @reflashEvent="reflashEvent" />
      <!-- <el-button class="btn through-btn" @click="through">通过</el-button>
      <el-button class="btn reject-btn" @click="reject">驳回</el-button>
      <el-button class="btn put-btn" @click="put">上架</el-button>
      <el-button class="btn off-btn" @click="off">下架</el-button> -->
    </section>
    <div class="status-list">
      <p :class="['status-item',activeType==item.value?'active':'']" @click="changeType(item)" v-for="item in statusList" :key="item.id" >{{item.label}}</p>
    </div>
    <div class="book-status">
      <p :class="['status-item',activeBookType==item.value?'active':'']" @click="changeBookType(item)" v-for="item in bookStatus" :key="item.id" >{{item.label}}</p>
    </div>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :height="height"
      :pagination="tableConfig.pagination" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <baseDialog :noFooter="true" v-if="dialogVisible" :showToScreen="false" :visible.sync="dialogVisible" width="720px" title="创建教材">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-form-item label="教材状态" prop="isPublishedTextbook">
          <el-radio-group v-model="form.isPublishedTextbook">
            <el-radio :label="true">已出版</el-radio>
            <el-radio :label="false">未出版</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材名称" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col> 
          <el-col :span="12">
            <el-form-item label="所属专业大类"  prop="bigMajorId">
              <el-select style="width:100%;" v-model="form.bigMajorId">
                <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材封面"  prop="coverImage">
              <el-upload
                class="cover-uploader"
                :action="actionUrl"
                :show-file-list="false"
                :data="uploadData"
                :headers="uploadHeaders"
                :on-success="handleAvatarSuccess">
                <img v-if="imageUrl" :src="imageUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <span class="upload-tip">建议图片尺寸为 120x160px，支持格式：jpg、png</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属学院"  prop="collegeId">
              <el-select style="width:100%;" v-model="form.collegeId" @change="selectCollege">
                <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属专业"  prop="majorId">
              <el-select style="width:100%;" v-model="form.majorId">
                <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
       
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="教材主编" prop="chiefEditorUserIds">
              <el-select @change="selectEditor" multiple style="width:100%;" filterable clearable placeholder="请选择授课老师" v-model="form.chiefEditorUserIds">
                <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col> 
          <el-col :span="12">
              <el-form-item label="教材协作者" prop="helperCreatorUserIds">
                  <el-select @change="selectCreator" :multiple-limit="10" multiple filterable clearable style="width:100%;" placeholder="请选择授课老师" v-model="form.helperCreatorUserIds">
                    <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" v-show="form.isPublishedTextbook">
          <el-col :span="12">
            <el-form-item label="书号" prop="publishedBookNumber">
              <el-input v-model="form.publishedBookNumber"></el-input>
            </el-form-item>
          </el-col> 
          <el-col :span="12">
              <el-form-item label="出版社" prop="publishingHouse">
                  <el-input v-model="form.publishingHouse"></el-input>
              </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="教材简介"  prop="description">
          <el-input type="textarea" v-model="form.description"></el-input>
        </el-form-item>
        <el-form-item label="备注"  prop="remark">
          <el-input v-model="form.remark"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 


    <el-dialog :visible.sync="passDialog" custom-class="pass-dialog" title="提示">
      <p>确定要通过该申请吗？</p>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button style="width:80px;" type="default" @click="passDialog = false;">取消</el-button>
        <el-button style="width:80px;" type="primary" @click="handlePass" v-preventReClick>保存</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="isSetFlowDialog" custom-class="flow-dialog" title="提示">
      <el-select v-model="templateId">
        <el-option v-for="item in flowList" :key="item.template.id" :value="item.template.id" :label="item.template.name"></el-option>
      </el-select>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button style="width:80px;" type="default" @click="isSetFlowDialog = false;">取消</el-button>
        <el-button style="width:80px;" type="primary" @click="handleSetFlow" v-preventReClick>保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="rejectDialog" custom-class="reject-dialog" title="提示">
      <p style="margin-bottom:10px;">确定要驳回该申请吗？</p>
      <el-input v-model="rejectText" placeholder="请填写驳回原因"></el-input>
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button style="width:80px;" type="default" @click="rejectDialog = false;">取消</el-button>
        <el-button style="width:80px;" type="danger" @click="handleReject">拒绝</el-button>
      </div>
    </el-dialog>

    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
// import { categories } from "@/constants/majoeCategories.js"
import { mapGetters } from "vuex";
import token from "@/utils/token.js";


const ApproveStatus = {
  PENDING:'待审批',
  APPROVED:'通过',
  REJECTED:'驳回',
  CANCELLED:'取消'
}

const TextbookOnLineStatus = {
  OnLine:'已上架',
  OffLine:'未上架',
}
export default {
  name: "TextbookPage",
  data() {
    const validateBookNumber = (rule, value, callback) => {
      if (this.form.isPublishedTextbook) {
        if (!value || value.trim() === '') {
          callback(new Error('请输入书号'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };

    const validatePublishingHouse = (rule, value, callback) => {
      if (this.form.isPublishedTextbook) {
        if (!value || value.trim() === '') {
          callback(new Error('请输入出版社'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      activeType: 1, // 状态
      statusList: [
        // 未出版教材 出版教材
        { id: 1, label: "未出版教材",value:1 },
        { id: 2, label: "出版教材",value:2 },
      ],
      activeBookType:1, // 教材状态
      bookStatus:[
        // 待审核 驳回 通过 已下架 已上架
        { id: 0, label: "全部",value: '' },
        { id: 1, label: "待审核",value: 'PENDING' },
        { id: 2, label: "驳回",value: 'REJECTED' },
        { id: 3, label: "通过",value: 'APPROVED'},
        { id: 4, label: "已下架",value:'OffLine' },
        { id: 5, label: "已上架",value:'OnLine' },
      ],
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          // },{
          //   label: "ID",
          //   prop: "id",
          },
          {
            label: "教材名称",
            prop: "name",
          },
          {
            label: "教材封面",
            prop: "coverImage",
            formatter: (row, column, cellValue) => {
              return `<img src="${cellValue}" alt="封面" style="width: 50px; height: 50px;">`;
            },
            formatters: (row, column, cellValue) => {
              return `<img src="${cellValue}" alt="封面" style="width: 50px; height: 50px;">`;
            }
          },
          {
            label: "专业大类",
            prop: "bigMajorId",
          },
          {
            label: "主编",
            prop: "publishedChiefEditor",
          },
         
          {
            label: "已上架版本",
            prop: "currentOnlineVersion",
            formatter: (row, column, cellValue) => {
              return row.currentOnlineVersion?.version;
            }
          },
          {
            label: "申请版本",
            prop: "textbookOnLineStatus",
            formatter: (row, column, cellValue) => {
              return TextbookOnLineStatus[row.textbookOnLineStatus];
            }
          },
          {
            label: "申请时间",
            prop: "createTime",
          },
          {
            label: "审核状态",
            prop: "wfApproveStatus",
            formatter: (row, column, cellValue) => {
              return  ApproveStatus[row.wfApproveStatus]||'';
            }
          },
          {
            label: "历史版本",
            prop: "textbookOnLineStatus",
            formatter: (row, column, cellValue) => {
              return TextbookOnLineStatus[row.textbookOnLineStatus];
            }
          },
          {
            label: "默认模板",
            prop: "defaultWFTemplateId",
            align: 'center',
            width: 80,
            formatter: (row, column, cellValue) => {
              return row.defaultWFTemplateId ? '有' : '无';
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 10,
          bigMajorId: '', // 专业id
          isPublishedTextbook: '', // true 出版  false 未出版 教材状态 
          textbookOnLineStatus:'', // 教材上下架状态 OnLine 上架 OffLine 下架
          wFApproveStatus: '', // 教材审核状态 PENDING, APPROVED, REJECTED, CANCELLED 1 待审核 2 驳回 3 通过
          meAsChiefEditor:'', // 教材主编
          meAsHelperCreator:'', // 教材创作者
          searchWord:'', // 搜索关键字
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        paginationLayout: 'total, sizes, prev, pager, next, jumper',
        notShowSearch: false,
        selectable: true,
        queryConfig: {},
      },
      // 补全模板中使用的变量
      firstLoad: true,
      loading: false,
      actionBtns: [
      ],
      operateBtns: [
        { name: '通过',class:'default-btn' },
        { name: '驳回',class:'del-btn' },
        { name: '审批流设置',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '详情',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 240,
      dialogVisible: false, // 新增教材弹窗
      actionUrl:window.FILEIP, // 上传图片地址
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      form: { // 新增教材表单
        id: "", // id
        name: "", // 教材名称
        bigMajorId: "", // 所属专业大类
        bigMajorName: "", // 所属专业大类
        isPublishedTextbook: false, // 教材状态
        publishedBookNumber: "", // 书号
        publishedChiefEditor: "", // 教材主编
        publishedCreator: "", // 教材创作者
        publishingHouse: "", // 出版社
        coverImage: "", // 教材封面
        description: "", // 教材简介
        remark: "", // 备注
        chiefEditorUserIds:[], // 教材主编
        helperCreatorUserIds:[], // 教材创作者
      },
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      imageUrl:'',
      rules: { // 表单验证规则
        name: [
          { required: true, message: "请输入教材名称", trigger: "blur",validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('请输入教材名称'));
              } else {
                callback();
              }
            } }
        ],
        bigMajorId: [
          { required: true, message: "请选择所属专业大类", trigger: "change" } 
        ],
        publishedBookNumber: [
          { validator: validateBookNumber, trigger: "blur" } 
        ],
        publishedChiefEditor: [
          { required: true, message: "请输入教材主编", trigger: "blur" } 
        ],
        publishedCreator: [
          { required: true, message: "请输入教材创作者", trigger: "blur" }
        ],
        publishingHouse: [
          { validator: validatePublishingHouse, trigger: "blur" } 
        ],
        coverImage: [
          { required: true, message: "请上传教材封面", trigger: "blur" }
        ],
        collegeId: [
          { required: true, message: "请选择所属学院", trigger: "change" }
        ],
        majorId: [
          { required: true, message: "请选择所属专业", trigger: "change" }
        ],
      },
      majorList: [], // 专业列表
      collegeList:[], // 学院列表
      teacherList: [], // 教师列表
      gradeList: [], // 教材列表
      categories: [], // 专业大类

      passDialog:false,// 通过弹窗
      rejectText:'', // 驳回原因
      rejectDialog:false, // 驳回弹窗
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
      height:'560px',
      isSetFlowDialog:false, // 审批流设置弹窗

      bizId:0,// 教材id/ 业务id
      templateId:'',//审批模板id
      flowList:[], // 审批流列表
      currentRow:{},// 当前行数据

    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/textbookManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // this.initTableData(); // 初始化表格数据

    this.getCollegeList(); // 获取学院列表
    // this.getMajorList(); // 获取专业列表
    this.getTeacherList(); // 获取教师列表
    // this.getGradeList(); // 获取教材列表
    this.getBigMajorList(); // 获取专业大类

    this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
  },
  computed: {
    ...mapGetters(["userInfo"]) // 从vuex中获取用户信息
  },
  methods: {
    // 初始化表格数据
    async initTableData(){
      this.loading = true;
      let res = await this.$api.GetTextbookList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false;
      },300);
    },
    // 获取专业大类
    getBigMajorList(){ 
      this.$api.GetMajorTree({}).then(res=>{
        this.categories = res.data;
      })
    },
    getTeacherList(){ // 获取教师列表
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000}).then(res=>{
        this.teacherList = res.data.items.filter(v=> v.enabled);
      })
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
      })
    },
    // 切换院校
    selectCollege(val){
      this.getMajorList(val); // 获取专业列表

    },
    // 切换类型
    changeType(item){
      this.activeType = item.value;
      this.tableConfig.searchParams.isPublishedTextbook = item.value == 1 ? false : true; // true 出版  false 未出版
      this.tableConfig.searchParams.pageIndex = 1; // 重置页码
      this.initTableData() // 初始化表格数据
    },
    changeBookType(item){ // 切换教材状态

      this.activeBookType = item.value;
      if(item.id == 1|| item.id ==2||item.id ==3 ){ // 待审核  // 驳回  // 驳回
        this.tableConfig.searchParams.wFApproveStatus = item.value; // 教材审核状态 1 待审核 2 驳回 3 通过
        this.tableConfig.searchParams.textbookOnLineStatus = ''; // 教材上下架状态 1 上架 2 下架
      }else if(item.value == 4||item.value == 5){ // 已下架
        this.tableConfig.searchParams.wFApproveStatus = ''; // 教材审核状态 1 待审核 2 驳回 3 通过
        this.tableConfig.searchParams.textbookOnLineStatus = item.value; // 教材上下架状态 1 上架 2 下架
      }else {
        this.tableConfig.searchParams.wFApproveStatus = ''; // 教材审核状态 1 待审核 2 驳回 3 通过
        this.tableConfig.searchParams.textbookOnLineStatus = ''; // 教材上下架状态 1 上架 2 下架
      }
      // this.tableConfig.searchParams.wFApproveStatus = item.value; // 教材审核状态 1 待审核 2 驳回 3 通过
      this.tableConfig.searchParams.pageIndex = 1; // 重置页码
      this.initTableData() // 初始化表格数据
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addTextbook(){ // 新增课程
      this.dialogVisible = true; // 打开弹窗
      this.form = { // 重置表单
        id: "", // id
        name: "", // 教材名称
        bigMajorId: "", // 所属专业大类
        bigMajorName: "", // 所属专业大类
        isPublishedTextbook: false, // 教材状态
        publishedBookNumber: "", // 书号
        publishedChiefEditor: "", // 教材主编
        publishedCreator: "", // 教材创作者
        publishingHouse: "", // 出版社
        coverImage: "", // 教材封面
        description: "", // 教材简介
        remark: "", // 备注
        chiefEditorUserIds:[], // 教材主编
        helperCreatorUserIds:[], // 教材创作者
      };
      this.imageUrl = ''; // 图片地址
    },
    selectEditor(){ // 选择主编
      let targrt = this.form.chiefEditorUserIds.map(v=>{
        
        let target = null
        this.teacherList.forEach(item=>{
          if(item.id == v){
            target = item.name
          }
        })
        return target
      });
      console.log("targrt",targrt)
      this.form.publishedChiefEditor = targrt.join(',');
    },
    selectCreator(){ // 选择创作者  
      let targrt = this.form.helperCreatorUserIds.map(v=>{
        let target = null
        this.teacherList.forEach(item=>{
          if(item.id == v){
            target = item.name
          }
        })
        return target
      });
      this.form.publishedCreator = targrt.join(',');
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateTextbook(this.form).then(res=>{
              if(res.errCode == 0){ // 编辑成功
                this.$message({
                  message: '编辑成功',
                  type: 'success'
                });
                console.log("课程详情",res)
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateTextbook(this.form).then(res=>{
            if(res.errCode == 0){ // 新增成功
              this.$message({
                message: '新增成功',
                type:'success'
              });
              console.log("新增课程",res)
              this.dialogVisible = false;
              this.initTableData()
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    getMajorList(val){
      this.$api.GetMajorList({pageIndex:1,pageSize:100,collegeId:val}).then(res=>{
        this.majorList = res.data.items;
      })
    },
    handleAvatarSuccess(file) { // 预览图片
      console.log(file);
      this.imageUrl = file.data; // 图片地址
      this.form.coverImage = file.data; // 图片地址
    },
    through(){ // 审核通过
      this.passDialog = true;
    },
    reject(){ // 驳回
      this.rejectDialog = true;
    },
    put(){ // 上架
    },
    off(){ // 下架
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
      this.tableConfig.searchParams = params;
      this.initTableData()
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      if (item.name === '通过'||item.name === '驳回') {
        // return scope.row.wfInstanceId
        return false;
      }else{
        return true;
      }
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case "通过":
          this.through();
          break;
        case "驳回":
          this.reject();
          break;
        case "审批流设置":
          this.isSetFlowDialog = true;
          this.currentRow = row; // 保存当前行数据`
          this.templateId = row.defaultWFTemplateId||''; // 保存当前行数据`
          this.$api.GetWorkflowTemplates({pageIndex:1,pageSize:1000}).then(res=>{
            this.flowList = res.data;
          })
          break;
        case "详情":
          this.detailDialogVisible = true;
          this.detailInfo ={
            ...row,
            children:[
              {
                title:'历史版本',
                list:row.currentOnlineVersion||[],
                name:'first',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'版本号',prop:'version'},
                  {label:'版本描述',prop:'description'},
                ]
              }]
            }
          break;
        case "编辑":
          this.dialogVisible = true; // 打开弹窗
          // 给表单赋默认值
          console.log("row",row)
          this.form = {
            id: row.id,
            name: row.name,
            bigMajorId: row.bigMajorId,
            bigMajorName: row.bigMajorName,
            isPublishedTextbook: row.isPublishedTextbook,
            publishedBookNumber: row.publishedBookNumber,
            publishedChiefEditor: row.publishedChiefEditor,
            publishedCreator: row.publishedCreator,
            publishingHouse: row.publishingHouse,
            coverImage: row.coverImage,
            description: row.description,
            remark: row.remark,
            chiefEditorUserIds: row.chiefEditorUsers.map(v=>v.id) || [],
            helperCreatorUserIds: row.helperCreatorUsers.map(v=>v.id) || [],
            collegeId:row.collegeId,
            majorId:row.majorId,
          };
          if(row.collegeId){ // 编辑
            this.getMajorList(row.collegeId); // 获取专业列表
          }
          this.imageUrl = row.coverImage; // 设置封面图片预览
          break;
        case "删除":
          await this.$zdDialog({
            contImg: '',
            contTitle: '确定删除?',
            contDesc: '删除后数据将不可恢复，请谨慎操作！',
          });
          this.$api.DeleteTextbook({id:row.id}).then(res=>{
            console.log("删除教材",res);
            this.initTableData();
          });
          break;
      }
    },
    handlePass(){ // 通过
      this.passDialog = false;
    },
    handleReject(){ // 驳回
      this.rejectDialog = false;
    },
    handleSetFlow(){ // 审批流设置
      this.$api.SetDefaultWFTemplate({
        "bisunessId": this.currentRow.id,
        "wfTemplateId": this.templateId,
      }).then(res=>{
        if(res.errCode == 0){ // 设置成功
          this.$message({
            message: '设置成功',
            type:'success'
          });
          this.isSetFlowDialog = false; // 关闭弹窗
          this.initTableData(); // 重新获取数据
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.textbook-manage-page {
  padding: 20px;
  
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
    .btn{
      width: 80px;
      height: 38px;
      border-radius: 4px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      padding: 0;
      color: #333333;
      border-color: #E7E7E7;
    }
    .through-btn{
      margin-left: 10px;
      color: #07C392;
      border: 1px solid #07C392;
    }
   .reject-btn{}
   .put-btn{}
   .off-btn{}
  }

  .status-list{
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F2F3F5;
    height: 50px;
    line-height: 50px;
    .status-item{
      width: 90px;
      line-height: 46px;
      margin-right: 20px;
      font-size: 14px;
      text-align: center;
      cursor: pointer;
      color: #5C6075;
      &.active{
        color: #0070FC;
        font-weight: 500;
        border-bottom: 2px solid #0070FC;
      }
    }

  }
  .book-status{
    display: flex;
    align-items: center;
    height: 50px;
    margin-top: 10px;
   .status-item{
    width: 80px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    margin-right: 10px;
    background: #FFFFFF;
    border-radius: 15px;
    border: 1px solid #E7E7E7; 
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    cursor: pointer;
    &:hover{
      color: #0070FC;
      background: #F2F4FC;
      border-color: transparent;

    }
    &.active{
      color: #0070FC;
      background: #F2F4FC;
      border-color: transparent;
    }
   }
  }
}


</style>
<style lang="scss">

.pass-dialog{
  width:500px;
  height:190px;
}
.flow-dialog{
  width:500px;
  height:240px;
}
.reject-dialog{
  width:500px;
  height:240px;
}
.cover-uploade .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .cover-uploade .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }
  .cover-uploade .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }
</style>