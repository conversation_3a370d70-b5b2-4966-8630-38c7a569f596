<template>
  <div class="course-manage-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.searchWord" @blur="initTableData" clearbale placeholder="请搜索课程名称" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select style="width:220px;margin-right:10px;" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.majorId" placeholder="请选择专业大类">
        <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addCourse" />
      <exportButton />
      <importButton />
      <reflashButton @reflashEvent="reflashEvent" />
    </section>

    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="tableConfig.queryConfig" 
      :total="tableConfig.total"
      :height="height"
      :pagination="tableConfig.pagination" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <baseDialog :noFooter="true" :showToScreen="false" v-if="dialogVisible" :visible.sync="dialogVisible" width="720px" :title="!form.id?'新增课程':'编辑课程'">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="课程名称" prop="name">
              <el-input v-model="form.name" maxLength="20" placeholder="请输入课程名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学时"  prop="hours">
              <el-input type="number" v-model="form.hours" :max="999" placeholder="请输入学时"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="所属专业大类"  prop="bigMajorId">
              <el-select style="width:100%;" placeholder="请选择专业大类" filterable v-model="form.bigMajorId">
                <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item  label="专业层次"  prop="majorCengCi">
              <el-select style="width:100%;" placeholder="请选择专业层次" v-model="form.majorCengCi">
                <el-option v-for="item in majorCengCiList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="课程封面"  prop="coverImage">
          <el-upload
            class="avatar-uploader"
            :action="actionUrl"
            :show-file-list="false"
            :data="uploadData"
            :headers="uploadHeaders"
            :on-success="handleAvatarSuccess">
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <span class="upload-tip">建议图片尺寸为 280x160px，支持格式：jpg、png</span>
        </el-form-item>
        <el-form-item  label="授课老师"  prop="subjectTeacherUserIds">
          <el-select multiple style="width:100%;" placeholder="请选择授课老师" v-model="form.subjectTeacherUserIds">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="关联教材"  prop="textbookIds">
          <el-select multiple style="width:100%;" placeholder="请选择关联教材" v-model="form.textbookIds">
            <el-option v-for="item in textBookList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联课程"  prop="relatedCourseIds">
              <el-select multiple style="width:100%;" placeholder="请选择关联课程" v-model="form.relatedCourseIds">
                <el-option v-for="item in tableConfig.tableData" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="课程描述"  prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入课程描述"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 

    <baseDialog v-if="detailDialogVisible" :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import token from "@/utils/token.js";

// import { categories } from "@/constants/majoeCategories.js"


const majorCengCiList = [
  { label: '中职', value: '中职' },
  { label: '高职', value: '高职' },
  { label: '本科', value: '本科' },
]
export default {
  name: "coursePage",
  data() {
    return {
      majorCengCiList,// 专业层次
      height:'660px',
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          // {
          //   label: "ID",
          //   prop: "id",
          // },
          {
            label: "课程名称",
            prop: "name",
          },
          {
            label: "学时",
            prop: "hours",
          },
          {
            label: "所属专业大类",
            prop: "bigMajorName",
          },
          {
            label: "选课班级数",
            prop: "teachClassIds",
            formatter: (row, column, cellValue) => {
              return cellValue.length;
            }
          },
          {
            label: "授课教师",
            prop: "subjectTeachers",
            formatter: (row, column, cellValue) => {
              return cellValue.length; 
            }
          },
          {
            label: "关联课程",
            prop: "relatedCourses",
            formatter: (row, column, cellValue) => {
              return cellValue.length;
            }
          },
          {
            label: "关联教材",
            prop: "textbooks",
            formatter: (row, column, cellValue) => {
              return cellValue.length;
            }
          }
        ], // 表格列
        tableData: [ ], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          majorId: '', // 专业id
          searchWord: '', // 搜索关键字
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        paginationLayout: 'total, prev, pager, next, jumper',
        notShowSearch: false,
        selectable: true,
        queryConfig: {},
      },
      // 补全模板中使用的变量
      firstLoad: true,
      loading: false,
      actionBtns: [
        // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 150,
      dialogVisible: false, // 新增课程弹窗
      form: { // 新增课程表单
        id: "", // 课程id
        name: "", // 课程名称
        hours: "", // 学时
        coverImage:'', // 封面图
        subjectTeacherUserIds: [], // 授课老师
        relatedCourseIds: [], // 关联课程
        textbookIds: [], // 关联教材
        majorId: "", // 所属专业
        bigMajorId:'',// 所属专业大类
        bigMajorName:'',// 所属专业大类
        collegeId: "", // 所属学院
        majorCengCi: "", // 专业层次
        description: "", // 课程描述
      },
      imageUrl:'',
      actionUrl:window.FILEIP, // 上传图片地址
      uploadData:{
        schoolId:0,
        uploadFileType:'Image',
        withPreSigned:false,
      },
      uploadHeaders: {
        schoolId:0,
        Authorization: token.getToken(),
      },
      rules: { // 表单验证规则
        name: [
          { required: true, message: "请输入课程名称", trigger: "blur",validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('请输入课程名称'));
              } else {
                callback();
              }
            } }
        ],
        hours: [
          { required: true, message: "请输入学时", trigger: "blur" },
          // 新增正整数校验规则
          {
            validator: (rule, value, callback) => {
              const reg = /^[1-9]\d*$/;
              if (!reg.test(value)) {
                callback(new Error('学时必须是正整数'));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        description: [
          { required: true, message: "请输入课程描述", trigger: "blur" }
        ],
        subjectTeacherUserIds: [
          { required: true, message: "请选择授课老师", trigger: "change" }        
        ],
        // relatedCourseIds: [
        //   { required: true, message: "请选择关联课程", trigger: "change" }
        // ],
        // textbookIds: [
        //   { required: true, message: "请选择关联教材", trigger: "change" }
        // ],
        majorId: [
          { required: true, message: "请选择所属专业", trigger: "change" }
        ],
        // 添加课程封面的校验规则
        coverImage: [
          { required: true, message: "请上传课程封面", trigger: "change" }
        ]

      },
      majorList: [], // 专业列表
      teacherList: [], // 教师列表
      textBookList: [], // 教材列表
      categories: [], // 专业大类
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/courseManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // this.initTableData(); // 初始化表格数据
    this.getMajorList(); // 获取专业列表
    this.getTeacherList(); // 获取教师列表
    this.getTextBookList(); // 获取教材列表
    this.getBigMajorList();// 获取专业大类

    this.uploadData.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
    this.uploadHeaders.schoolId = this.userInfo.schools[0].id; // 上传图片时的学校id;
  },
    computed: {
    ...mapGetters(["userInfo"]) // 从vuex中获取用户信息
  },
  methods: {
    // 初始化表格数据
    async initTableData(){
      this.loading = true;
      let res = await this.$api.GetCourseList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(()=>{
        this.loading = false;
      },300)
    },
    // 获取专业大类
    getBigMajorList(){ 
      this.$api.GetMajorTree({}).then(res=>{
        this.categories = res.data;
      })
    },
    getTeacherList(){ // 获取教师列表
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000}).then(res=>{
        this.teacherList = res.data.items.filter(v=> v.enabled);
      })
    },
    getTextBookList(){ // 获取教材列表
      this.$api.GetTextbookList({pageIndex:1,pageSize:100}).then(res=>{
        this.textBookList = res.data.items;
      })
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addCourse(){ // 新增课程
      this.dialogVisible = true; // 打开弹窗
      this.form = { // 重置表单
        id: "", // 课程id
        name: "", // 课程名称
        hours: "", // 学时
        coverImage:'', // 封面图
        subjectTeacherUserIds: [], // 授课老师
        relatedCourseIds: [], // 关联课程
        textbookIds: [], // 关联教材
        majorId: "", // 所属专业
        collegeId: "", // 所属学院
        majorCengCi: "", // 专业层次
        description: "", // 课程描述
      };
      this.imageUrl = ''; // 重置图片地址
    },
    handleAvatarSuccess(file) { // 预览图片
      console.log(file);
      this.imageUrl = file.data; // 图片地址
      this.form.coverImage = file.data; // 图片地址
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateCourse(this.form).then(res=>{
              if(res.errCode==0){
                this.$message({
                  type: 'success',
                  message: '更新成功'
                })
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateCourse(this.form).then(res=>{
            if(res.errCode==0){
              this.$message({
                type:'success',
                message: '新增成功'
              })
              this.dialogVisible = false;
              this.initTableData()
            }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    getMajorList(){
      this.$api.GetMajorList({pageIndex:1,pageSize:100}).then(res=>{
        this.majorList = res.data.items;
      })
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      this.tableConfig.searchParams = params;
      this.initTableData()
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case "详情":
          this.detailDialogVisible = true;
          // 班级
          // 课程
          console.log(row);
          let relatedCoursesList = []; // 关联课程列表
          let relatedClass=[];//关联班级

          if(row.relatedCourses&&row.relatedCourses.length>0){
            let {data}= await this.$api.GetCourseList({pageIndex:1,pageSize:100,courseIds:[row.relatedCourses]})
            relatedCoursesList = data.items;
            console.log("relatedCourses",relatedCoursesList)
          }
          if(row.teachClassIds&&row.teachClassIds.length>0){
            let {data}= await this.$api.GetClassList({pageIndex:1,pageSize:100,classIds:[row.teachClassIds]})
            relatedClass = data.items;
            console.log("relatedClass",relatedClass)
          }
          // 教材
          this.detailInfo ={
            ...row,
            children:[
              {
                title:'任课老师',
                list:row.subjectTeachers,
                name:'first',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'任课老师',prop:'name'},
                ]
              },
              {
                title:'所选班级',
                list: relatedClass,
                name:'secend',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'任课老师',prop:'name'},
                  {
                    label:'任教课程',
                    prop:'teachers',
                    formatter:(row,column,cellValue)=>{
                      return cellValue.map(v=>v.name).join(',')
                    }
                  },
                ]
              },
              {
                title:'关联课程',
                list:relatedCoursesList,
                name:'three',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'课程名称',prop:'name'},
                  {label:'所属专业大类',prop:'bigMajorId'},
                ]
              },
              {
                title:'关联教材',
                list:row.textbooks,
                name:'fore',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'教材名称',prop:'name'},
                  {label:'教材描述',prop:'description'},
                ]
              }
            ]
          }; // 假设详情信息保存在 row 中，根据实际情况修改
        break;
        case "编辑":
          this.dialogVisible = true; // 打开弹窗
          this.form = {
            id: row.id,
            name: row.name,
            hours: row.hours,
            subjectTeacherUserIds: row.subjectTeachers.map(t => t.id),
            relatedCourseIds: row.relatedCourses.map(c => c),
            textbookIds: row.textbooks.map(t => t.id),
            majorId: row.majorId,
            bigMajorId: row.bigMajorId,
            bigMajorName: row.bigMajorName,
            collegeId: row.collegeId,
            coverImage: row.coverImage,
            majorCengCi: row.majorCengCi,//专业层次
            description: row.description,
          };
          this.imageUrl = row.coverImage; // 图片地址
        break;
        case "删除":
          await this.$zdDialog({
          contImg: '',
          contTitle: '确定删除?',
          contDesc: '删除后数据将不可恢复，请谨慎操作！',
        })
        this.$api.DeleteCourse({id:row.id}).then(res=>{
          console.log("删除课程",res)
          this.initTableData()
        })
        break;
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.course-manage-page {
  padding: 20px;
  
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}
</style>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>