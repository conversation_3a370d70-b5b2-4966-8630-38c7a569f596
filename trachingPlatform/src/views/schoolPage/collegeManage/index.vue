<template>
  <div class="college-manage-page" v-loading="loading">
    <section class="top-tool">
      <el-input class="serach-input" clearable v-model="tableConfig.searchParams.searchWord" @blur="initTableData" placeholder="请搜索学校名称/负责人/联系电话" suffix-icon="iconfont icon-sousuo"></el-input>
      <addButton @addEvent="addCollege" />
      <exportButton />
      <importButton />
      <reflashButton @reflashEvent="reflashEvent" />
      
    </section>
    <table2 
      @selectionChange="selectionChange" 
      :notShowSearch="notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :height="height" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>
    <baseDialog :noFooter="true" :showToScreen="false" width="500px" :visible.sync="dialogVisible" :title="!form.id?'新增院校':'编辑院校'">
      <el-form ref="form" :model="form" label-width="100px" label-position="top" :rules="rules">
        <el-form-item label="学院名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入学院名称" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="principalUserId">
          <el-select style="width:100%;" clearable filterable v-model="form.principalUserId" placeholder="请选择负责人" @change="selectPrincipal">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="教材额度" prop="textbookQuota">
          <el-input type="number" v-model="form.textbookQuota" placeholder="请输入教材额度" :max="quotaInfo.avaQuota"></el-input>
        </el-form-item>
        <el-form-item label="剩余额度/学校总额度">
          <el-input :disabled="true" v-model="form.totalQuota"></el-input>
        </el-form-item>
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 

    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数

export default {
  name: "schoolPage",
  data() {
    // 定义自定义校验函数
    const validateTextbookQuota = (rule, value, callback) => {
      if (value > this.quotaInfo.avaQuota) {
        callback(new Error(`教材额度不能大于 ${this.quotaInfo.avaQuota}`));
      } else {
        callback();
      }
    };
    // 定义手机号校验规则
    const validatePhone = (rule, value, callback) => {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };
    return {
      height:'660px',
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          // {
          //   label: "ID",
          //   prop: "id",
          // },
          {
            label: "学院名称",
            prop: "name",
          },
          {
            label: "负责人",
            prop: "principal",
          },
          {
            label: "联系方式",
            prop: "phone",
          },
          {
            label: "下设专业",
            prop: "majorCount",
          },
          {
            label: "教材额度",
            prop: "textbookQuota",
          },
          {
            label: "教材数量",
            prop: "textbookCount",
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              return formatISOString(cellValue); 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          searchWord:'',
        },
        btnList: {
          detail: {
            enable: true
          }
        },
         // 补全模板中使用的变量
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
      },
     
      queryConfig: {},
      firstLoad: true,
      loading: false,
      actionBtns: [
        // // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 150,
      dialogVisible:false,// 弹窗是否显示
      form:{ // 新增表单
        id:0, // 院校ID
        name:'', // 名称
        principalUserId:'', // 负责人ID
        principal:'', // 负责人
        phone:'', // 联系方式
        textbookQuota:0, // 教材额度
        totalQuota:'',// 剩余额度/学校总额度
      },
      
      // 添加表单校验规则
      rules: {
        name: [
          { required: true,message: '学院名称不能为空', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('学院名称不能为空'));
              } else {
                callback();
              }
            } }
        ],
        // principalUserId: [
        //   { required: true,message: '负责人不能为空', trigger: 'change' }
        // ],
        phone: [
          { required: true,message: '联系电话不能为空', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        textbookQuota: [
          { required: true,message: '教材额度不能为空', trigger: 'blur' },
          // 添加自定义校验规则
          { validator: validateTextbookQuota, trigger: 'blur' } 
        ]
      },
      teacherList :[],
      quotaInfo:{}, // 额度
      detailInfo:{}, // 详情信息
      detailDialogVisible:false, //详情
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/collegeManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    // 初始化表格数据
    // this.initTableData();
    this.getTeacherList();
   },
  methods: {
    // 初始化表格数据
    async initTableData(){
      this.loading = true;
      let res = await this.$api.GetCollegeList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    getTeacherList(){
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000,userType:'SchoolAdmin,SchoolEmployee,Teacher'}).then(res=>{
        this.teacherList = res.data.items.filter(v=> v.enabled);
      })
    },
    // 选择负责人
    selectPrincipal(){
      let target = this.teacherList.find(item=>item.id == this.form.principalUserId);
      this.form.principal = target.name;
      this.form.phone = target.phone;
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    addCollege(type) {
      this.getCollegeQuotaInfo({isCreated:true,editCollegeId: 0});
      this.dialogVisible = true; 
      this.form = { // 新增表单
        id:0, // 院校ID
        name:'', // 名称
        principalUserId:'', // 负责人ID
        principal:'', // 负责人
        phone:'', // 联系方式
        textbookQuota:0, // 教材额度
        totalQuota:'',// 剩余额度/学校总额度
      }
    },
    save(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.form.id){ // 编辑
            this.$api.UpdateCollege(this.form).then(res=>{
              if(res.errCode==0){
                this.$message.success("更新成功")   
                this.dialogVisible = false;
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateCollege(this.form).then(res=>{
            if(res.errCode==0){
                this.$message.success("新增成功")   
                this.dialogVisible = false;
                this.initTableData()
              }
          })
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      this.tableConfig.searchParams = params;
      this.initTableData()
      // 这里可以添加搜索逻辑
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    async getCollegeQuotaInfo(params){
      let res = await this.$api.GetCollegeQuotaInfo(params)
      this.quotaInfo = res.data;
      this.form.totalQuota = `${res.data.avaQuota}/${res.data.totalQuota}`
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      switch (btn.name) {
        case '详情':
          this.detailDialogVisible = true;
          // 获取学院下的专业列表
          this.$api.GetMajorList({pageIndex:1,pageSize:100,collegeId:row.id,}).then(res=>{
            // 获取学院下的专业列表
            this.detailInfo = {
              ...row,
              children:[
                {
                  title:'下设专业',
                  list:res.data.items,
                  columns:[
                    {
                      label: "序号",
                      type: "index",
                      width: 80,
                    },{
                      label: "专业名称",
                      prop: "name",
                    },
                    {
                      label: "负责人",
                      prop: "principal",
                    },
                    {
                      label: "联系方式",
                      prop: "phone",
                    },
                  ],
                  name:'first',
                },
                {
                  title:'关联教材',
                  list:row.textbooks,
                  name:'secend',
                  columns:[
                    {
                      label: "序号",
                      type: "index",
                      width: 80,
                    },
                    {
                      label: "教材名称",
                      prop: "name",
                    }, 
                    {
                      label: "专业大类",
                      prop: "majorCategory",
                    },
                    {
                      label: "主编",
                      prop: "publishedChiefEditor",
                    },
                  ]
                }
              ]
            }
          })
          break;
        case '编辑':
          this.form = { // 编辑表单
            id:row.id, // 院校ID
            name:row.name, // 名称
            principal:row.principal, // 负责人
            principalUserId:row.principalUserId, // 负责人ID
            phone:row.phone, // 联系方式
            textbookQuota:row.textbookQuota, // 教材额度
            totalQuota: ''
          }
          // 这里可以添加编辑逻辑
          this.getCollegeQuotaInfo({isCreated:false,editCollegeId:row.id})
          this.dialogVisible = true;
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
      // 这里可以添加按钮点击逻辑
    },
    getMajorList(collegeId){
     
    },
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该院校会被删除, 是否继续?',
      })
      this.$api.DeleteCollege({id:row.id}).then(res=>{
        console.log("删除院校",res)
        this.initTableData()
      })

    }
  },
};
</script>

<style lang="scss" scoped>
.college-manage-page {
  padding: 20px;
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
  
}
</style>