<template>
    <div class="work-branch-page">
        <div class="content">
            <div class="to-be-done">
                <h3 class="title"><i class="iconfont icon-daibanshixiang1"></i> <p>我的待办 <span>及时清理待办，可以有效提升流程效率</span></p></h3>
                <el-row class="un-do">
                    <el-col class="part part-1" :span="12" @click.native="goWorkFlow(1)">
                        <p class="lable">待审批</p>
                        <p class="num">{{pendingCount}}</p>
                        <i class="iconfont icon-daishenpi"></i>
                    </el-col>
                    <el-col class="part part-2" :span="12" @click.native="goWorkFlow(2)">
                        <p class="lable">我已审批</p>
                        <p class="num">{{approvedCount}}</p>
                        <i class="iconfont icon-shenpi-daishenpi"></i>
                    </el-col>
                    <!-- <el-col class="part part-3" :span="8" @click.native="goWorkFlow(3)">
                        <p class="lable">我发起的</p>
                        <p class="num">{{createCount}}</p>
                        <i class="iconfont icon-mti-wofaqide"></i>
                    </el-col> -->
                </el-row>
            </div>

            <div class="statistics">
                <h3 class="title"><i class="iconfont icon-daibanshixiang1"></i> <p>教务统计 <span>教务管理相关工作数据统计</span></p></h3>
                <el-row :gutter="10">
                    <el-col :span="6" v-for="item in statisticsList" :key="item.label">
                        <div class="statistics-item" @click="skipPart(item)">
                            <span class="icon-span" :style="{ backgroundColor: item.iconBgColor }"> 
                                <i :class="['iconfont', item.icon]" :style="{ color: item.color }"></i>
                            </span>
                            <div class="item-data">
                                <p class="label">{{item.label}}</p>
                                <p class="num">{{item.num}}</p>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    data() {
        return {
            pendingCount:0,//待审批数量
            approvedCount:0,//我已审批数量
            createCount:0,//我发起的数量
            statisticsList:[]
        };
    },
    mounted() {
        this.GetTodoList()
        this.GetWorkflowInstancesApprovedByMe()
        this.GetWorkflowInstanceOfMine()
        this.schoolBasicStatistic()
    },
    methods: {
        goWorkFlow(type){
            this.$router.push({
                path:'/school/workFlow',
                query:{
                    type:type
                }
            })
        },
        // 快捷跳转
        skipPart(item){
            if(item.path){
                this.$router.push({path:item.path})
            }
        },
        // 统计
        schoolBasicStatistic(){
            this.$api.SchoolBasicStatistic({}).then(res => {
                console.log(res.data)
                let info = res.data;
                this.statisticsList = [
                    {
                    label:'学院数',
                    num: info.collegeCount,
                    icon:'icon-xuexiao',
                    color:'#0070FC',
                    iconBgColor:'#EDF2FF',
                    path:'/school/collegeManage',
                },
                {
                    label:'专业数',
                    num:info.majorCount,
                    icon:'icon-zhuanye',
                    color:'#FC8800',
                    iconBgColor:'#FCF5E2',
                    path:'/school/professionalManage',
                },
                {
                    label:'累计课程(门)',
                    num:info.courseCount,
                    icon:'icon-kecheng',
                    color:'#ED7CEA',
                    iconBgColor:'#FEF1FF',
                    path:'/school/courseManage',
                },
                {
                    label:'累计教材(门)',
                    num:info.textbookTotalCount,
                    icon:'icon-shuzijiaocai',
                    color:'#49C567',
                    iconBgColor:'#E7FAF3',
                    path:'/school/textbookManage',
                },
                {
                    label:'剩余教材额度',
                    num:info.collegeCount,
                    icon:'icon-xinyongedu',
                    color:'#ED7CEA',
                    iconBgColor:'#FEF1FF'
                },
                {
                    label:'教师数',
                    num:info.teacherTotalCount,
                    icon:'icon-jiaoshi',
                    color:'#0070FC',
                    iconBgColor:'#EDF2FF',
                    path:'/school/teacherManage',
                },
                {
                    label:'行政班级数',
                    num:info.classAdminCount,
                    icon:'icon-wodebanji',
                    color:'#49C567',
                    iconBgColor:'#E7FAF3',
                    path:'/school/classManage?classType=1',
                },
                {
                    label:'行政班学生数',
                    num:info.classAdminStudentCount,
                    icon:'icon-xunjianweibao',
                    color:'#FC8800',
                    iconBgColor:'#FCF5E2',
                    path:'/school/studentManage',
                },
                {
                    label:'教学班级数',
                    num:info.classTeachCount,
                    icon:'icon-wodebanji',
                    color:'#49C567',
                    iconBgColor:'#E7FAF3',
                    path:'/school/classManage?classType=2',
                },
                {
                    label:'教学班学生数',
                    num:info.classTeachStudentCount,
                    icon:'icon-xunjianweibao',
                    color:'#FC8800',
                    iconBgColor:'#FCF5E2',
                    path:'/school/studentManage',
                },
                {
                    label:'已分配/累计教材额度',
                    num:`${info.textbookQuotaUsed}/${info.textbookQuotaTotal}` ,
                    icon:'icon-ziyuan143',
                    color:'#FC8800',
                    iconBgColor:'#FCF5E2'
                }
                ]
            })
        },
        // 获取待办数量
        GetTodoList() {
            this.$api.GetTodoList({pageSize:1000,pageIndex:1,wFApproveStatus:'PENDING'}).then(res => {
                this.pendingCount = res.data.total;
            })
        },
        // 获取我审批的数量
        GetWorkflowInstancesApprovedByMe(){
            this.$api.GetWorkflowInstancesApprovedByMe({pageSize:1000,pageIndex:1}).then(res => {
                this.approvedCount = res.data.total;
            })
        },
        // 获取我发起的数量
        GetWorkflowInstanceOfMine(){
            this.$api.GetWorkflowInstanceOfMine({pageSize:1000,pageIndex:1}).then(res => {
                this.createCount = res.data.total;
            })
        }
        
    }
}
</script>
<style lang="scss">
.work-branch-page{
    background-color: #F6F8FA;
    height: 100%;
    overflow: hidden;

    .content{
        width: 1020px;
        margin: 28px auto;
        height: 100%;
        .title{
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            i{
                color: #409EFF;
                font-size: 20px;
                margin-right: 10px;   
            }
            span{
             font-family: PingFangSC, PingFang SC;
             font-weight: 400;
             font-size: 14px;
             margin-left: 8px;
             color: #999999;
            }
        }
    }
    .to-be-done{
       padding: 0 10px;
       .un-do{
            background-color: #fff;
            padding: 20px;
            .part{
                height: 100px;
                border-radius: 10px;
                position: relative;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                flex-direction: column;
                width: 464px;
                cursor: pointer;
                transition: all .3s ease-in-out;
                transform: scale(1);
                &:hover{
                    box-shadow: 2px 3px 4px rgba(153,153,153,0.1); 
                    transform: scale(1.05);
                }
                
                .iconfont{
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    font-size: 50px;
                }

                .lable{
                    font-size: 15px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    margin-bottom: 14px;
                    padding-left: 30px;
                }
                .num{
                    font-size: 26px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    padding-left: 30px;
                }
            }
           p{
               font-size: 14px;
               color: #666;
               margin-bottom: 5px;
           }
           p:nth-child(2){
               font-size: 20px;
               color: #333;
           }
           .part-1{
                background: linear-gradient( 178deg, #FFECDA 0%, #FCFCFB 100%);
                margin-right: 30px;
                .iconfont{
                    color: #F6E8DB;
                }
                .lable{
                    color: #592F06;
                }
               .num{
                    color: #592F06;
                }
           }
           .part-2{
            background: linear-gradient( 180deg, #E4EDFF 0%, #F6F9FF 100%);
            // margin-right: 30px; 
            .iconfont{
                color: #D6DDF0;
            }
            .lable{
                color: #092661;
            }
           .num{
                color: #092661;
            }
           }
           .part-3{
            background: linear-gradient( 180deg, #DCF3FF 0%, #F4FBFF 100%);
            .iconfont{
                color: #D7E9F2;
            }
            .lable{
                color: #074B72 ;
            }
           .num{
                color: #074B72 ;
            }
           }
       }
    }

    .statistics{
        padding: 10px 10px 0;
        .statistics-item{
            width: 243px;
            height: 100px;
            background: #FFFFFF;
            box-shadow: 1px 1px 10px 0px rgba(153,153,153,0.1);
            border-radius: 4px;
            margin-bottom: 18px;
            display: flex;
            align-items: center;
            cursor: pointer;
            justify-content: flex-start;
            transition: all .3s ease-in-out;
            transform: scale(1);
            &:hover{
                box-shadow: 2px 3px 4px rgba(153,153,153,0.1); 
                transform: scale(1.02);
            }
            .icon-span{
                width: 48px;
                height: 48px;
                margin: 0 20px;
                text-align: center;
                line-height: 48px;
                display: inline-block;
                border-radius: 24px;
                background: #EDF2FF;
            }
            .item-data{
                .label{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #999;
                }
                .num{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 500;
                    font-size: 26px;
                    color: #333;
                    line-height: 37px;
                }
            }
        }
    }

}
</style>