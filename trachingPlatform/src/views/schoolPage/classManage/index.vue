<template>
  <div class="class-manage-page"  v-loading="loading">
    <div class="class-type">
       <p v-for="item in classType" :key="item.type" @click="changeType(item)" :class="['type',activeType==item.type?'active':'']">{{item.label}}</p>
    </div>
    <section class="top-tool">
      <el-input class="serach-input" v-model="tableConfig.searchParams.className" @change="initTableData" @blur="initTableData" clearable placeholder="请搜索班级名称" suffix-icon="iconfont icon-sousuo"></el-input>
      <el-select style="width:220px;margin-right:10px;" filterable clearable @change="selectCollegeSearch" v-model="tableConfig.searchParams.collegeId" placeholder="请选择院校">
        <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-select style="width:220px;margin-right:10px;" filterable clearable @change="initTableData" v-model="tableConfig.searchParams.majorId" placeholder="请选择专业">
        <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <addButton @addEvent="addClass" />
      <exportButton @exportEvent="exportEvent" />
      <importButton @importEvent="importEvent" type="Class" />
      <reflashButton @reflashEvent="reflashEvent" />
    </section>
    <table2 
     
      @selectionChange="selectionChange" 
      :notShowSearch="tableConfig.notShowSearch" 
      ref="tablePreview" 
      @selection-change="handleSelectionChange" 
      :selectable="tableConfig.selectable" 
      :data="tableConfig.tableData" 
      :columns="tableConfig.columns" 
      :queryFormConfig="queryConfig" 
      :total="tableConfig.total"
      :pagination="tableConfig.pagination" 
      :height="height" 
      :paginationLayout="tableConfig.paginationLayout" 
      :firstLoad="firstLoad" 
      :searchParams="tableConfig.searchParams" 
      @handleSearch="handleSearch">
      <empty slot="empty" :loading="loading" msg="暂无数据" size="middle" />
      <template #operate>
        <el-table-column v-if="operateBtns.length" label="操作" fixed="right" :width="operateWidth">
          <template slot-scope="scope">
            <el-button v-for="(item, index) in operateBtns" type="text"
                       v-show="isShowOperateBtn(scope, item)"
                       :class="item.class || ''"
                       @click="btnClick(scope.row, item)" :key="index"
                       :disabled="scope.row.dataLocked">
              {{ item.name }}
            </el-button>
          </template>
        </el-table-column>
      </template>
    </table2>

    <!-- 添加班级 -->
    <baseDialog :noFooter="true" :showToScreen="false" :visible.sync="dialogVisible" width="500px" :title="!form.id?'新增班级':'编辑班级'">
      <el-form ref="form" :model="form" label-width="80px" label-position="top" :rules="rules">
        <el-form-item label="班级名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入班级名称" maxLength="20"></el-input>
        </el-form-item>
        <el-form-item label="所属学院"  prop="collegeId">
          <el-select style="width:100%;" v-model="form.collegeId" placeholder="请选择所属学院" @change="selectCollege">
            <el-option v-for="item in collegeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属专业"  prop="majorId">
          <el-select style="width:100%;" v-model="form.majorId"  placeholder="请选择所属专业">
            <el-option v-for="item in majorList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属年级"  prop="grade">
          <el-select style="width:100%;" placeholder="请选择所属年级" v-model="form.grade">
            <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班主任" prop="formTeacherUserId">
          <el-select style="width:100%;"  placeholder="请选择班主任" v-model="form.formTeacherUserId">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item  label="任教老师" prop="subjectTeacherUserIds">
          <el-select style="width:100%;" placeholder="请选择任教老师" multiple v-model="form.subjectTeacherUserIds">
            <el-option v-for="item in teacherList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item style="text-align:right;margin-bottom:0;margin-top:40px;">
          <el-button type="default" @click="dialogVisible = false;">取消</el-button>
          <el-button type="primary" @click="save" v-preventReClick>保存</el-button>
        </el-form-item>
      </el-form>
    </baseDialog> 
    <baseDialog :noFooter="true" title="详情" width="720px" :visible.sync="detailDialogVisible">
      <detail :detailInfo="detailInfo" />
      <div style="text-align:right;margin-bottom:0;margin-top:40px;">
        <el-button type="default" @click="detailDialogVisible = false;">取消</el-button>
        <el-button type="primary" @click="detailDialogVisible = false">确定</el-button>
      </div>
    </baseDialog>
  </div>
</template>

<script>
import { formatISOString } from "@/utils/format-date.js"; // 导入日期格式化工具函数
import { exportExcel } from '@/utils/common.js' // 导入导出excel
export default {
  name: "schoolPage",
  data() {
    return {
      height:'660px',
      tableConfig: {
        columns: [
          {
            label: "序号",
            type: "index",
            width: 80,
          },
          // {
          //   label: "ID",
          //   prop: "id",
          // },
          {
            label: "班级名称",
            prop: "name",
          },
          {
            label: "所属专业",
            prop: "majorName",
          },
          {
            label: "所属学院",
            prop: "collegeName",
          },
          {
            label: "年级",
            prop: "grade",
          },
          {
            label: "学生数量",
            prop: "studentCount",
          },
          {
            label: "班主任",
            prop: "formTeacherUserName",
          },
          {
            label: "任课教师",
            prop: "teachers",
            formatter: (row, column, cellValue, index) => {
              return row.teachers.length;
            }
          },
          {
            label: "创建时间",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              return formatISOString(cellValue); 
            }
          }
        ], // 表格列
        tableData: [], // 表格数据
        pagination: true,
        total: 0,
        searchParams: {
          pageIndex: 1,
          pageSize: 12,
          collegeId: '', // 院校ID
          majorId: '', // 专业ID
          classType:'Admin', // 班级类型
          className: '' // 搜索关键词
        },
        btnList: {
          detail: {
            enable: true
          }
        },
        notShowSearch: false,
        selectable: true,
        pagination: true,
        paginationLayout: 'total, prev, pager, next, jumper',
      },
      // 补全模板中使用的变量
      queryConfig: {},
      firstLoad: true,
      loading: false,
      actionBtns: [
        // { id: 1, name: '新增' }
      ],
      operateBtns: [
        { name: '详情',class:'default-btn' },
        { name: '编辑',class:'default-btn' },
        { name: '删除',class:'del-btn' }
      ],
      operateWidth: 150,
      dialogVisible: false, // 新增班级弹窗
      form: { // 新增班级表单
        id:0,
        name: "",
        collegeId: '',
        majorId: '',
        grade: "",
        formTeacherUserId: '',
        subjectTeacherUserIds:[],//任课老师
        classType:'Admin',//行政班
      },
      collegeList:[],// 学院列表
      majorList:[],// 专业列表
      gradeList:[],// 年级列表
      teacherList:[],// 教师列表
      rules: { // 表单验证规则
        name: [
          { required: true, message: '请输入班级名称', trigger: 'blur',validator: (rule, value, callback) => {
              // 去除前后空格
              const trimmedValue = value.trim(); 
              if (trimmedValue === '') {
                callback(new Error('班级名称不能为空'));
              } else {
                callback();
              }
            } }  
        ],
        collegeId: [
          { required: true, message: '请选择所属学院', trigger: 'change' } 
        ],
        majorId: [
          { required: true, message: '请选择所属专业', trigger: 'change' } 
        ],
        grade: [
          { required: true, message: '请选择所属年级', trigger: 'change' }
        ],
        formTeacherUserId: [
          { required: true, message: '请选择班主任', trigger: 'blur' }
        ],
        subjectTeacherUserIds: [
          { required: true, message: '请选择任课教师', trigger: 'change' }
        ],
      },
      activeType:'Admin', // 班级类型 Admin:行政班 Teach:普通班
      classType:[ // 班级类型
        {type:'Admin',label:'行政班'},
        {type:'Teach',label:'教学班'}
      ],
      detailDialogVisible: false, // 详情弹窗
      detailInfo: {}, // 详情信息
    }; 
  },
  components: {
    // 补全模板中使用的组件
    addButton: () => import('@/components/base/button/addButton.vue'), // 新增按钮
    reflashButton: () => import('@/components/base/button/reflashButton.vue'), // 新增按钮
    exportButton: () => import('@/components/base/button/exportButton.vue'), // 导出按钮
    importButton: () => import('@/components/base/button/importButton.vue'), // 导入按钮
    baseDialog: () => import("@/components/base/dialog.vue"), // 弹窗
    detail:()=>import('@/views/schoolPage/classManage/components/detail.vue'),
    empty:()=>import("@/components/base/empty.vue"),
  },
  mounted() {
    if(this.$route.query.classType==2){
      this.activeType = 'Teach'; // 普通班
      this.tableConfig.searchParams.classType = 'Teach'; // 更新查询参数
    }else if(this.$route.query.classType==1){
      this.activeType = 'Admin'; // 行政班
      this.tableConfig.searchParams.classType = 'Admin'; // 更新查询参数
    }
    // 初始化表格数据
    this.getGradeList(); // 获取年级列表
    this.initTableData();
    this.getCollegeList(); // 获取学院列表

  },
  methods: {
    getGradeList(){ // 获取年级列表
      // 获取当前年份
      const currentYear = new Date().getFullYear();
      // 往前推四年，往后推一年
      for (let i = -4; i <= 1; i++) {
        const year = currentYear + i;
        this.gradeList.push({
          id: year,
          name: `${year}级`,
        });
      }
    },
    // 初始化表格数据
    async initTableData(){
      this.loading = true;
      let res = await this.$api.GetClassList(this.tableConfig.searchParams)
      this.tableConfig.tableData = res.data.items
      this.tableConfig.total = res.data.total;
      setTimeout(() => {
        this.loading = false;
      },300);
    },
    getCollegeList(){ // 获取学院列表
      this.$api.GetCollegeList({pageIndex:1,pageSize:100}).then(res=>{
        this.collegeList = res.data.items;
      })
    },
    getTeacherList(val){ // 获取教师列表
      this.$api.GetTeacherList({pageIndex:1,pageSize:1000,collegeId:val,}).then(res=>{
        this.teacherList = res.data.items.filter(v=> v.enabled);
      })
    },
    // 刷新
    reflashEvent(){
      this.initTableData()
    },
    // 导出
    exportEvent(){     
      let params={
        type:"Class",
      }
      exportExcel('/Excel/DownloadTemplate',params,'班级模板')
    },
    // 导入
    importEvent(file){
     this.initTableData()
    },
    addClass(){
      this.dialogVisible = true;
      this.form = { // 新增表单
        id:0,
        name: "",
        collegeId: '',
        majorId: '',
        grade: "",
        formTeacherUserId: '',
        subjectTeacherUserIds:[],//任课老师
        classType:'',//行政班
      }
    },
    save(){
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.form.classType = this.activeType; // 更新表单中的班级类型
          if(this.form.id){ // 编辑
            this.$api.UpdateClass(this.form).then(res=>{
              if(res.errCode == 0){ // 编辑成功
                this.$message({
                  message: '编辑成功',
                  type: 'success'
                });
                this.$refs.form.resetFields();
                this.dialogVisible = false;
                // 重置表单
                this.initTableData()
              }
            })
            return;
          }
          this.$api.CreateClass(this.form).then(res=>{
            if(res.errCode == 0){ // 新增成功
              this.$message({
                message: '新增成功',
                type:'success'
              });
              this.$refs.form.resetFields();
              this.dialogVisible = false;
              // 重置表单
              this.initTableData()
            }
            
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
      
    },
    changeType(item){
      this.activeType = item.type;
      this.form.classType = item.type; // 更新表单中的班级类型
      this.tableConfig.searchParams.classType = item.type; // 更新搜索参数
      this.tableConfig.searchParams.pageIndex = 1; // 重置页码
      this.initTableData();
    },
    selectCollegeSearch(val){
      this.selectCollege(val);
      this.tableConfig.searchParams.collegeId = val; // 更新搜索参数
      this.tableConfig.searchParams.pageIndex = 1; // 重置页码
      this.tableConfig.searchParams.majorId = ''; // 重置专业ID
      this.form.majorId = ''; // 重置表单中的专业ID
      this.initTableData();
    },
    selectCollege(val){ // 选择学院
      this.$api.GetMajorList({collegeId:val,pageIndex:1,pageSize:100}).then(res=>{
        this.majorList = res.data.items;
      })
      
      this.form.majorId= '';
      this.form.formTeacherUserId= '';
      this.getTeacherList(val); // 获取教师列表
    },
    // 补全模板中使用的方法
    selectionChange(selection) {
      console.log('selection changed:', selection);
    },
    handleSelectionChange(selection) {
      console.log('handle selection changed:', selection);
    },
    handleSearch(params) {
      console.log('search params:', params);
      // 这里可以添加搜索逻辑
      this.tableConfig.searchParams = params; // 更新搜索参数
      this.initTableData();
    },
    isShowOperateBtn(scope, item) {
      // 这里可以添加操作按钮显示逻辑
      return true;
    },
    changeSelection(selection) {
      this.$emit("changeSelection", selection);
    },
    formatDataSourceBtns(btn) {
      if (btn.type === "detail") {
        btn.enable = this.tableConfig.btnList.detail.enable;
      }
      return btn;
    },
    handlePlus(btn) {
      this.$emit("handlePlus", btn); 
    },
    btnClick(row, btn) {
      console.log('button clicked:', row, btn);
      // 这里可以添加按钮点击逻辑
      switch (btn.name) {
        case '详情':
          this.detailDialogVisible = true;
          this.detailInfo ={
            ...row,
            children:[
              {
                title:'任课老师',
                list:row.teachers,
                name:'first',
                columns:[
                  {
                    label: "序号",
                    type: "index",
                    width: 80,
                  },
                  {label:'任课老师',prop:'name'},
                  {label:'任教课程',prop:'phone'},
                ]
              }
            ]
          }; // 假设详情信息保存在 row 中，根据实际情况修改
          break;
        case '编辑':
          this.dialogVisible = true;
          if(row.collegeId){ // 编辑
            this.selectCollege(row.collegeId); // 获取专业列表
          }
          
          this.form = { // 编辑表单
            ...row,
            subjectTeacherUserIds: row.teachers.map(item => item.id),
          }

          
          break;
        case '删除':
          // 这里可以添加删除逻辑
          this.handleDelete(row)
          break; 
      }
    },    
    async handleDelete(row){
      await this.$zdDialog({
        contImg: '',
        contTitle: '确定删除?',
        contDesc: '确定后，该班级会被删除, 是否继续?',
      })
      this.$api.DeleteClass({id:row.id}).then(res=>{
        console.log("删除院校",res)
        this.initTableData()
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.class-manage-page {
  padding: 20px;

  .class-type{
    display: flex;
    align-items: flex-start;
    height: 30px;
    margin-bottom: 20px;
    border-bottom:1px solid #F2F3F5 ;
    .type{
      width: 60px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #333;
      cursor: pointer;
      text-align: center;
      line-height: 14px;
      height: 30px;
      margin-right: 28px;
      &.active{
       color: #0070FC; 
       border-bottom: #0070FC 2px solid;
      }
    }
  }
  .top-tool{
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .serach-input{
      width: 300px;
      margin-right: 10px;
      ::v-deep .el-input__suffix{
        padding-right: 6px;
      }
    }
  }
}
</style>