/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-09 17:35:11
 * @LastEditors: song <EMAIL>
 * @LastEditTime: 2025-03-06 18:07:33
 * @Description:
 */
import { USER_INFO, NOW_CASE_INFO } from "@/utils/cookies";
const getters = {
    permission_routes: (state) => state.permission.routes,
    sidebar: (state) => state.app.sidebar,
    userInfo: (state) => {
        if (!state.userInfo.id) {
            const userInfo = JSON.parse(sessionStorage.getItem(USER_INFO) || "{}");
            state.userInfo = userInfo;
        }
        return state.userInfo;
    }, // 用户信息
    isToggleTheme: (state) => state.isToggleTheme, // 是否切换主题
    caseInfo: (state) => {
        // 当前的案例信息
        if (!state.caseInfo.id) {
            const caseInfo = JSON.parse(sessionStorage.getItem(NOW_CASE_INFO) || "{}");
            state.caseInfo = caseInfo;
        }
        return state.caseInfo
    },
    getWorkTabList: (state) => state.workTabList, // work branch 头部的tab切换
    nodeBusinessData: (state) => state.process.nodeBusinessData, // 业务流程节点需要的业务信息
    sceneNodeInfo: (state) => state.sceneNodeInfo, // 当前操作的场景信息
    getCourseInfo: (state) => state.courseInfo, // 获取当前课程信息
    // 获取新建作业信息
    newHomeWorkInfo: (state) => state.newHomeWorkInfo,
    voucherWorkInfo: (state) => state.voucherWorkInfo,
    insertTopicList: (state) => state.insertTopicList,
    accountInfo: (state) => state.accountInfo,
    accounteditInfo: (state) => state.accounteditInfo,
    accountdetailInfo: (state) => state.accountdetailInfo,
    teachereditInfo: (state) => state.teachereditInfo,
    knowledgeGraphVm: (state) => state.knowledgeGraphVm, // 知识图谱的弹窗只允许有一个
    getSysMenuList: state => state.sysMenuList, // 授权菜单列
    getGlobalTaskFlag: state => state.openGlobalTask, // 获取是否开启全局任务模式的标识
    getChapterTree: (state) => state.chapterTree, // 获取章节树
    getChapterShowType: (state) => state.chapterShowType, // 获取章节显示类型 1 树结构 2 思维导图
    getSelectedSceneNode: (state) => state.selectedSceneNode, // 获取已选择的场景数据（包含场景 流程和核算）
    getReviewList: (state) => state.reviewList, //获取带批阅的学生列表
};
export default getters;