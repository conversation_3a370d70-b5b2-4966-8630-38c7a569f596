import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import state from "@/constants/state";
import { setCookies, USER_INFO } from '@/utils/cookies';
import createPersistedState from 'vuex-persistedstate';
Vue.use(Vuex);

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    const value = modulesFiles(modulePath);
    modules[moduleName] = value.default;
    return modules;
}, {});
const store = new Vuex.Store({
    modules,
    state: {
        ...state.rootState,
        // showTaskDialog: true, //显示任务导航弹窗
        // showQueStem: true, //显示题干弹窗
    },
    getters,
    mutations: {
        // 重置vuex本地储存状态
        // resetStore(state) {
        //   Object.keys(state).forEach((key) => {
        //     state[key] = window.SITE_CONFIG["storeState"][key];
        //   });
        // },
        isToggleTheme(state, status) {
            //切换主题
            state.isToggleTheme = status;
        },
        userInfo(state, value) {
            // 用户信息
            state.userInfo = value;
            sessionStorage.setItem("userInfo", JSON.stringify(value));
            setCookies(USER_INFO, JSON.stringify(value));
        },
        caseInfo(state, value) {
            // 案例信息
            state.caseInfo = value;
        },
        setWorkTabList(state, value) {
            // 新增tabList
            if (state.workTabList) {
                state.workTabList.push(value);
            } else {
                state.workTabList = [value];
            }
        },
        delWorkTabTag(state, value) {
            // 关闭tag
            state.workTabList.splice(value.index, 1);
        },
        //操作的场景节点信息（控制台部分）
        sceneNodeInfo(state, value) {
            state.sceneNodeInfo = value;
        },
        // 课程信息
        setCourseInfo(state, value) {
            state.courseInfo = value;
        },
        newHomeWorkInfo(state, value) {
            state.newHomeWorkInfo = value;
        },
        insertTopicList(state, value) {
            state.insertTopicList = value;
        },
        voucherWorkInfo(state, value) {
            state.voucherWorkInfo = value;
        },
        accountInfo(state, value) {
            state.accountInfo = value;
        },
        accounteditInfo(state, value) {
            state.accounteditInfo = value;
        },
        accountdetailInfo(state, value) {
            state.accountdetailInfo = value;
        },
        teachereditInfo(state, value) {
            state.teachereditInfo = value;
        },
        knowledgeGraphVm(state, vm) {
            if (state.knowledgeGraphVm instanceof Array) {
                state.knowledgeGraphVm.push(vm)
            } else {
                state.knowledgeGraphVm = [vm]
            }
        },
        setSysMenuList: (state, value) => { // 配置的系统菜单
            state.sysMenuList = value
        },
        setGlobalTaskFlag: (state, value) => { // 设置 开启任务模式的标识
            state.openGlobalTask = value
        },
        setChapterTree(state, value) { // 存章节树
            state.chapterTree = value;
        },
        setChapterShowType(state, value) { // 章节显示类型
            state.chapterShowType = value;
        },
        setSelectedSceneNode(state, value) { // 存储已选中的场景列表（包含场景 流程和核算）
            state.selectedSceneNode = value;
        },
        setReviewList(state, value) { // 存储待批阅的学生列表
            state.reviewList = value;
        },
    },
    actions: {},
    plugins: [createPersistedState({
        // 你可以在此配置存储的方式，默认为 localStorage
        storage: window.sessionStorage,
    })]
});
export default store;