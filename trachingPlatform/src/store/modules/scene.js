const state = {
  scenes: [],
}

const mutations = {
  ADD_SCENE: (state, scene) => {
    if (state.scenes.some((v) => v.path === scene.path)) return
    state.scenes.push(
      Object.assign({}, scene, {
        title: scene.meta.title || 'no-name',
      })
    )
  },

  DEL_SCENE: (state, scene) => {
    for (const [i, v] of state.scenes.entries()) {
      if (v.path === scene.path) {
        state.scenes.splice(i, 1)
        break
      }
    }
  },

  UPDATE_SCENE: (state, scene) => {
    for (let v of state.scenes) {
      if (v.path === scene.path) {
        v = Object.assign(v, scene)
        break
      }
    }
  },
}

const actions = {
  addScene({ commit }, scene) {
    commit('ADD_SCENE', scene)
  },
  delScene({ commit }, scene) {
    commit('DEL_SCENE', scene)
  },
  updateScene({ commit }, scene) {
    commit('UPDATE_SCENE', scene)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
