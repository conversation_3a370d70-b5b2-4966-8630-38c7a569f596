import findLast from "lodash/findLast";
const store = {
  namespaced: true,
  state: {
    open: [],
    zIndex: 2000
  },
  getters: {
    active: state => (state.open.length > 0 ? state.open[0] : null),
    allOpend: state => state.open,
    zIndex: state => state.zIndex
  },
  mutations: {
    OPEN: (state, payload) => {
      return state.open.unshift(payload);
    },
    CLOSE: (state, payload) =>
      (state.open = state.open.filter(e => e !== payload)),
    UPDATE_INDEX: (state, payload) => (state.zIndex = payload.zIndex),
    GET_INDEX: (state, payload) => (state.zIndex)
  },
  actions: {
    open: ({ commit }, payload) => commit("OPEN", payload),
    close: ({ commit }, payload) => commit("CLOSE", payload),
    updateIndex: ({ commit }, payload) => commit("UPDATE_INDEX", payload),
    getIndex: ({ commit }, payload) => commit("GET_INDEX", payload),
  }
};

export default store;
