// 字体大小模块化混合器
@mixin font-size($font-size) {
  font-size: $font-size + px;
}

@for $size from 0 through 2000 {
  .size-#{$size} {
    @include font-size($size);
  }
  .h-#{$size} {
    height: $size + px;
  }
  .h-#{$size}-i {
    height: $size + px !important;
  }
  .min-h-#{$size} {
    min-height: $size + px;
  }
  .max-h-#{$size} {
    max-height: $size + px;
  }
  .h-p-#{$size} {
    height: $size * 1%
  }
  .h-#{$size}vh {
    height: $size + vh;
  }
  .h-#{$size}rem {
    height: $size *0.01 + rem;
  }
  .w-#{$size}-i {
    width: $size + px !important;
  }
  .w-#{$size} {
    width: $size + px;
  }
  .w-#{$size}rem {
    width: $size *0.01 + rem;
  }
  .i-w-#{$size} {
    width: $size + px !important;
  }
  .min-w-#{$size} {
    min-width: $size + px;
  }
  .max-w-#{$size} {
    max-width: $size + px;
  }
  .w-p-#{$size} {
    width: $size * 1%
  }
  .w-#{$size}vw {
    width: $size + vw;
  }
  .wh-#{$size} {
    width: $size + px;
    height: $size + px;
  }

  .m-l-#{$size} {
    margin-left: $size + px;
  }

  .m-r-#{$size} {
    margin-right: $size + px;
  }

  .m-t-#{$size} {
    margin-top: $size + px;
  }

  .m-b-#{$size} {
    margin-bottom: $size + px;
  }

  .m-x-#{$size} {
    margin-left: $size + px;
    margin-right: $size + px;
  }
  .m-x-#{$size}rem {
    margin-left: $size *0.01 + rem;
    margin-right: $size *0.01 + rem;
  }
  .m-y-#{$size} {
    margin-top: $size + px;
    margin-bottom: $size + px;
  }

  .m-#{$size} {
    margin: $size + px;
  }

  .p-l-#{$size} {
    padding-left: $size + px;
  }

  .p-r-#{$size} {
    padding-right: $size + px;
  }

  .p-t-#{$size} {
    padding-top: $size + px;
  }

  .p-b-#{$size} {
    padding-bottom: $size + px;
  }

  .p-x-#{$size} {
    padding-left: $size + px;
    padding-right: $size + px;
  }
  .p-x-p-#{$size} {
    padding-left: $size * 1%;
    padding-right: $size * 1%;
  }
  .p-l-p-#{$size} {
    padding-left: $size * 1%;
  }
  .p-r-p-#{$size} {
    padding-right: $size * 1%;
  }
  .p-y-#{$size} {
    padding-top: $size + px;
    padding-bottom: $size + px;
  }

  .p-#{$size} {
    padding: $size + px;
  }

  .b-t-l-#{$size} {
    border-top-left-radius: $size + px;
  }

  .b-t-r-#{$size} {
    border-top-right-radius: $size + px;
  }

  .b-b-l-#{$size} {
    border-bottom-left-radius: $size + px;
  }

  .b-b-r-#{$size} {
    border-bottom-right-radius: $size + px;
  }

  .b-t-#{$size} {
    border-top-left-radius: $size + px;
    border-top-right-radius: $size + px;
  }

  .b-b-#{$size} {
    border-bottom-left-radius: $size + px;
    border-bottom-right-radius: $size + px;
  }
  .b-#{$size} {
    border-radius: $size + px;
  }

  .line-#{$size} {
    line-height: $size + px;
  }
  .transform-rotate-#{$size} {
    transform: rotate($size + deg);
  }
  .t-#{$size} {
    top: $size + px;
  }
  .l-#{$size} {
    left: $size + px;
  }
  .r-#{$size} {
    right: $size + px;
  }
  .bottom-#{$size} {
    bottom: $size + px;
  }

  .border-#{$size} {
    border: $size + px solid #d8d8d8;
  }
  .border-b-#{$size} {
    border-bottom: $size + px solid #d8d8d8;
  }
  .border-t-#{$size} {
    border-top: $size + px solid #d8d8d8;
  }
  .border-l-#{$size} {
    border-left: $size + px solid #d8d8d8;
  }
  .border-r-#{$size} {
    border-right: $size + px solid #d8d8d8;
  }

  .weight-#{$size} {
    font-weight: $size;
  }
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.wh-full {
  height: 100%;
  width: 100%;
}
.min-wh-full {
  min-height: 100%;
  min-width: 100%;
}
.w-100vw {
  width: 100vw;
}

.h-100vh {
  height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-between,.justify-between {
  justify-content: space-between;
}

.flex-space-around {
  justify-content: space-around;
}

.flex-column-center {
  justify-content: center;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column-end {
  justify-content: end;
}

.flex-row-center,.items-center {
  display: flex;
  align-items: center;
}

.flex-row-end {
  align-items: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.bold {
  font-weight: bold;
}

.color-fff,.color-ffffff,.color-white {
  color: #ffffff;
}

.color-primary {
  color: #1890ff;
}

.bg-white {
  background: white;
}
.bg-black {
  background: black;
}
.bg-f9f9f9 {
  background: #f9f9f9;
}
.bg-f6f6f6 {
  background: #f6f6f6;
}
.bg-primary {
  background: #1890ff;
}
.overflow {
  overflow: auto;
}
.border {
  border: 1px solid #d8d8d8;
}
.b-b {
  border-bottom: 1px solid #d8d8d8;
}
.b-t {
  border-top: 1px solid #ebeef5;
}
.b-l {
  border-left: 1px solid #d8d8d8;
}
.b-r {
  border-right: 1px solid #d8d8d8;
}

.cursor {
  cursor: pointer;
}