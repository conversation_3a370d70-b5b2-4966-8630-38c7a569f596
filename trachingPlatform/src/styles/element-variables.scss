// /* 改变主题色变量 */
// /* theme color */
// $--color-primary: #0973F1;
// $--color-success: #13ce66;
// $--color-warning: #ffba00;
// $--color-danger: #EE6868;
// // $--color-info: #1E1E1E;
// $--border-color-base: #F0F0F0;
// /* Button
// -------------------------- */
// $--button-font-weight: 400;
// $--button-default-font-color: #50607C;
// $--button-default-border-color: #A7AEBD;
// // $--color-text-regular: #1f2d3d;
// $--border-color-light: #dfe4ed;
// $--border-color-lighter: #e6ebf5;
// /* Table
// -------------------------- */
// $--table-border: 1px solid transparent;
// $--table-header-font-color: #666;
// $--table-font-color: #333;
// $--table-header-background-color: #f2f3f5;
// $--table-fixed-box-shadow: 0 0 10px rgba(210, 210, 210, 0.5);
// $--table-current-row-background-color: #C1C1C1;
// /* Scrollbar
// --------------------------*/
// $--scrollbar-background-color: #C1C1C1;
// $--scrollbar-hover-background-color: #C1C1C1;
// /* 改变 icon 字体路径变量，必需 */
// $--font-path: '~element-ui/lib/theme-chalk/fonts';
// @import "~element-ui/packages/theme-chalk/src/index";
// :root {
//     /* el-input */
//     --input-color: #fff;
//     --input-borderColor: #043d3b;
//     --input-hover-borderColor: rgba(13, 228, 213, 1);
//     // el-select-dropdown
//     --select-dropdown-border-color: rgba(0, 214, 198, 1);
//     --select-dropdown-bgColor: rgba(0, 118, 122, 0.9);
//     --select-dropdown-item-color: rgba(255, 255, 255, 1);
//     --select-dropdown-item-selected-color: rgba(13, 228, 213, 1);
//     --select-dropdown-item-hover-bgColor: rgba(0, 195, 191, 1);
//     // el-cascader__dropdown
//     --cascader-dropdown-border-color: rgba(0, 214, 198, 1);
//     --cascader-dropdown-bgColor: rgba(0, 118, 122, 0.9);
//     --cascader-dropdown-node-color: #fff;
//     --cascader-dropdown-node-active-color: rgba(13, 228, 213, 1);
//     --cascader-dropdown-node-hover-bgColor: rgba(0, 195, 191, 1);
//     // el-checkbox
//     --checkbox-checked-color: rgba(13, 228, 213, 1);
//     --checkbox-checked-borderColor: rgba(13, 228, 213, 1);
//     --checkbox-checked-bgColor: rgba(13, 228, 213, 1);
//     --checkbox-indeterminate-borderColor: rgba(13, 228, 213, 1);
//     --checkbox-indeterminate-bgColor: rgba(13, 228, 213, 1);
//     // el-radio
//     --radio-borderColor: rgba(13, 228, 213, 1);
//     --radio-checked-color: rgba(13, 228, 213, 1);
//     --radio-checked-borderColor: rgba(13, 228, 213, 1);
//     --radio-checked-bgColor: rgba(13, 228, 213, 1);
//     // picker-panel
//     --picker-panel-bgColor: rgba(0, 118, 122, 0.9);
//     --picker-panel-border-color: rgba(0, 214, 198, 1);
//     --picker-panel-color: #fff;
//     --picker-panel-active-color: rgba(13, 228, 213, 1);
//     // el-date-editor
//     --date-editor-range-bgColor: rgba(0, 0, 0, .1);
//     --date-editor-range-borderColor: rgba(13, 228, 213, 1);
//     --date-editor-date-table-bgColor: rgba(13, 228, 213, 1);
//     --date-editor-date-table-hover-color: rgba(13, 228, 213, 1);
//     // el-loading-mask
//     --loading-mask-text-color: rgba(63, 255, 239, 1);
// }
//  :export {
//     theme: $--color-primary;
// }
// 设置全局UI 四个主色
// 主色调 主要按钮
$--color-primary: #2B66FF;
// 成功
$--color-success: #13ce66;
// 警告
$--color-warning: #F59337;
// 危险 删除按钮
$--color-danger: #FF0000;
// 备注色
$--color-info: #BBBBBB;
$--button-font-weight: 400;
// $--color-text-regular: #1f2d3d;
$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;
$--header-cnt-border: 1px solid #e4e7ed;
$--teaching-cnt-border: 1px solid #e4e7ed;
$--teaching-cnt-sm-border: 1px solid #e4e7ed;
$--table-border: 1px solid #e4e7ed;
$--main-border: 1px solid #e4e7ed;

/* icon font path, required */

$--font-path: '~element-ui/lib/theme-chalk/index.css';
// @import "~element-ui/packages/theme-chalk/src/index";
:export {
    theme: $--color-primary;
    url: '@/element-ui/lib/theme-chalk/index.css'
}

@keyframes refresh {
    from {
        transform: scale(1) rotate(0);
    }
    to {
        transform: scale(1) rotate(359deg);
    }
}