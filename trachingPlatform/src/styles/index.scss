@import "./variables.scss";
@import "./element-variables.scss";
@import "./mixin.scss";
@import "./element-ui.scss";
// @import "./teaching.scss";
body {
    font-size: 16px;
    height: 100%;
    overflow: hidden;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

html {
    height: 100%;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
}

#app {
    height: 100%;
    width: 100%;
    font-size: 16px;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

.no-padding {
    padding: 0px !important;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

th {
    font-weight: normal;
}

div:focus {
    outline: none;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.block {
    display: block;
}

.pointer {
    cursor: pointer;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: " ";
        clear: both;
        height: 0;
    }
}


/* tooltip三角箭头部分 */

.el-tooltip__popper .popper__arrow {
    /* 上方箭头 */
    border-top-color: #fff !important;
    /* 下方箭头 */
    border-bottom-color: #fff !important;
}

.el-tooltip__popper .popper__arrow:after {
    border-top-color: #fff !important;
    border-bottom-color: #fff !important;
}


/* tooltip主体部分 */

.el-tooltip__popper {
    // padding:0 !important;
    // border-radius: 4px !important;
    max-width: 500px;
    border-color: #e6e6e6 !important;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.13) !important;
}

// 公共滚动条样式
::-webkit-scrollbar-track-piece {
    border: 2px solid #f2f2f2;
    background-color: #f2f2f2;
}

::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 20px;
    border: 1px solid #f2f2f2;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: var(--theme_bg_color);
    color: #fff;
}

.el-tabs__item:hover {
    color: var(--theme_font_color);
}

.el-tabs__item.is-active {
    color: var(--theme_font_color);
}

.el-tabs__active-bar {
    background-color: var(--theme_bg_color);
}

.form-designer-drawer {
    .colStyle {
        padding: 0px;
        height: 100%;
        border: 1px solid #e0e0e0;
        overflow-x: hidden;
        overflow-y: hidden;
    }
    .el-form-item__content {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    .el-drawer__header {
        margin-bottom: 16px;
    }
}

.full-width-form {
    .el-form-item__content {
        flex: 1;
    }
    .el-select,
    .el-cascader,
    .el-input {
        width: 100%;
    }
}

.base-dispatch-dialog {
    .content-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        .el-tabs,
        .el-tab-pane,
        .el-tabs__content,
        .dlg-dispatch-content-wrapper,
        .dispatch-page,
        .dispatch-content-wrapper,
        .dispatch-table-preview,
        .base-table {
            height: 100%;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
        }
    }
}

.flow-content-dialog {
    .content-wrapper,
    .flow-table-preview,
    .base-table {
        height: 100%;
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }
}

.splitpanes {
    .splitpanes__pane {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    &.splitpanes--vertical>.splitpanes__splitter {
        width: 7px;
        border-right: 1px solid #eee;
        border-left: 1px solid #eee;
        margin-left: -1px;
        background-color: #fff;
        box-sizing: border-box;
        position: relative;
        flex-shrink: 0;
        cursor: col-resize;
        &:before {
            transform: translateY(-50%);
            width: 1px;
            height: 30px;
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #00000026;
            transition: background-color 0.3s;
            margin-left: -2px;
        }
        &:after {
            margin-left: 1px;
            transform: translateY(-50%);
            width: 1px;
            height: 30px;
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #00000026;
            transition: background-color 0.3s;
        }
    }
    &.splitpanes--horizontal>.splitpanes__splitter {
        height: 7px;
        border-top: 1px solid #eee;
        margin-top: -1px;
        background-color: #fff;
        box-sizing: border-box;
        position: relative;
        flex-shrink: 0;
        cursor: row-resize;
        &:before {
            transform: translate(-50%);
            width: 30px;
            height: 1px;
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #00000026;
            transition: background-color .3s;
        }
        &:after {
            margin-top: 1px;
            transform: translate(-50%);
            width: 30px;
            height: 1px;
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            background-color: #00000026;
            transition: background-color .3s;
        }
    }
}

.boxShaow {
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.08);
}

.fabric-tool-clicked {
    .canvasLayout {
        z-index: 2000;
    }
    .blackboard-tool-box {
        z-index: 9999;
    }
}

@media screen and (min-width:3100px) {
    body {
        transform: scale(2);
        overflow: auto;
        zoom: 2;
    }
}

@media screen and (max-width:1930px) {
    body {
        font-size: 16px;
    }
}

.el-popup-parent--hidden {
    padding-right: 0 !important;
}

.w-e-full-screen-container {
    z-index: 999;
}

.del-btn.el-button--text {
    color: #333;
    &:hover {
        color: #F11B1B;
    }
}

.default-btn.el-button--text {
    color: #333;
    &:hover {
        color: #0070FC;
    }
}