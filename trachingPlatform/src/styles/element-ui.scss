@import '@/styles/element-variables.scss';
// cover some element-ui styles

/* width */

::-webkit-scrollbar {
    width: 10px;
}


/* Track */

::-webkit-scrollbar-track {
    background: #f1f1f1;
}


/* Handle */

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 5px;
}


/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.el-input__inner {
    border: $--main-border;
}

.base-form {
    // el-input-number (controls-position="right")
    .el-input-number.is-controls-right {
        .el-input-number__decrease {
            display: none;
        }
        .el-input-number__increase {
            display: none;
            top: 2px; // fix style bug
        }
        &:hover {
            .el-input-number__decrease {
                display: inline-block;
            }
            .el-input-number__increase {
                display: inline-block;
            }
        }
        .el-input__inner {
            text-align: left;
            padding-left: 5px;
            padding-right: 40px;
        }
    }
    // el-date-picker datetimerange
    .el-date-editor.el-date-editor--datetimerange {
        .el-range-separator {
            width: 24px;
            color: #999;
            padding: 0;
        }
        .el-range__icon {
            margin-left: 0;
        }
        &.el-input__inner {
            vertical-align: middle;
            padding: 3px 5px;
        }
        &.el-range-editor--medium {
            width: 380px;
            .el-range-separator {
                line-height: 30px;
            }
        }
        &.el-range-editor--mini {
            width: 330px;
            .el-range-separator {
                line-height: 22px;
            }
        }
    }
    // el-date-picker  not datetimerange
    .el-date-editor {
        .el-input__prefix {
            left: 0;
            top: 1px;
        }
        .el-input__suffix {
            right: 0;
            top: 1px;
        }
        .el-input__inner {
            padding: 0 25px;
        }
        &.el-input--mini {
            width: 175px;
        }
        &.el-input--medium {
            width: 195px;
        }
    }
    // input padding
    .el-input__inner {
        padding: 0 5px;
    }
}

.input-with-select {
    .el-input__suffix {
        display: flex;
        align-items: center;
        .el-input__suffix-inner {
            position: relative;
            .el-icon-circle-close:before {
                position: absolute;
                top: 1px;
                right: 5px;
                content: "\e79d" !important;
                font-size: 18px;
            }
        }
    }
}

.el-table__cell .cell .el-button--text {
    padding-top: 0;
    padding-bottom: 0;
}