@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin text-ellipsis($w, $display: block) {
  display: $display;
  max-width: $w;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  word-wrap: normal;
}

// 背景色动画
@mixin animation-bg-color-mixin($name, $color-var) {
  @keyframes #{$name} {
    50% {
      background-color: $color-var;
    }
  }
}

@mixin animation-text-color-mixin($name, $color-var) {
  @keyframes #{$name} {
    50% {
      color: $color-var;
    }
  }
}

// @include animation-bg-color-mixin(to-yellow, yellow);
// animation: to-yellow 4s ease infinite;

@mixin content-box-shadow() {
  border-radius: 6px;
  box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
}
// item hover 阴影
@mixin content-item-with-hover($bgColor: #fff) {
  border: 1px solid #e4e7ed;
  background-color: $bgColor;

  &:hover {
    box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
    background-color: #f6f8fa;
  }
}

// 边框hover阴影
@mixin hover-border-animation($color: #2B66FF, $height: 32px, $size: 2px) {
  position: relative;

  &:before {
    width: 0;
    height: $size;
    transition: width 0.0375s;
    transition-delay: 0.1125s;
    top: 0;
    left: 0;
    content: "";
    position: absolute;
    background: $color;
  }

  &:after {
    width: 0;
    height: $size;
    transition: width 0.0375s;
    transition-delay: 0.0375s;
    bottom: 0;
    right: 0;
    content: "";
    position: absolute;
    background: $color;
  }

  div {
    &:before {
      height: 0;
      width: $size;
      transition: height 0.0375s;
      transition-delay: 0s;
      bottom: 0;
      left: 0;
      content: "";
      position: absolute;
      background: $color;
    }

    &:after {
      height: 0;
      width: $size;
      transition: height 0.0375s;
      transition-delay: 0.075s;
      top: 0;
      right: 0;
      content: "";
      position: absolute;
      background: $color;
    }
  }

  &:hover {
    &:before {
      transition-delay: 0s;
      width: 100%;
    }

    div {
      &:before {
        transition-delay: 0.1125s;
        height: $height;
      }

      &:after {
        transition-delay: 0.0375s;
        height: $height;
      }
    }

    &:after {
      transition-delay: 0.075s;
      width: 100%;
    }
  }
}

@mixin sidebar-item-with-hover($h: 40px, $border-color: #2b66ff, $bg-color: #f3f8ff) {
  height: $h;
  font-size: 14px;

  &:hover {
    height: $h;
    border-left: 2px solid $border-color;
    background-color: $bg-color;
  }

  &.active {
    border-left: 2px solid $border-color;
    background-color: $bg-color;
    color: #2b66ff;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  }

  @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }

  @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  }

  @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}