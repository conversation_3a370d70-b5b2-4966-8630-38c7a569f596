import Vue from 'vue';
// a modern alternative to CSS resets
import "./styles/normalize.css"; // a modern alternative to CSS resets
// global css
import "@/styles/index.scss";
import "@/styles/reset.css";
import "@/styles/baseClass.scss";
import "@/styles/theme-ui.css";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import "uno.css";

Vue.use(ElementUI);
// import * as echarts from 'echarts';
import "@/styles/button.css"; // 自定义按钮
import App from "./App.vue";
import store from "./store";
import axios from "axios";
import api from "@/api";
import VueRouter from "vue-router";
import router from "./router";
import utils from "./utils/index";
import token from "./utils/token";
import bignumber from "./utils/bignumber";
import formatData from "./utils/format-data";
import formatDate from "./utils/format-date";
import lazyLoading from "./utils/directive";
import loading from "@/components/zd-second-dialog/zdDialog.js";
import "./utils/directives.js";
import { Message } from "element-ui";

import Toast from "./plugins/toast.js"; //自定义提示框
Vue.prototype.$toast = Toast;
// import VueClipBoard from "vue-clipboard2";
// Vue.use(VueClipBoard);
import JsonViewer from "vue-json-viewer";
Vue.use(JsonViewer);
import eventBus from "./utils/eventBus.js";
Vue.prototype.$eventBus = eventBus;
Vue.prototype.$zdDialog = loading;

function isPcChrome() {
    return (
        navigator.userAgent.indexOf("Chrome") !== -1 &&
        navigator.userAgent.indexOf("Safari") !== -1 &&
        navigator.userAgent.indexOf("Edg") === -1
    );
}

function isMobile() {
    const flag =
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
        );
    return flag;
}

// if (!isMobile() && !isPcChrome()) {
//     Message({
//         showClose: true,
//         duration: 0,
//         dangerouslyUseHTMLString: true,
//         message: `<div>
//         为了获得最佳用户体验，建议使用
//         <a
//           style="text-decoration: underline;padding-right: 20px;"
//           href="https://www.google.cn/chrome/"
//           target="_blank">
//           Google Chrome浏览器
//         </a>
//       </div>`,
//         type: "warning"
//     });
// }

const degrade =
    window.localStorage.getItem("degrade") === "true" ||
    !window.Proxy ||
    !window.CustomElementRegistry;
const props = {
    jump: (name) => {
        router.push({ name });
    },
};

import draggable from "vuedraggable"; //vue拖拽组件
Vue.component("draggable", draggable); // 注册全局组件

import amountInput from "./components/base/amount-input.vue";
Vue.component("amount-input", amountInput);
import amountSpan from "./components/base/amount-span.vue";
Vue.component("amount-span", amountSpan);
import BaseDialog from "@/components/base/dialog.vue";
Vue.component("base-dialog", BaseDialog); // 弹窗
import Table2 from "@/components/base/table2/index.vue";
Vue.component("table2", Table2); // 弹窗
import zdDefaultPage from "@/components/zd-default-page/index.vue";
Vue.component("zdDefaultPage", zdDefaultPage); // 全局缺省页

import cloneDeep from "lodash/cloneDeep";
import state from "@/constants/state";

// 富文本全局引入
import VueTinymce from "@packy-tang/vue-tinymce"; // tinymce
// Vue.prototype.$tinymce = tinymce; // 将全局tinymce对象指向给Vue作用域下
Vue.use(VueTinymce); // 安装vue的tinymce组件

import SearchBar from "search-bar-vue2";
Vue.use(SearchBar);

import layer from "vue-layer";
import "vue-layer/lib/vue-layer.css";
Vue.config.productionTip = true;
Vue.use(lazyLoading);
Vue.use(VueRouter);
// const size =
//     window.screen.width >= 1900 ?
//     "medium" :
//     window.screen.width >= 1400 ?
//     "small" :
//     "mini";
// Vue.use(ELEMENT, { size: size });
Vue.prototype.msgModule = function(message, type) {
    this.$message({
        showClose: true,
        message,
        type,
        duration: 3000,
    });
};
Vue.directive("focus", {
    inserted: function(el) {
        el.querySelector("input").focus();
        el.querySelector("input").select();
    },
});

import fabric from "fabric"; //引入fabric画布库
Vue.use(fabric);

// token解析
const jwt = require("jsonwebtoken");

import "@/permission";

// 需要挂载到Vue原型上
Object.defineProperties(Vue.prototype, {
    $utils: {
        value: utils,
    },
    $bignumber: {
        value: bignumber,
    },
    $formatData: {
        value: formatData,
    },
    $formatDate: {
        value: formatDate,
    },
    $jwt: {
        // token解析
        value: jwt,
    },
    $axios: {
        value: axios,
    },
    $layer: {
        value: layer(Vue),
    },
    $api: {
        value: api,
    },
    $local: {
        value: token,
    },
});
window.$vue = new Vue({
    router,
    store,
    render: (h) => h(App),
}).$mount("#app");
