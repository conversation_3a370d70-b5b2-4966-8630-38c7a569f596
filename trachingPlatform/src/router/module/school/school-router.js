import IconList from "@/components/iconList/iconList.vue";

export default [{
    path: "/school",
    name: "School",
    component: () =>
        import (
            /* webpackChunkName: "school" */
            "@/views/schoolPage/index.vue"
        ),
    redirect: "/school/workBranch",
    meta: {
        title: "学校端",
    },
    children: [{
            path: "/school/workBranch",
            name: "wrokBranch",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/workBranch.vue"
                ),
            meta: {
                title: "工作台",
                type: '',
            },
        },
        {
            path: "/school/workFlow",
            name: "workFlow",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/workFlow.vue"
                ),
            meta: {
                title: "工作台",
                type: '',
            },
        },
        {
            path: "/school/collegeManage",
            name: "collegeManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/collegeManage/index.vue"
                ),
            meta: {
                title: "学院管理",
                type: 'base',
                icon: 'icon-xuexiao',
            },
        },
        {
            path: "/school/professionalManage",
            name: "professionalManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/professionalManage/index.vue"
                ),
            meta: {
                title: "专业管理",
                type: 'base',
                icon: 'icon-zhuanye',
            },
        },
        {
            path: "/school/teacherManage",
            name: "teacherManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/teacherManage/index.vue"
                ),
            meta: {
                title: "教师管理",
                type: 'base',
                icon: 'icon-jiaoshi',
            },
        },
        {
            path: "/school/classManage",
            name: "classManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/classManage/index.vue"
                ),
            meta: {
                title: "班级管理",
                type: 'base',
                icon: 'icon-wodebanji',
            }
        },

        {
            path: "/school/studentManage",
            name: "studentManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/studentManage/index.vue"
                ),
            meta: {
                title: "学生管理",
                type: 'base',
                icon: 'icon-xunjianweibao',
            },
        },
        {
            path: "/school/textbookManage",
            name: "textbookManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/textbookManage/index.vue"
                ),
            meta: {
                title: "教材管理",
                type: 'resourcece',
                icon: 'icon-shuzijiaocai',
            }
        },
        {
            path: "/school/courseManage",
            name: "courseManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/courseManage/index.vue"
                ),
            meta: {
                title: "课程管理",
                type: 'resourcece',
                icon: 'icon-kecheng',
            }
        },

        {
            path: "/school/userManage",
            name: "userManage",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/userManage/index.vue"
                ),
            meta: {
                title: "用户管理",
                type: 'account',
                icon: 'icon-yonghu2',
            }
        },
        {
            path: "/school/approvalProcess",
            name: "approvalProcess",
            component: () =>
                import (
                    /* webpackChunkName: "school" */
                    "@/views/schoolPage/workflow/index.vue"
                ),
            meta: {
                title: "审批流程",
                type: 'account',
                icon: 'icon-shenpi',
            }
        }
    ]
}, ];