export default [{
    path: "/teacher",
    name: "Teacher",
    component: () =>
        import (
            /* webpackChunkName: "teacher" */
            "@/views/teacherPage/index.vue"
        ),
    redirect: "/teacher/teacherHome",
    meta: {
        title: "教师端",
    },
    children: [{
        path: "/teacher/teacherHome",
        name: "TeacherHome",
        component: () =>
            import (
                /* webpackChunkName: "teacherHome" */
                "@/views/teacherPage/teacherHome/index.vue"
            ),
        meta: {
            title: "工作台",
            icon: 'icon-riFill-home-6-fill',
            type: 'show',
        },
    }, {
        path: "/teacher/workFlow",
        name: "workFlow",
        component: () =>
            import (
                /* webpackChunkName: "teacherHome" */
                "@/views/teacherPage/teacherHome/workFlow.vue"
            ),
        meta: {
            title: "审批界面",
            icon: 'icon-riFill-home-6-fill',
            type: 'noShow',
        },
    }, {
        path: "/teacher/teacherCourse",
        name: "TeacherCourse",
        component: () =>
            import (
                /* webpackChunkName: "teacherCourse" */
                "@/views/teacherPage/teacherCourse/index.vue"
            ),
        meta: {
            title: "我的课程",
            icon: 'icon-kecheng',
            type: 'show',
        },
    }, {
        path: "/teacher/teacherCourseDetail",
        name: "TeacherCourseDetail",
        component: () =>
            import (
                /* webpackChunkName: "teacherCourseDetail" */
                "@/views/teacherPage/teacherCourse/courseDetail.vue"
            ),
        meta: {
            title: "课程详情",
            icon: '',
            type: 'noShow',
        },
        // }, {
        //     path: "teacherCourseDetailEdit",
        //     name: "TeacherCourseDetailEdit",
        //     component: () =>
        //         import (
        //             /* webpackChunkName: "teacherCourseDetailEdit" */
        //             "@/views/teacherPage/teacherCourseDetailEdit.vue"
        //         ),
        //     meta: {
        //         title: "课程详情编辑",
        //         icon: '',
        //         type: '',
        //     },
    }, {
        path: '/teacher/teacherTextBook',
        name: 'TeacherTextBook',
        component: () =>
            import ('@/views/teacherPage/teacherTextBook/index.vue'),
        meta: {
            title: '我的教材',
            icon: 'icon-shuzijiaocai',
            type: 'show',
        }
    }, {
        path: '/teacher/Ai',
        name: 'TeacherAi',
        component: () =>
            import ('@/views/teacherPage/teacherAi/index.vue'),
        meta: {
            title: '智能助手',
            icon: 'icon-kefu-out',
            type: 'show',
        }
    }, {
        path: '/teacher/teacherClass',
        name: 'TeacherClass',
        component: () =>
            import ('@/views/teacherPage/teacherClass/index.vue'),
        meta: {
            title: '班级管理',
            icon: 'icon-zuoye',
            type: 'show',
        }
    }, {
        path: '/teacher/textbook/chapter/:id',
        name: 'TeacherTextbookChapterManage',
        component: () =>
            import ('@/views/teacherPage/teacherTextBook/chapterManage.vue'),
        meta: {
            title: '章节管理',
            icon: '',
            type: 'noShow',
        }
    }, ]
}, ];