export default [{
    path: "/platForm",
    name: "platForm",
    redirect: "/platForm/workBranch",
    component: () =>
        import (
            /* webpackChunkName: "platForm" */
            "@/views/platFormPage/index.vue"
        ),
    meta: {
        title: "平台管理",
    },
    children: [{
            path: "/platForm/workBranch",
            name: "workBranch",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/workBranch/index.vue"
                ),
            meta: {
                title: "工作台",
                type: '',
            }
        },
        {
            path: "/platForm/schoolManage",
            name: "schoolManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/schoolManage/index.vue"
                ),
            meta: {
                title: "学校管理",
                type: 'base',
                icon: 'icon-xuexiao',
            },
            children: [{
                path: "detail",
                name: "schoolDetail",
                component: () =>
                    import (
                        /* webpackChunkName: "platForm" */
                        "@/views/platFormPage/schoolManage/detail.vue"
                    ),
                meta: {
                    title: "学校详情",
                    type: 'base',
                    icon: 'icon-xuexiao',
                }
            }]
        },
        {
            path: "/platForm/collegeManage",
            name: "collegeManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/collegeManage/index.vue"
                ),
            meta: {
                title: "院系管理",
                type: 'base_child',
                icon: 'icon-xuexiao',
            }
        },
        // 专业管理
        {
            path: "/platForm/professionalManage",
            name: "professionalManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/professionalManage/index.vue"
                ),
            meta: {
                title: "专业管理",
                type: 'base_child',
                icon: 'icon-zhuanye ',
            }
        },
        // 班级管理
        {
            path: "/platForm/classManage",
            name: "classManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/classManage/index.vue"
                ),
            meta: {
                title: "班级管理",
                type: 'base_child',
                icon: 'icon-wodebanji',
            }
        },
        // 教师管理
        {
            path: "/platForm/teacherManage",
            name: "teacherManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/teacherManage/index.vue"
                ),
            meta: {
                title: "教师管理",
                type: 'base_child',
                icon: 'icon-jiaoshi',
            }
        },
        // 学生管理
        {
            path: "/platForm/studentManage",
            name: "studentManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/studentManage/index.vue"
                ),
            meta: {
                title: "学生管理",
                type: 'base_child',
                icon: 'icon-xunjianweibao',
            }
        },

        // 课程管理
        {
            path: "/platForm/courseManage",
            name: "courseManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/courseManage/index.vue"
                ),
            meta: {
                title: "课程管理",
                type: 'resource',
                icon: 'icon-kecheng',
            }
        },
        // 教材管理
        {
            path: "/platForm/textbookManage",
            name: "textbookManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/textbookManage/index.vue"
                ),
            meta: {
                title: "教材管理",
                type: 'resource',
                icon: 'icon-kecheng',
            }
        },
        // 用户管理
        {
            path: "/platForm/userManage",
            name: "userManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/userManage/index.vue"
                ),
            meta: {
                title: "用户管理",
                type: 'account',
                icon: 'icon-kecheng',
            }
        },
        // 权限管理
        {
            path: "/platForm/permissionManage",
            name: "permissionManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/permissionManage/index.vue"
                ),
            meta: {
                title: "权限管理",
                type: 'account',
                icon: 'icon-kecheng'
            }
        },
        // 专业大类管理
        {
            path: "/platForm/majorManage",
            name: "majorManage",
            component: () =>
                import (
                    /* webpackChunkName: "platForm" */
                    "@/views/platFormPage/majorManage/index.vue"
                ),
            meta: {
                title: "专业大类管理",
                type: 'account',
                icon: 'icon-zhuanye',
            }
        },
        // 系统配置
        // {
        //     path: "/platForm/systemConfig",
        //     name: "systemConfig",
        //     component: () =>
        //         import (
        //             "@/views/platFormPage/systemConfig/index.vue"
        //         ),
        //     meta: {
        //         title: "系统配置",
        //         type: 'system',
        //         icon: 'icon-xitongpeizhi',
        //     }
        // },
        // 数据模块
        // {
        //     path: "/platForm/dataModule",
        //     name: "dataModule",
        //     component: () =>
        //         import (
        //             "@/views/platFormPage/dataModule/index.vue"
        //         ),
        //     meta: {
        //         title: "数据模块",
        //         type: 'system',
        //         icon: 'icon-shujujiedian',
        //     }
        // }
    ]
}, {
    path: "/schoolManage/detail",
    name: "schoolDetail",
    component: () =>
        import (
            /* webpackChunkName: "platForm" */
            "@/views/platFormPage/schoolManage/detail.vue"
        ),
    meta: {
        title: "学校详情",
        type: 'base_child',
        icon: 'icon-xuexiao',
    }
}]