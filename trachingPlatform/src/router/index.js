import Vue from 'vue';
import VueRouter from "vue-router";
import token from "@/utils/token";

Vue.use(VueRouter);
// 不需要登录的页面
// 公共路由
import commonRouter from "./module/public/public-router.js";
import teacheRouter from "./module/teacher/teacher-router.js";
import studentRouter from "./module/student/student-router.js";
import schoolRouter from "./module/school/school-router.js";
import platFormRouter from "./module/platForm/platForm-router.js";
// 学生端路由放这里
const teacher = [...teacheRouter]; // 教师端路由放这里
const student = [...studentRouter]; // 学生端路由放这里
const school = [...schoolRouter]; // 学校端路由放这里
const publicRouter = [...commonRouter]; // 公共路由放这里
const platForm = [...platFormRouter]; // 平台路由放这里




const routers = [{
        path: "/",
        redirect: "/login",
    },
    ...teacher,
    ...student,
    ...school,
    ...publicRouter,
    ...platForm,
];
const createRouter = () =>
    new VueRouter({
        mode: "hash",
        base: process.env.BASE_URL,
        scrollBehavior: () => ({
            y: 0
        }),
        routes: [...routers]
    });
const router = createRouter();
// 根据登录Userinfo动态加载路由
export function dispatchRouters(userType) {

}
// 清除除login以外的路由
export function resetRouters(val = false) {
    const newRouter = new VueRouter();
    router.matcher = newRouter.matcher;
    loginRouter.forEach(rt => {
        router.addRoute(rt);
    });
    if (!val) {
        token.clearLoginInfo();
    }
}

// 避免路由重复的报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
};
const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function push(location) {
    return originalReplace.call(this, location).catch(err => err);
};
export default router;