import router from "./router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import getPageTitle from "@/utils/get-page-title";
import token from "@/utils/token.js";
import { verifyKey, doSuccess } from "@/utils/doTokenSuccess";
import api from "@/api";
import { NextLoading } from '@/utils/loading.js';
import store from "./store";
import { difference } from "@/utils/common.js"
NProgress.configure({ showSpinner: false });
// 路由白名单
const whiteList = ["/login", "/jc"];

router.beforeEach(async(to, from, next) => {
    // 白名单
    if (whiteList.includes(to.path)) {
        next();
        return;
    }
    const tk = token.getToken();

    next();
});

router.afterEach(() => {
    NextLoading.done(100);
    NProgress.done();
});