import request from "@/utils/request";
/**
 * 学生管理
 */
const studentApi = {
    // 获取学生列表
    GetStudentList(params) {
        return request.send("/Student/GetStudentList", params, "GET");
    },
    // 获取学生详情
    GetStudentDetail(params) {
        return request.send("/Student/GetStudentDetail", params, "GET");
    },
    // 新增学生
    CreateStudent(params) {
        return request.send("/Student/CreateStudent", params, "POST");
    },
    // 编辑学生
    UpdateStudent(params) {
        return request.send(`/Student/UpdateStudent?id=${params.id}`, params, "POST");
    },
    // 删除学生
    DeleteStudent(params) {
        return request.send("/Student/DeleteStudent", params, "DELETE");
    },
};
export default studentApi