import request from "@/utils/request";
/**
 * 教材管理
 */
const textbookApi = {
    // 获取教材列表
    GetTextbookList(params) {
        return request.send("/Textbook/GetTextbookList", params, "GET");
    },
    // 获取教材详情
    GetTextbookDetail(params) {
        return request.send("/Textbook/GetTextbookDetail", params, "GET");
    },
    // 新增教材
    CreateTextbook(params) {
        return request.send("/Textbook/CreateTextbook", params, "POST");
    },
    // 编辑教材
    UpdateTextbook(params) {
        return request.send(`/Textbook/UpdateTextbook?id=${params.id}`, params, "POST");
    },
    // 删除教材
    DeleteTextbook(params) {
        return request.send("/Textbook/DeleteTextbook", params, "DELETE");
    },
    // 获取我(教师)主编或者协作的教材列表
    GetMyTextbookList(params) {
        return request.send("/Textbook/GetMyTextbookList", params, "GET");
    },
};
export default textbookApi