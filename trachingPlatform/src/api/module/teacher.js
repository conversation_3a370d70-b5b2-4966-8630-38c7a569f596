import request from "@/utils/request";
/**
 * 教师管理
 */
const teacherApi = {
    // 获取教师列表
    GetTeacherList(params) {
        return request.send("/Teacher/GetTeacherList", params, "GET");
    },
    // 获取教师详情
    GetTeacherDetail(params) {
        return request.send("/Teacher/GetTeacherDetail", params, "GET");
    },
    // 新增教师
    CreateTeacher(params) {
        return request.send("/Teacher/CreateTeacher", params, "POST");
    },
    // 编辑教师
    UpdateTeacher(params) {
        return request.send(`/Teacher/UpdateTeacher?id=${params.id}`, params, "POST");
    },
    // 删除教师
    DeleteTeacher(params) {
        return request.send("/Teacher/DeleteTeacher", params, "DELETE");
    },
    // 账号的启用和停用
    EnableTeacher(params) {
        return request.send("/Teacher/EnableTeacher", params, "POST");
    },
    // 教师端统计
    GetTeacherMyStatisticsResponse(params) {
        return request.send("/Teacher/GetTeacherMyStatisticsResponse", params, "GET");
    },


};
export default teacherApi