// 
import request from "@/utils/request";
/**
 * 任务管理
 */
const taskApi = {
    // 获取任务列表
    TaskGetList(params) {
        return request.send("/Task/GetList", params, "GET");
    },
    // 获取单个任务信息
    TaskGetById(params) {
        return request.send("/Task/GetById", params, "GET");
    },
    // 创建单个任务
    CreateTask(params) {
        return request.send("/Task/Create", params, "POST");
    },
    // 更新任务
    UpdateTask(params) {
        return request.send(`/Task/Update?id=${params.id}`, params, "POST");
    },
    // 删除任务
    DeleteTask(params) {
        return request.send("/Task/Delete", params, "DELETE");
    },
};
export default taskApi