import request from "@/utils/request";
/**
 * 班级管理
 */
const classApi = {
    // 获取班级列表
    GetClassList(params) {
        return request.send("/Class/GetClassList", params, "GET");
    },
    // 获取班级详情
    GetClassDetail(params) {
        return request.send("/Class/GetClassDetail", params, "GET");
    },
    // 新增班级
    CreateClass(params) {
        return request.send("/Class/CreateClass", params, "POST");
    },
    // 编辑班级
    UpdateClass(params) {
        return request.send(`/Class/UpdateClass?id=${params.id}`, params, "POST");
    },
    // 删除班级
    DeleteClass(params) {
        return request.send("/Class/DeleteClass/DeleteClass", params, "DELETE");
    },
    //  教师端 我的班级列表
    GetMyTeachClassList(params) {
        return request.send("/Class/GetMyTeachClassList", params, "GET");
    },


};
export default classApi