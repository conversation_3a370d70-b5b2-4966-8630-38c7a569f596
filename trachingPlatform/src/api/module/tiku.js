// 
import request from "@/utils/request";
/**
 * 学校管理
 */
const tiKuA<PERSON> = {
    // 获取题库列表
    GetList(params) {
        return request.send("/Question/GetList", params, "GET");
    },
    // 获取单个题目信息
    GetById(params) {
        return request.send("/Question/GetById", params, "GET");
    },
    // 创建单个题目
    CreateQuestion(params) {
        return request.send("/Question/CreateQuestion", params, "POST");
    },
    // 批量导入题目
    BatchCreate(params) {
        return request.send("/Question/BatchCreate", params, "POST");
    },
    // 更新题目
    UpdateQuestion(params) {
        return request.send(`/Question/UpdateQuestion?id=${params.id}`, params, "POST");
    },
    // 删除题目
    DeleteQuestion(params) {
        return request.send("/Question/DeleteQuestion", params, "DELETE");
    },
    // 批量删除题目
    BatchDelete(params) {
        return request.send("/Question/BatchDelete", params, "DELETE");
    },
};
export default tiKuApi