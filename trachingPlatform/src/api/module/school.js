// 
import request from "@/utils/request";
/**
 * 学校管理
 */
const schoolApi = {
    // 新增学校
    SchoolCreate(params) {
        return request.send("/School/Create", params, "POST");
    },
    // 更新学校
    SchoolUpdate(params) {
        return request.send(`/School/Update?id=${params.id}`, params, "POST");
    },
    // 设置学校配额
    SchoolSetQuota(params) {
        return request.send(`/School/SetQuota`, params, "POST");
    },
    // 删除学校
    SchoolDelete(params) {
        return request.send("/School/Delete", params, "DELETE");
    },
    // 教务统计
    SchoolBasicStatistic(params) {
        return request.send("/School/SchoolBasicStatistic", params, "GET");
    },
    // 获取学校
    SchoolGetList(params) {
        return request.send("/School/GetList", params, "GET");
    },


};
export default schoolApi