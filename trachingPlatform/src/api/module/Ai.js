// 
import request from "@/utils/request";
/**
 * Ai 智能体
 */
const AiApi = {
    // 智能体 对话
    ChatStream(params) {
        return request.send("/AIAgent/ChatStream", params, "POST");
    },
    // 获取会话
    ChatSessionList(params) {
        return request.send("/AIAgent/ChatSessionList", params, "get");
    },
    // 删除会话
    ChatSessionDelete(params) {
        return request.send("/AIAgent/ChatSessionDelete", params, "POST");
    },
    // 编辑会话标题
    ChatSessionEditTitle(params) {
        return request.send("/AIAgent/ChatSessionEditTitle", params, "POST");
    },
    // 通过id获取会话消息
    ChatSessionGetMessagesByChatId(params) {
        return request.send("/AIAgent/ChatSessionGetMessagesByChatId", params, "get");
    },
};
export default AiApi