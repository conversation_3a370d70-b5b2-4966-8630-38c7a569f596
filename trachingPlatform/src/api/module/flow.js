import request from "@/utils/request";
/**
 * 专业管理
 */
const flowApi = {
    // 获取流程模板列表
    GetWorkflowTemplates(params) {
        return request.send("/WorkFlow/GetWorkflowTemplates", params, "GET");
    },
    // 获取模板详情
    GetWorkflowTemplate(params) {
        return request.send("/WorkFlow/GetWorkflowTemplate", params, "GET");
    },
    // 新增模板
    CreateWorkflowTemplate(params) {
        return request.send("/WorkFlow/CreateWorkflowTemplate", params, "POST");
    },
    // 编辑模板
    UpdateWorkflowTemplate(params) {
        return request.send(`/WorkFlow/UpdateWorkflowTemplate?id=${params.id}`, params, "POST");
    },
    // 删除模板
    DeleteWFTemplate(params) {
        return request.send(`/WorkFlow/DeleteWFTemplate?templateId=${params.templateId}&schoolId=${params.schoolId}`, params, "POST");
    },
    // 获取我的待办
    GetTodoList(params) {
        return request.send("/WorkFlow/GetTodoList", params, "GET");
    },
    // 获取我的已经审批的流程
    GetWorkflowInstancesApprovedByMe(params) {
        return request.send("/WorkFlow/GetWorkflowInstancesApprovedByMe", params, "GET");
    },
    // 获取我创建的审批流
    GetWorkflowInstanceOfMine(params) {
        return request.send("/WorkFlow/GetWorkflowInstanceOfMine", params, "GET");
    },



    //  发起审批
    StartWorkflow(params) {
        return request.send("/WorkFlow/StartWorkflow", params, "POST");
    },
    // 流程实例 -通过 instanceld 获取工作流实例详情
    GetWorkflowInstanceByInstanceId(params) {
        return request.send("/WorkFlow/GetWorkflowInstanceByInstanceId", params, "GET");
    },
    // 流程实例-通过 业务数据id 获取该业务最新的一次工作流实例详情
    GetLatestWorkflowInstanceFullByBizId(params) {
        return request.send("/WorkFlow/GetLatestWorkflowInstanceFullByBizId", params, "GET");
    },
    // 流程实例-通过 业务数据id 获取该业务最新的一次工作流实例详情
    WorkFlowApprove(params) {
        return request.send("/WorkFlow/Approve", params, "POST");
    },
    // 撤回工作流实例
    WithdrawWorkflow(params) {
        return request.send(`/WorkFlow/WithdrawWorkflow?instanceId=${params.instanceId}`, params, "POST");
    },


    // 教材设置默认审批流模板
    SetDefaultWFTemplate(params) {
        return request.send("/Textbook/SetDefaultWFTemplate", params, "POST");
    },
    // 流程 绑定
    BizTemplateBindSet(params) {
        return request.send("/WorkFlowBizBind/BizTemplateBindSet", params, "POST");
    },
    // 通过业务id 查询模板
    BizTemplateGetByBizId(params) {
        return request.send("/WorkFlowBizBind/BizTemplateGetByBizId", params, "get");
    },



};
export default flowApi