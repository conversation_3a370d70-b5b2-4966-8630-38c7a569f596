import request from "@/utils/request";
/**
 * 审批流程管理
 */
const processApi = {
    // 流程模板创建
    CreateWorkflowTemplate(params) {
        return request.send("/WorkFlow/CreateWorkflowTemplate", params, "POST");
    },
    // 流程模板删除 
    DeleteWFTemplate(params) {
        return request.send("/WorkFlow/DeleteWFTemplate", params, "POST");
    },
    // 流程模板编辑
    UpdateWorkflowTemplate(params) {
        return request.send("/WorkFlow/UpdateWorkflowTemplate", params, "POST");
    },
    // 流程模板详情
    GetWorkflowTemplate(params) {
        return request.send("/WorkFlow/GetWorkflowTemplate", params, "GET");
    },
    // 流程模板列表
    GetWorkflowTemplates(params) {
        return request.send("/WorkFlow/GetWorkflowTemplates", params, "GET");
    },
};
export default processApi