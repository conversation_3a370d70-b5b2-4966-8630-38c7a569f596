import request from "@/utils/request";
/**
 * 专业管理
 */
const majorApi = {
    // 获取专业列表
    GetMajorList(params) {
        return request.send("/Major/GetMajorList", params, "GET");
    },
    // 获取专业详情
    GetMajorDetail(params) {
        return request.send("/Major/GetMajorDetail", params, "GET");
    },
    // 获取专业详情 额度
    GetMajorQuotaInfo(params) {
        return request.send("/Major/GetMajorQuotaInfo", params, "GET");
    },
    // 新增专业
    CreateMajor(params) {
        return request.send("/Major/CreateMajor", params, "POST");
    },
    // 编辑专业
    UpdateMajor(params) {
        return request.send(`/Major/UpdateMajor?id=${params.id}`, params, "POST");
    },
    // 删除专业
    DeleteMajor(params) {
        return request.send("/Major/DeleteMajor", params, "DELETE");
    },


    // 获取专业大类
    GetMajorTree(params) {
        return request.send("/BigMajor/GetMajorTree", params, "GET");
    },
    // 新增专业大类
    BigMajorCreate(params) {
        return request.send("/BigMajor/Create", params, "POST");
    },
    // 更新专业大类
    BigMajorUpdate(params) {
        return request.send("/BigMajor/Update", params, "POST");
    },
    // 删除专业大类
    BigMajorDelete(params) {
        return request.send("/BigMajor/Delete", params, "DELETE");
    },
    // 设置专业排序
    SetBigMajorSort(params) {
        return request.send("/BigMajor/SetMajorSort", params, "POST");
    },
};
export default majorApi