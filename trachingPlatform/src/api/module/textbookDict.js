import request from "@/utils/request";
/**
 * 词典管理
 */
const textbookDictApi = {
    // 获取词典列表
    GetTextbookDict(params) {
        return request.send("/TextbookDict/GetList", params, "GET");
    },
    // 获取详情
    GetTextbookDictDetail(params) {
        return request.send("/TextbookDict/GetById", params, "GET");
    },
    // 新增词典
    CreateTextbookDict(params) {
        return request.send("/TextbookDict/Create", params, "POST");
    },
    // 编辑词典
    UpdateTextbookDict(params) {
        return request.send(`/TextbookDict/Update?id=${params.id}`, params, "POST");
    },
    // 删除词典
    DeleteTextbookDict(params) {
        return request.send("/TextbookDict/Delete", params, "DELETE");
    },
};
export default textbookDictApi