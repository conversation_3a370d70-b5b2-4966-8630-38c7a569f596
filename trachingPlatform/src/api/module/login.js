import request from "@/utils/request";
/**
 * 登录
 */
const login = {
    // 获取验证码
    GetVerifyCode(params) {
        return request.send("/Account/GetVerifyCode", params, "GET");
    },
    // 登录
    Login(params) {
        return request.send("/Account/Login", params, "POST");
    },
    // 登录
    Login2(params) {
        return request.send("/Account/Login2", params, "POST");
    },


    // 新增用户
    UserCreate(params) {
        return request.send("/User/Create", params, "POST");
    },
    // 获取个人信息
    GetUserProfile(params) {
        return request.send("/Account/GetUserProfile", params, "GET");
    },
    // 重置密码
    ResetPassword(params) {
        return request.send("/Account/ResetPassword", params, "POST");
    },
    //  发送验证码
    SendPhoneVerifyCode(params) {
        return request.send("/Account/SendPhoneVerifyCode", params, "POST");
    },
    //  修改手机号
    ChangePhone(params) {
        return request.send("/Account/ChangePhone", params, "POST");
    },
    // 
};
export default login