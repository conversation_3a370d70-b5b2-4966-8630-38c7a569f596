import request from "@/utils/request";
/**
 * 文献管理
 */
const textbookLiteratureApi = {
    // 获取文献列表
    GetTextbookLiterature(params) {
        return request.send("/textbookLiterature/GetList", params, "GET");
    },
    // 获取详情
    GetTextbookLiteratureDetail(params) {
        return request.send("/textbookLiterature/GetById", params, "GET");
    },
    // 新增文献
    CreateTextbookLiterature(params) {
        return request.send("/textbookLiterature/Create", params, "POST");
    },
    // 编辑文献
    UpdateTextbookLiterature(params) {
        return request.send(`/textbookLiterature/Update?id=${params.id}`, params, "POST");
    },
    // 删除文献
    DeleteTextbookLiterature(params) {
        return request.send("/textbookLiterature/Delete", params, "DELETE");
    },
};
export default textbookLiteratureApi