import request from "@/utils/request";
/**
 * 教材章节
 */
const textbookSection = {
    // 获取教材章节树形结构
    GetSectionTree(params) {
        return request.send("/textbookSection/GetSectionTree", params, "GET");
    },
    // 新增章节
    CreateSection(params) {
        return request.send("/textbookSection/Create", params, "POST");
    },
    // 修改章节
    UpdateSection(params) {
        return request.send(`/textbookSection/Update?id=${params.id}`, params, "POST");
    },
    // 设置章节排序
    SetSectionSort(params) {
        return request.send("/textbookSection/SetSectionSort", params, "POST");
    },
    // 指定教材章节协作者列表
    GetEditors(params) {
        return request.send("/textbookSection/GetEditors", params, "GET");
    },
    // 保存章节协作者
    SaveEditors(params) {
        return request.send(`/textbookSection/SaveEditors`, params, "POST");
    },
    //  删除章节
    DeleteTextbookSection(params) {
        return request.send("/textbookSection/Delete", params, "DELETE");
    },
    //  章节内容详情
    SectionGetById(params) {
        return request.send("/textbookSection/GetById", params, "GET");
    },
    // 保存章节内容
    SaveSectionContent(params) {
        return request.send("/textbookSection/SaveSectionContent", params, "POST");
    },
    RemoveEditor(params) {
        return request.send("/textbookSection/RemoveEditor", params, "POST");
    },
    // 章节版本及编辑记录
    GetSectionVersionList(params) {
        return request.send("/textbookSection/GetHistory", params, "GET");
    },
    // 还原章节内容
    RestoreSection(params) {
        return request.send("/textbookSection/Restore", params, "GET");
    }
};
export default textbookSection