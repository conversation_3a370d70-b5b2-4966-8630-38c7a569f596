// 
import request from "@/utils/request";
/**
 * 院校管理
 */
const collegeApi = {
    // 获取院校列表
    GetCollegeList(params) {
        return request.send("/College/GetCollegeList", params, "GET");
    },
    // 获取院校详情
    GetCollegeDetail(params) {
        return request.send("/College/GetCollegeDetail", params, "GET");
    },
    // 获取院校教材额度
    GetCollegeQuotaInfo(params) {
        return request.send("/College/GetCollegeQuotaInfo", params, "GET");
    },

    // 新增院校
    CreateCollege(params) {
        return request.send("/College/CreateCollege", params, "POST");
    },
    // 编辑院校
    UpdateCollege(params) {
        return request.send(`/College/UpdateCollege?id=${params.id}`, params, "POST");
    },
    // 删除院校
    DeleteCollege(params) {
        return request.send("/College/DeleteCollege/DeleteCollege", params, "DELETE");
    },
};
export default collegeApi