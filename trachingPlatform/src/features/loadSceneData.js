import isString from 'lodash/isString'
export const loadSceneData = async (api, sceneId, message) => {
  const { data, code } = await api.GetDynamicById({
    id: Number(sceneId)
  })
  return new Promise((resolve, reject) => {
    if (code === 200) {
      resolve(data)
    } else {
      message.error('接口错误')
      resolve({})
    }
  })
}

// 根据场景Id或者通用票据模板id查找场景信息（调度，知识点信息）
export const loadBillSceneData = async (api, sceneId, message, templateNo) => {
  let res
  if (templateNo) {
    res = await api.GetDynamicFormByTemplateId({
      templateId: templateNo
    })
  } else {
    res = await api.GetDynamicById({
      id: Number(sceneId)
    })
  }
  const { data, code } = res
  const { config, ...rest } = data
  let list = []
  if (isString(config)) {
    list = JSON.parse(config)?.list
  }
  return new Promise((resolve, reject) => {
    if (code === 200) {
      resolve({ code, data, list })
    } else {
      message.error('接口错误')
      resolve({
        code
      })
    }
  })
}
