/*
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-25 09:11:40
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-03-01 21:26:14
 * @FilePath: \fusion_front\src\features\loadModuleType.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { arrayToTree } from '@/utils/base'
// 将数据转成树结构
// const loadChildNodes = (list, node, options) => {
const loadChildNodes = (node, options) => {
  // node.children = list.filter(item => item.parentId === node.id);
  // 是否将场景挂载到树上
  if (!options?.noScene && node.scenes?.length > 0) {
    // 末级 挂载了具体场景
    const sceneChildren = node.scenes.map(v => {
      return {
        ...v
        // text: v.scenceName
        // text: v.name
      }
    })
    node.children = [...node.children, ...sceneChildren]
  }
  if (node.children) {
    for (const childNode of node.children) {
      // loadChildNodes(list, childNode, options);
      loadChildNodes(childNode, options)
    }
  }
}

export const loadModuleWithScene = async (api, message, options) => {
  // options: { map: 数据mapping, noScene: 是否挂载场景}
  const { data, code } = await api.GetCustomTypeAndForm({})
  return new Promise((resolve, reject) => {
    if (code === 200) {
      const resultData = data.map(d => {
        return options?.map ? options.map(d) : d
      })
      // const rootNodes = resultData.filter(item => item.parentId === 0);
      // for (const rootNode of rootNodes) {
      for (const rootNode of resultData) {
        // loadChildNodes(resultData, rootNode, options);
        loadChildNodes(rootNode, options)
      }
      // resolve(rootNodes);
      resolve(resultData)
    } else {
      message.error('接口错误')
      resolve([])
    }
  })
}

export const loadModuleMenuSceneTree = async (api, message, options) => {
  // options: { map: 数据mapping, noScene: 是否挂载场景}
  const { data, code } = await api.GetMenuSceneTree({ })
  return new Promise((resolve, reject) => {
    if (code === 200) {
      resolve(data)
    } else {
      message.error('接口错误')
      resolve([])
    }
  })
}

export const loadModuleArrayTree = async (api, message, map) => {
  const { data, code } = await api.getMIBTypeList({})
  return new Promise((resolve, reject) => {
    if (code === 200) {
      const resultData = data.map(d => {
        return map ? map(d) : d
      })
      resolve(arrayToTree(resultData))
    } else {
      message.error('接口错误')
      resolve([])
    }
  })
}

export const loadModuleTree = async (api, message, map) => {
  const { data, code } = await api.GetModuleTree({})
  return new Promise((resolve, reject) => {
    if (code === 200) {
      resolve(data)
    } else {
      message.error('接口错误')
      resolve([])
    }
  })
}

const loopSceneTree = data => {
  return data.map(d => {
    if (d.children) {
      return {
        ...d,
        children: [
          ...loopSceneTree(d.children),
          ...(d.scenes || []).map(s => {
            return { ...s, value: s.id, label: s.name }
          })
        ],
        value: d.id,
        label: d.name
      }
    } else {
      return {
        ...d,
        value: d.id,
        label: d.name
      }
    }
  })
}

export const loadModuleTreeWithScene = async (api, message) => {
  const { data, code } = await api.GetModuleTree({
    sceneType: 2
  })
  return new Promise((resolve, reject) => {
    if (code === 200) {
      resolve(loopSceneTree(data))
    } else {
      message.error('接口错误')
      resolve([])
    }
  })
}
