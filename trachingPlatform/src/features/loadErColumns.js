/*
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-22 16:21:30
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-04-14 21:11:19
 * @FilePath: \fusion_front\src\features\loadErColumns.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// export const loadErColumnsById = async (api, erId, map) => {
//   // 根据erId获取主表id，然后再根据主表ID获取主表columns
//   // 从columns去除列字段，配置field
//   const result = await api.GetDispatchERDetail({
//     id: erId
//   });
//   const columnRes = await api.GetDispatchTableColumns({
//     id: result.data.mainTableId
//   });
//   const columnOptions = columnRes.data.map(map);
//   return new Promise((resolve, reject) => {
//     resolve({
//       columnOptions,
//       tableId: result.data.mainTableId
//     });
//   });
// };

import { flatData } from '@/utils/base';
const getTableColumns = async function(api, tableItem, map, mainTableId) {
  const columnRes = await api.GetDispatchTableColumns({
    id: tableItem.tableId,
  });
  const columnOptions = columnRes.data.map(d => {
    return { ...d, tableId: tableItem.tableId };
  }).map(map);
  return new Promise((resolve, reject) => {
    resolve({
      ...tableItem,
      isMainTable: mainTableId === tableItem.tableId, // 区分主表字表，用于动态表格取字表下拉
      columnOptions: columnOptions
    });
  });
};

export const loadErColumnsById = async(api, erId, map) => {
  // 根据erId获取主表id，以及字表id，然后再根据表ID获取表columns
  // 从columns取出列字段，配置field
  const { data } = await api.GetDispatchERDetail({
    id: erId
  });
  const defers = [];
  // 主表ID
  const mId = data.mainTableId;
  const subTables = JSON.parse(data.relationConfig);
  // defers.push(getTableColumns(api, data.mainTableId, map));
  if (subTables) {
    flatData(subTables).forEach(table => {
      defers.push(getTableColumns(api, table, map, mId));
    });
  }
  const results = await Promise.all(defers);
  return new Promise((resolve, reject) => {
    resolve(results);
  });
};

// 通过erId  获取主表的字段和对应的数据
/**
 *
 * @param {*} api
 * @param {*} params
 * @param {*} isFirst
 * @returns
 */
export const LoadTableByErId = async(api, erId, params, isFirst, map) => {
  // 根据 erId 获取主表的数据
  let tableColumn = null;
  let columnOptions = [];
  if (isFirst&&erId) {
    const allTableColumn = await api.queryNodeInfoByERId(erId);
    tableColumn = allTableColumn?.data.find(v => v.tableType === 'main'); // 找到主表
    tableColumn = tableColumn?.columns.filter(v => v.addType === 0); // 过滤系统自带的字段
    columnOptions = tableColumn?.map(map);
  }
  // 根据ErId 获取主表的列
  let tableData ={}
  if(params.dataSourceID){
    tableData =  await api.GetDynamicSqlData({
      dataSourceID: params.dataSourceID,
      pageIndex: params.pageIndex,
      pageSize: params.pageSize,
      queryDefaultGroupList: [],
      queryValueList: [],
    });
  }else if(params.erId){ // 存在则查询
    tableData = await api.SearchMainTableDataByERId(params);
  }
  return new Promise((resolve, reject) => {
    resolve({
      tableData: tableData.data||[],
      total: tableData.count||0,
      columns: columnOptions||[],
    });
  });
};
