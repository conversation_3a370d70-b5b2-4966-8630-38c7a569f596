const localState = sessionStorage.getItem('state');
const rootState = {
    caseInfo: {}, // 案例信息
    serverUrl: "", // ws 通讯地址
    userInfo: {},
    isToggleTheme: false, //是否切换主题
    workTabList: [], // work brach tab切换
    sceneNodeInfo: {}, // 场景节点信息
    courseInfo: {}, //课程信息,
    // 新建作业信息
    newHomeWorkInfo: {
        taskInfo: {
            details: []
        }
    },
    //分录
    voucherWorkInfo: {
        vocherInfo: {
            details: []
        }
    },
    insertTopicList: [], //插入试题
    accountInfo: {},
    accounteditInfo: {},
    accountdetailInfo: {},
    teachereditInfo: {},
    knowledgeGraphVm: [], // 知识图谱弹窗
    showTaskDialog: true, //显示任务导航弹窗
    showQueStem: true, //显示题干弹窗
    sysMenuList: [], // 系统授权的菜单权限
    openGlobalTask: false, // 是否开启全局任务模式
    chapterTree: [], // 课程的章节树
    chapterShowType: 1, // 菜单展示类型 1 树结构 2 思维导图
    selectedSceneNode: [], // 教材新交互的选中场景插入到教材中的数据
    reviewList: [], //待批阅的学生列表
};


if (localState) {
    const state = JSON.parse(localState);

    rootState.serverUrl = state.serverUrl;
    rootState.userInfo = state.userInfo; // 用户信息
    rootState.accountInfo = state.accountInfo; // 核算信息
    rootState.accounteditInfo = state.accounteditInfo; // 核算信息
    rootState.isToggleTheme = state.isToggleTheme; //是否切换主题
    rootState.teachereditInfo = state.teachereditInfo; // 核算信息
    rootState.caseInfo = state.caseInfo; //是否切换主题
    rootState.workTabList = state.workTabList; // 工作台tab
    rootState.sceneNodeInfo = state.sceneNodeInfo; //场景节点信息
    rootState.courseInfo = state.courseInfo; //课程信息
    rootState.accountdetailInfo = state.accountdetailInfo; //核算任务信息
    rootState.newHomeWorkInfo = state.newHomeWorkInfo; //新建作业
    rootState.voucherWorkInfo = state.voucherWorkInfo; //新建作业
    rootState.insertTopicList = state.insertTopicList; //新建作业
    rootState.chapterTree = state.chapterTree; // 课程的章节树
    rootState.chapterShowType = state.chapterShowType; // 课程的章节树
    rootState.selectedSceneNode = state.selectedSceneNode; // 新的课程的已选中的场景
    // rootState.knowledgeGraphVm = state.knowledgeGraphVm;
    rootState.showTaskDialog = state.showTaskDialog; //显示任务导航弹窗
    rootState.showQueStem = state.showQueStem; //显示题干弹窗
    rootState.sysMenuList = state.sysMenuList; //系统授权的菜单权限
    rootState.openGlobalTask = state.openGlobalTask; // 是否开启全局任务模式
}

export default {
    rootState,
};