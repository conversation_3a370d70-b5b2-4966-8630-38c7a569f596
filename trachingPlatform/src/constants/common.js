export const taskStatusType = {
        NotStarted: '未开始',
        InProgress: '进行中',
        Finished: '已完成'
    }
    /**
     * 注册类型
     */
export const registrationType = [{
        value: 1,
        label: '用户激活'
    },
    {
        value: 2,
        label: '后台添加'
    },
    {
        value: 3,
        label: '老师手动添加'
    },
    {
        value: 4,
        label: '管理员添加的正式考试账号'
    }
]


// 字段类型 （系统字典）
export const DataType = [{
        Id: 1626666293051394,
        Code: "VARCHAR",
        Name: "单行文本",
        Length: 255,
        Decimalplaces: 0,
        ParentId: 1626665437413377,
        Remark: "普通文本类型"
    },
    {
        Id: 1626667731697667,
        Code: "TEXT",
        Name: "多行文本",
        Length: 0,
        Decimalplaces: 0,
        ParentId: 1626665437413377,
        Remark: "多行文本，例如场景说明多文本存储展示"
    },
    {
        Id: 1626667731697668,
        Code: "INT",
        Name: "整数",
        Length: 11,
        Decimalplaces: 0,
        ParentId: 1626665437413377,
        Remark: "整数"
    },
    {
        Id: 1626667731697668,
        Code: "BIGINT",
        Name: "长整数",
        Length: 0,
        Decimalplaces: 0,
        ParentId: 1626665437413377,
        Remark: "长整数"
    },
    {
        Id: 1626671212969989,
        Code: "DECIMAL",
        Length: 20,
        Decimalplaces: 4,
        Name: "金额",
        ParentId: 1626665437413377,
        Remark: "金额类型，可带小数点，默认保留小数点后4位"
    },
    {
        Id: 1626671212969990,
        Code: "DECIMAL",
        Length: 27,
        Decimalplaces: 15,
        Name: "浮点",
        ParentId: 1626665437413377,
        Remark: "浮点类型，可带小数点，默认保留小数点后15位，总长度27位数（此位数包含小数点后位数）"
    },
    {
        Id: 1626688552222726,
        Code: "DATETIME",
        Length: 0,
        Decimalplaces: 0,
        Name: "日期",
        ParentId: 1626665437413377,
        Remark: "日期类型"
    },
    {
        Id: 1626688552222727,
        Code: "JSON",
        Length: 0,
        Decimalplaces: 0,
        Name: "附件",
        ParentId: 1626665437413377,
        Remark: "附件上传，json方式，可放多个"
    },
    {
        Id: 1685835829067791,
        Code: "SELECT",
        Length: 0,
        Decimalplaces: 0,
        Name: "选项集",
        ParentId: 1626665437413377,
        Remark: "选项集"
    },
    {
        Id: 1685836017811472,
        Code: "RELATION",
        Length: 0,
        Decimalplaces: 0,
        Name: "关系字段",
        ParentId: 1626665437413377,
        Remark: "关系字段"
    },
    {
        Id: 1685836017811473,
        Code: "NUMBER",
        Length: 50,
        Decimalplaces: 0,
        Name: "编号",
        ParentId: 1626665437413377,
        Remark: "指定是自动生成编号类型，可配置编号生成规则"
    }
];

export const ERModel = [{
        Id: 1627717420269570,
        Code: 'oneToMany',
        Name: '一对多',
        ParentId: 1627717294440449,
        ParentList: 'ModelStruct',
        Type: 0,
        Status: 0,
        CreateTime: '2023-12-25T20:44:44',
        UpdateTime: '2023-12-25T20:44:44',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0,
        Remark: '一对多'
    },
    {
        Id: 1627717558681604,
        Code: 'oneToOne',
        Name: '一对一',
        ParentId: 1627717294440449,
        ParentList: 'ModelStruct',
        Type: 0,
        Status: 0,
        CreateTime: '2023-12-25T20:45:11',
        UpdateTime: '2023-12-25T20:45:11',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0,
        Remark: '一对一'
    }
]

export const TableRelative = [{
            Id: 1627723081859073,
            Code: 'ErModelRelationship',
            Name: 'er关系',
            ParentId: 0,
            ParentList: 'ErModelRelationship',
            Type: 0,
            Status: 0,
            CreateTime: '2023-12-25T21:03:36',
            UpdateTime: '2023-12-25T21:03:36',
            UpdateUserId: 0,
            CreateUserId: 0,
            IsDeleted: 0,
            Remark: 'er模型关系说明，子表外键对应主表字段 or 子表字段 对应主表外键'
        },
        {
            Id: 1627723455152130,
            Code: 'parentField',
            Name: '子表外键 对应 主表字段',
            ParentId: 1627723081859073,
            ParentList: 'ErModelRelationship',
            Type: 0,
            Status: 0,
            CreateTime: '2023-12-25T21:04:51',
            UpdateTime: '2023-12-25T21:04:51',
            UpdateUserId: 0,
            CreateUserId: 0,
            IsDeleted: 0,
            Remark: 'er模型关系说明，子表外键对应主表字段'
        },
        {
            Id: 1627724025577475,
            Code: 'childField',
            Name: '子表字段 对应 主表外键',
            ParentId: 1627723081859073,
            ParentList: 'ErModelRelationship',
            Type: 0,
            Status: 0,
            CreateTime: '2023-12-25T21:06:45',
            UpdateTime: '2023-12-25T21:06:45',
            UpdateUserId: 0,
            CreateUserId: 0,
            IsDeleted: 0,
            Remark: 'er模型关系说明，子表字段 对应主表外键'
        }
    ]
    /**
     * 模版类型
     */
export const MODELTYPE = [{
        name: '自定义表单',
        type: 1
    },
    {
        name: '复杂表单',
        type: 2
    },
    {
        name: '通用票据',
        type: 3
    },
    {
        name: '内控制度',
        type: 4
    }
]

/**
 * 辅助核算类别
 */
export const Auxiliary = [{
        Id: 1630626525331458,
        Name: '供应商',
        TypeId: 1,
        ParentId: 1630625803911169,
        PathList: null,
        Status: 0,
        Remark: '区分表单类型',
        CreateBy: 0,
        UpdateBy: 0,
        CreateTime: '2024-01-01T14:21:44',
        UpdateTime: '2024-01-01T14:21:44',
        IsDelete: 0,
        Code: 'Supplier',
        Type: 0,
        ParentList: 'TableType',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0
    },
    {
        Id: 1630626722463747,
        Name: '客户',
        TypeId: 2,
        ParentId: 1630625803911169,
        PathList: null,
        Status: 0,
        Remark: '区分表单类型',
        CreateBy: 0,
        UpdateBy: 0,
        CreateTime: '2024-01-01T14:22:24',
        UpdateTime: '2024-01-01T14:22:24',
        IsDelete: 0,
        Code: 'Customer',
        Type: 0,
        ParentList: 'TableType',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0
    },
    {
        Id: 1630627007676420,
        Name: '部门',
        TypeId: 3,
        ParentId: 1630625803911169,
        PathList: null,
        Status: 0,
        Remark: '区分表单类型',
        CreateBy: 0,
        UpdateBy: 0,
        CreateTime: '2024-01-01T14:23:22',
        UpdateTime: '2024-01-01T14:23:22',
        IsDelete: 0,
        Code: 'Department',
        Type: 0,
        ParentList: 'TableType',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0
    },
    {
        Id: 1630627586490373,
        Name: '人员类型',
        TypeId: 4,
        ParentId: 1630625803911169,
        PathList: null,
        Status: 0,
        Remark: '区分表单类型',
        CreateBy: 0,
        UpdateBy: 0,
        CreateTime: '2024-01-01T14:25:17',
        UpdateTime: '2024-01-01T14:25:17',
        IsDelete: 0,
        Code: 'Employee',
        Type: 0,
        ParentList: 'TableType',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0
    },
    {
        Id: 1630627712319494,
        Name: '资金',
        TypeId: 5,
        ParentId: 1630625803911169,
        PathList: null,
        Status: 0,
        Remark: '区分表单类型',
        CreateBy: 0,
        UpdateBy: 0,
        CreateTime: '2024-01-01T14:25:42',
        UpdateTime: '2024-01-01T14:25:42',
        IsDelete: 0,
        Code: 'Capital',
        Type: 0,
        ParentList: 'TableType',
        UpdateUserId: 0,
        CreateUserId: 0,
        IsDeleted: 0
    }
]

// 课程中内容的类型
export const CoursePrepareType = {
    '流程图': 1,
    '业务场景': 2,
    '核算场景': 3,
    '个人知识点': 4,
    '知识仓知识点': 5,
    '票据池': 6,
    '公共素材': 7,
    '个人素材': 8,
    '课件': 9,
    '富文本': 10,
    '任务': 11,
}

export const UserType = [{
    value: 'PlatformAdmin',
    type: 1,
    label: '平台管理员'
}, {
    value: 'PlatformOperator',
    type: 2,
    label: '平台运营人员'
}, {
    value: 'SchoolAdmin',
    type: 4,
    label: '学校管理员'
}, {
    value: 'SchoolEmployee',
    type: 8,
    label: '教务人员'
}, {
    value: 'Teacher',
    type: 16,
    label: '教师'
}, {
    value: 'Student',
    type: 32,
    label: '学生'
}]

// 单位类型
export const SchoolType = [{
            value: 'School',
            type: 1,
            label: '学校'
        },
        {
            value: 'Publisher',
            type: 2,
            label: '出版社'
        }
    ]
    // 学历类型
export const educationLevel = [{
            value: 'BACHELOR',
            type: 1,
            label: '本科'
        },
        {
            value: 'JUNIOR_COLLEGE',
            type: 2,
            label: '大专'
        },
        {
            value: 'HIGHER_VOCATIONAL',
            type: 3,
            label: '高职'
        },
        {
            value: 'SECONDARY_VOCATIONAL',
            type: 4,
            label: '中职'
        },
    ]
    // 合作类型
export const cooperationType = [{
    value: 'Cooperation',
    type: 2,
    label: '合作'
}, {
    value: 'Trial',
    type: 1,
    label: '试用'
}]