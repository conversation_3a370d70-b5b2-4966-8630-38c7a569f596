//验证对象是否有值 提交语句对应到字段名
export const checkObjectFields = (obj, message, convertObj, self) => {
  //self 全局this对象
  //obj 所验证的对象
  //message 相当于lable 针对与那一部分
  // convertObj 转换对象 例如 let convertObj = {ruleType: "计分规则",weight: "权重",attendanceRate: "出勤率",}
  const isEmpty = value => {
    // 检查是否为空值，这里可以根据需要扩展条件
    return value === null || value === undefined || value === "" || (typeof value === 'object' && Object.keys(value).length === 0);
  }
  const emptyField = Object.keys(obj).find(key => isEmpty(obj[key]));
  if (emptyField) {
    self.$message.error(`${message}中「${convertObj[emptyField]}」字段未填写，请填写完整！`);
    return false;// 停止运行
  }
  return true; // 所有字段都非空
}