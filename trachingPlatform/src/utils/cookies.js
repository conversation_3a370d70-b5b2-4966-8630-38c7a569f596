/*
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-04-09 09:26:21
 * @LastEditors: chenjiongming
 * @LastEditTime: 2024-04-11 17:42:56
 * @Description: 对照token.js 存储一些信息
 */
import Cookies from "js-cookie";
export const CLASSROOM_ID = 'CLASSROOM_ID';
export const NOW_CASE_INFO = "nowCaseInfo"
export const USER_INFO="userInfo";// 用户信息
export const setCookies = (key, value, time = 365) => {
  Cookies.set(key, value, { expires: time });
};

export const getCookies = (key) => {
  if(CLASSROOM_ID===key)
  return Cookies.get(key);
};

export const removeCookies = (key) => {
  Cookies.remove(key);
};

const cookieWhiteList = [CLASSROOM_ID]
export const clearCookies = () => {
  // 获取document中所有的cookies
  const cookies = document.cookie.split(';');
  // 遍历并删除每个cookie
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i];
    const eqPos = cookie.indexOf('=');
    const name = (eqPos > -1 ? cookie.substr(0, eqPos) : cookie).trim();
    if(name&&!cookieWhiteList.includes(name)){
      Cookies.remove(name);
    }
  } 
}
