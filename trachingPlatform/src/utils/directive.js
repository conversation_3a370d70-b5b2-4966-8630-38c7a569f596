function debounce(fn, delay) {
  //  防抖
  var timeout = null; // 创建一个标记用来存放定时器的返回值
  return (e) => {
    clearTimeout(timeout); // 每当用户输入的时候把前一个 setTimeout clear 掉
    // 然后又创建一个新的 setTimeout, 这样就能保证interval 间隔内如果时间持续触发，就不会执行 fn 函数
    timeout = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}



export default {
  install(Vue) {
    Vue.directive('lazyLoading', {
      bind(el, binding) {
        const { value } = binding;
        let elementClass = null;
        let lazyFun = null;
        if (typeof value === 'object') {
          const { elementClass: _elementClass, lazyFun: _lazyFun } = value; //   elementClass 滚动盒子的class,   lazyFun 调用的函数
          elementClass = _elementClass;
          lazyFun = _lazyFun;
        } else if (typeof value === 'function') {
          lazyFun = value;
        } else {
          console.err('传参错误');
          return;
        }
        // 获取element-ui定义好的scroll盒子
        const SELECTWRAP_DOM = el.querySelector('.' + elementClass);
        SELECTWRAP_DOM.addEventListener('scroll', function() {
          const { clientHeight, scrollTop, scrollHeight } = this;
          // 当用户的滚动距离大于等于滚动条的总高度, 就执行lazyFun方法
          const CONDITION = Math.ceil(clientHeight + scrollTop) >= scrollHeight;
          if (CONDITION) {
            debounce(lazyFun(), 300);
          }
        });
      }
    });
  }
};
