export const DATATYPE = [{
        label: "文本类型",
        value: 1,
    },
    {
        label: "数字类型",
        value: 2,
    },
    {
        label: "日期",
        value: 3,
    },
    {
        label: "日期时间",
        value: 4,
    },
    {
        label: "布尔类型",
        value: 5,
    },
];

export const PATTERN = [
    { id: 1, name: "列展示" },
    { id: 2, name: "行展示" },
];

/**
 *
 * @param {*} type
 * @returns
 */
export function pattern(type) {
    switch (type) {
        case 1:
            return "列展示";
        case 2:
            return "行展示";
    }
}

/**
 *
 * @param {*} type
 * @returns
 */
export function dataFormat(type) {
    switch (type) {
        case 1:
            return "键值对";
        case 2:
            return "数组";
        case 3:
            return "印章";
        case 4:
            return "附件";
        case 5:
            return "描述";
        case 6:
            return "二维矩阵对象";
    }
}

export const DATAFORMAT = [{
        label: "填空类型",
        value: 1,
    },
    {
        label: "表格类型",
        value: 2,
    },
    {
        label: "印章",
        value: 3,
    },
    {
        label: "附件",
        value: 4,
    },
    {
        label: "描述对象",
        value: 5,
    },
    {
        label: "二维矩阵对象",
        value: 6,
    },
];

export function dataType(type) {
    switch (type) {
        case "text":
            return 1;
        case "number":
            return 2;
        case "date":
            return 3;
        case "dateTime":
            return 4;
        case "bool":
            return 5;
        case "select":
            return 6;
    }
}