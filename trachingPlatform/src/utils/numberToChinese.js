export const numberToChinese = function (string) {
    if (!string) return
    let num = [{
        number: '0',
        chinese: '零'
    }, {
        number: '1',
        chinese: '壹'
    },
    {
        number: '2',
        chinese: '贰'
    },
    {
        number: '3',
        chinese: '叁'
    },
    {
        number: '4',
        chinese: '肆'
    },
    {
        number: '5',
        chinese: '伍'
    },
    {
        number: '6',
        chinese: '陆'
    },
    {
        number: '7',
        chinese: '柒'
    },
    {
        number: '8',
        chinese: '捌'
    },
    {
        number: '9',
        chinese: '玖'
    }
    ]
    let arr = string.split('')
    let chineseStr = ''
    arr.forEach(item => {
        num.forEach(item2 => {
            if (item == item2.number) {
                chineseStr += item2.chinese
            }
        })
    })
    return chineseStr
}

// 将金额拆分成单个数字
export const handlerMoney = function (val, i) {
    if (!val) return
    let arr = val.split('.')
    let intNum = arr[0] ? arr[0].split('') : []
    let floatNum = arr[1] ? arr[1].split('') : []
    switch (i) {
        case 0:
            return floatNum[1] ? floatNum[1] : 0
            break
        case 1:
            return floatNum[0] ? floatNum[0] : 0
            break
        case 2:
            return intNum[intNum.length - i + 1]
            break
        case 3:
            return intNum[intNum.length - i + 1]
            break
        case 4:
            return intNum[intNum.length - i + 1]
            break
        case 5:
            return intNum[intNum.length - i + 1]
            break
        case 6:
            return intNum[intNum.length - i + 1]
            break
        case 7:
            return intNum[intNum.length - i + 1]
            break
        case 8:
            return intNum[intNum.length - i + 1]
            break
        case 9:
            return intNum[intNum.length - i + 1]
            break
        case 10:
            return intNum[intNum.length - i + 1]
            break
    }
}
export const changeTwoDecimal_f = function (x) {
    let f_x = parseFloat(x);
    if (isNaN(f_x)) {
        return false;
    }
    f_x = Math.round(f_x * 100) / 100;
    let s_x = f_x.toString();
    let pos_decimal = s_x.indexOf(".");
    if (pos_decimal < 0) {
        pos_decimal = s_x.length;
        s_x += ".";
    }
    while (s_x.length <= pos_decimal + 2) {
        s_x += "0";
    }
    return s_x;
}


//计算字符串中大写数字转小写方法
export const getMoneyNum = (money, i, isSymbol) => {
    //isSymbol 是否有中文币种符号
    let Symbol = '';
    if (isSymbol) {
        const currencySymbols = {
            '美元': '$',
            '欧元': '€',
            '日元': '¥',
            '英镑': '£',
            '人民币': '¥',
            '澳元': 'A$',
            '加元': 'C$',
            '卢布': '₽',
            '卢比': '₹',
            '韩元': '₩',
            '俄罗斯卢布': '₽',
            '瑞士法郎': 'CHF',
            '港元': 'HK$',
            // 添加更多中文币种和符号
        };
        // 函数：将中文币种名称转换为符号
        const convertCurrencyToSymbol = (currencyString) => {
            // 遍历映射并替换对应的中文币种为符号
            return currencyString.replace(/美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|瑞士法郎|港元/g, (match) => {
                return currencySymbols[match] || match; // 如果找不到匹配，则保留原币种
            });
        }
        let curr = money.match(/(?:美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|瑞士法郎|港元)/g);
        Symbol = convertCurrencyToSymbol(curr.join(''));
    }

    const dic = {
        零: 0,
        壹: 1,
        贰: 2,
        叁: 3,
        肆: 4,
        伍: 5,
        陆: 6,
        柒: 7,
        捌: 8,
        玖: 9,
    };

    // 处理仟,佰,拾;'叁仟肆佰伍拾壹' => '3451'
    const getInfo = (item) => {
        let itemCount = 0;

        // 将仟，佰，拾 拆分数组 '叁仟肆佰伍拾壹' => ['叁仟', '肆佰', '伍拾', '壹']
        let arr = item
            .replace(/(\w*仟)(\w*)/, "$1,$2")
            .replace(/(\w*佰)(\w*)/, "$1,$2")
            .replace(/(\w*拾)(\w*)/, "$1,$2")
            .split(',')
            .filter(_item => {
                return _item && _item.trim();
            });

        // 处理每个单位对应的值
        for (let subItem of arr) {
            let currValue = 0;
            let subArr = subItem.split('');
            if (subArr[1] === '拾') {
                // 处理拾位
                currValue = dic[subArr[0]] * 10;
            } else if (subArr[1] === '佰') {
                //处理佰位
                currValue = dic[subArr[0]] * 100;
            } else if (subArr[1] === '仟') {
                // 处理仟位
                currValue = dic[subArr[0]] * 1000;
            } else {
                // 处理个位
                currValue = dic[subArr[0]];
            }
            itemCount += currValue;
        }
        return itemCount;
    };

    let totalMoney = 0;

    // 按照亿,万,拆分成数组;'叁仟肆佰伍拾亿零壹佰陆拾柒万叁仟肆佰伍拾壹元捌角伍分' => ['叁仟肆佰伍拾亿', '壹佰陆拾柒万', '叁仟肆佰伍拾壹元', '捌角', '伍分']
    let newMoney = money
        .replace(/零/g, '').replace(/整/g, '') // 去掉 '零','整'
        .replace(/美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|瑞士法郎|港元/g, '')
        .replace(/(\w*亿)(\w*)/, "$1,$2")
        .replace(/(\w*万)(\w*)/, "$1,$2")
        .replace(/(\w*元)(\w*)/, "$1,$2")
        .replace(/(\w*圆)(\w*)/, "$1,$2")
        .replace(/(\w*角)(\w*)/, "$1,$2")
        .split(',')
        .filter(_item => {
            return _item && _item.trim();
        });

    // 按照亿,万及以下单位为组, 循环求解求解数值;
    for (let mainItem of newMoney) {
        let currMoney = 0;
        let mainArr = mainItem.split('');

        if (mainArr[mainArr.length - 1] === '亿') {
            mainArr.pop(); // 去掉亿单位
            currMoney = getInfo(mainArr.join('')) * 100000000; // 求仟,佰,拾
        } else if (mainArr[mainArr.length - 1] === '万') {
            mainArr.pop(); // 去掉万单位
            currMoney = getInfo(mainArr.join('')) * 10000; // 求仟,佰,拾
        } else if (mainArr[mainArr.length - 1] === '元') {
            mainArr.pop(); // 去掉元
            currMoney = getInfo(mainArr.join('')); // 求仟,佰,拾
        } else if (mainArr[mainArr.length - 1] === '圆') {
            mainArr.pop(); // 去掉元
            currMoney = getInfo(mainArr.join('')); // 求仟,佰,拾
        } else if (mainArr[mainArr.length - 1] === '角') {
            mainArr.pop(); // 去掉角
            currMoney = dic[mainArr[0]] * 0.1;
        } else if (mainArr[mainArr.length - 1] === '分') {
            mainArr.pop(); // 去掉分
            currMoney = dic[mainArr[0]] * 0.01;
        }
        totalMoney += currMoney;
    }
    let arr1 = totalMoney.toFixed(2).toString().split('.')
    let intNum1 = arr1[0] ? arr1[0].split('') : []
    let floatNum1 = arr1[1] ? arr1[1].split('') : []
    if (i && i >= 0) {
        switch (i) {
            case 1:
                return floatNum1[0] ? floatNum1[0] : 0
                break
            case 2:
                return intNum1[intNum1.length - i + 1]
                break
            case 3:
                return intNum1[intNum1.length - i + 1]
                break
            case 4:
                return intNum1[intNum1.length - i + 1]
                break
            case 5:
                return intNum1[intNum1.length - i + 1]
                break
            case 6:
                return intNum1[intNum1.length - i + 1]
                break
            case 7:
                return intNum1[intNum1.length - i + 1]
                break
            case 8:
                return intNum1[intNum1.length - i + 1]
                break
            case 9:
                return intNum1[intNum1.length - i + 1]
                break
            case 10:
                return intNum1[intNum1.length - i + 1]
                break
        }
    } else {
        if (isSymbol) {
            return Symbol + totalMoney
        }
        if (i == 0) {
            return floatNum1[1] ? floatNum1[1] : 0
        }
        return Number(totalMoney.toString());
    }
}
//金额大写数字转小写方法
export const inputAmount = (money, isSymbol) => {
    const dic = {
        零: 0,
        壹: 1,
        贰: 2,
        叁: 3,
        肆: 4,
        伍: 5,
        陆: 6,
        柒: 7,
        捌: 8,
        玖: 9,
    };

    const currencySymbols = {
        '美元': '$',
        '欧元': '€',
        '日元': '¥',
        '英镑': '£',
        '人民币': '¥',
        '澳元': 'A$',
        '加元': 'C$',
        '卢布': '₽',
        '卢比': '₹',
        '韩元': '₩',
        '卢布': '₽',
        '法郎': 'CHF',
        '港元': 'HK$',
    };
    //将中文币种名称转换为符号
    const convertCurrencyToSymbol = (currencyString) => {
        return currencyString.replace(/美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|法郎|港元/g, match => currencySymbols[match] || match);
    };

    let Symbol = isSymbol ? convertCurrencyToSymbol(money.match(/(?:美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|法郎|港元)/)?.[0] || '') : '';
    // 按照亿,万,拆分成数组;'叁仟肆佰伍拾亿零壹佰陆拾柒万叁仟肆佰伍拾壹元捌角伍分' => ['叁仟肆佰伍拾亿', '壹佰陆拾柒万', '叁仟肆佰伍拾壹元', '捌角', '伍分']
    const getInfo = (item) => {
        const units = { 仟: 1000, 佰: 100, 拾: 10 };
        return item.split('').reduce((total, char, index) => {
            if (dic[char] !== undefined && units[item[index + 1]]) {
                return total + dic[char] * units[item[index + 1]];
            } else if (dic[char] !== undefined) {
                return total + dic[char];
            }
            return total;
        }, 0);
    };

    let totalMoney = 0;
    const newMoney = money
        .replace(/零|整/g, '') // 去掉 '零','整'
        .replace(/(?:美元|欧元|日元|英镑|人民币|澳元|加元|卢布|卢比|韩元|瑞士法郎|港元)/g, '')
        .replace(/(亿|万|元|圆|角|分)/g, '$1,') // 在单位后添加逗号
        .split(',')
        .filter(item => item && item.trim());
    //按照亿,万及以下单位为组, 循环求解求解数值
    newMoney.forEach(mainItem => {
        let currMoney = 0;
        if (mainItem.includes('亿')) {
            currMoney = getInfo(mainItem.replace('亿', '')) * 1e8;
        } else if (mainItem.includes('万')) {
            currMoney = getInfo(mainItem.replace('万', '')) * 1e4;
        } else if (mainItem.includes('元') || mainItem.includes('圆')) {
            currMoney = getInfo(mainItem.replace(/元|圆/, ''));
        } else if (mainItem.includes('角')) {
            currMoney = dic[mainItem[0]] * 0.1;
        } else if (mainItem.includes('分')) {
            currMoney = dic[mainItem[0]] * 0.01;
        }
        totalMoney += currMoney;
    });

    return isSymbol ? Symbol + totalMoney.toString() : totalMoney.toString();
};

export function getFormatDate(val) {
    let date = ''
    if (val) {
        date = new Date(val);
    } else {
        date = new Date();
    }
    console.log(date);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let hour = date.getHours();
    let minutes = date.getMinutes();
    let seconds = date.getSeconds();
    month = (month < 10) ? '0' + month : month;
    day = (day < 10) ? '0' + day : day;
    hour = (hour < 10) ? '0' + hour : hour;
    minutes = (minutes < 10) ? '0' + minutes : minutes;
    seconds = (seconds < 10) ? '0' + seconds : seconds;
    let currentDate = year + "-" + month + "-" + day +
        " " + hour + ":" + minutes + ":" + seconds;
    return currentDate;
}