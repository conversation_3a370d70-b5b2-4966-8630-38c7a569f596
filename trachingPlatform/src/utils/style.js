export function getStyle(style, isAccount, filter = []) {
    const needUnit = ["fontSize", "width", "height", "top", "left", "borderWidth", "letterSpacing", "borderRadius"];

    const result = {};
    if (!style) return
    Object.keys(style).forEach(key => {
        if (!filter.includes(key)) {
            if (key != "rotate") {
                result[key] = style[key];

                if (needUnit.includes(key)) {
                    if (isAccount && key == 'height') {
                        result[key] = "590px";
                    } else {
                        // result[key] += "px";
                    }
                }
            } else {
                result.transform = key + "(" + style[key] + "deg)";
            }
        }
    });

    return result;
}