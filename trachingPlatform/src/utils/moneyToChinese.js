export default function DX(n) { //大写转小写
    // console.log(n, '相关数字');
    if (n == 0) {
        return '零元整'
    }
    if (!/^(-)?(0|[1-9]\d*)(\.\d+)?$/.test(n)) {
        return ''
    }
    let unit = '仟佰拾亿仟佰拾万仟佰拾元角分',
        str = ''
    if (n < 0) {
        console.log(n);
        str += '（负数）'
        n = n.toString().slice(1)
    }
    n += '00'
    let p = n.indexOf('.')
    if (p >= 0) {
        n = n.substring(0, p) + n.substr(p + 1, 2)
    } //小数点
    unit = unit.substr(unit.length - n.length)
    for (let i = 0; i < n.length; i++) {
        str += '零壹贰叁肆伍陆柒捌玖'.charAt(n.charAt(i)) + unit.charAt(i)
    }
    return str.replace(/零(仟|佰|拾|角)/g, '零')
        .replace(/(零)+/g, '零')
        .replace(/零(万|亿|元)/g, '$1')
        .replace(/^元零?|零分/g, '')
        .replace(/元$/g, '元整')
        .replace(/亿万/g, '亿')
}


export const dealBigMoney = function(n) {
    var fraction = ['角', '分'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
    ];
    var head = n < 0 ? '欠' : '';
    n = Math.abs(n);

    var s = '';

    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);

    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

// 金额三位间隔
export const interval = (key) => {
    key += '';
    let x = key.split('.');
    let x1 = x[0];
    let x2 = x.length > 1 ? '.' + x[1] : '';
    let reg = /(\d+)(\d{3})/;
    while (reg.test(x1)) {
        x1 = x1.replace(reg, '$1' + ',' + '$2');
        // x2 = x2.replace(reg, '$1' + ',' + '$2');
    }
    return x1 + x2;
}