function makeError() {
  return new DOMException("The request is not allowed", "NotAllowedError");
}

async function copyExecCommand() {
  let success = false;
  try {
    success = window.document.execCommand("copy");
  } finally {
    console.log("error");
  }

  if (!success) throw makeError();
}

async function copyClipboardApi(text) {
  // Use the Async Clipboard API when available. Requires a secure browsing
  // context (i.e. HTTPS)
  if (!navigator.clipboard) {
    throw makeError();
  }
  return navigator.clipboard.writeText(text);
}

const setSelectStyle = td => {
  const selection = window.getSelection();
  const range = window.document.createRange();
  selection.removeAllRanges();
  range.selectNode(td?.firstChild || td);
  selection.addRange(range);
};

async function clipboardCopy(text) {
  try {
    await copyClipboardApi(text);
  } catch (err) {
    // ...Otherwise, use document.execCommand() fallback
    try {
      await copyExecCommand();
    } catch (err2) {
      throw err2 || err || makeError();
    }
  }
}
const clipboardCopyHandler = (e, getTarget) => {
  // getTarget = (e:any) => {
  //   const target = e.target;
  //   return target?.tagName === "TD"
  //     ? target
  //     : target?.offsetParent?.tagName === "TD"
  //       ? target?.offsetParent
  //       : null;
  // }
  if(process.env.NODE_ENV !== 'development'){
    e.preventDefault();
    e.stopPropagation();
  }
  const dom = getTarget(e);
  if (!dom) {
    return;
  }
  setSelectStyle(dom);
  clipboardCopy(dom?.innerText.trim());
};

const setupCopy = (getTarget, triggerType = "contextmenu") => {
  // window.addEventListener("dblclick", clipboardCopyHandler);
  // window.addEventListener("contextmenu", clipboardCopyHandler);
  window.addEventListener(triggerType, e => {
    clipboardCopyHandler(e, getTarget);
  });
};
export { setupCopy, clipboardCopy };
