import each from "lodash/each";
import union from "lodash/union";
import groupBy from "lodash/groupBy";
import cloneDeep from "lodash/cloneDeep";
export const objectToArray = (obj, keyName) => {
  const arr = [];
  Object.keys(obj).forEach(k => {
    arr.push({ [keyName || "key"]: k, ...obj[k] });
  });
  return arr;
};
export const arrayToObject = (arr, key, format) => {
  const obj = {};
  arr.forEach((a, i) => (obj[a[key]] = format ? format(a, i) : a));
  return obj;
};

export const flatData = (data, childKey = "children") => {
  let result = [];
  each(data, d => {
    result.push({ ...d });
    d[childKey] && (result = union(result, flatData(d[childKey], childKey)));
  });
  return result;
};

export const flatDataWithParent = (
  data,
  childKey = "children",
  fillParent,
  parent
) => {
  let result = [];
  each(data, d => {
    result.push(fillParent ? { ...d, ...fillParent(parent, d) } : d);
    d[childKey] &&
      (result = union(
        result,
        flatDataWithParent(d[childKey], childKey, fillParent, d)
      ));
  });
  return result;
};

export const objectToParams = object => {
  return Object.keys(object)
    .map(key => {
      return `${key}=${object[key]}`;
    })
    .join("&");
};
export const arrayToTree = (array, options) => {
  options = Object.assign(
    {
      id: "id",
      pid: "parentId",
      children: "children",
      firstPid: "0"
    },
    options
  );
  const groupArray = groupBy(cloneDeep(array), a => a[options.pid]);
  const firstArray = groupArray[options.firstPid];
  const children = options.children;
  const childrenParams = [children];
  const transform = startList => {
    if (startList) {
      each(startList, item => {
        groupArray[item[options.id]] &&
          (item[childrenParams] = groupArray[item[options.id]]);
        transform(item[options.children]);
      });
    }
  };
  transform(firstArray);
  return firstArray;
};
export const toJsonStr = val => {
  return JSON.stringify(val, null, 2);
};

export const toText = dom => {
  const reg = new RegExp(String.fromCharCode(160), "g");
  const replacements = {
    "&amp;":"&",
    "&lt;": "<" ,
    "&gt;": ">" ,
    "&quot;": '""' ,
    "&apos;": "'",
    "&ldquo;": '"',
    "&rdquo;": '"',
    "&lt;&gt;": "<>"
  };
  return dom
    .replace(reg, " ")
    .replace(/&nbsp;/g, " ")
    .replace(/<(style|script|iframe)[^>]*?>[\s\S]+?<\/\1\s*>/gi, "")
    .replace(/<[^>]+?>/g, "")
    .replace(/\s+/g, " ")
    .replace(/ /g, " ")
    .replace(/>/g, " ")
    .replace(/(&amp;|&lt;|&gt;|&quot;|&apos;|&lt;|&gt;|&ldquo;|&rdquo;)/gi, function (noe) {
      return replacements[noe];
    });
};

export function uuid(len, radix) {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  var uuid = [], i;
  radix = radix || chars.length;
  if (len) {
      for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random()*radix];
  } else {
      var r;
      uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
      uuid[14] = '4';
      for (i = 0; i < 36; i++) {
          if (!uuid[i]) {
              r = 0 | Math.random()*16;
              uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
          }
      }
  }
  return uuid.join('');
}


export const getBase64FromUrl = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous'; // 这行很重要，用于处理跨域问题
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      const dataURL = canvas.toDataURL('image/png');
      resolve(dataURL);
    };
    img.onerror = error => {
      reject(error);
    };
    img.src = url;
  });
}