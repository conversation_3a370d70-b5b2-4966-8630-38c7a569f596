import {
    getV<PERSON>ueByLabel,
    optionKeyList,
    questionTypeMenu,
    questionInitConfig,
    getCorrectAnswer
} from "@/components/base/question/util";
import { guid } from "@/utils/utils.js";

// 提取重复的正则表达式
const REGEX_QUESTION_TYPE = /(【单选题】|【多选题】|【判断题】|【填空题】|【简答题】)\s*$/;
const REGEX_ANSWER = /^\s*参考答案[:：]/;
const REGEX_DETAIL = /^\s*答案解析[:：]/;
const REGEX_DIFFICULTY = /^\s*难易度[:：]/;
const REGEX_KNOWN = /^\s*知识点[:：]/;
const REGEX_SKILL = /^\s*技能点[:：]/;
const REGEX_PLACEHOLDER = /【空[一二三四五六七八九十百千]+】/g;
const REGEX_DROP_DOWN = /【下拉[一二三四五六七八九十百千]+(\[[^\]]+\])?】/g;

// 去除字符串首尾空格
function trimString(str) {
    return str.replace(/^\s+|\s+$/g, "");
}

/**
 * 创建试题对象
 */
export function createQuestion(index) {
    return {
        type: 1,
        title: "",
        desc: "",
        optionConfig: {
            options: []
        },
        answerDetail: "",
        knowledge: [],
        skill: [],
        difficulty: 0,
        sort: index + 1,
        cateId: 0,
        answer: ""
    };
}

/**
 * 处理试题标题
 * @param {*} item
 * @param {*} question
 * @param {*} longArray
 */
export function handleQuestionTitle(item, question, longArray) {
    const questionTypeMatch = item.match(REGEX_QUESTION_TYPE);
    const questionType = questionTypeMatch ? questionTypeMatch[1] : null;
    switch (questionType) {
        case "【单选题】":
            question.type = questionTypeMenu.radio;
            question.questionType = questionTypeMenu.radio;
            handleSingleOrMultipleChoice(item, question, longArray);
            break;
        case "【多选题】":
            question.type = questionTypeMenu.checkbox;
            question.questionType = questionTypeMenu.checkbox;
            handleSingleOrMultipleChoice(item, question, longArray);
            break;
        case "【判断题】":
            question.type = questionTypeMenu.isTrue;
            question.questionType = questionTypeMenu.isTrue;
            handleTrueFalseQuestion(item, question, longArray);
            break;
        case "【填空题】":
            question.type = questionTypeMenu.content;
            question.questionType = questionTypeMenu.content;
            handleFillInTheBlankOrShortAnswer(item, question);
            break;
        case "【简答题】":
            question.type = questionTypeMenu.shortAnswer;
            question.questionType = questionTypeMenu.shortAnswer;
            handleFillInTheBlankOrShortAnswer(item, question);
            break;
        default:
            break;
    }
}

/**
 * 处理单选题或多选题
 * @param {*} item
 * @param {*} question
 * @param {*} longArray
 */
function handleSingleOrMultipleChoice(item, question, longArray) {
    const title = trimString(item.replace(/(【单选题】|【多选题】)$/, ""));
    const matches = title.match(/[\(（][A-Z]+[\)）]/g);
    if (matches && matches.length > 0) {
        const lastMatch = matches[matches.length - 1];
        question.title = trimString(
            title.replace(lastMatch, "( )").replace(/^([0-9]+、|[0-9]+\.)/, "")
        );
        const answerOptions = lastMatch.match(/[A-Z]/g);
        if (answerOptions.length === 1) {
            // question.type = questionTypeMenu.radio;
            longArray.push(answerOptions[0]);
        } else if (answerOptions.length > 1) {
            // question.type = questionTypeMenu.checkbox;
            answerOptions.forEach(option => longArray.push(option));
        }
        question.answer = JSON.stringify({ longArray });
    } else {
        // question.type = questionTypeMenu.checkbox;
        question.title = trimString(title.replace(/^([0-9]+、|[0-9]+\.)/, ""));
    }
}

/**
 * 处理判断题
 * @param {*} item
 * @param {*} question
 * @param {*} longArray
 */
function handleTrueFalseQuestion(item, question, longArray) {
    question.type = questionTypeMenu.isTrue;
    const title = trimString(item.replace(/(【判断题】)$/, ""));
    let matches = title.match(/[\(（](正确|错误)[\)）]/g);
    if (matches && matches.length > 0) {
        const lastMatch = matches[matches.length - 1];
        question.title = trimString(
            title.replace(lastMatch, "").replace(/^([0-9]+、|[0-9]+\.)/, "")
        );
        const answer = lastMatch.match(/正确|错误/g)[0];
        const answerValue = answer == "正确" ? "A" : "B";
        longArray.push(answerValue);
        question.answer = JSON.stringify({ longArray });

        const init = questionInitConfig[questionTypeMenu.isTrue];
        init.options.forEach(option => {
            option.label = option.value === "A" ? "正确" : "错误";
            option.isAnswer = longArray.includes(option.value);
            question.optionConfig.options.push(option);
        });
    } else {
        matches = title.match(/[\(（][A-Z]+[\)）]/g);
        if (matches && matches.length > 0) {
            const lastMatch = matches[matches.length - 1];
            question.title = trimString(
                title.replace(lastMatch, "").replace(/^([0-9]+、|[0-9]+\.)/, "")
            );
            const answerOptions = lastMatch.match(/[A-Z]/g);
            if (answerOptions.length) {
                longArray.push(answerOptions[0]);
                question.answer = JSON.stringify({ longArray });
            }
        }
        question.title = trimString(title.replace(/^([0-9]+、|[0-9]+\.)/, ""));
        // question.questionType = questionTypeMenu.isTrue;
    }
}

/**
 * 处理填空题或简答题
 * @param {*} item
 * @param {*} question
 */
function handleFillInTheBlankOrShortAnswer(item, question) {
    const title = trimString(
        item.replace(/(【填空题】|【选择填空题】|【简答题】)$/, "")
    );
    question.title = trimString(title.replace(/^([0-9]+、|[0-9]+\.)/, ""));
    // question.type = questionTypeMenu.content;
}

/**
 * 处理试题详情
 * @param {*} item
 * @param {*} question
 * @param {*} longArray
 */
export function handleQuestionDetails(item, question, longArray) {
    const trimmedItem = trimString(item);

    if (/【描述】/.test(trimmedItem)) {
        // 修正替换逻辑，去掉【描述】
        question.desc = trimString(item.replace(/【描述】/, ""));
    }

    if (
        [
            questionTypeMenu.radio,
            questionTypeMenu.checkbox,
            questionTypeMenu.isTrue
        ].includes(question.type)
    ) {
        handleOptions(item, question, longArray);
    }

    if (
        /【填空】|【选择填空题】/.test(trimmedItem) &&
        question.type === questionTypeMenu.content
    ) {
        handleFillInTheBlankPlaceholder(item, question);
    }

    // && question.type === 3
    if (REGEX_ANSWER.test(trimmedItem)) {
        handleAnswerDetails(item, question);
    }

    if (REGEX_DETAIL.test(trimmedItem)) {
        question.answerDetail = trimString(item.replace(REGEX_DETAIL, ""));
    }

    if (REGEX_KNOWN.test(trimmedItem)) { // 知识点
        let name = trimString(item.replace(REGEX_KNOWN, ""))
        if (name == "未知") return
        let nameList = name.split('、')
        nameList.forEach(item => {
            question.knowledge.push({
                knowledgeId: 0,
                name: trimString(item)
            })
        })
    }
    if (REGEX_SKILL.test(trimmedItem)) { // 技能点
        let name = trimString(item.replace(REGEX_SKILL, ""))
        if (name == "未知") return
        let nameList = name.split('、')
        nameList.forEach(item => {
            question.skill.push({
                knowledgeId: 0,
                name: trimString(item)
            })
        })
    }
    if (REGEX_DIFFICULTY.test(trimmedItem)) {
        question.difficulty = getValueByLabel(
            trimString(item.replace(REGEX_DIFFICULTY, ""))
        );
    }
}

/**
 * 处理选项
 * @param {*} item
 * @param {*} question
 * @param {*} longArray
 */
function handleOptions(item, question, longArray) {
    const regExp = /^\s*[A-Z][、|\.]/;
    if (regExp.test(item)) {
        const optionValue = item.match(/[A-Z]/)[0];
        const optionLabel = trimString(item.replace(regExp, ""));
        const isAnswer = longArray.includes(optionValue);
        question.optionConfig.options.push({
            value: optionValue,
            label: optionLabel,
            isAnswer
        });
    }
}

/**
 * 处理填空题占位符
 * @param {*} item
 * @param {*} question
 */
function handleFillInTheBlankPlaceholder(text, question) {
    const settingArr = [];
    let html = text;


    const placeholders = [
        ...(text.match(REGEX_PLACEHOLDER) || []),
        ...(text.match(REGEX_DROP_DOWN) || [])
    ];

    placeholders.forEach((placeholder, index) => {
        const isDrop = placeholder.includes("下拉");
        const tagType = isDrop ? "compSelectTag" : "compInputTag";
        const timestamp = `answerId_${guid()}`;
        // const timestamp = `answerId_${index}`;
        const template = `<span data-w-e-type="${tagType}" data-vue-value="${timestamp}"></span>`;

        // 对占位符中的特殊字符进行转义
        const escapedPlaceholder = placeholder.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
        );
        const regex = new RegExp(escapedPlaceholder, "g");
        const settingItem = {
            answerId: timestamp,
            answerCont: "",
            isCorrect: true
        };

        if (isDrop) {
            // settingItem.compSelectTag = [];
            settingItem.selectOptions = [];
            const dropMatch = placeholder.match(/\[(.*?)\]/);
            if (dropMatch && dropMatch[1]) {
                const optionsStr = dropMatch[1];
                const options = optionsStr.split("|");
                options.forEach(option => {
                    // settingItem.compSelectTag.push({
                    settingItem.selectOptions.push({
                        value: option,
                        label: option
                    });
                });
            }
        }

        settingArr.push(settingItem);
        html = html.replace(regex, template);
    });

    question.optionConfig.settingArr = settingArr;
    question.optionConfig.html = html;
}

/**
 * 处理答案详情
 * @param {*} item
 * @param {*} question
 */
function handleAnswerDetails(item, question) {
    // debugger;
    const detailName = trimString(item.replace(REGEX_ANSWER, ""));
    if (question.type === questionTypeMenu.content) { // 填空题
        const matches = detailName.split("|");
        question.optionConfig.settingArr.forEach((option, index) => {
            // if (matches[index].includes("/")) {
            //   const items = matches[index].split("/");
            //   debugger;
            //   items.forEach(te => {
            //     option.compSelectTag.push({ vulue: te, label: te });
            //   });
            // } else {
            //   option.answerCont = matches[index];
            // }
            option.answerCont = matches[index];
        });
        const longArray = getCorrectAnswer(
            questionTypeMenu.content,
            question.optionConfig
        );
        question.answer = JSON.stringify({ longArray });
    } else if (question.type === questionTypeMenu.isTrue || question.type === questionTypeMenu.radio) { // 判断题/单选题
        const longArray = [detailName];
        question.answer = JSON.stringify({ longArray });
        question.optionConfig.options.forEach(item => {
            item.isAnswer = longArray.includes(item.value) ? true : false;
        })

    } else if (question.type === questionTypeMenu.checkbox) { // 多选题
        const longArray = detailName.split(","); // 假设答案以空格分隔，你可以根据实际情况进行调整
        question.answer = JSON.stringify({ longArray });
        question.optionConfig.options.forEach(item => {
            item.isAnswer = longArray.includes(item.value) ? true : false;
        })

    } else if (question.type === questionTypeMenu.shortAnswer) { // 简答题
        const answer = detailName.split(","); // 假设答案以空格分隔，你可以根据实际情况进行调整
        question.answer = JSON.stringify({ answer });
    }
    return question;
}

/**
 * 解析输入文本
 * @param {*} input
 * @returns
 */
export function parseInput(input) {
    const lines = input.split("\n");
    const topicList = [];
    let temp = [];

    lines.forEach(line => {
        // console.log("l" + line);
        const trimmedLine = trimString(line);
        const isQuestionType = REGEX_QUESTION_TYPE.test(trimmedLine);
        if (isQuestionType) {
            if (temp.length > 0) topicList.push(temp);
            temp = [trimmedLine];
        } else if (trimmedLine.length > 0) {
            temp.push(trimmedLine);
        }
    });
    if (temp.length > 0) topicList.push(temp);

    return topicList;
}