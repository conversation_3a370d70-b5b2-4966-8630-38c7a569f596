import token from "./token";

export async function requestSse (url, data,abortController, onMessage, headers) {
  const response = await fetch(url, {
      method: 'POST',
      headers: {
          Authorization: sessionStorage.getItem('tokenName'),
          'Content-Type': 'application/json', // 文本返回格式
          caseId: sessionStorage.getItem('caseId')
      },
      body: JSON.stringify(data),
      signal: abortController?.signal
  })
  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
      const {value, done} = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split("\n\n");

      for (const line of lines) {
          if (line.trim() === "[DONE]") {
            onMessage({
              type: 'close',
              content: ''
             })
            break;
          }
          if (line.trim() === "") continue;

          try {
              const data = JSON.parse(line);
              onMessage(data)
          } catch (e) {
              console.error("解析响应数据失败", e);
          }
      }
  }
}


/**
 * 创建SSE连接进行聊天流式响应
 * @param {Object} data - 请求的数据
 * @param {Function} onMessage - 接收消息的回调函数
 * @param {Function} onError - 错误处理回调函数
 * @param {Function} onComplete - 请求完成回调函数
 * @returns {Object} - 包含关闭连接的方法
 */
export function createChatStream(data, onMessage, onError, onComplete) {
    // 检查浏览器是否支持EventSource
    if (typeof EventSource === 'undefined') {
        const error = new Error('当前浏览器不支持Server-Sent Events')
        if (onError) {
            onError(error)
        }
        return { close: () => {} }
    }

    // 由于需要POST请求，需要创建一个支持POST的SSE连接
    // 使用fetch API
    const controller = new AbortController()
    const signal = controller.signal

    // 获取token
    const tokens = token.getToken() ||
            token.getTouristToken() ||
            token.getTccessToken()

    // 使用fetch发起POST请求
    fetch(window.PUBLICHOSTA + '/dfs/AIAgent/chatStream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': tokens ? `Bearer ${tokens}` : ''
        },
        body: JSON.stringify(data),
        signal
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误! 状态码: ${response.status}`)
            }

            // 获取响应的reader流
            const reader = response.body.getReader()
            const decoder = new TextDecoder('utf-8')

            // 处理流数据
            function processStream() {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        if (onComplete) {
                            onComplete()
                        }
                        return
                    }

                    // 解码接收到的数据
                    const text = decoder.decode(value, { stream: true })
                    // 按换行符分割数据
                    const lines = text.split('\n\n');
                    let fullText = '';

// 遍历每一行
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (line === '' || line === '[DONE]') {
                            continue;
                        }
                        try {
                            const obj = JSON.parse(line);
                            fullText += obj.content;
                        } catch (error) {
                            console.error(`解析第 ${i + 1} 行时出错:`, error);
                        }
                    }

// 动态识别章节标题
                        const possibleChapterTitles = [];
                        const linesInFullText = fullText.split('\n');
                        for (const line of linesInFullText) {
                            const trimmedLine = line.trim();
                            if (trimmedLine.endsWith('：') || trimmedLine.endsWith(':') || trimmedLine.endsWith('\\n') || /(封面|目录|结尾|章节)/.test(trimmedLine)) {
                                possibleChapterTitles.push(trimmedLine.replace(/[:：\n]/g, ''));
                            }
                        }

                        // 生成动态正则表达式
                        const chapterRegex = new RegExp(`(${possibleChapterTitles.join('|')})`);

                        // 按章节分割内容
                        const parts = fullText.split(chapterRegex).filter(part => part.trim());

                        const outline = [];
                    if(text.includes('\\n -')){
                        for (let i = 0; i < parts.length; i += 2) {
                            const chapterTitle = parts[i]?.trim();
                            const chapterContent = parts[i + 1]?.trim().split('\n - ').filter(item => item);
                            outline.push({
                                title: chapterTitle,
                                items: chapterContent
                            });
                        }
                    }else{
                        outline.push(
                            {title:'请提供具体主题或内容方向，以便我为您生成合适的PPT大纲!'}
                        )
                    }
                    onMessage(text,outline)
                    return outline
                }).catch(error => {
                    if (onError) {
                        onError(error)
                    }
                })
            }

            processStream()
        })
        .catch(error => {
            if (onError) {
                onError(error)
            }
        })

    // 返回一个对象，包含关闭连接的方法
    return {
        close: () => {
            controller.abort()
        }
    }
}
