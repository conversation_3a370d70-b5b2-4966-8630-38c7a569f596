/**
 * 两个时间相差多少月
 * @param {*} time 数组[开始时间，结束时间]
 */
export function handlerDateDurationCurrent(time = []) {
  const startDate = new Date(time[0] || '');

  const endDate = new Date(time[1] || '');

  let number = 0;

  const yearToMonth = (endDate.getFullYear() - startDate.getFullYear()) * 12;

  number += yearToMonth;

  const monthToMonth = endDate.getMonth() - startDate.getMonth();

  number += monthToMonth;
  return number;
}
