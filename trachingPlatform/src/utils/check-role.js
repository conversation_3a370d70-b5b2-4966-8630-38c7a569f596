export const checkScreenPermission = () => {
  // 只有教师可以投屏
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
  let isSHowHeard = sessionStorage.getItem("isSHowHeard")
  return ["2", "6"].includes(userInfo.userType)&&isSHowHeard!='true';
  // if (userInfo?.userType == "2") {
  //   return true;
  // } else {
  //   return false;
  // }
};

export const checkExportPermission = () => {
 // 资源组和管理员导出
 const userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
 return ["0", "6"].includes(userInfo.userType);
};

export const checkCaseEditPermission = () => {
  // 资源组和管理员 新增、修改、删除案例
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
  return ["0", "6"].includes(userInfo.userType);
};


export const checkSetMobilePermission = () => {
  // 资源组和管理员 新增、修改、删除案例
  const userInfo = JSON.parse(sessionStorage.getItem("userInfo") || "{}");
  return ["0", "6"].includes(userInfo.userType);
};
