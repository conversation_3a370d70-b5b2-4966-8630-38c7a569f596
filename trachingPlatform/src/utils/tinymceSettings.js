import Snowflake from "@/utils/snowflakeId.js";
import { Message } from "element-ui";
/**
 * 编辑器部分公用配制
 */
let settings = {
    menubar: false,
    // inline: true,
    toolbar: `lineheight layout | undo redo |  addField  addFieldSelect addSection |  importword upfile   |  fontselect fontsizeselect forecolor backcolor
    | fullscreen 
    | formatselect alignleft aligncenter alignright alignjustify 
    | link unlink | numlist bullist | image media table  
    | bold italic underline strikethrough | indent outdent | superscript subscript | removeformat | indent2em letterspacing 
    | preview formatpainter  code codesample | paste pastetext`,
    quickbars_selection_toolbar: `removeformat | bold italic underline strikethrough | fontsizeselect forecolor backcolor`,
    plugins: `lineheight link image imagetools media table lists fullscreen quickbars addExtractField addField addFieldSelect addSection importword upfile indent2em letterspacing layout
    preview formatpainter code codesample`,
    //addInput addSelect addRadio addCheckBox
    contextmenu: `addSection | addExtractField | addField | addFieldSelect | link | image | inserttable | cell row column | advtablesort | tableprops deletetable`,
    font_formats:  `Helvetica=helvetica;
                    微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;
                    苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;
                    黑体=SimHei,serif;
                    隶书=LiSu,serif;
                    宋体=STSong,serif;
                    仿宋=FangSong,serif;
                    新宋体=NSimSun,serif;
                    幼圆=YouYuan,serif;
                    楷体=KaiTi_GB2312,serif;
                    华文隶书=STLiti,serif;
                    华文新魏=STXinwei,serif;
                    华文行楷=STXingkai,serif;
                    `,
    table_cell_class_list: [
        // 单元格
        { title: "无", value: "" },
        { title: "设置关键字段", value: "table-blue" }
    ],
    // fontsize_formats: "8pt 9pt 10pt 11px 12pt 13pt 14pt 15pt 16pt 17pt 18pt 19pt 20pt 21pt 22pt 23pt 24pt 25pt 26pt 27pt 28pt 29pt 30pt 31pt 32pt 33pt 34pt 35pt 36pt",
    fontsize_formats: "8px 9px 10px 11px 12px 13px 14px 15px 16px 17px 18px 19px 20px 21px 22px 23px 24px 25px 26px 27px 28px 29px 30px 31px 32px 33px 34px 35px 36px",
    formats: {
        borderstyle: {
            selector: "td,th",
            styles: {
                borderTopStyle: "solid",
                borderRightStyle: "solid",
                borderBottomStyle: "solid",
                borderLeftStyle: "solid"
            },
            remove_similar: true
        },
        bordercolor: {
            selector: "td,th",
            styles: {
                borderTopColor: "#32CD32",
                borderRightColor: "#32CD32",
                borderBottomColor: "#32CD32",
                borderLeftColor: "#32CD32"
            },
            remove_similar: true
        },
        backgroundcolor: { selector: "td,th", styles: { backgroundColor: "#006400" }, remove_similar: true },
        formatpainter_removeformat: [{
                selector: "b,strong,em,i,font,u,strike,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins",
                remove: "all",
                split: true,
                expand: false,
                block_expand: true,
                deep: true
            },
            {
                selector: "span",
                attributes: ["style", "class"],
                remove: "empty",
                split: true,
                expand: false,
                deep: true
            },
            {
                selector: "*:not(tr,td,th,table)",
                attributes: ["style", "class"],
                split: false,
                expand: false,
                deep: true
            }
        ]
    },

    height: 500,
    content_style: `
        body { 
            font-family:Helvetica,Arial,sans-serif;
            font-size:14px;
            caret-color: #000!important;
            cursor: default !important;
        }
        `,
    style_formats: [{
        title: '首行缩进',
        block: 'p',
        styles: { 'text-indent': '2em', 'padding': '0', 'margin': '0' }
    }],
    // 一键布局
    layout_options: {
        style: {
            "text-align": "justify",
            "text-indent": "2em",
            "line-height": 1.5
        },
        filterTags: ["table>*", "tbody"], //'table，'tbody','td','tr' 将会忽略掉 同时 table>*，忽略table 标签 以及所有子标签
        clearStyle: ["text-indent"], //text-indent 将会被清除掉
        tagsStyle: {
            table: {
                "line-height": 3,
                "text-align": "center"
            },
            "table,tbody,tr,td": {
                //支持并集选择
                "line-height": 2
            },
            "tr>td,table>tbody": {
                //支持, 精准定位 通过 ' > '
                "line-height": 3,
                "text-align": "center"
            }
        }
    },
    letterspacing: "0px 2px 4px 6px 24px",
    table_icons: {
        // 以下下为默认配置
        "align-right-table": `<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm6 4h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm-6-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>`,
        "align-left-table": `<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2zm0-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>`,
        "align-center-table": `<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm3 4h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm-3-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>`
    },
    language: "zh_CN",
    height: 650,
    zIndex: 3000,
    images_upload_url: window.FILEIP,
    //自定义,默认空对象
    selectObj: [], //定义选中模型对象
    initSelectItem: true, // 是否重新加载默认的模型对象
    //insertSectionObj: [],
    //selectSectionItem: "",
    watchObj: {}, //文本提交模型
    snowflake: new Snowflake(),
    images_upload_handler: example_image_upload_handler,
    automatic_uploads:true,// 图片自动上传
    paste_data_images: true,
    init_instance_callback: editor => {
        editor.on("paste", (evt) => {
            // 监听粘贴事件
            onPaste_callback(evt,editor);
        });
      },
    importword_handler: importword_handler, // word 导入的转换
    importword_filter: importword_filter,
    file_callback: file_callback,//上传文件
    skin: 'oxide',
};

export function file_picker_callback(callback, value, meta) {
    //文件分类
    var filetype = ".pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4";
    //后端接收上传文件的地址
    var upurl = "/demo/upfile.php";
    //为不同插件指定文件类型及后端地址
    switch (meta.filetype) {
        case "image":
            filetype = ".jpg, .jpeg, .png, .gif";
            upurl = "upimg.php";
            break;
        case "media":
            filetype = ".mp3, .mp4";
            upurl = "upfile.php";
            break;
        case "file":
        default:
    }
    //模拟出一个input用于添加本地文件
    var input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", filetype);
    input.click();
    input.onchange = function() {
        var file = this.files[0];

        var xhr, formData;
        console.log(file.name);
        xhr = new XMLHttpRequest();
        xhr.withCredentials = false;
        xhr.open("POST", upurl);
        xhr.onload = function() {
            var json;
            if (xhr.status != 200) {
                failure("HTTP Error: " + xhr.status);
                return;
            }
            json = JSON.parse(xhr.responseText);
            if (!json || typeof json.location != "string") {
                failure("Invalid JSON: " + xhr.responseText);
                return;
            }
            callback(json.location);
        };
        formData = new FormData();
        formData.append("file", file, file.name);
        xhr.send(formData);

        //下方被注释掉的是官方的一个例子
        //放到下面给大家参考

        /*var reader = new FileReader();
        reader.onload = function () {
            // Note: Now we need to register the blob in TinyMCEs image blob
            // registry. In the next release this part hopefully won't be
            // necessary, as we are looking to handle it internally.
            var id = 'blobid' + (new Date()).getTime();
            var blobCache =  tinymce.activeEditor.editorUpload.blobCache;
            var base64 = reader.result.split(',')[1];
            var blobInfo = blobCache.create(id, file, base64);
            blobCache.add(blobInfo);

            // call the callback and populate the Title field with the file name
            callback(blobInfo.blobUri(), { title: file.name });
        };
        reader.readAsDataURL(file);*/
    };
}
/**
 * 附件上传
 * @param {*} file_callback
 */
export function file_callback(file, succFun) {
    let xhr, formData;
    xhr = new XMLHttpRequest();
    xhr.withCredentials = false;
    xhr.open("POST", window.FILEIP);

    // xhr.upload.onprogress = function(e) {
    //     progress((e.loaded / e.total) * 100);
    // };
    xhr.onload = function() {
        let json;
        if (xhr.status === 403) {
            failure("HTTP Error: " + xhr.status, { remove: true });
            return;
        }
        if (xhr.status < 200 || xhr.status >= 300) {
            failure("HTTP Error: " + xhr.status);
            return;
        }
        json = JSON.parse(xhr.responseText);
        console.log("文件上传", json);
        if (!json || typeof json.data != "string") {
            failure("Invalid JSON: " + xhr.responseText);
            return;
        }
        succFun(json.data, { text: file.name }); //成功回调函数 text 显示的文本
        //success(json.data);
    };
    xhr.onerror = function() {
        failure("file upload failed due to a XHR Transport error. Code: " + xhr.status);
    };
    formData = new FormData();
    formData.append("file", file);
    formData.append('iscompressed', false); //是否压缩
    xhr.send(formData);
}

/**
 *
 * @param {*} editor
 * @param {*} files
 * @param {*} next
 */
export function importword_filter(result, insert, messaget) {
    insert(result); //回插函数
}

/**
 * 导入word 内容
 * @param {*} editor
 * @param {*} files
 * @param {*} next
 */
export function importword_handler(editor, files, next) {
    var file_name = files[0].name;
    if (file_name.substr(file_name.lastIndexOf(".") + 1) == "docx") {
        editor.notificationManager.open({
            text: "正在转换中...",
            type: "info",
            closeButton: false
        });
        next(files);
    } else {
        editor.notificationManager.open({
            text: "目前仅支持docx文件格式，若为doc，请将扩展名改为docx",
            type: "warning"
        });
    }
    // next(files);
}

/**
 * 富文本编辑器 上传文件方法
 */

export function example_image_upload_handler(blobInfo, success, failure, progress) {
    let xhr, formData;
    xhr = new XMLHttpRequest();
    // 如果需要发送 cookies 或其他凭据
    xhr.withCredentials = true;
    xhr.open("POST", window.FILEIP);
    // iscompressed  是否压缩
    xhr.upload.onprogress = function(e) {
        progress((e.loaded / e.total) * 100);
    };
    xhr.onload = function() {
        let json;
        if (xhr.status === 403) {
            failure("HTTP Error: " + xhr.status, { remove: true });
            return;
        }
        if (xhr.status < 200 || xhr.status >= 300) {
            failure("HTTP Error: " + xhr.status);
            return;
        }
        json = JSON.parse(xhr.responseText);
        console.log("图片上传", json);
        if (!json || typeof json.data != "string") {
            failure("Invalid JSON: " + xhr.responseText);
            return;
        }
        success(json.data);
    };
    xhr.onerror = function() {
        failure("Image upload failed due to a XHR Transport error. Code: " + xhr.status);
    };
    formData = new FormData();
    formData.append("file", blobInfo.blob(), blobInfo.filename());
    formData.append('iscompressed', false); //是否压缩
    xhr.send(formData);
}

export function onPaste_callback(event, editor) {
    // 实现图⽚粘贴上传
    const items = (event.clipboardData || window.clipboardData).items;
    let len = items.length;
    for (let i = 0; i < len; i++) {
        console.log('items[i]',items[i]);
      if (items[i].kind == "file" && items[i].type.indexOf("image") != -1) {
        // 判断是否为图⽚类型
        event.preventDefault(); // 如果是图⽚阻⽌⾃动粘贴到富⽂本编辑器
        let files = items[i].getAsFile(); // 获取⽂件数据
        let blobInfo = new Blob([files], { type: files.type });
        console.log('blobInfo',blobInfo);
        const formData = new FormData();
        formData.append("file", blobInfo, files.name);
        formData.append('iscompressed', false); //是否压缩
        let xhr = new XMLHttpRequest();
        // 如果需要发送 cookies 或其他凭据
        xhr.withCredentials = true;
        xhr.open("POST", window.FILEIP);
        // xhr.upload.onprogress = function(e) {
        //     progress((e.loaded / e.total) * 100);
        // };
        xhr.onload = function() {
            let json;
            if (xhr.status === 403) {
                console.log("HTTP Error: " + xhr.status, { remove: true });
                return;
            }
            if (xhr.status < 200 || xhr.status >= 300) {
                console.log("HTTP Error: " + xhr.status);
                return;
            }
            json = JSON.parse(xhr.responseText);
            console.log("图片上传", json);
            if (!json || typeof json.data != "string") {
                console.log("Invalid JSON: " + xhr.responseText);
                return;
            }
            // success(json.data);
            tinymce.execCommand(
                "mceReplaceContent",
                false,
                `<img class="copyImg" src="${json.data}" >`
            );
        };
        xhr.onerror = function() {
            console.log("Image upload failed due to a XHR Transport error. Code: " + xhr.status);
        };
        xhr.send(formData);
      } else {
        // 不是图⽚类型直接粘贴, 跳过oss上传处理
        console.log("粘贴的不是图⽚");
      }
    }
    let iframeContent = $(editor.container).find('.tox-edit-area').find('iframe')[0]
    var iframeDoc = iframeContent.contentDocument || iframeContent.contentWindow.document;
    setTimeout(()=>{
        let text = $(iframeDoc).find('body').html()
        if(text.indexOf('file:///')>-1){
            Message({
                type:'warning',
                message:'图片粘贴失败，请单独选中图片再复制粘贴',
                offset:260,
            })
        }
    },500)
  }

export { settings };