// 数字转换为中文数字

export const handleCNnum =  function (val) {
  let CNnum = ''
  switch (val) {
    case '1':
      CNnum = '一'
      break
    case '2':
      CNnum = '二'
      break
    case '3':
      CNnum = '三'
      break
    case '4':
      CNnum = '四'
      break
    case '5':
      CNnum = '五'
      break
    case '6':
      CNnum = '六'
      break
    case '7':
      CNnum = '七'
      break
    case '8':
      CNnum = '八'
      break
    case '9':
      CNnum = '九'
      break
    case '0':
      CNnum = '零'
      break
    default:
      break
  }
  return CNnum
}
export default function numToCN(val) {
  const num = val + ''
  const nums = num.split('').reverse()
  let CNnums = ''
  nums.forEach((item, index) => {
    let CNnum = ''
    // 多个零时取一个零
    if (item === '0' && nums[index + 1] === '0') {
      return
    } else {
      // 十位为一时不显示一
      if (item === '1' && index === 1) {
        return
      } else {
        CNnum = handleCNnum(item)
      }
    }
    if (item !== '0') {
      if (index === 1) {
        CNnum = '十' + CNnum
      } else if (index === 2) {
        CNnum = '百' + CNnum
      } else if (index === 3) {
        CNnum = '千' + CNnum
      } else if (index === 4) {
        CNnum = '万' + CNnum
      }
    }
    CNnums += CNnum
  })
  const value = CNnums.split('')
  // 个位为零时不显示
  if (value[0] === '零') {
    value.shift()
  }
  return value.reverse().join('')
}

 
