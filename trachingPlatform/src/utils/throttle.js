/*
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-10 08:53:59
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-02-20 11:35:30
 * @FilePath: \fusion_front\src\utils\throttle.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export function throttle(func, limit) {
    let lastFunc;
    let lastRan;
    return function () {
        const context = this;
        const args = arguments;
        if (!lastRan) {
            func.apply(context, args);
            lastRan = Date.now();
        } else {
            clearTimeout(lastFunc);
            lastFunc = setTimeout(function () {
                if ((Date.now() - lastRan) >= limit) {
                    func.apply(context, args);
                    lastRan = '';
                }
            }, limit);
        }
    }
} 
export function debounce(fn, delay) {
  //  防抖
  var timeout = null; // 创建一个标记用来存放定时器的返回值
  return (e) => {
    clearTimeout(timeout); // 每当用户输入的时候把前一个 setTimeout clear 掉
    // 然后又创建一个新的 setTimeout, 这样就能保证interval 间隔内如果时间持续触发，就不会执行 fn 函数
    timeout = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}