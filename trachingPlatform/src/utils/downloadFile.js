/*
 * @Author: xiaochongyang <EMAIL>
 * @Date: 2024-11-22 09:40:27
 * @LastEditors: xiaochongyang <EMAIL>
 * @LastEditTime: 2024-12-16 15:59:52
 * @FilePath: \fusion_front\src\utils\downloadFile.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export function downloadFile(url, fileName, self) {//下载文件方法
  const loading = self.$loading({
    lock: true,
    text: '转换中...',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.8)'
  });
  const x = new XMLHttpRequest()
  x.open("GET", url, true)
  x.responseType = 'blob'
  let extension = url.split('.').pop();
  if (extension == 'png!png') {//服务器格式不一样 导致下载不了对应的文件
    extension = 'png';
  } else if (extension == 'zip!w1200') {
    extension = 'zip';
  }
  x.onload = function (e) {
    console.log(x, 'createObjectURL');
    if (x.readyState === 4 && x.status === 200) {
      const url = window.URL.createObjectURL(x.response);
      const a = document.createElement('a');
      a.href = url;
      a.target = '_blank';
      a.download = `${fileName}.${extension}`;
      a.click();
      a.remove();
      // 销毁创建的url
      window.URL.revokeObjectURL(url)
      setTimeout(() => {
        loading.close();
      }, 500);
    }
  }
  x.send()
}