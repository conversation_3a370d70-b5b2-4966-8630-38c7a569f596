/**
 * Created by PanJiaChen on 16/11/18.
 */
import numToCN from './numToCN'

// 正则校验&message提示
export const expObject = {
  exp1: { // 检验数字英文和中划线
    RegExp: /^[A-Za-z0-9-]*$/,
    errorMsg1: '请输入数字英文和中划线'
  },
  exp2: { // 检验数字中英文和中划线
    RegExp: /^[-\u4e00-\u9fa5a-z-A-Z0-9]*$/,
    errorMsg1: '请输入数字中英文和中划线'
  },
  exp3: { // 检验数字中英文
    RegExp: /^[\u4e00-\u9fa5a-z-A-Z0-9]*$/,
    errorMsg1: '请输入数字和中英文'
  }
}

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/** 判断是否是大于0小于num的自然数,最多digit位小数
 * @param {string} value
 * @param {string} rule
 * @param {string} digit 小数位
 * @param {string} num 最大值
 * @returns callback
 */
export function validateNumber(rule, value, callback, num = 100, digit = 4) {
  const exp = new RegExp(`^(?:0|[1-9][0-9]?)(\.[0-9]{0,${digit}})?$`)
  const expNumber = /^\d+(\.\d+)?$/
  if (!expNumber.test(value)) {
    return callback(new Error(`请输入数字,最多${numToCN(digit)}位小数`))
  }
  if (value <= 0 || value >= num || !exp.test(value)) {
    return callback(new Error(`请输入大于0小于${num}的自然数,最多${numToCN(digit)}位小数`))
  } else {
    return callback()
  }
}
/** 判断是否是大于0小于一亿的自然数,最多3位小数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validateNumber2(rule, value, callback) {
  if (!/^-?(?:0.\d{1,3}|[1-9][0-9]{0,9}|[0-9]{1,10}.\d{1,3})$/.test(value)) {
    return callback(new Error('请输入数字,最多三位小数'))
  }
  if (value <= 0 || value >= 100000000) {
    return callback(new Error('请输入大于0小于100000000的自然数'))
  } else {
    return callback()
  }
}

/** 判断是否是大于0小于num的自然数,最多2位小数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 * @param {number} num
 */
export function validateNumber3(rule, value, callback, num) {
  if (!/^-?(?:0.\d{1,2}|[1-9][0-9]{0,9}|[0-9]{1,50}(.\d{1,2})?)$/.test(value)) {
    return callback(new Error('请输入数字,最多两位小数'))
  }
  if (value <= 0 || value >= num) {
    return callback(new Error(`请输入大于0小于${num}的自然数`))
  } else {
    return callback()
  }
}

/** 大于0整数，最大4位数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validateNumber4(rule, value, callback) {
  if (!/^\d{0,4}$/.test(value)) {
    return callback(new Error('请输入大于0小于一万的整数'))
  } else {
    return callback()
  }
}
/** 大于0整数，最大7位数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validateNumber5(rule, value, callback) {
  if (!/^\d{0,7}$/.test(value)) {
    return callback(new Error('请输入大于0小于一千万的整数'))
  } else {
    return callback()
  }
}
/** 大于0整数，最大6位数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validateNumber6(rule, value, callback) {
  if (!/^\d{0,6}$/.test(value)) {
    return callback(new Error('请输入大于0小于一百万的整数'))
  } else {
    return callback()
  }
}
/** 大于0小于31整数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validateNumber7(rule, value, callback) {
  if (!/^\d+$/.test(value)) {
    return callback(new Error('请输入大于0小于等于31的整数'))
  }
  if (value <= 0 || value > 31) {
    return callback(new Error('请输入大于0小于等于31的整数'))
  } else {
    return callback()
  }
}
/** 大于0整数，最大8位数
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
// export function validateNumber9(rule, value, callback) {
//   if (!/^\d{0,8}$/.test(value)) {
//     return callback(new Error('请输入大于0小于一亿的整数'))
//   } else {
//     return callback()
//   }
// }
/** 手机号
 * @param {string} value
 * @param {string} rule
 * @returns callback
 */
export function validatePhone(rule, value, callback) {
  if (!/^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/.test(value)) {
    return callback(new Error('手机号码格式不正确'))
  } else {
    return callback()
  }
}

/** 大于0小于等于num的自然数，保留x位小数
 * @param {string} value
 * @param {string} rule
 * @param {string} digit 小数位
 * @param {string} num 最大值
 * @returns callback
 */
export function rangeNumberValidate(rule, value, callback, num = 100, digit = 2) {
  const exp = new RegExp(`^(?:0|[1-9][0-9]?)(\.[0-9]{0,${digit}})?$`)
  const expNumber = /^\d+(\.\d+)?$/
  if (!expNumber.test(value)) {
    callback(new Error(`请输入数字,最多${numToCN(digit)}位小数`))
  }
  if (value <= 0 || value > num || !exp.test(value)) {
    callback(new Error(`请输入大于0小于${num}的自然数,最多${numToCN(digit)}位小数`))
  } else {
    callback()
  }
}

/** 判断是否是大于0的自然数,最多digit位小数
 * @param {string} value
 * @param {string} rule
 * @param {number} digit 小数位
 * @param {number} intDigit 整数位
 * @returns callback
 */
export function numberValidate(rule, value, callback, digit = 4, intDigit) {
  const expNumber = /^\d+(\.\d+)?$/
  const exp = new RegExp(`^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,${digit}}[0-9]{0,${digit}}$`)
  const strVal = String(value)
  const valNum = strVal.split('.')
  if (!expNumber.test(value)) {
    callback(new Error(`请输入数字,最多${numToCN(digit)}位小数`))
  }
  if (parseFloat(value) <= 0 || !exp.test(value)) {
    callback(new Error(`请输入大于0的自然数,最多${numToCN(digit)}位小数`))
  }
  if (intDigit && valNum[0]?.length > intDigit) {
    callback(
      new Error(`请输入${numToCN(intDigit)}位整数,最多${numToCN(digit)}位小数`)
    )
  } else {
    callback()
  }
}

/** 常规校验方法
 * @param {string} value
 * @param {string} rule
* @param {string} type
 * @returns callback
 */
export function normalValidate(rule, value, callback, type) {
  const regular = expObject[type].RegExp
  const msg = expObject[type].errorMsg1
  if (!regular.test(value)) {
    callback(new Error(`${msg}`))
  } else {
    callback()
  }
}
