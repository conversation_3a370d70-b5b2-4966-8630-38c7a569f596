import rem from './rem';
import './addMeta';

const  getBox=(x, y, width, height)=> {
  const x1 = (x + width) < x ? (x + width) : x
  const x2 = (x + width) > x ? (x + width) : x
  const y1 = (y + height) < y ? (y + height) : y
  const y2 = (y + height) > y ? (y + height) : y
  return {
      x1, x2, y1, y2
  }
}
// --------------------------------------- from antv-g6-editor end
export default {
  rem,
  getBox
};
// eslint-disable-next-line no-extend-native
String.prototype.trim = function() {
  return this.replace(/(^\s*)|(\s*$)/g, '');
};
