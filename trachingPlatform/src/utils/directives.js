import Vue from 'vue'


// let allOpendLast = 3000;
/*
 *  定义公共js里，在入口文件main.js中import；
 *  给elementUI的dialog上加上 v-dialogDrag 指令就可以实现弹窗的全屏和拉伸了。
 */
// v-dialogDrag: 弹窗拖拽+水平方向伸缩
Vue.directive('dialogDrag', {
    bind(el, binding, vnode, oldVnode) {
        if (binding.value === false) {
            return
        }
        el.style.pointerEvents = 'none';
        // console.log(el); //el-dialog__wrapper
        //弹框可拉伸最小宽高
        let minWidth = 500;
        let minHeight = 300;
        //初始非全屏
        let isFullScreen = false;
        //当前宽高
        let nowWidth = 0;
        let nowHight = 0;
        //当前顶部高度
        let nowMarginTop = 0;
        //获取弹框头部（这部分可双击全屏）
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        //弹窗
        const dragDom = el.querySelector('.el-dialog');
        //给弹窗加上overflow auto；不然缩小时框内的标签可能超出dialog；
        // dragDom.style.overflow = "auto";
        //清除选择头部文字效果
        dialogHeaderEl.onselectstart = new Function("return false");
        //头部加上可拖动cursor
        dialogHeaderEl.style.cursor = 'move';
        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);
        let moveDown = (e) => {
            // 全屏不拖动
            if (dragDom.className.indexOf('full-dialog') > -1) return
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - dialogHeaderEl.offsetLeft;
            const disY = e.clientY - dialogHeaderEl.offsetTop;
            // 获取到的值带px 正则匹配替换
            let styL, styT;
            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (sty.left.includes('%')) {
                styL = +document.body.clientWidth * (+sty.left.replace(/%/g, '') / 100);
                styT = +document.body.clientHeight * (+sty.top.replace(/%/g, '') / 100);
            } else {
                styL = +sty.left.replace(/px/g, '');
                styT = +sty.top.replace(/px/g, '');
            };
            document.onmousemove = function (e) {
                // 通过事件委托，计算移动的距离
                const l = e.clientX - disX;
                const t = e.clientY - disY;
                let left = l + styL;
                let top = t + styT;
                const offsetWidth = (el.offsetWidth - dragDom.offsetWidth) / 2
                const offsetTop = (el.offsetHeight - dragDom.offsetHeight) / 2
                if (top > offsetTop + dragDom.offsetHeight - 70) {
                    top = offsetTop + dragDom.offsetHeight - 70;
                } else if (top < -offsetTop) {
                    top = -offsetTop;
                }
                if (left > offsetWidth + dragDom.offsetWidth - 150) {
                    left = offsetWidth + dragDom.offsetWidth - 150;
                } else if (left < -offsetWidth) {
                    left = -offsetWidth;
                }
                dragDom.style.left = `${left}px`;
                dragDom.style.top = `${top}px`;
                // }
                //将此时的位置传出去
                //binding.value({x:e.pageX,y:e.pageY})
            };
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
        dialogHeaderEl.onmousedown = moveDown;
        dialogHeaderEl.onclick = (e) => {
        }
        return true
        //双击头部效果
        dialogHeaderEl.ondblclick = (e) => {
            if (isFullScreen == false) {
                nowHight = dragDom.clientHeight;
                nowWidth = dragDom.clientWidth;
                nowMarginTop = dragDom.style.marginTop;
                dragDom.style.left = 0;
                dragDom.style.top = 0;
                dragDom.style.height = "100VH";
                dragDom.style.width = "100VW";
                dragDom.style.marginTop = 0;
                isFullScreen = true;
                dialogHeaderEl.style.cursor = 'initial';
                dialogHeaderEl.onmousedown = null;
            } else {
                dragDom.style.height = "auto";
                dragDom.style.width = nowWidth + 'px';
                dragDom.style.marginTop = nowMarginTop;
                isFullScreen = false;
                dialogHeaderEl.style.cursor = 'move';
                dialogHeaderEl.onmousedown = moveDown;
            }
        }
        //拉伸(右下方)
        let resizeEl = document.createElement("div");
        dragDom.appendChild(resizeEl);
        //在弹窗右下角加上一个10-10px的控制块
        resizeEl.style.cursor = 'se-resize';
        resizeEl.style.position = 'absolute';
        resizeEl.style.height = '10px';
        resizeEl.style.width = '10px';
        resizeEl.style.right = '0px';
        resizeEl.style.bottom = '0px';
        resizeEl.style.zIndex = '99';
        //鼠标拉伸弹窗
        resizeEl.onmousedown = (e) => {
            // 记录初始x位置
            let clientX = e.clientX;
            // 鼠标按下，计算当前元素距离可视区的距离
            let disX = e.clientX - resizeEl.offsetLeft;
            let disY = e.clientY - resizeEl.offsetTop;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                let contentDom = el.querySelector('.detail-text');
                let contentDoms = el.querySelector('.detail-texts');
                let contentDominfo = el.querySelector('.detail-textn');
                // 通过事件委托，计算移动的距离
                let x = e.clientX - disX + (e.clientX - clientX); //这里 由于elementUI的dialog控制居中的，所以水平拉伸效果是双倍
                let y = e.clientY - disY;
                //比较是否小于最小宽高
                dragDom.style.width = x > minWidth ? `${x}px` : minWidth + 'px';
                dragDom.style.height = y > minHeight ? `${y}px` : minHeight + 'px';
                if (contentDom) {
                    contentDom.style.height = y > minHeight ? `${y - 170}px` : minHeight - 170 + 'px';
                }
                if (contentDoms) {
                    contentDoms.style.height = y > minHeight ? `${y - 220}px` : minHeight - 220 + 'px';
                }
                if (contentDominfo) {
                    contentDominfo.style.height = y > minHeight ? `${y - 170}px` : minHeight - 170 + 'px';
                }
            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        //拉伸(右边)
        let resizeElR = document.createElement("div");
        dragDom.appendChild(resizeElR);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElR.style.cursor = 'w-resize';
        resizeElR.style.position = 'absolute';
        resizeElR.style.height = '100%';
        resizeElR.style.width = '5px';
        resizeElR.style.right = '0px';
        resizeElR.style.top = '0px';
        //鼠标拉伸弹窗
        resizeElR.onmousedown = (e) => {
            let elW = dragDom.clientWidth;
            let EloffsetLeft = dragDom.offsetLeft;
            // 记录初始x位置
            let clientX = e.clientX;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                //右侧鼠标拖拽位置
                if (clientX > EloffsetLeft + elW - 10 && clientX < EloffsetLeft + elW) {
                    //往左拖拽
                    if (clientX > e.clientX) {
                        if (dragDom.clientWidth < minWidth) { } else {
                            dragDom.style.width = elW - (clientX - e.clientX) * 2 + 'px';
                        }
                    }
                    //往右拖拽
                    if (clientX < e.clientX) {
                        dragDom.style.width = elW + (e.clientX - clientX) * 2 + 'px';
                    }
                }

            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        //拉伸(左边)
        let resizeElL = document.createElement("div");
        dragDom.appendChild(resizeElL);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElL.style.cursor = 'w-resize';
        resizeElL.style.position = 'absolute';
        resizeElL.style.height = '100%';
        resizeElL.style.width = '10px';
        resizeElL.style.left = '0px';
        resizeElL.style.top = '0px';
        //鼠标拉伸弹窗
        resizeElL.onmousedown = (e) => {
            let elW = dragDom.clientWidth;
            let EloffsetLeft = dragDom.offsetLeft;
            // 记录初始x位置
            let clientX = e.clientX;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                //左侧鼠标拖拽位置
                if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
                    //往左拖拽
                    if (clientX > e.clientX) {
                        dragDom.style.width = elW + (clientX - e.clientX) * 2 + 'px';
                    }
                    //往右拖拽
                    if (clientX < e.clientX) {
                        if (dragDom.clientWidth < minWidth) { } else {
                            dragDom.style.width = elW - (e.clientX - clientX) * 2 + 'px';
                        }
                    }
                }

            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        // 拉伸(下边)
        let resizeElB = document.createElement("div");
        dragDom.appendChild(resizeElB);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElB.style.cursor = 'n-resize';
        resizeElB.style.position = 'absolute';
        resizeElB.style.height = '10px';
        resizeElB.style.width = '100%';
        resizeElB.style.left = '0px';
        resizeElB.style.bottom = '0px';
        //鼠标拉伸弹窗
        resizeElB.onmousedown = (e) => {
            let EloffsetTop = dragDom.offsetTop;
            let ELscrollTop = el.scrollTop;
            let clientY = e.clientY;
            let elH = dragDom.clientHeight;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                let contentDom = el.querySelector('.detail-text');
                let contentDoms = el.querySelector('.detail-texts');
                let contentDominfo = el.querySelector('.detail-textn');
                //底部鼠标拖拽位置
                if (ELscrollTop + clientY > EloffsetTop + elH - 20 && ELscrollTop + clientY < EloffsetTop + elH) {
                    //往上拖拽
                    if (clientY > e.clientY) {
                        if (dragDom.clientHeight < minHeight) { } else {
                            dragDom.style.height = elH - (clientY - e.clientY) * 2 + 'px';
                            if (contentDom) {
                                contentDom.style.height = Number(elH - (clientY - e.clientY) * 2 - 180) + 'px';
                            }
                            if (contentDoms) {
                                contentDoms.style.height = Number(elH - (clientY - e.clientY) * 2 - 220) + 'px';
                            }
                            if (contentDominfo) {
                                contentDominfo.style.height = Number(elH - (clientY - e.clientY) * 2 - 170) + 'px';
                            }
                        }
                    }
                    //往下拖拽
                    if (clientY < e.clientY) {
                        dragDom.style.height = elH + (e.clientY - clientY) * 2 + 'px';
                        if (contentDom) {
                            contentDom.style.height = Number(elH + (e.clientY - clientY) * 2 - 180) + 'px';
                        }
                        if (contentDoms) {
                            contentDoms.style.height = Number(elH + (e.clientY - clientY) * 2 - 220) + 'px';
                        }
                        if (contentDominfo) {
                            contentDominfo.style.height = Number(elH + (e.clientY - clientY) * 2 - 170) + 'px';
                        }
                    }
                }
            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
    }
})
Vue.directive('dialogDragTask', {
    bind(el, binding, vnode, oldVnode) {
        // console.log(el); //el-dialog__wrapper
        //弹框可拉伸最小宽高
        let minWidth = 500;
        let minHeight = 300;
        //初始非全屏
        let isFullScreen = false;
        //当前宽高
        let nowWidth = 0;
        let nowHight = 0;
        //当前顶部高度
        let nowMarginTop = 0;
        //获取弹框头部（这部分可双击全屏）
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        //弹窗
        const dragDom = el.querySelector('.el-dialog');
        //给弹窗加上overflow auto；不然缩小时框内的标签可能超出dialog；
        // dragDom.style.overflow = "auto";
        //清除选择头部文字效果
        dialogHeaderEl.onselectstart = new Function("return false");
        //头部加上可拖动cursor
        dialogHeaderEl.style.cursor = 'move';
        // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);
        let moveDown = (e) => {
            // 鼠标按下，计算当前元素距离可视区的距离
            const disX = e.clientX - dialogHeaderEl.offsetLeft;
            const disY = e.clientY - dialogHeaderEl.offsetTop;
            // 获取到的值带px 正则匹配替换
            let styL, styT;
            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (sty.left.includes('%')) {
                styL = +document.body.clientWidth * (+sty.left.replace(/%/g, '') / 100);
                styT = +document.body.clientHeight * (+sty.top.replace(/%/g, '') / 100);
            } else {
                styL = +sty.left.replace(/px/g, '');
                styT = +sty.top.replace(/px/g, '');
            };
            document.onmousemove = function (e) {
                // 通过事件委托，计算移动的距离
                const l = e.clientX - disX;
                const t = e.clientY - disY;

                //let maxW = document.body.clientWidth;
                let maxH = document.body.clientHeight;
                //let styW = sty.width.replace('px', '') - 0;
                let styH = sty.height.replace('px', '') - 0;
                // 移动当前元素
                if (l + styL <= 0 || t + styT <= 0) {
                    dragDom.style.left = `${(l + styL) < 0 ? 0 : (l + styL)}px`;
                    dragDom.style.top = `${(t + styT) < 0 ? 0 : (t + styT)}px`;
                } else if ((t + styT) >= maxH - styH) {
                    dragDom.style.top = `${(t + styT) > maxH - styH ? maxH - styH : (t + styT)}px`;
                } else {
                    dragDom.style.left = `${(l + styL)}px`;
                    dragDom.style.top = `${(t + styT)}px`;
                }
                //将此时的位置传出去
                //binding.value({x:e.pageX,y:e.pageY})
            };
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
        dialogHeaderEl.onmousedown = moveDown;
        //双击头部效果
        dialogHeaderEl.ondblclick = (e) => {
            return
            if (isFullScreen == false) {
                nowHight = dragDom.clientHeight;
                nowWidth = dragDom.clientWidth;
                nowMarginTop = dragDom.style.marginTop;
                dragDom.style.right = 0;
                dragDom.style.top = '100px';
                dragDom.style.height = "calc(100VH - 110px)";
                dragDom.style.width = "500px";
                dragDom.style.marginTop = 0;
                isFullScreen = false;
                // isFullScreen = true;
                // dialogHeaderEl.style.cursor = 'initial';
                // dialogHeaderEl.onmousedown = null;
            } else {
                dragDom.style.height = "auto";
                dragDom.style.width = nowWidth + 'px';
                dragDom.style.marginTop = nowMarginTop;
                isFullScreen = false;
                dialogHeaderEl.style.cursor = 'move';
                dialogHeaderEl.onmousedown = moveDown;
            }
        }

        //拉伸(右下方)
        let resizeEl = document.createElement("div");
        dragDom.appendChild(resizeEl);
        //在弹窗右下角加上一个10-10px的控制块
        resizeEl.style.cursor = 'se-resize';
        resizeEl.style.position = 'absolute';
        resizeEl.style.height = '10px';
        resizeEl.style.width = '10px';
        resizeEl.style.right = '0px';
        resizeEl.style.bottom = '0px';
        resizeEl.style.zIndex = '99';
        //鼠标拉伸弹窗
        resizeEl.onmousedown = (e) => {
            // 记录初始x位置
            let clientX = e.clientX;
            // 鼠标按下，计算当前元素距离可视区的距离
            let disX = e.clientX - resizeEl.offsetLeft;
            let disY = e.clientY - resizeEl.offsetTop;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                let contentDom = el.querySelector('.detail-text');
                let contentDoms = el.querySelector('.detail-texts');
                let contentDominfo = el.querySelector('.detail-textn');
                // 通过事件委托，计算移动的距离
                let x = e.clientX - disX + (e.clientX - clientX); //这里 由于elementUI的dialog控制居中的，所以水平拉伸效果是双倍
                let y = e.clientY - disY;
                //比较是否小于最小宽高
                dragDom.style.width = x > minWidth ? `${x}px` : minWidth + 'px';
                dragDom.style.height = y > minHeight ? `${y}px` : minHeight + 'px';
                if (contentDom) {
                    contentDom.style.height = y > minHeight ? `${y - 170}px` : minHeight - 170 + 'px';
                }
                if (contentDoms) {
                    contentDoms.style.height = y > minHeight ? `${y - 220}px` : minHeight - 220 + 'px';
                }
                if (contentDominfo) {
                    contentDominfo.style.height = y > minHeight ? `${y - 170}px` : minHeight - 170 + 'px';
                }
            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        //拉伸(右边)
        let resizeElR = document.createElement("div");
        dragDom.appendChild(resizeElR);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElR.style.cursor = 'w-resize';
        resizeElR.style.position = 'absolute';
        resizeElR.style.height = '100%';
        resizeElR.style.width = '5px';
        resizeElR.style.right = '0px';
        resizeElR.style.top = '0px';
        //鼠标拉伸弹窗
        resizeElR.onmousedown = (e) => {
            let elW = dragDom.clientWidth;
            let EloffsetLeft = dragDom.offsetLeft;
            // 记录初始x位置
            let clientX = e.clientX;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                //右侧鼠标拖拽位置
                if (clientX > EloffsetLeft + elW - 10 && clientX < EloffsetLeft + elW) {
                    //往左拖拽
                    if (clientX > e.clientX) {
                        if (dragDom.clientWidth < minWidth) { } else {
                            dragDom.style.width = elW - (clientX - e.clientX) * 2 + 'px';
                        }
                    }
                    //往右拖拽
                    if (clientX < e.clientX) {
                        dragDom.style.width = elW + (e.clientX - clientX) * 2 + 'px';
                    }
                }

            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        //拉伸(左边)
        let resizeElL = document.createElement("div");
        dragDom.appendChild(resizeElL);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElL.style.cursor = 'w-resize';
        resizeElL.style.position = 'absolute';
        resizeElL.style.height = '100%';
        resizeElL.style.width = '10px';
        resizeElL.style.left = '0px';
        resizeElL.style.top = '0px';
        //鼠标拉伸弹窗
        resizeElL.onmousedown = (e) => {
            let elW = dragDom.clientWidth;
            let EloffsetLeft = dragDom.offsetLeft;
            // 记录初始x位置
            let clientX = e.clientX;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                //左侧鼠标拖拽位置
                if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
                    //往左拖拽
                    if (clientX > e.clientX) {
                        dragDom.style.width = elW + (clientX - e.clientX) * 2 + 'px';
                    }
                    //往右拖拽
                    if (clientX < e.clientX) {
                        if (dragDom.clientWidth < minWidth) { } else {
                            dragDom.style.width = elW - (e.clientX - clientX) * 2 + 'px';
                        }
                    }
                }

            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }

        // 拉伸(下边)
        let resizeElB = document.createElement("div");
        dragDom.appendChild(resizeElB);
        //在弹窗右下角加上一个10-10px的控制块
        resizeElB.style.cursor = 'n-resize';
        resizeElB.style.position = 'absolute';
        resizeElB.style.height = '10px';
        resizeElB.style.width = '100%';
        resizeElB.style.left = '0px';
        resizeElB.style.bottom = '0px';
        //鼠标拉伸弹窗
        resizeElB.onmousedown = (e) => {
            let EloffsetTop = dragDom.offsetTop;
            let ELscrollTop = el.scrollTop;
            let clientY = e.clientY;
            let elH = dragDom.clientHeight;
            document.onmousemove = function (e) {
                e.preventDefault(); // 移动时禁用默认事件
                let contentDom = el.querySelector('.detail-text');
                let contentDoms = el.querySelector('.detail-texts');
                let contentDominfo = el.querySelector('.detail-textn');
                //底部鼠标拖拽位置
                if (ELscrollTop + clientY > EloffsetTop + elH - 20 && ELscrollTop + clientY < EloffsetTop + elH) {
                    //往上拖拽
                    if (clientY > e.clientY) {
                        if (dragDom.clientHeight < minHeight) { } else {
                            dragDom.style.height = elH - (clientY - e.clientY) * 2 + 'px';
                            if (contentDom) {
                                contentDom.style.height = Number(elH - (clientY - e.clientY) * 2 - 180) + 'px';
                            }
                            if (contentDoms) {
                                contentDoms.style.height = Number(elH - (clientY - e.clientY) * 2 - 220) + 'px';
                            }
                            if (contentDominfo) {
                                contentDominfo.style.height = Number(elH - (clientY - e.clientY) * 2 - 170) + 'px';
                            }
                        }
                    }
                    //往下拖拽
                    if (clientY < e.clientY) {
                        dragDom.style.height = elH + (e.clientY - clientY) * 2 + 'px';
                        if (contentDom) {
                            contentDom.style.height = Number(elH + (e.clientY - clientY) * 2 - 180) + 'px';
                        }
                        if (contentDoms) {
                            contentDoms.style.height = Number(elH + (e.clientY - clientY) * 2 - 220) + 'px';
                        }
                        if (contentDominfo) {
                            contentDominfo.style.height = Number(elH + (e.clientY - clientY) * 2 - 170) + 'px';
                        }
                    }
                }
            };
            //拉伸结束
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        }
    }
})

// 水平方向
Vue.directive('drag-x', {
    bind: function (el, binding) {
        let odiv = el; //获取当前元素
        odiv.onmousedown = e => {
            //算出鼠标相对元素的位置
            let disX = e.clientX - odiv.offsetLeft;
            document.onmousemove = e => {
                //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
                let left = e.clientX - disX;
                //绑定元素位置到positionX和positionY上面
                odiv.style.left = left + "px";
                binding.value(e.pageX)
            };
            document.onmouseup = e => {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        };
    }
});

Vue.directive('drag', {
    bind: function (el) {
        // 确保我们绑定到的是el-dialog元素
        if (!el.classList.contains('el-dialog__wrapper')) return;

        // 获取对话框的头部  目标头部拖拽
        let dialogHeaderEl = el.querySelector('.el-dialog__header');
        const dragDom = el.querySelector('.el-dialog');
        if (!dialogHeaderEl) return;
        //清除选择头部文字效果
        dialogHeaderEl.onselectstart = new Function("return false");
        dialogHeaderEl.style.cursor = 'move';
        const sty = dragDom.currentStyle || window.getComputedStyle(dragDom, null);
        dialogHeaderEl.onmousedown = e => {
            // 阻止默认行为，如选择文本
            e.preventDefault();
            //算出鼠标相对元素的位置
            let disX = e.clientX - dialogHeaderEl.offsetLeft;
            let disY = e.clientY - dialogHeaderEl.offsetTop;
            let styL, styT;
            // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
            if (sty.left.includes('%')) {
                styL = +document.body.clientWidth * (+sty.left.replace(/%/g, '') / 100);
                styT = +document.body.clientHeight * (+sty.top.replace(/%/g, '') / 100);
            } else {
                styL = +sty.left.replace(/px/g, '');
                styT = +sty.top.replace(/px/g, '');
            };
            document.onmousemove = e => {
                //用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
                const l = e.clientX - disX;
                const t = e.clientY - disY;
                let left = l + styL;
                let top = t + styT;
                const offsetWidth = (el.offsetWidth - dragDom.offsetWidth) / 2
                const offsetTop = (el.offsetHeight - dragDom.offsetHeight) / 2

                if (top > offsetTop + dragDom.offsetHeight) {//可视宽度调整
                    top = offsetTop + dragDom.offsetHeight + 9;//向下
                } else if (top < -(offsetTop - dragDom.offsetHeight)) {
                    top = -(offsetTop - dragDom.offsetHeight + 20);//向上
                }
                if (left > offsetWidth) {
                    left = offsetWidth;//向右
                } else if (left < -offsetWidth) {
                    left = -offsetWidth;//向左
                }

                //绑定元素位置到positionX和positionY上面
                dragDom.style.left = `${left}px`;
                dragDom.style.top = `${top}px`;
            };
            document.onmouseup = e => {
                // 清除事件监听器
                document.onmousemove = null;
                document.onmouseup = null;
            };
        };
    },
    unbind(el) {
        const dialogHeaderEl = el.querySelector('.el-dialog__header');
        dialogHeaderEl.onmousedown = null;
    }
});
// 拖拽

Vue.directive('PartyDrag', {
    bind: function (el, binding, vnode) {
        // console.log("PartyDrag事件触发");
        let clientOffset = {};
        el.onselectstart = new Function("return false");
        // console.log(document.body, "document.body");
        let rangWidth = document.body.clientWidth; //父元素宽度，即 移动范围
        let rangHeight = document.body.clientHeight; //父元素高度
        el.onmousedown = function (e) {
            //鼠标按下，计算当前元素距离可视区的距离
            let disX = e.clientX - el.offsetLeft;
            let disY = e.clientY - el.offsetTop;
            const dWdith = el.clientWidth;
            const dHeight = el.clientHeight;
            const dMoveLeft = rangWidth - dWdith;
            const dMoveTop = rangHeight - dHeight;

            var disx = e.pageX - el.offsetLeft
            var disy = e.pageY - el.offsetTop
            clientOffset.clientX = e.clientX;
            clientOffset.clientY = e.clientY;
            document.onmousemove = function (e) {
                //通过事件委托，计算移动的距离
                let left = e.clientX - disX;
                let top = e.clientY - disY;
                el.style.left = e.pageX - disx + 'px'
                el.style.top = e.pageY - disy + 'px'
                //如果元素的移动位置大于窗口位置，则不再移动
                if (left > dMoveLeft) {
                    el.style.left = `${dMoveLeft}px`;
                } else {
                    el.style.left = `${left}px`;
                }
                if (left < 0) {
                    el.style.left = `0px`;
                }
                if (top > dMoveTop) {
                    el.style.top = `${dMoveTop}px`;
                } else {
                    el.style.top = `${top}px`;
                }
                if (top < 0) {
                    el.style.top = `0px`;
                }
            }
            document.onmouseup = function () {
                document.onmousemove = document.onmouseup = null
            }
        }
        el.addEventListener('click', (event) => { //鼠标抬起后的事件，判断是拖拽还是点击
            let clientX = event.clientX;
            let clientY = event.clientY;
            // console.log(clientOffset.clientX,clientX,clientOffset.clientY,clientY,'clientY');
            if (Math.abs(clientOffset.clientX - clientX) < 5 && Math.abs(clientOffset.clientY - clientY) < 5) { //这里触发点击事件，将click方法移入这里
                let text = $(event.target).parents('.cut-box').html()
                let that = vnode.context //通过vnode参数来获取this,此时的that就可以获取到js中的this
                if (that.$route.path == "/blackboardPage" && text?.indexOf("切换新版") > 1) {
                    that.backNewClick()
                } else if (that.$route.path == "/newblackboardPage" && text?.indexOf("切换旧版") > 1) {
                    that.backOldClick()
                }
            } else {
                // 对于ie9以上浏览器（手机浏览器，一直是保持最新内核标准，当然也支持这种方式获取）window.innerWidth
                // 对于ie9以下 document.documentElement.clientWidth || document.body.clientWidth
                let w = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
                el.style.left = (w - 92) + 'px';
                el.style.transition = "all 0.5s";
                setTimeout(() => {
                    el.style.transition = "none";
                }, 500);
                console.log("拖拽事件触发");
                return //这里是拖拽事件
            }
        })
    }
})
Vue.directive('vocherDrag', {
    bind: function (el, binding, vnode) {
        // console.log("PartyDrag事件触发");
        let clientOffset = {};
        el.onselectstart = new Function("return false");
        // console.log(document.body, "document.body");
        let rangWidth = document.body.clientWidth; //父元素宽度，即 移动范围
        let rangHeight = document.body.clientHeight; //父元素高度
        el.onmousedown = function (e) {
            //鼠标按下，计算当前元素距离可视区的距离
            let disX = e.clientX - el.offsetLeft;
            let disY = e.clientY - el.offsetTop;
            const dWdith = el.clientWidth;
            const dHeight = el.clientHeight;
            const dMoveLeft = rangWidth - dWdith;
            const dMoveTop = rangHeight - dHeight;

            var disx = e.pageX - el.offsetLeft
            var disy = e.pageY - el.offsetTop
            clientOffset.clientX = e.clientX;
            clientOffset.clientY = e.clientY;
            
            document.onmousemove = function (e) {
                //通过事件委托，计算移动的距离
                let left = e.clientX - disX;
                let top = e.clientY - disY;
                el.style.left = e.pageX - disx + 'px'
                el.style.top = e.pageY - disy + 'px'
                //如果元素的移动位置大于窗口位置，则不再移动
                if (left > dMoveLeft) {
                    el.style.left = `${dMoveLeft}px`;
                } else {
                    el.style.left = `${left}px`;
                }
                if (left < 0) {
                    el.style.left = `0px`;
                }
                if (top > dMoveTop) {
                    el.style.top = `${dMoveTop}px`;
                } else {
                    el.style.top = `${top}px`;
                }
                if (top < 0) {
                    el.style.top = `0px`;
                }
            }
            document.onmouseup = function () {
                document.onmousemove = document.onmouseup = null
            }
        }
    }
})
// 节流 防止el-button重复点击 v-preventReClick="2000"
Vue.directive('preventReClick', {
    inserted(el, binding) {
        el.addEventListener('click', () => {
            if (!el.disabled) {
                el.disabled = true;
                el.style.cursor = 'not-allowed'
                setTimeout(() => {
                    el.disabled = false
                    el.style.cursor = 'pointer'
                }, binding.value || 2000)
            }
        })
    }
})

// v-focus: 自动聚焦指令
Vue.directive('focus', {
    inserted(el) {
        // 如果元素是input或textarea，直接聚焦
        if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
            el.focus()
        } else {
            // 否则尝试查找子元素中的输入框并聚焦
            const input = el.querySelector('input')
            if (input) {
                input.focus()
            }
        }
    }
})

Vue.directive('premission', {
    bind(el, binding) {
        const code = binding.value
        let chapterMenu
        setTimeout(() => {
            chapterMenu = window.sessionStorage.getItem('chapterMenu')
            const hasPermission = chapterMenu?.includes(code)
            if (!hasPermission) {
                el.style.display = "none"
                el.remove()
                el.parentNode && el.parentNode.removeChild(el)
            }
        })
    }
})
