import tokenFunc from "@/utils/token";
import { dispatchRouters } from "@/router/index";
import api from "@/api";
import store from "../store";
import { Message } from "element-ui";
import { setCurrentCaseId, setNowCaseInfo } from "@/utils/token.js";
// import { saveState } from "@/utils/common";

const jwt = require("jsonwebtoken");
const handleLoginSuccess = (userInfo, router) => {
    localStorage.removeItem("submitPaperFlag");
    store.commit("userInfo", userInfo);
    // saveState();
    dispatchRouters(userInfo.userType, router);
    sessionStorage.setItem("eventType", 1); // 0:省赛 1:x赛
    // 0管理员 1学生 2教师 3教务 4专家 5开发 6专业组 7普通用户
    // userType 0管理员 1考生 2教师端发布者
    // 老师
    if (userInfo.userType === "2") {
        //7 普通用户身份
        // schoolType 0中职  1高职  2本科
        // if (userInfo.schoolType == 2 && this.keyType == 2) {
        //判断用户类型
        //判断首页选择
        sessionStorage.setItem("authenticationCode", 3);
    } else if (userInfo.userType == 1 || userInfo.userType == 7) {
        // 学生
        // if (userInfo.schoolType == 1 && this.keyType != 2) {
        sessionStorage.setItem("authenticationCode", 1);
    } else if (
        userInfo.userType == 0 ||
        userInfo.userType == 4 ||
        userInfo.userType == 6
    ) {
        //0 管理员身份  4专家身份 6 专业组
        sessionStorage.setItem("authenticationCode", 3);
    } else {
        Message.error("此账号暂无权限！");
        return;
    }
};
export const doSuccess = async(token, router, callback, caseId) => {
    tokenFunc.setToken(token);
    const userInfo = jwt.decode(token);
    let caseRes = {};
    if (caseId) {
        caseRes = await api.GetCaseInfoById({ caseId: caseId });
    } else {
        caseRes = await api.GetDefaultCaseByUserId({});
    }
    const { code, data } = caseRes;
    if (code === 200) {
        if (data) {
            sessionStorage.setItem("defaultCaseInfo", JSON.stringify(data));
            // caseInfo 写入 store,  写入 sessionStorage
            store.commit("caseInfo", data);
            setNowCaseInfo(data);
            // caseId 写入 sessionStorage
            setCurrentCaseId(data.id);
        }
        handleLoginSuccess(userInfo, router);
        callback();
    }
};
export const verifyKey = (key, router, callback) => {
    api.GetExperienceToken({ loginType: 3 }).then(tokenRes => {
        if (tokenRes.data) {
            api
                .GetScreenProjectionData({ key }, {
                    token: tokenRes.data.access_token
                })
                .then(res => {
                    // 认证通过，将认证后的信息存储到本地存储中
                    doSuccess(res.data.token, router, () => {
                        setTimeout(() => {
                            let url = res.data.url;
                            if (url.match(/\?/)) {
                                url = `${url}&screenKey=${key}`;
                            } else {
                                url = `${url}?screenKey=${key}`;
                            }
                            router.replace(url);
                        }, 100);
                    });

                    // callback({ status: 200, url: res.data.url });
                })
                .catch(err => {
                    // 认证失败，重定向到登录页
                    callback({ status: 0 });
                });
        } else {
            callback({ status: 0 });
        }
    });
};

export default doSuccess;