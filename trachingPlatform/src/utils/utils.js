// import Vue from 'vue';
import axios from 'axios';
import isArray from "lodash/isArray";
import { fileTypeMenu } from "./file.js"; //文件类型
export function swap(arr, i, j) {
    const temp = arr[i];
    Vue.set(arr, i, arr[j]);
    Vue.set(arr, j, temp);
}

// 将二维数组转为一维数组
export function flatten(arr) {
    // return [].concat(...arr.map(x =>
    //     Array.isArray(x) ? flatten(x) : x
    // ))
    return [].concat.apply([], arr);
}

export function $(selector) {
    return document.querySelector(selector);
}

/**
 * 判断计算公式是否正确
 * @param {} string
 * @param {*} obj
 * @returns
 */
export function fn(string, obj) {
    //let error = [];
    let result = {
        code: 200,
        msg: ""
    };
    // console.log(string);
    //console.log(obj);
    // 剔除空白符
    string = string.replace(/\s/g, "");
    // 错误情况，空字符串
    if ("" === string) {
        // console.error(& amp; quot; 空字符串 & amp; quot;);
        result.msg = "空字符串!";
        return result;
    }
    if (/^[\x\÷\+\-\*\/]/.test(string)) {
        // console.error(& amp; quot; 运算符开头 & amp; quot;);
        result.msg = "运算符开头!";
        return result;
    }

    //错误情况，运算符结尾
    if (/[\x\÷\+\-\*\/]$/.test(string)) {
        // console.error(& amp; quot; 运算符结尾 & amp; quot;);
        result.msg = "运算符结尾!";
        return result;
    }

    // 错误情况，(后面是运算符或者)
    if (/\([\x\÷\+\-\*\/]/.test(string)) {
        // console.error(& amp; quot; (后面是运算符或者) & amp; quot;);
        result.msg = "(后面是运算符或者)!";
        return result;
    }
    // 错误情况，运算符连续
    if (/[\x\÷\+\-\*\/]{2,}/.test(string)) {
        result.msg = "运算符连续!";
        return result;
    }

    //错误情况，使用除()+-*/之外的字符
    if (/[^\+\-\*\/0-9.a-zA-Z\(\)]/.test(string)) {
        result.msg = "使用除()+-*/之外的字符!";
        return result;
    }
    //运算符号不能在首末位
    if (/^[\+\-\*\/.]|[\+\-\*\/.]$/.test(string)) {
        result.msg = "运算符号不能在首末位!";
        return result;
    }
    // 空括号
    if (/\(\)/.test(string)) {
        // error.push("运算符连续!");
        result.msg = "空括号!";
        return result;
    }

    // 错误情况，括号不配对
    let stack = [];
    for (let i = 0, item; i < string.length; i++) {
        item = string.charAt(i);
        if ("(" === item) {
            stack.push("(");
        } else if (")" === item) {
            if (stack.length > 0) {
                stack.pop();
            } else {
                result.msg = "括号不配对!";
                return result;
            }
        }
    }

    if (0 !== stack.length) {
        result.msg = "(后面是运算符或者)!";
        return result;
    }

    // 错误情况，(后面是运算符
    if (/\([\x\÷\+\-\*\/]/.test(string)) {
        result.msg = "(后面是运算符!";
        return result;
    }

    // 错误情况，)前面是运算符
    if (/[\x\÷\+\-\*\/]\)/.test(string)) {
        result.msg = ")前面是运算符!";
        return result;
    }

    // 错误情况，(前面不是运算符
    if (/[\x\÷\+\-\*\/]\(/.test(string)) {
        result.msg = "(前面不是运算符!";
        return result;
    }

    // 错误情况，)后面不是运算符
    if (/\)[\x\÷\+\-\*\/]/.test(string)) {
        result.msg = ")后面不是运算符!";
        return result;
    }

    // 错误情况，变量没有来自“待选公式变量”
    var tmpStr = string.replace(/[\(\)\x\÷\+\-\*\/]{1,}/g, "`");
    var array = tmpStr.split("`");
    for (let i = 0, item; i < array.length; i++) {
        item = array[i];
        if (/[A-Z]/i.test(item) && "undefined" == typeof obj[item]) {
            result.msg = "变量没有来自“待选公式变量”";
            return result;
        }
    }
    let stringarr = string.replace(/[\(\)\x\÷\+\-\*\/]{1,}/g, "`").split("`");
    let objarr = Object.keys(obj);
    for (let index = 0; index < stringarr.length; index++) {
        if (objarr.indexOf(stringarr[index]) > -1) {
            if (stringarr[index + 1] == undefined) {} else if (
                stringarr[index + 1] !== "+" &&
                stringarr[index + 1] !== "." &&
                stringarr[index + 1] !== "-" &&
                stringarr[index + 1] !== "x" &&
                stringarr[index + 1] !== "÷" &&
                stringarr[index + 1] !== "(" &&
                stringarr[index + 1] !== ")"
            ) {
                //result.msg = "(后面是运算符或者)!";
                return result;
            }
        }
    }

    return result;
}

/**
 * 去重
 */
export function unique(arr, key) {
    const res = new Map();
    return arr.filter(a => !res.has(a[key]) && res.set(a[key], 1));
}

export function getImgUrl(file) {
    // if (!file) return
    // let suffix = file.substring(file.lastIndexOf('.') + 1);
    // let videoSuffixs = ['avi', 'rmvb', 'rm', 'asf', 'divx', 'mpg', 'mpeg', 'mpe', 'wmv', 'mp4', 'mkv', 'vob']
    // if (suffix == 'docx' || suffix == 'doc') {
    //   return require('@/assets/material/doc.png')
    // } else if (suffix == 'ppt' || suffix == 'pptx') {
    //   return require('@/assets/material/ppt.png')
    // } else if (suffix == 'pdf') {
    //   return require('@/assets/material/pdf.png')
    // } else if (suffix == 'png' || suffix == 'jpg'||suffix=='png!png') {
    //   return require('@/assets/material/img.png')
    // } else if (videoSuffixs.includes(suffix)) {
    //   return require('@/assets/material/video.png')
    // } else if (suffix == 'xls' || suffix == 'xlsx') {
    //   return require('@/assets/material/xlsx-material.png')
    // } else {
    //   // return require('@/assets/material/txt.png')  平台素材库不支持txt
    // }
    return;
};

/**
 * 
 * @returns 随机生成id
 */
export function guid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// 上传图片, 返回图片url
// export async function uploadImg(img) {
//     return new Promise((resolve, reject) => {
//         img.onload = async function() {
//             // 创建一个Canvas元素
//             var canvas = document.createElement('canvas');
//             canvas.width = img.width;
//             canvas.height = img.height;

//             // 获取2D上下文
//             var ctx = canvas.getContext('2d');

//             // 在Canvas上绘制图像
//             ctx.drawImage(img, 0, 0);

//             // 将Canvas转换为Data URL
//             var dataURL = canvas.toDataURL('image/jpeg');

//             // 将Data URL转换为Blob对象
//             var byteString = atob(dataURL.split(',')[1]);
//             var ab = new ArrayBuffer(byteString.length);
//             var ia = new Uint8Array(ab);
//             for (var i = 0; i < byteString.length; i++) {
//                 ia[i] = byteString.charCodeAt(i);
//             }
//             var blob = new Blob([ab], { type: 'image/jpeg' });
//             // 执行其他操作，如上传到服务器等
//             var formData = new FormData();
//             formData.append("file", blob, 'pic.jpg'); // filename 参数可选，指定上传时显示的文件名

//             // 发送 AJAX 请求或者其他上传操作
//             let { data, status } = await axios.post('http://slrh.chinazdap.com:9992/api/v1/UploadFiles/PostFiles', formData, {
//                 headers: {
//                     'Accept': '*/*',
//                     'Content-Type': 'multipart/form-data', // 设置请求头为 multipart/form-data
//                 }
//             });

//             if (status === 200) {
//                 resolve(data.data);
//             } else {
//                 resolve('');
//             }
//         };
//     });
// }


export function getLastDay(year, month) { //返回月份最后一天，不写参数默认返回本月最后一天
    var date = new Date(),
        date2, day;
    if (year === undefined) year = date.getFullYear(); //获取今年年份
    if (month === undefined) month = date.getMonth() + 1; //获取本月月份
    date2 = new Date(year, month, 0); //在JS中,如果使用new Date函数,可以使用传参数形式获取某一月的最后一天,并且自动判断润平年和大小月;
    day = date2.getDate(); //最后一天
    console.log(year, month, day);
    return day;
}

export function formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/*
 * 判断数据是否为空
 * date: 所需要判断的数据
 * diyType: 默认根据date的类型判断，若有自定义的type，则先根据diyType来判断，例如富文本是否为空
 * */
export function checkEmpty(data) {
    if (!data) return true

    if (typeof data === 'object') {
        if (isArray(data)) {
            return !data.length
        } else {
            return !Object.keys(data).length
        }
    } else if (typeof data === 'string') {
        return /^\s*$/.test(data)
    } else if (typeof data === 'number') {
        return data !== 0 && !data
    }
    return false
}

// 根据文件后缀名获取文件类型
export function getFileType(file) {
    // if (file.name.indexOf('.xlsx') > -1 || file.name.indexOf('.xls') > -1) {
    //     return fileTypeMenu.Excel
    // } else if (file.name.indexOf('.docx') > -1 || file.name.indexOf('.doc') > -1) {
    //     return fileTypeMenu.Word
    // } else if (file.name.indexOf('.pdf') > -1) {
    //     return fileTypeMenu.PDF
    // } else if (file.name.indexOf('.ppt') > -1) {
    //     return fileTypeMenu.PPT
    // } else if (file.name.indexOf('.jpg') > -1 || file.name.indexOf('.png') > -1 || file.name.indexOf('.tif') > -1 || file.name.indexOf('.gif') > -1 || file.name.indexOf('.svg') > -1) {
    //     return fileTypeMenu.Img
    // } else if (file.name.indexOf('.mp4') > -1) {
    //     // file.name.indexOf('.avi') > -1 || file.name.indexOf('.mpeg') > -1 || 
    //     return fileTypeMenu.Video
    // } else if (file.name.indexOf('.zip') > -1 || file.name.indexOf('.ZIP') > -1) {
    //     return fileTypeMenu.ZIP
    // } else if (file.name.indexOf('.txt') > -1 || file.name.indexOf('.TXT') > -1) {
    //     return fileTypeMenu.TXT
    // }
    return -1
}


//判断不同类型的资源Icon
export function getIcon(row, index) {
    //index 判断是否需要文件夹图标  row素材库中根据接口filesType展示图标
    // const fileTypeIcons = {
    //     [fileTypeMenu.Img]: require('@/assets/material/img.png'),
    //     [fileTypeMenu.Excel]: require('@/assets/material/excel-icon.png'),
    //     [fileTypeMenu.Word]: require('@/assets/material/doc.png'),
    //     [fileTypeMenu.PPT]: require('@/assets/material/ppt.png'),
    //     [fileTypeMenu.PDF]: require('@/assets/material/pdf.png'),
    //     [fileTypeMenu.Video]: require('@/assets/material/video.png'),
    //     [fileTypeMenu.TXT]: require('@/assets/material/txt.png'),
    //     [fileTypeMenu.ZIP]: require('@/assets/material/zip.png'),
    // };
    // if (index && !row.isFile) {
    //     return require('@/assets/question/folder.png');
    // }

    // const icon = fileTypeIcons[row.filesType] || '';
    // return icon;
    return ''
}