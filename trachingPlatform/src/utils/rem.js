/*
 * @Author: ztong <EMAIL>
 * @Date: 2024-01-10 08:53:59
 * @LastEditors: ztong <EMAIL>
 * @LastEditTime: 2024-03-27 10:03:39
 * @FilePath: \fusion_front\src\utils\rem.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const baseUI = 1920; // 表示1920的设计图

function setRem() {
    // 基准大小
    const baseSize = 100;
    const basePc = baseUI / baseSize; // 表示1440的设计图,使用100PX的默认值
    let vW = window.innerWidth; // 当前窗口的宽度
    if (vW <= 1440) vW = 1440;

    const rem = vW / basePc; // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值
    document.documentElement.style.fontSize = rem + 'px';
}
// 初始化
// setRem();
// 改变窗口大小时重新设置 rem
// window.onresize = function() {
//     setRem();
// };

// // 改变窗口大小时重新设置 rem
// window.onresize = function() {
//     setRem();
// };

// eslint-disable-next-line no-extend-native
// String.prototype.num2rem = function() {
//     const size = Number(this);
//     const newSize = size * window.innerWidth / baseUI;
//     return newSize || this;
// };
// // eslint-disable-next-line no-extend-native
// String.prototype.formatFontSize = function() {
//     const fonstSize = document.documentElement.style.fontSize;
//     return this * parseFloat(fonstSize) / 100;
// };

export default setRem;