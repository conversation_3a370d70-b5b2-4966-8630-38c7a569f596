/*
 * @Author: ch<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-27 16:03:55
 * @LastEditors: chenjiongming
 * @LastEditTime: 2024-04-09 14:44:03
 * @Description:
 */
import { getCurrentCaseId } from "@/utils/token.js";

export const getSceneJumpPath = ({ typeId, caseId, moduleId, item }) => {
  let path = "";
  switch (typeId) {
    case 1: // 动态表单
      path = `/case/${caseId}/form/${moduleId}`;
      break;
    case 2: // 葡萄城
      path = `/case/${caseId}/sheets/${moduleId}`;
      break;
    case 3: // 通用票据
      path = `/case/${caseId}/bill/${moduleId}`;
      break;
    case 4: // 业务基础数据
      path = `/case/${caseId}/data/${moduleId}`;
      break;
    case 5: // 列表
      path = `/case/${caseId}/page/${moduleId}`;
      break;
    case 7: // 附件页面
      path = `/case/${caseId}/attachment/${moduleId}`;
      break;
    default:
      // path = `/case/${caseId}/form/${moduleId}`;
      break;
  }
  return path;
};
export const getScreenSceneJumpPath = ({ typeId, caseId, moduleId }) => {
  return `/device${getSceneJumpPath({ typeId, caseId, moduleId })}`;
};

export const getSceneJumpPathFromRelationForm = relationForm => {
  const caseId = getCurrentCaseId();
  const formId = relationForm.relationForm;
  const typeId = relationForm.relationFormTypeId;
  const path = getSceneJumpPath({
    typeId,
    caseId,
    moduleId: formId
  });
  return path;
};
