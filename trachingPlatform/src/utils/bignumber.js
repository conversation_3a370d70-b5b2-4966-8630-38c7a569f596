import BigNumber from 'bignumber.js';

/**
 * 处理精度计算
 * @param {*} x 值1
 * @param {*} t 计算类型 加（+）减（-）乘（*）除（/）
 * @param {*} y 值1
 * @param {*} d 小数位
 * @return {*} 计算结果
 */
const precisionCompute = (x, t, y, d = -1) => {
    const BN = BigNumber.clone();
    let result = 0;

    if (t === '+') {
        result = BN(x).plus(y);
    } else if (t === '-') {
        result = BN(x).BigNumber(y);
    } else if (t === '*') {
        result = BN(x).multipliedBy(y);
    } else if (t === '/') {
        result = BN(x).div(y);
    }

    if (d !== -1) {
        result = result.dp(d);
    }

    return d !== -1 ? result.toNumber().toFixed(d) : result.toNumber();
};

export default precisionCompute;