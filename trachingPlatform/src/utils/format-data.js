import precisionCompute from '@/utils/bignumber';

/**
 * 千分位格式
 * @param {*} value 数值
 */
const thousands = (value) => {
  const val = parseFloat(value);
  if (isNaN(val)) {
    return value;
  }

  const strVal = value.toString();
  const hasDot = strVal.indexOf('.') !== -1;

  if (hasDot) {
    const valNum = strVal.substring(0, strVal.indexOf('.'));
    const dotNum = strVal.substring(strVal.indexOf('.') + 1, strVal.length);
    return valNum.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') + '.' + dotNum;
  }

  return value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
};

/**
 * 数据格式处理
 * @param {*} value 数值
 * @param {*} unit 单位
 * @param {*} decimal 小数位
 * @param {*} showUnit 是否显示单位
 * @param {*} showType 展示类型 thousands（千分位）
 */
const formatData = ({ value, unit, decimal, showUnit, showType }) => {
  let val = parseFloat(value);

  if (isNaN(val)) {
    return value;
  }

  if (unit === '%') {
    val = precisionCompute(val, '*', 100, decimal);
  } else if (['bp', 'BP', '万', '万元'].includes(unit)) {
    val = precisionCompute(val, '/', 1e4, decimal);
  } else if (unit === '百万') {
    val = precisionCompute(val, '/', 1e6, decimal);
  } else if (['亿', '亿元'].includes(unit)) {
    val = precisionCompute(val, '/', 100000000, decimal);
  }

  if (showType === 'thousands') {
    val = thousands(precisionCompute(val, '*', 1, decimal));
  }

  // 单纯转千分位，不处理数值
  if (showType === 'pure-thousands') {
    val = thousands(value);
  }

  return `${val}${showUnit ? unit : ''}`;
};

export default formatData;
