import { DISPATCH_RULE_VALUE_TYPE } from "@/constants/rule";
// 投屏时，根据触发点，将需要展示投屏的数据以json的格式传给后台，
// 后台将json数据存放到redis，返回对应的key
export const prepareScreenConfig = (dataSourceId, rules, triggerData) => {
  //req: {
  //   "dataSourceID": 1640588675039588,
  //   "queryValueList": [],
  //   "queryDefaultGroupList": [
  //     {
  //       "logicalOperationSymbol": 1,
  //       "queryDefaultConditionList": [
  //         {
  //           "ColumnName": "c001",
  //           "ColumnAlias": "ac001",
  //           "operator": 7,
  //           "tableName": "d_tb_1636785944561271",
  //           "TableAlias": "a",
  //           "queryValue": "1",
  //           "logicalOperationSymbol": 1
  //         }
  //       ]
  //     }
  //   ]
  // }
  return {
    dataSourceID: dataSourceId,
    queryDefaultGroupList: rules.map(r => {
      return {
        ...r,
        queryDefaultConditionList: r.queryDefaultConditionList.map(c => {
          const { valueType, ...rest } = c;
          // 不是自定义的通过triggerData取对应queryValue（字段名）的数据
          if (valueType !== DISPATCH_RULE_VALUE_TYPE) {
            // 学生端作业，葡萄城根据学生填写的数据，{columnId: xxx} 查询调度信息
            // 葡萄城设置调度的时候没有存columnName, 取columnId的值
            // 这里做下columnId转换为queryValue
            // 给个空值，后台需要这个字段，不然报错
            rest.queryValue =
              triggerData[c.queryValue] || triggerData[c.originColumnId] || "";
            return rest;
          } else {
            return rest;
          }
        })
      };
    })
  };
};

// 前端根据redis的投屏key，获取数据json，通过数据json，还原投屏的页面
// 数据源，根据数据源id和查询条件还原数据源页面
// 场景，根据场景id和数据id，还原场景页面
export const parseScreenConfig = () => {};

const getDeviceRecoverUrl = item => {
  const mapping = {
    销售: "sales",
    筹资: "financing",
    投资: "investment",
    采购: "purchase",
    分配: "distribution",
    生产: "produce",
    主屏: "main",
    四流融合: "main"
  };
  return mapping[item.name] ? `/device/${mapping[item.name]}` : null;
};

export const recoverScreen = (instance, item) => {
  const url = getDeviceRecoverUrl(item);
  if (!url) return;
  instance
    .$confirm(`数据大屏(${item.name})将回到初始页, 是否继续?`, "确定", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
    .then(() => {
      instance.$api
        .ScreenProjection({
          deviceId: item.id,
          classRoomId: item.classRoomId,
          isMain: item.isMain,
          deviceName: item.deviceName,
          token: sessionStorage.getItem("tokenName"),
          url,
          config: item.config
        })
        .then(res => {
          if (res.code === 200) {
            instance.$message({
              type: "success",
              message: "已恢复!"
            });
          }
        });
    })
    .catch(_ => {});
};

export const recoverAllScreen = (instance, items) => {
  instance
    .$confirm("所有数据大屏将回到初始页, 是否继续?", "确定", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
    .then(() => {
      instance.$api
        .BatchScreenProjection({
          devices: items
            .filter(item => item.status)
            .map(item => {
              return {
                deviceId: item.id,
                data: {
                  deviceId: item.id,
                  classRoomId: item.classRoomId,
                  isMain: item.isMain,
                  deviceName: item.name,
                  url: getDeviceRecoverUrl(item),
                  token: sessionStorage.getItem("tokenName"),
                  config: item.config
                }
              };
            })
            .filter(item => item.data.url)
        })
        .then(res => {
          if (res.code === 200) {
            instance.$message({
              type: "success",
              message: "已恢复!"
            });
          }
        });
    })
    .catch(_ => {});
};

export const getIPs = callback => {
  var ipDups = {};

  // 创建RTCPeerConnection接口
  var RTCPeerConnection =
    window.RTCPeerConnection ||
    window.mozRTCPeerConnection ||
    window.webkitRTCPeerConnection;

  // 如果浏览器不支持RTCPeerConnection，则直接返回
  if (!RTCPeerConnection) {
    callback("Your browser does not support WebRTC");
    return;
  }

  // 创建一个虚拟的RTCPeerConnection
  var rtc = new RTCPeerConnection({ iceServers: [] });

  // 监听onicecandidate事件
  rtc.onicecandidate = function (evt) {
    if (evt.candidate) {
      var ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3})/g;
      var ipAddr = ipRegex.exec(evt.candidate.candidate)[1];

      if (ipDups[ipAddr] === undefined) {
        callback(ipAddr);
      }

      ipDups[ipAddr] = true;
    }
  };

  // 创建一个数据通道
  rtc.createDataChannel("");

  // 创建一个offer，触发上面的onicecandidate回调
  rtc.createOffer(
    function (offerDesc) {
      rtc.setLocalDescription(offerDesc);
    },
    function (e) {
      console.warn("offer failed", e);
    }
  );
};
