/**
 * 时间格式处理
 * @param {*} time 时间数据
 * @param {*} cFormat 时间格式 '{y}-{m}-{d} {h}:{i}:{s}'
 */
const formatDate = (time, cFormat) => {
    if (!time) {
        return null;
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
    let date;
    if (typeof time === 'object') {
        date = time;
    } else {
        if ((typeof time === 'string')) {
            if ((/^[0-9]+$/.test(time))) {
                time = parseInt(time);
            } else {
                time = time.replace(new RegExp(/-/gm), '/');
            }
        }
        date = new Date(time);
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    };
    const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
        const value = formatObj[key];
        if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value]; }
        return value.toString().padStart(2, '0');
    });
    return time_str;
};

/**
 * 获取日期范围内天的数组
 * @param startDate
 * @param endDate
 * @returns {any[]}
 */
function getDay(startDate, endDate, formatType = 'yyyy-MM') {
    const result = [];
    const ab = startDate.split('-');
    const ae = endDate.split('-');
    const db = new Date();
    db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
    const de = new Date();
    de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
    const unixDb = db.getTime();
    const unixDe = de.getTime();
    for (let k = unixDb; k <= unixDe;) {
        result.push(formatDate(new Date(parseInt(k)), formatType));
        k = k + 24 * 60 * 60 * 1000;
    }
    // console.log(result);
    return result;
}

/**
 * 获取日期范围内月份数组
 * @param start
 * @param end
 * @returns {*[]}
 */
function getMonth(start, end, formatType = 'yyyy-MM') {
    const result = [];
    const s = start.split('-');
    const e = end.split('-');
    const min = new Date();
    const max = new Date();
    min.setFullYear(s[0], s[1] * 1 - 1, 1); // 开始日期

    max.setFullYear(e[0], e[1] * 1 - 1, 1); // 结束日期
    const curr = min;
    // eslint-disable-next-line no-unmodified-loop-condition
    while (curr <= max) {
        var month = curr.getMonth();
        result.push(formatDate(curr, formatType));
        curr.setMonth(month + 1);
    }
    return result;
}

/**
 * 获取周数组
 * @param start
 * @param end
 * @returns {*[]}
 */
function getWeak(start, end) {
    // 1、获取开始日期属于第几周，哪年
    let startWeekNum = getWeekNum(start);
    const startYearNum = parseInt(start.split('-')[0]);

    if (startWeekNum >= 53) {
        startWeekNum = 52;
    }

    // 2、获取结束日期属于第几周，哪年
    let endWeekNum = getWeekNum(end);
    const endYearNum = parseInt(end.split('-')[0]);

    if (endWeekNum >= 53) {
        endWeekNum = 52;
    }

    // 3、计算周数
    const result = [];
    let yearAdd = 0; // 跨年数
    let yearWeekNum = startWeekNum; // 当前取到的周数
    for (
        let i = 0; i <= (endYearNum - startYearNum) * 52 - startWeekNum + endWeekNum; i++
    ) {
        if (startWeekNum + i <= 0) {
            // 如果开始周数是0，则取开始年上一年的52周
            result.push(startYearNum - 1 + '52');
        } else {
            if (yearWeekNum === 52) {
                yearAdd++;
            }
            yearWeekNum = startWeekNum + i - yearAdd * 52;
            if (yearWeekNum <= 0) {
                // 如果开始周数是0，则取开始年上一年的52周
                result.push(startYearNum + '52');
            } else {
                if (yearWeekNum < 10) {
                    result.push(startYearNum + yearAdd + '0' + yearWeekNum);
                } else {
                    result.push(startYearNum + yearAdd + '' + yearWeekNum);
                }
            }
        }
    }
    return result;
}

/**
 * 获取周数
 * @param dt 时间
 * @returns {*[]}
 */
function getWeekNum(dt) {
    const d1 = new Date(dt);
    const d2 = new Date(dt);
    d2.setMonth(0);
    d2.setDate(1);
    const rq = d1 - d2;
    const days = Math.ceil(rq / (24 * 60 * 60 * 1000));
    const num = Math.ceil(days / 7);
    return num;
}

/**
 * 传入开始时间，结束时间及事件类型返回范围内的月份数组或者日期数组
 * @param startDateTime
 * @param endDateTime
 * @param dateType
 * @returns {*[]}
 */
export function getDateArry(startDateTime, endDateTime, dateType, formatType = 'yyyy-MM') {
    if (dateType === 'month') {
        return getMonth(startDateTime, endDateTime, formatType);
    } else if (dateType === 'day') {
        return getDay(startDateTime, endDateTime, formatType);
    } else if (dateType === 'week') {
        return getWeak(startDateTime, endDateTime);
    }
}

export default formatDate;


// 处理 ISO 时间格式的方法
export const formatISOString = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}