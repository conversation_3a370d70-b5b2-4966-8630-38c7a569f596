
// 字母转大写
export const transToUpperCase = function (data) {
  function getType(params) {
    return Object.prototype.toString.call(params).substring(8).replace(']', '')
  }

  function getKeys(obj, blank) {
    Object.keys(obj).forEach(key => {
      let low = key.replace(key[0], key[0].toUpperCase());
      let value = obj[key];
      let valueType = getType(value);
      if (valueType === 'Object') {
        blank[low] = {};
        getKeys(value, blank[low])
      } else if (valueType === 'Array') {
        blank[low] = [];
        getKeys(value, blank[low])
      } else {
        blank[low] = value;
        return blank
      }
    })
  }
  let blankObj;
  if (data instanceof Array) {
    blankObj = [];
  } else {
    blankObj = {};
  }
  getKeys(data, blankObj)
  return blankObj;
}

// 统一转小写
export const transToLowerCase = function (data) {
  function getType(params) {
    return Object.prototype.toString.call(params).substring(8).replace(']', '')
  }

  function getKeys(obj, blank) {
    Object.keys(obj).forEach(key => {
      let low = key.replace(key[0], key[0].toLowerCase());
      let value = obj[key];
      let valueType = getType(value);
      if (valueType === 'Object') {
        blank[low] = {};
        getKeys(value, blank[low])
      } else if (valueType === 'Array') {
        blank[low] = [];
        getKeys(value, blank[low])
      } else {
        blank[low] = value;
        return blank
      }
    })
  }
  let blankObj;
  if (data instanceof Array) {
    blankObj = [];
  } else {
    blankObj = {};
  }
  getKeys(data, blankObj)
  return blankObj;
}

/**
 * 判断是否是 https
 */
export const isHttps = function () {
  return location.protocol == 'https:' ? true : false;
}