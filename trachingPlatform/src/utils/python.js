import * as monaco from 'monaco-editor'
export default [
    /**   * 内置函数   */
    {
        label: 'as',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'as',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'abs',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'abs',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'all',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'all',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'and',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'and',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'any',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'any',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'apply',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'apply',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'as',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'as',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'ascii',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'ascii',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'assert',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'assert',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'async',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'async',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'basestring',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'basestring',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'callable',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'callable',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'cascii',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: '# -*- coding: ascii -*-',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'buffer',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'buffer',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'import',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'import',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'random',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'random',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'def',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `def`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'try',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `try:`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'try Try/Except',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `try:\n\t# TODO: write code...\nexcept Exception, e:\n\traise e`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'try Try/Except/Else',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `try:\n\t# TODO: write code...\nexcept Exception, e:\n\traise e\nelse:\n\t# TODO: write code...`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'try Try/Except/Finally',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `try:\n\t# TODO: write code...\nexcept Exception, e:\n\traise e\nfinally:\n\t# TODO: write code...`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'try Try/Except/Else/Finally',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `try:\n\t# TODO: write code...\nexcept Exception, e:\n\traise e\nelse:\n\t# TODO: write code...\nfinally:\n\t# TODO: write code...`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'pass',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'pass',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'return',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'return',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'for',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `for item in items:\n\t# TODO: write code.....`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'xpath',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'xpath',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'append',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'append',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'print',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'print',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'range',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'range',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'len',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'len',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'except',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'except',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'if',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'if',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'if',//触发提示的文本
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `if condition:\n\t# TODO: write code...`,//补全的文本
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'with',//触发提示的文本
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `with expr as var:\n\t# TODO: write code...`,//补全的文本
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'with',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'with',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'else',//触发提示的文本
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `else:\n\t# TODO: write code...`,//补全的文本
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'else',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'else',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'from',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'from package import module',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'from',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'from',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'exec',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'exec',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'not',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'not',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'finally',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'finally',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'or',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'or',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'break',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'break',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'class',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'class',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'continue',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'continue',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'global',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'global',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'raise',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'raise',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'del',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'del',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'elif',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'elif',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'in',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'in',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'while',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'while',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'is',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'is',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'lambda',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'lambda',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'yield',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'yield',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'dict',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'dict',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'divmod',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'divmod',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'input',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'input',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'open',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'open',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'staticmethod',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'staticmethod',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'enumerate',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'enumerate',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'int',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'int',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'ord',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'ord',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'str',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'str',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'eval',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'eval',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'isinstance',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'isinstance',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'super',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'super',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'bin',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'bin',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'file',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'file',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'iter',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'iter',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'property',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'property',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'property',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: `def foo():\n\tdoc = "The foo property."\n\tdef fget(self):\n\t\treturn self._foo\n\tdef fset(self, value):\n\t\tself._foo = value`,
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'snippet'
    },
    {
        label: 'tuple',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'tuple',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'bool',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'bool',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'filter',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'filter',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'type',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'type',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'bytearray',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'bytearray',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'float',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'float',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'list',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'list',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'raw_input',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'raw_input',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'unichr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'unichr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'callable',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'callable',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'format',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'format',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'locals',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'locals',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'reduce',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'reduce',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'unicode',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'unicode',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'chr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'chr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'frozenset',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'frozenset',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'long',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'long',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'reload',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'reload',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'vars',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'vars',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'classmethod',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'classmethod',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'getattr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'getattr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'map',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'map',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'repr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'repr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'xrange',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'xrange',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'cmp',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'cmp',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'globals',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'globals',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'max',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'max',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'reversed',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'reversed',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'zip',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'zip',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'compile',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'compile',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'hasattr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'hasattr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'memoryview',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'memoryview',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'round',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'round',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: '__import__',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: '__import__',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'complex',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'complex',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'hash',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'hash',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'min',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'min',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'set',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'set',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'delattr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'delattr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'help',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'help',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'next',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'next',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'setattr',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'setattr',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'hex',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'hex',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'object',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'object',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'slice',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'slice',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'dir',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'dir',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'id',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'id',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'oct',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'oct',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    {
        label: 'sorted',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'sorted',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'keyword'
    },
    
]