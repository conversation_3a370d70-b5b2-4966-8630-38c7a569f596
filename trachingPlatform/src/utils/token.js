/**
 * 功能：token操作
 */

// COOKIES
// {
//   CLASSROOM_ID: 0,
//   userInfo:{},
//   caseId:""
//   tokenName:""
// }
export const CLASSROOM_ID = "CLASSROOM_ID";
export const USER_INFO = "userInfo"; // 用户信息
export const CASE_ID = "caseId"; // 当前案例ID
export const DEFAULT_CASE_INFO = "defaultCaseInfo"; // 默认案例信息
export const NOW_CASE_INFO = "nowCaseInfo"; // 当前案例信息


export const TOKEN_NAME = "tokenName";
export const TOURIST_TOKEN_NAME = "TOURIST_TOKEN_NAME";
export const CURRENTSTUDENTINFO = "CURRENTSTUDENTINFO";
export const TASKTEAMINFO = "TASKTEAMINFO";
export const MODULECOMPLETEINFO = "MODULECOMPLETEINFO";
export const knowledge_Page = "knowledgePage";

import { getCookies, setCookies, clearCookies } from "./cookies.js";
const token = {
    CLASSROOM_ID,
    USER_INFO,
    CASE_ID,
    NOW_CASE_INFO,
    TOKEN_NAME,
    TOURIST_TOKEN_NAME,
    CURRENTSTUDENTINFO,
    TASKTEAMINFO,
    MODULECOMPLETEINFO,
    knowledge_Page,
    getCookies,
    setCookies,
    /**
     * 功能：设置token
     * @param {*} token
     */
    setToken(token) {
        sessionStorage.setItem(TOKEN_NAME, token);
        setCookies(TOKEN_NAME, token);
    },

    /**
     * 功能：获取token
     */
    getToken() {
        const sToken = sessionStorage.getItem(TOKEN_NAME);
        if (sToken && sToken.length > 10) {
            return sToken;
        }
    },
    /**
     * 功能：储存知识仓分页页码
     */
    setKnowledgePage(page) {
        sessionStorage.setItem(knowledge_Page, page);
        setCookies(knowledge_Page, page);
    },
    /**
     * 功能：获取知识仓分页页码
     */
    getKnowledgePage() {
        return sessionStorage.getItem(knowledge_Page);
    },
    getTccessToken() {
        return sessionStorage.getItem("AccessToken");
    },
    /**
     * 功能：设置游客token
     * @param {*} token
     */
    setTouristToken(token) {
        sessionStorage.setItem(TOURIST_TOKEN_NAME, token);
    },

    /**
     * 功能：获取游客token
     */
    getTouristToken() {
        return sessionStorage.getItem(TOURIST_TOKEN_NAME);
    },

    /**
     * 功能：获取当前学生信息
     */
    getCurrentStudentInfo() {
        // console.log(sessionStorage.getItem(CURRENTSTUDENTINFO))
        return JSON.parse(sessionStorage.getItem(CURRENTSTUDENTINFO));
    },
    /**
     * 功能：获取当前学校信息
     */
    getSchoolId() {
        return JSON.parse(sessionStorage.getItem('schoolId'));

    },
    /**
     * 功能：设置当前学校信息
     */
    setSchoolId(id) {
        return sessionStorage.setItem('schoolId', JSON.stringify(id));
    },

    /**
     * 功能：设置当前学生用户信息
     */
    setCurrentStudentInfo(info) {
        return sessionStorage.setItem(CURRENTSTUDENTINFO, JSON.stringify(info));
    },

    /**
     * 功能：获取六大模块信息
     */
    getModuleCompleteInfo() {
        return JSON.parse(sessionStorage.getItem(MODULECOMPLETEINFO)) || [];
    },

    /**
     * 功能：设置六大模块信息
     */
    setModuleCompleteInfo(info) {
        return sessionStorage.setItem(MODULECOMPLETEINFO, JSON.stringify(info));
    },
    /**
     * 功能：获取当前学生团队信息
     */
    getStudentTeam() {
        return JSON.parse(sessionStorage.getItem(TASKTEAMINFO));
    },

    /**
     * 功能：设置当前学生用户信息
     */
    setStudentTeam(info) {
        return sessionStorage.setItem(TASKTEAMINFO, JSON.stringify(info));
    },
    /**
     * 功能：获取当前案例的信息
     */
    getNowCaseInfo() {
        const info = sessionStorage.getItem(NOW_CASE_INFO);
        if (info) {
            return JSON.parse(info);
        } else {
            const defaultCase = sessionStorage.getItem(DEFAULT_CASE_INFO)
            if (defaultCase) {
                sessionStorage.setItem(NOW_CASE_INFO, defaultCase);
                return JSON.parse(defaultCase)
            } else {
                return {}
            }
        }
    },
    /**
     * 功能：存储当前案例的信息
     * @param {*} caseInfo
     */
    setNowCaseInfo(caseInfo) {
        const str = typeof caseInfo === 'string' ? caseInfo : JSON.stringify(caseInfo)
        sessionStorage.setItem(NOW_CASE_INFO, str);
    },
    /**
     * 功能：获取当前案例的id
     */
    getCurrentCaseId() {
        return sessionStorage.getItem(CASE_ID);
    },
    setCurrentCaseId(val) {
        sessionStorage.setItem(CASE_ID, val);
    },
    removeCurrentCaseId() {
        sessionStorage.removeItem(CASE_ID);
    },
    /**
     * 凭证人 rpa 状态存储
     */
    setVoucherRpa(info) {
        return sessionStorage.setItem("voucherRpa", info);
    },
    /**
     * 获取 凭证rpa 状态
     */
    getVoucherRpa() {
        return sessionStorage.getItem("voucherRpa");
    },
    /**
     * 获取是否进入核算得状态
     */
    getIsEnter() {
        return sessionStorage.getItem("isEnter");
    },
    clearLoginInfo() {
        sessionStorage.clear()
        clearCookies()
    }
};
export const getCurrentCaseId = token.getCurrentCaseId
export const setCurrentCaseId = token.setCurrentCaseId
export const removeCurrentCaseId = token.removeCurrentCaseId
export const getToken = token.getToken
export const setNowCaseInfo = token.setNowCaseInfo
export const clearLoginInfo = token.clearLoginInfo
export default token