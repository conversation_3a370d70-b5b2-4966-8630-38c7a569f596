// 消息队列
let messageQueue = [];

// 发送队列数据的函数
function sendQueueData() {
  if (messageQueue.length > 0) {
    console.log("Sending click data to server:", messageQueue);
    // 实际的发送逻辑，比如使用 fetch 或 axios 发送数据到后端API
    messageQueue = []; // 清空队列
  }
}


Vue.directive("track-click", {
  bind(el, binding) {
    el.addEventListener("click", event => {
      // 收集点击事件数据
      const eventData = {
        eventType: "click",
        element: binding.value, // 绑定值可以是任意用于标识元素的字符串
        timestamp: Date.now()
      };
      // 将事件数据添加到队列
      messageQueue.push(eventData);

      // （可选）如果队列达到一定长度，立即发送数据
      if (messageQueue.length >= 10) {
        // 假设队列长度达到10则立即发送
        sendQueueData();
      }
    });
  }
});

// 使用指令
// <button v-track-click="'button1'">Button 1</button>
