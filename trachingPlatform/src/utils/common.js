/**
 * 项目公共函数
 */
import token from '@/utils/token';
import store from '@/store/index';
import axios from "axios";
import { getCurrentCaseId, getToken } from "@/utils/token.js";

/**
 * 递归找到对象中指定的所有属性值 返回数组
 * @param {*} obj
 * @param {*} property
 * @returns
 */
export const findProperties = function(obj, property) {
    var result = [];

    // 检查obj是否是对象
    if (typeof obj === 'object' && obj !== null) {
        // 检查obj是否有指定的属性
        if (property in obj) {
            result.push(obj[property]);
        }
        // 遍历obj的所有属性
        for (var key in obj) {
            // 递归调用findProperties函数
            var subResult = findProperties(obj[key], property);
            // 将子结果合并到结果数组中
            result = result.concat(subResult);
        }
    }

    return result;
};


/**
 * 千分制展示金额
 * @param {*} num
 * @returns
 */
export const formatMoney = function(num) {
    if (!num) {
        return '';
    }

    // 将数字转换为字符串，并将小数点分隔为整数部分和小数部分
    var numParts = String(num).split(".");

    // 获取整数部分
    var integerPart = numParts[0];
    // 将整数部分每三位添加一个逗号
    var formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    // 如果有小数部分，则将整数部分和小数部分拼接起来
    if (numParts.length > 1) {
        var decimalPart = numParts[1];
        return formattedIntegerPart + "." + decimalPart;
    } else {
        return formattedIntegerPart;
    }
};

/**
 * 还原千分制金额
 * @param {*} formattedAmount
 * @returns
 */
export const restoreMoney = function(formattedAmount) {
    if (!formattedAmount) {
        return 0;
    }

    // 去除金额中的逗号
    var amountWithoutCommas = formattedAmount.toString().replace(/,/g, "");

    // 将还原后的金额转换为数字
    var restoredAmount = parseFloat(amountWithoutCommas);

    return restoredAmount;
};

/**
 * 导出excel
 * @param {*} url
 * @param {*} params
 * @param {*} fileName
 */
export const exportExcel = function(url, params, fileName, type) {
    const paramsArray = [];
    Object.keys(params).forEach((key) => paramsArray.push(`${key}=${params[key]}`));
    const urlParams = `${url}?${paramsArray.join("&")}`;
    axios({
            url: `${window.PUBLICHOSTA}${urlParams}`,
            method: type || 'GET',
            responseType: 'arraybuffer',
            headers: {
                Authorization: getToken(),
                // caseId: getCurrentCaseId(),
                schoolId: token.getSchoolId(),
                ContentType: 'application/json; charset=UTF-8'
            }
        })
        .then(response => {
            const blob = new Blob([response.data], { type: 'application/octet-stream;charset=UTF-8' });
            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = `${fileName}.xlsx`; // 设置下载的文件名
            // 添加下载链接到页面
            document.body.appendChild(downloadLink);
            // 触发点击事件
            downloadLink.click();
            // 移除下载链接
            document.body.removeChild(downloadLink);
        })
        .catch(error => {
            console.error('请求失败', error);
        });
};


// 将Base64字符串转换为Blob对象
function dataURLtoBlob(base64, fileName = 'cover') {
    // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
    let data = base64.split(','),
        // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
        type = data[0].match(/:(.*?);/)[1],
        // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
        suffix = type.split('/')[1],
        // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
        bstr = window.atob(data[1]),
        // 获取解码结果字符串的长度
        n = bstr.length,
        // 根据解码结果字符串的长度创建一个等长的整形数字数组
        // 但在创建时 所有元素初始值都为 0
        u8arr = new Uint8Array(n)
        // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
    while (n--) {
        // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
        u8arr[n] = bstr.charCodeAt(n)
    }
    // 利用构造函数创建File文件对象
    const file = new File([u8arr], `${fileName}.${suffix}`, {
            type: type
        })
        // 返回file
    return file
}
// 原生上传base64 格式的图片
export const uploadBase64 = function(base64Img, url) {
    let result = null;
    const file = dataURLtoBlob(base64Img);
    var formData = new FormData();
    formData.append('image', file);
    var xhr = new XMLHttpRequest();
    xhr.open('POST', url, false);
    xhr.onload = function(res) {
        if (this.status == 200) {
            console.log('res', res);
            let { currentTarget } = res;
            result = JSON.parse(currentTarget.response);
        } else {
            console.error('上传失败');
        }
    };
    xhr.send(formData);

    return result;
}

// 检测字符串是否为空
export function checkStringEmpty(str) {
    return Boolean(str.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, '').trim());
}
// 获取当前北京时间
/**
 * @param {推迟的时间 单位/分钟} lateTime 
 * @returns 
 */
export function getCurrentTime(lateTime) {
    // 创建一个 Date 对象，它将自动获取用户设备上的当前时间
    const date = new Date();

    // 获取当前的UTC时间偏移（分钟）
    const offset = date.getTimezoneOffset();

    // 根据UTC时间偏移，计算出北京时间的毫秒数
    let beijingTime = date.getTime() + (offset + 480) * 60 * 1000; // 北京时间的偏移为 UTC+8
    if (lateTime && Number(lateTime)) { // 存在延时时间则添加
        beijingTime += lateTime * 60 * 1000;
    }

    // 将毫秒数转换为日期对象
    const beijingDate = new Date(beijingTime);

    // 格式化时间
    const yy = beijingDate.getFullYear(); // 年
    const mm = (beijingDate.getMonth() + 1).toString().padStart(2, '0'); // 月
    const dd = beijingDate.getDate().toString().padStart(2, '0'); // 日
    const hours = beijingDate.getHours().toString().padStart(2, '0'); // 补足两位数
    const minutes = beijingDate.getMinutes().toString().padStart(2, '0');
    const seconds = beijingDate.getSeconds().toString().padStart(2, '0');

    // 返回格式化后的北京时间字符串
    return `${yy}-${mm}-${dd} ${hours}:${minutes}:${seconds}`;
}


// 获取arr1 与arr2 数组值的差集
export function difference(arr1, arr2) {
    return arr1.filter(element => !arr2.includes(element))
}

/**
 * 处理限制并发请求的问题
 * @param {*} params  请求具体的参数
 * @param {*} apiNameFunction  请求的api方法
 * @param {*} concurrency 并发数
 * @returns 
 */
export async function fetchWithConcurrency(params, apiNameFunction, concurrency) {
    const results = [];
    const executing = [];
    for (const param of params) {
        // const promise = this.$Api.CopyCoursDesignatedUser(param).then(data => {
        const promise = apiNameFunction(param).then(data => {
            results.push(data);
        }).catch(error => {
            console.error("Error fetching URL:", param, error);
        });
        executing.push(promise);
        if (executing.length >= concurrency) {
            await Promise.race(executing);
            executing.splice(executing.indexOf(promise), 1);
        }
    }
    await Promise.all(executing);
    return results;
}

// 移动光标
export const placeCaretAtEnd = function(el) {
    el.focus();
    if (typeof window.getSelection != "undefined" &&
        typeof document.createRange != "undefined") {
        const range = document.createRange();
        range.selectNodeContents(el);
        range.collapse(false);
        const sel = window.getSelection();
        sel.removeAllRanges();
        sel.addRange(range);
    } else if (typeof document.body.createTextRange != "undefined") {
        const textRange = document.body.createTextRange();
        textRange.moveToElementText(el);
        textRange.collapse(false);
        textRange.select();
    }
}

// 数组对象 根据对象中的指定属性去重
/**
 * array 数组
 * prop 属性
 */
export const handleUniqueArray = function(array, pro) {
    // debugger
    const seen = new Set();
    const uniqueArray = array.filter(item => {
        const target = item[pro];
        return seen.has(target) ? false : seen.add(target);
    });

    return uniqueArray;
}

export const openUniqueTab = function(url, key) {
    // 检查 localStorage 中是否有已打开的标签页
    const existingTabId = localStorage.getItem(key);
    // 如果已有标签页存在，尝试关闭
    if (existingTabId) {
        const existingTab = window.open('', existingTabId);
        if (existingTab) {
            existingTab.close(); // 关闭旧的标签页
        }
    }
    // 打开新的标签页，并保存其名称到 localStorage
    const newTab = window.open(url, key);
    if (newTab) {
        localStorage.setItem(key, key);
    } else {
        alert('无法打开新标签页，请检查浏览器设置是否允许弹出窗口。');
    }
}

export const openOrFocusTab = function(url, userInfo) {
    const userKey = `userTab_${userInfo.id}`; // 每个用户唯一的 key
    const existingTab = sessionStorage.getItem(userKey); // 获取已存的 tabId
    console.log("existingTab----------", existingTab)
    if (existingTab) {
        // 如果已存在该用户的标签页，尝试获取它
        const tab = window.open("", existingTab);
        if (tab && !tab.closed) {
            tab.location.href = url; // 直接跳转目标页
            tab.focus(); // 聚焦到该 tab
            return;
        }
    }

    // 如果标签页不存在，或者之前的 tab 被关闭，则新开一个
    const newTab = window.open(url, `_blank`);
    if (newTab) {
        sessionStorage.setItem(userKey, userKey); // 记录新的 tabId
    } else {
        alert("弹出窗口被拦截，请允许弹出窗口。");
    }
}