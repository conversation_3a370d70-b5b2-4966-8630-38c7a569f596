import axios from "axios";
import { Message, Loading } from "element-ui";
import token from "./token";
import { getCurrentCaseId } from "./token";
import router from "@/router/index.js";
let showMsg = false;

const request = axios.create({
    baseURL: process.env.VUE_APP_BASE_API || window.PUBLICHOSTA,
    timeout: 60000 * 10
});
let globalLoading;
let pendingRequestCount = 0;
const closeLoading = () => {
    if (--pendingRequestCount <= 0) {
        // globalLoading.close();
    }
};
// 处理参数为数组的转换情况
// request.defaults.transformRequest = [function(data, headers) {
//     if (Array.isArray(data)) {
//         return JSON.stringify(data);
//     }
//     return data;
// }];

/**
 * 功能：数据请求
 * @param {*} params
 */
request.send = (url, params, method = "POST", extraConfig) => {
    if (pendingRequestCount <= 0) {
        pendingRequestCount = 0;
        // globalLoading = Loading.service({ lock: true, body: true, text: '正在处理，请稍候...' });
    }
    pendingRequestCount++;
    return request(url, {
        method: method,
        data: params,
        extraConfig,
        headers: {
            Authorization: token.getToken() ||
                token.getTouristToken() ||
                token.getTccessToken() || '',
            schoolId: params.schoolId || token.getSchoolId() || "673583370671685",
            // CaseId:
            //   params?.caseId ||
            //   getCurrentCaseId() ||
            //   sessionStorage.getItem("selectCaseId"), // 如果参数中包含了caseId 则使用参数中的caseId
            "Content-Type": "application/json"
        }
    });
};
let sqlInterface = false; //sql接口不出现msg弹窗
// 请求拦截
request.interceptors.request.use(
    config => {
        // if (sessionStorage.getItem('cancleHttp') == 1) {
        //     CancelToken.cancel('Abort Request');
        // }
        // 处理get post 的参数问题
        if (config.method === "get" || config.method === "delete") {
            // 拼接参数
            const paramsArray = [];
            Object.keys(config.data).forEach(key =>
                paramsArray.push(`${key}=${config.data[key]}`)
            );
            config.url = `${config.url}?${paramsArray.join("&")}`;
        }
        if (config.url === "/case2/SQLOnline/ExecuteSql") {
            sqlInterface = true;
        }
        // if (cancelMap.has(config.url)) {
        //     cancelMap.get(config.url)() // 取消请求
        //     cancelMap.delete(config.url) // 仓库里删除链接
        // }
        // 统一在请求前添加 cancelToken 方法
        // config.cancelToken = new CancelToken(function executor(c) {
        //     // executor 函数接收一个 cancel 函数作为参数
        //     cancelMap.set(config.url, c)
        // })

        return config;
    },

    error => {
        return Promise.reject(error);
    }
);

// 响应拦截
request.interceptors.response.use(
    response => {
        closeLoading();
        // 请求成功后清除该链接
        // if (cancelMap.has(response.config.url)) {
        //     cancelMap.delete(response.config.url)
        // }

        const res = response.data;
        // 文件流
        if (Object.prototype.toString.call(response.data) === "[object Blob]") {
            return res;
        }
        /**
         * 响应码code
         * '200' 正常返回
         * '500' 系统繁忙，请稍后再试！
         */
        // if (res.code == 401) {
        //     sessionStorage.setItem('cancleHttp', 1);
        // }
        if (res.errCode && res.errCode !== 0) {
            if (res.errCode == 1) {
                Message({
                    message: res.errMsg || "请求频繁，请稍后再试！",
                    type: 'warning',
                    duration: 2 * 1000
                });
                return res;
            }
            if (res.errCode === 500) {
                Message({
                    message: res.errMsg || "系统繁忙，请稍后再试！",
                    type: "error",
                    duration: 2 * 1000
                });
            } else if (res.errCode === 400) {
                Message({
                    message: res.errMsg || "系统错误，请稍后再试！",
                    type: "error",
                    duration: 2 * 1000
                });
            } else if (res.errCode === 1000) {
                // 题目修改后 学生未获取到最新题目信息的提示
                Message({
                    message: "当前任务已更新，系统自动刷新后请按最新要求作答",
                    type: "warning",
                    duration: 5 * 1000
                });
            } else if (res.errCode === 1001) {
                // 学生提交的答案格式有误，提示文案
                Message({
                    message: "提交的答案格式有误，请修改后提交",
                    type: "warning",
                    duration: 5 * 1000
                });
            } else if (
                res.msg.indexOf("继续作答") > -1 &&
                res.msg.indexOf("已交卷") < 0
            ) {
                Message({
                    message: "",
                    type: "success",
                    duration: 2 * 1000
                });
            } else {
                if (sqlInterface) {
                    return res;
                }
                if (!(
                        response.config &&
                        response.config.extraConfig &&
                        response.config.notShowErrorMessage
                    )) {
                    Message({
                        message: res.errMsg || "请求频繁，请稍后再试！",
                        type: "error",
                        duration: 2 * 1000
                    });
                }
            }
            return res;
        } else {
            return res;
        }
    },
    error => {
        closeLoading();

        if (error && error.response) {
            switch (error.response.status) {
                case 401:
                    if (!showMsg) {
                        //解决抢登token失效多次弹框问题
                        showMsg = true;
                        setTimeout(() => {
                            showMsg = false;
                        }, 2000);
                        Message({
                            message: "您的身份已失效，请重新登录!",
                            type: "error",
                            duration: 2 * 1000
                        });
                        sessionStorage.clear();
                        // 直接跳转登录
                        router.push("/login");
                    }
                    break;
                case 403:
                    //
                    Message({
                        message: "403 没有权限访问",
                        type: "error",
                        duration: 2 * 1000
                    });
                    break;
                case 404:
                    Message({
                        message: "404 网络资源未找到，请刷新界面",
                        type: "error",
                        duration: 2 * 1000
                    });
                    break;
                case 500:
                    Message({
                        message: "500 服务异常,请检查网络",
                        type: "warning",
                        duration: 2 * 1000
                    });
                    break;
                case 502:
                case 503:
                    Message({
                        message: "502 服务异常,请检查网络",
                        type: "warning",
                        duration: 3 * 1000
                    });
                    break;
                default:
                    // 其他错误
            }
        }
        // // 请求失败后清除该链接
        // if (cancelMap.has(error.config.url)) {
        //     cancelMap.delete(error.config.url)
        // }
        // if (String(error).indexOf("401") > -1) {
        //   if (!showMsg) {
        //     //解决抢登token失效多次弹框问题
        //     showMsg = true;
        //     setTimeout(() => {
        //       showMsg = false;
        //     }, 2000);
        //     Message({
        //       message: "您的账号已在别处登录，请重新登录",
        //       type: "error",
        //       duration: 2 * 1000
        //     });
        //     sessionStorage.clear();
        //   }
        // } else if (String(error).indexOf("502") > -1 || String(error).indexOf("503") > -1) {
        //   Message({
        //     message: "提交频繁，稍后再试",
        //     type: "warning",
        //     duration: 3 * 1000
        //   });
        // } else if (String(error).indexOf("504") > -1) {
        //   const loadingIns = Loading.service({});
        //   Message({
        //     message: "提交频繁，稍后再试",
        //     type: "warning",
        //     duration: 3 * 1000
        //   });
        //   setTimeout(() => {
        //     if (loadingIns) loadingIns.close();
        //   }, 10000);
        // } else {
        //   Message({
        //     message: "服务器繁忙，请稍后重试",
        //     type: "error",
        //     duration: 2 * 1000
        //   });
        // }

        return Promise.reject(error);
    }
);

export default request;