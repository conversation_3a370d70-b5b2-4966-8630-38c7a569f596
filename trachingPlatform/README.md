平台名称： 数字化教材平台


技术相关：
vue2 + elementUI + sass + eslint

屏幕适配
rem
设计稿使用 1920 版，同比例缩小 100 倍。
如：设计稿为 10px =》 0.1rem
真实 rem 值，可根据实际情况做适当调整

目录说明：
├─ views                    # 视图目录
│  ├─ echarts              # 图表相关组件
│  ├─ public               # 公共模块
│  │  ├─ forgetPassword.vue # 忘记密码页面
│  │  ├─ jc.vue            # 未知页面
│  │  ├─ login.vue         # 登录页面
│  │  └─ personal-center.vue # 个人中心页面
│  ├─ question            # 题库相关模块
│  ├─ schoolPage          # 学校端页面模块
│  ├─ student             # 学生相关模块()
│  ├─ studentPage         # 学生页面模块
│  ├─ teacherPage         # 教师页面模块
│  ├─ teaching-page       # 教学页面模块
│  └─ home.vue            # 首页组件

代码规范：
icon 尽量用 svg 矢量图，避免失真

