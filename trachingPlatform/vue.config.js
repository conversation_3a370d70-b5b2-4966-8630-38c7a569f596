"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
const CopyPlugin = require("copy-webpack-plugin");
const UnoCSS = require("@unocss/webpack").default;
let timeStamp = new Date().getTime();

const OptimizeCSSAssetsPlugin = require("optimize-css-assets-webpack-plugin");
// const CssMinimizerPlugin = require('css-minimizer-webpack-plugin'); // 引入 CSS 压缩插件
// const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin; // 打包分析
// const CommonsChunkPlugin = require('CommonsChunkPlugin')
// const UselessFile = require('useless-files-webpack-plugin')

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title; // page title

// 服务端口
const port = process.env.port || process.env.npm_config_port || 9010; // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  transpileDependencies: [
    /[/\\]node_modules[/\\]simple-mind-map[/\\]/,
    /[/\\]node_modules[/\\]@svgdotjs[/\\]/,
  ],
  publicPath: "./", // 可直接运行index.html
  filenameHashing: false, // 打包的时候不使用hash值.因为我们有时间戳来确定项目的唯一性了.
  // publicPath: '/',
  outputDir: "dist",
  assetsDir: "static",
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    // overlay: {
    //   warnings: false,
    //   errors: true
    // },
    // hot: true,
    disableHostCheck: true,
    //   proxy: {
    //     "/api": {
    //         target: "http://localhost:3000/",
    //         changeOrigin: true,
    //         pathRewrite: {
    //             "^/api": "", // 代理的路径
    //         },
    //     },
    // },
    // before: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    // devtool: "cheap-module-source-map",
    name: name,
    externals: {
      // 'G6': 'G6',
      "element-ui": "ELEMENT",
      vue: "Vue",
      // 'GC':"GC",
      moment: "moment",
    },
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
    //
    optimization: {
      runtimeChunk: "single",
      splitChunks: {
        chunks: "all",
        // maxInitialRequests: Infinity,
        minSize: 50 * 1024,
        maxSize: 0,
        minChunks: 1, //拆分前必须共享模块的最小块数。
        maxAsyncRequests: 5, //按需加载时并行请求的最大数目。
        maxInitialRequests: 3, //入口点的最大并行请求数
        automaticNameDelimiter: "~", //默认情况下，webpack将使用块的来源和名称（例如vendors~main.js）生成名称。此选项允许您指定要用于生成的名称的分隔符。
        automaticNameMaxLength: 30, //允许为SplitChunksPlugin生成的块名称的最大长度
        name: true,
        cacheGroups: {
          "@grapecity": {
            test: /[\\/]node_modules[\\/]@grapecity[\\/]/,
            name: "grapecity",
            chunks: "all",
            priority: -10,
            minChunks: 2,
            minSize: 50 * 1024,
            maxSize: 5 * 1000 * 1024,
            reuseExistingChunk: true, //如果当前块包含已经从主包中分离出来的模块，那么该模块将被重用，而不是生成新的模块
          },
          vendor: {
            name: `chunk-vendors`,
            test: /[\\/]node_modules[\\/]/, //控制此缓存组选择的模块。省略它将选择所有模块。它可以匹配绝对模块资源路径或块名称。匹配块名称时，将选择块中的所有模块。
            minChunks: 1,
            // maxInitialRequests: 12,
            maxAsyncRequests: 5,
            minSize: 50 * 1024,
            maxSize: 5 * 1000 * 1024,
            priority: -10, //一个模块可以属于多个缓存组,模块出现在优先级最高的缓存组中
          },
          common: {
            name: `chunk-common`,
            minChunks: 2,
            priority: -20,
            chunks: "initial",
            reuseExistingChunk: true, //如果当前块包含已经从主包中分离出来的模块，那么该模块将被重用，而不是生成新的模块
          },
          // 新增 CSS 缓存组
          styles: {
            name: "styles",
            test: /\.css$/,
            chunks: "all",
            enforce: true,
          },
        },
      },
      minimizer: [
        // new BundleAnalyzerPlugin({
        //     analyzerPort: 8089,
        //     generateStatsFile: false
        // })
        // 使用扩展运算符引入默认的 minimizer
        // ...(config.optimization.minimizer || []),
        // new CssMinimizerPlugin() // 添加 CSS 压缩插件
        new OptimizeCSSAssetsPlugin({}),
      ],
    },
    performance: {
      // 入口起点的最大体积
      maxEntrypointSize: 10000000,
      // 生成文件的最大体积
      maxAssetSize: 30000000,
    },
    plugins: [
      new CopyPlugin({
        patterns: [
          {
            from: path.resolve(__dirname, "./static"),
            to: path.resolve(__dirname, "./dist/static"),
          },
        ],
        options: {
          concurrency: 500, // 并发数
        },
      }),
      UnoCSS({}),
      // new CommonsChunkPlugin({

      // }),
    ],
    output: {
      // 输出重构 打包编译后的js文件名称,添加时间戳.
      filename: `js/js[name].[hash].js?v=${timeStamp}`,
      chunkFilename: `js/chunk.[id].[hash].js?v=${timeStamp}`,
    },
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin("preload").tap(() => [
      {
        rel: "preload",
        // to ignore runtime.js
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: "initial",
      },
    ]);
    // config.plugin('uselessFile')
    //     .use(
    //       new UselessFile({
    //         root: path.resolve(__dirname, './src'), // 项目目录
    //         out: './fileList.json', // 输出文件列表
    //         clean: false, // 是否删除文件,
    //         exclude: /node_modules/ // 排除文件列表
    //       })
    //     )
    config.resolve.alias.set("countup.js", "countup.js/dist/countUp.min.js");
    config.module.rule("vue").uses.store.delete("cache-loader");
    config.module.rule("js").uses.store.delete("cache-loader");
    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete("prefetch");

    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    config.when(process.env.NODE_ENV !== "development", (config) => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // only package third parties that are initially dependent
          },
          // cdn 处理
          // elementUI: {
          //     name: "chunk-elementUI", // split elementUI into a single package
          //     priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          //     test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          // },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
          // 新增 CSS 缓存组
          styles: {
            name: "styles",
            test: /\.css$/,
            chunks: "all",
            enforce: true,
          },
        },
      });
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk("single");
    });

    config.module.rule("vue").uses.delete("cache-loader");
    config.module.rule("tsx").uses.delete("cache-loader");
    config.merge({
      cache: process.env.NODE_ENV === "development",
    });
  },
  css: {
    // extract: {
    //   // 打包后css文件名称添加时间戳
    //   filename: `css/[name].[hash].css?v=${timeStamp}`,
    //   chunkFilename: `css/chunk.[id].[hash].css?v=${timeStamp}`,
    //   ignoreOrder: true, // 打包时禁用顺序检查
    // },
    extract:
      process.env.NODE_ENV === "development"
        ? {
            filename: "[name].css",
            chunkFilename: "[name].[hash:9].css",
          }
        : true,
    loaderOptions: {
      sass: {
        implementation: require("sass"),
      },
    },
  },
  pwa: {
    iconPaths: {
      favicon32: "favicon.ico",
      favicon16: "favicon.ico",
      appleTouchIcon: "favicon.ico",
      maskIcon: "favicon.ico",
      msTileImage: "favicon.ico",
    },
  },
};
