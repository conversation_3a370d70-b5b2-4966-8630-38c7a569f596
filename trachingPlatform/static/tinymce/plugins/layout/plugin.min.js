/**
 * layout 1.5v  2020-01-14
 * The tinymce-plugins is used to set up a layout
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("layout",function(e,l){var w=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=e.getParam("layout_options",{clearStyle:[],filterTags:["table>*"],style:{"text-align":"justify","text-indent":"2em","line-height":1.5},tagsStyle:{}}),g={},h={},m;for(m in c.filterTags)-1!=c.filterTags[m].indexOf(">*")?h[c.filterTags[m].replace(">*","").toUpperCase()]=!0:g[c.filterTags[m].toUpperCase()]=!0;for(var q in c.tagsStyle){l=q.split(",");for(var r in l)-1!=l[r].indexOf(">*")?h[l[r].replace(">*",
"").toUpperCase()]=q:g[l[r].toUpperCase()]=q}var v=function(){function t(a,b){return(a=b.match(new RegExp(a+':?(.+?)"?[;}]')))?a[1]:!1}function u(a){var b=a.tagName;if(g[b]||h[b])return c.tagsStyle[g[b]]?n(a,c.tagsStyle[g[b]]):"",!0;for(var d=a.parentNode,k=d.tagName;"BODY"!==k;){b=k+">"+b;if(g[b]||h[k])return c.tagsStyle[h[k]]?n(a,c.tagsStyle[h[k]]):"",c.tagsStyle[g[b]]?n(a,c.tagsStyle[g[b]]):"",!0;d=d.parentNode;k=d.tagName}return!1}function n(a,b){for(var d in b)f.setStyle(a,d,b[d]);b["text-indent"]&&
(b="",a&&a.children["0"]&&a.children["0"].attributes&&a.children["0"].attributes.style&&(b=t("font-size",a.children["0"].attributes.style.textContent),d=t("letter-spacing",a.children["0"].attributes.style.textContent),b=b?2*(parseInt(b)+parseInt(d?d:0))+"px":2*(parseInt(d?d:0)+16)+"px"),f.setStyle(a,"text-indent",c.style["text-indent"]&&"2em"!=c.style["text-indent"]?c.style["text-indent"]:b?b:"2em"))}var f=e.dom;e.execCommand("selectAll");var x=e.selection.getSelectedBlocks(),p="";w.each(x,function(a){""==
p&&(f.hasClass(a,"layoutFV")?(p="remove",f.removeClass(a,"layoutFV")):(p="add",f.addClass(a,"layoutFV")));if("add"==p)if(u(a)?"":n(a,c.style),c.clearStyle){var b=f.getAttrib(a,"style");for(d in c.clearStyle)b=b.replace(new RegExp(c.clearStyle[d]+':?(.+?)"?[;}]'),"");f.setAttrib(a,"style",b)}else"";else if(u(a))"";else{var d=f.getAttrib(a,"style");for(b in c.style)d=d.replace(new RegExp(b+':?(.+?)"?[;}]'),"");f.setAttrib(a,"style",d)}})};e.ui.registry.getAll().icons.layout||e.ui.registry.addIcon("layout",
'<svg t="1603868236215" class="icon" viewBox="0 0 1035 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17720" width="20" height="20"><path d="M357.445818 285.358545L1005.730909 518.830545c21.76 8.192 32.628364 31.394909 24.471273 51.87491a40.634182 40.634182 0 0 1-21.748364 23.214545l-245.992727 110.592a80.034909 80.034909 0 0 0-42.135273 42.321455l-104.657454 249.856c-8.145455 20.48-32.616727 30.045091-53.003637 21.85309-10.868364-4.096-19.025455-13.661091-23.098182-24.576L305.792 337.221818a40.075636 40.075636 0 0 1 24.471273-51.874909c8.145455-4.096 17.664-4.096 27.182545 0z m8.145455-255.32509v99.67709c0 16.384-13.579636 30.033455-29.893818 30.033455-16.302545 0-29.905455-13.649455-29.905455-30.033455V30.021818C305.803636 13.649455 319.406545 0 335.709091 0c16.314182 0 29.905455 13.649455 29.905454 30.033455zM29.905455 303.104h99.211636c16.302545 0 29.905455 13.649455 29.905454 30.033455s-13.602909 30.045091-29.905454 30.04509H29.905455C13.591273 363.170909 0 349.521455 0 333.137455s13.591273-30.033455 29.905455-30.033455zM645.573818 66.897455l-144.058182 144.73309c-12.241455 12.288-29.905455 12.288-42.135272 0-12.229818-12.288-12.229818-30.045091 0-42.33309l144.058181-144.721455c12.229818-12.288 29.905455-12.288 42.135273 0 10.868364 10.926545 10.868364 30.033455 0 42.321455zM67.944727 20.48L212.014545 165.201455c12.241455 12.288 12.241455 30.045091 0 42.33309-12.218182 12.288-29.905455 12.288-42.123636 0L25.832727 62.801455c-12.241455-12.288-12.241455-30.033455 0-42.321455 10.868364-12.288 29.893818-12.288 42.123637 0z m149.515637 480.593455L73.402182 645.818182c-12.241455 12.288-29.905455 12.288-42.146909 0-12.218182-12.288-12.218182-30.045091 0-42.333091l144.058182-144.721455c12.241455-12.288 29.905455-12.288 42.146909 0 12.218182 12.288 12.218182 30.033455 0 42.321455z" style="width:20px; height:20px" p-id="17721"></path></svg>');
e.ui.registry.addToggleButton("layout",{icon:"layout",tooltip:"\u4e00\u952e\u5e03\u5c40",onAction:function(){e.undoManager.transact(function(){e.focus();v()})}});e.ui.registry.addMenuItem("layout",{text:"\u4e00\u952e\u5e03\u5c40",onAction:function(){e.undoManager.transact(function(){e.focus();v()})}});return{getMetadata:function(){return{name:"\u4e00\u952e\u5e03\u5c40",url:"https://github.com/Five-great/tinymce-plugins"}}}});