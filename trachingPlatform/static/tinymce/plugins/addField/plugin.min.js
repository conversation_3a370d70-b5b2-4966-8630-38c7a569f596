/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2022-05-26)
 */
(function(domGlobals) {
    "use strict";

    var global = tinymce.util.Tools.resolve("tinymce.PluginManager");

    var setContent = function(editor, html) {
        editor.focus();
        console.log("html", html);
        editor.undoManager.transact(function() {
            editor.setContent(html);
        });
        // editor.selection.setCursorLocation();
        // editor.nodeChanged();
    };
    var getContent = function(editor) {
        return editor.getContent({ source_view: true });
    };
    // "alertbanner" : {},
    // "bar": {},
    // "button": {},
    // "checkbox": {},
    // "colorinput": {},
    // "colorpicker": {},
    // "dropzone": {},
    // "grid": {},
    // "iframe": {},
    // "input": {},
    // "selectbox": {},
    // "sizeinput": {},
    // "textarea": {},
    // "urlinput": {},
    // "customeditor": {},
    // "htmlpanel": {},
    // "imagetools": {},
    // "collection": {},
    // "label": {},
    // "table": {},
    // "panel": {}
    //<span index="序号" values="col_1389578932699144" data-values="col_1389578932699144" data-id="1389578932699144" class="fileKey"><span style="color:#2B66FF;font-size:12px;">{{序号}} <span></span></span></span>

    var controls = function() {
        let control = [
            "input",
            "textarea",
            "checkbox"
            // "selectbox"
        ];

        let controlItem = [];
        //字段类型模版
        control.forEach(element => {
            controlItem.push({ text: element, value: element + "" });
        });

        return controlItem;
    };

    let width = 250;
    var open = function(editor) {
        // let editorContent = getContent(editor);

        let objectItem = []; // editor.settings.selectObj
        editor.settings.selectObj.forEach(element => {
            objectItem.push({ text: element.name, value: element.id + "" });
        });

        // let controlItem = []; // editor.settings.selectObj
        // control.forEach(element => {
        //     controlItem.push({ text: element, value: element + "" });
        // });

        editor.windowManager.open({
            title: "动态插入属性字段",
            body: {
                type: "panel",
                items: [
                    { type: "selectbox", name: "dataObjectId", label: "所属对象模型", items: objectItem },
                    { type: "selectbox", name: "type", label: "插入字段类型", items: controls() },
                    { type: "input", name: "title", label: "插入模型字段名称" },
                    { type: "input", name: "width", label: "文本框宽度" }
                ]
            },
            buttons: [
                { type: "cancel", text: "Close" },
                { type: "submit", text: "Save", primary: true }
            ],
            initialData: {
                width: "80"
            },
            onSubmit: function(api) {
                let data = api.getData();
                // 将输入框内容插入到内容区光标位置
                // setContent(editor, data);
                let col = "col_" + editor.settings.snowflake.generate();
                // let colId = "col_" + editor.settings.snowflake.generate();
                // let colName = "col_" + editor.settings.snowflake.generate();
                if (data.title != "" || data.type == "checkbox") {
                    //生成控件
                    let control = controlHtml(data, col);
                    //插入
                    editor.insertContent(control);
                    // editor.insertContent(
                    //     `<input class='fileKey' style="border:none;border-bottom:1px solid #ccc;height:26px;margin:0 2px;width:'${data.width}'" id='${col}'   data-values='${col}' title="${data.title}" value=""></input>`
                    // );

                    if (editor.settings.selectObj.length > 0) {
                        editor.settings.selectObj.forEach(element => {
                            if (element.id == data.dataObjectId) {
                                element.columns.push({
                                    colNo: 0,
                                    columnName: col,
                                    dataObjectId: data.dataObjectId,
                                    displayName: data.title
                                });
                            }
                            // else {
                            //     element.columns.push({
                            //         colNo: 0,
                            //         columnName: col,
                            //         dataObjectId: data.dataObjectId || 0,
                            //         displayName: data.title
                            //     });
                            // }
                        });
                    }
                } else {
                    editor.notificationManager.open({
                        text: "模型字段名称不能为空！"
                    });
                }
                api.close();
            }
        });
    };

    var controlHtml = function(data, col) {
        let html = ``;
        // contenteditable="true"<span contenteditable="true">11111111111111111</span>
        switch (data.type) {
            case "input":
                html = `&nbsp;<span class='fileKey addFiled input' tabindex="0" style="min-width:${data.width}px" id='${col}' data-values='${col}' title="${data.title}" value="">&nbsp;&nbsp;</span>`;
                return html;
            case "textarea":
                html = `<textarea class='fileKey textarea' cols="40" rows="6" style="min-width:${data.width}px" id='${col}' data-values='${col}' title="${data.title}" value=""> </textarea>`;
                return html;
            case "checkbox":
                html = `<input class='fileKey checkFiled' type='checkbox' id='${col}' data-values='${col}' title="${data.title}" value=""/>`;
                if (data.title != "") {
                    html += `<label for='${col}'>${data.title}</label>`;
                }
                return html;
            case "selectbox":
                html = `<select style="min-width:${data.width}px" id='${col}' data-values='${col}' title="${data.title}">
                        <option>18岁以下</option>
                        <option>18-28岁</option>
                        <option>28-38岁</option>
                        <option>38岁以上</option>
                    </select>`;
                return html;
        }
    };

    var register = function(editor) {
        editor.addCommand("mceCodeEditor", function() {
            open(editor);
        });
    };
    //

    //editor.ui.registry.getAll().icons.addField ||

    var register$1 = function(editor) {
        editor.ui.registry.addIcon(
            "addField",
            '<svg t="1659518338851" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4163" width="20" height="20"><path d="M947.2 1024H25.6V0h972.8v1024h-51.2z m0-972.8H76.8v921.6h870.4V51.2zM230.4 768h563.2v102.4H230.4v-102.4z m358.4-51.2H435.2V256H230.4V153.6h563.2v102.4h-204.8v460.8z" fill="#484D55" p-id="4164"></path></svg>'
        );

        editor.ui.registry.addButton("addField", {
            tooltip: "addField",
            icon: "addField",
            onAction: function() {
                return open(editor);
            }
        });
        editor.ui.registry.addMenuItem("addField", {
            text: "插入模型字段",
            icon: "addField",
            onAction: function() {
                return open(editor);
            }
        });
    };

    function Plugin() {
        global.add("addField", function(editor) {
            register(editor);
            register$1(editor);
            return {};
        });
    }

    Plugin();
})(window);