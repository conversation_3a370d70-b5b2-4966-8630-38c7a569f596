/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2022-05-26)
 */

(function(domGlobals) {
    "use strict";
    var textareaValue;

    var global = tinymce.util.Tools.resolve("tinymce.PluginManager");

    var setContent = function(editor, html) {
        editor.focus();
        editor.undoManager.transact(function() {
            editor.setContent(html);
        });
    };

    var getContent = function(editor) {
        return editor.getContent({ source_view: true });
    };

    var open = function(editor) {
        //let editorContent = getContent(editor);
        let objectItem = []; // editor.settings.selectObjd
        // editor.settings.selectObj.forEach(element => {
        //     objectItem.push({
        //         text: element.name,
        //         value: element.id + ""
        //     });
        // });
        if (textareaValue == "") {
            editor.notificationManager.open({
                text: "请选中需要提取的文本信息！"
            });
            return;
        }

        editor.windowManager.open({
            title: "动态提取内容字段",
            body: {
                type: "panel",
                items: [
                    // {
                    //     type: "selectbox",
                    //     name: "dataObjectId",
                    //     label: "所属对象模型",
                    //     items: objectItem
                    // },
                    {
                        type: "input",
                        name: "title",
                        label: "插入模型字段名称"
                    },
                    {
                        type: "textarea",
                        name: "sectionContent",
                        label: "提取内容"
                    }
                ]
            },
            buttons: [{
                    type: "cancel",
                    text: "Close"
                },
                {
                    type: "submit",
                    text: "Save",
                    primary: true
                }
            ],
            initialData: {
                //add_custom: editorContent
                sectionContent: textareaValue
            },
            onSubmit: function(api) {
                var data = api.getData();

                // 将输入框内容插入到内容区光标位置
                var col = "s_" + editor.settings.snowflake.generate();

                if (data.title == "") {
                    editor.notificationManager.open({
                        text: "模型字段名称不能为空！"
                    });
                    api.close();
                    return;
                    // let content = editor.getContent();
                    // console.log('content', content)
                    // editor.insertContent(
                    //     `<input class='fileKey' style="border:none;border-bottom:1px solid #ccc;height:26px;margin:0 2px;width:'${data.width}'" id='${col}'   data-values='${col}' title="${data.title}" value=""></input>`
                    // );
                    // if (editor.settings.selectObj.length > 0) {
                    //     editor.settings.insertSectionObj.forEach(element => {
                    //         if (element.id == data.dataObjectId) {
                    //             element.insertSectionObj.push({
                    //                 colNo: 0,
                    //                 sectionKey: col,
                    //                 dataObjectId: data.dataObjectId,
                    //                 sectionName: data.title,
                    //                 sectionContent: data.SectionContent
                    //             });
                    //         } else {
                    //             element.insertSectionObj.push({
                    //                 colNo: 0,
                    //                 sectionKey: col,
                    //                 dataObjectId: data.dataObjectId || 0,
                    //                 sectionName: data.title,
                    //                 sectionContent: data.SectionContent
                    //             });
                    //         }
                    //     });
                    // }
                }
                // 赋值原型对象
                if (data.title && data.sectionContent) {
                    editor.settings.watchObj.id = "0";
                    editor.settings.watchObj.sectionKey = col;
                    editor.settings.watchObj.objectTemplateId = editor.settings.selectObj.objectTemplateId;
                    editor.settings.watchObj.sectionName = data.title;
                    editor.settings.watchObj.sectionContent = data.sectionContent;
                    api.close();
                    return;
                }
                //
            }
        });

        // document.querySelector("textarea").value = textareaValue;
    };

    var register = function(editor) {
        editor.addCommand("mceCodeEditor", function() {
            open(editor);
        });
    };

    // 获取鼠标已复制value
    document.querySelector("body").addEventListener("mouseup", e => {
        if (document.querySelector("iframe")) {
            try {
                textareaValue = document
                .querySelector("iframe")
                .contentWindow.getSelection()
                .toString()
                .trim();
            } catch (error) {
                
            }
            
        }
    });

    // 触发弹窗
    var register$1 = function(editor) {
        // editor.ui.registry.addButton('addSection', {
        //     tooltip: 'addSection',
        //     text: '选择 addSection',
        //     onAction: function() {
        //         return open(editor);
        //     }
        // });

        editor.ui.registry.addIcon(
            "addSection",
            '<svg t="1659519032498" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6748" width="24" height="24"><path d="M509.606998 104.904235c-24.043602 0-45.922912 13.226233-56.177464 33.95637L356.189863 336.302419l-223.674269 32.54216c-22.983457 3.304256-42.100864 18.718317-49.481971 39.659255-7.381108 21.048385-1.812275 44.23241 14.431687 60.033281l163.916257 160.125931-38.011732 222.016513c-3.868097 22.408359 6.03239 44.819788 25.458835 57.94676 10.69662 7.116071 23.204491 10.784624 35.757388 10.784624 10.298554 0 20.663622-2.475378 30.055526-7.337105l194.987926-102.7205L704.662463 912.072815c9.369392 4.861728 19.712971 7.337105 29.990035 7.337105 12.57541 0 25.082258-3.668553 35.778878-10.784624 19.426445-13.126972 29.305443-35.538401 25.460882-57.94676l-38.012755-222.016513 163.937746-160.125931c16.22145-15.812127 21.810748-38.984896 14.408151-60.033281-7.402597-20.940938-26.51898-36.353976-49.503461-39.659255L663.04767 336.302419l-97.240695-197.441814C555.619962 118.131491 533.695626 104.904235 509.606998 104.904235L509.606998 104.904235z" p-id="6749"></path></svg>'
        );

        editor.ui.registry.addMenuItem("addSection", {
            text: "提取文本",
            icon: "addSection",
            onAction: function() {
                return open(editor);
            }
        });
    };

    function Plugin() {
        global.add("addSection", function(editor) {
            register(editor);
            register$1(editor);
            return {};
        });
    }

    Plugin();
})(window);