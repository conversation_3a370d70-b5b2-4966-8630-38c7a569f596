/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2022-05-26)
 */
(function(domGlobals) {
    "use strict";

    var global = tinymce.util.Tools.resolve("tinymce.PluginManager");

    var setContent = function(editor, html) {
        editor.focus();

        editor.undoManager.transact(function() {
            editor.setContent(html);
        });
    };
    var getContent = function(editor) {
        return editor.getContent({ source_view: true });
    };

    // 初始化插入字段类型
    var controls = function() {
        let control = [
            "input",
            "textarea",
            "checkbox"
            // "selectbox"
        ];

        let controlItem = [];
        //字段类型模版
        control.forEach(element => {
            controlItem.push({ text: element, value: element + "" });
        });

        return controlItem;
    };

    let bodyItemsConfig = [
        { type: "selectbox", name: "dataObjects", label: "选择对象模型", items: [] },
        { type: "selectbox", name: "columnName", label: "模型模型字段", items: [] },
        { type: "selectbox", name: "type", label: "插入字段类型", items: controls() },
        { type: "input", name: "width", label: "文本框宽度" }
    ];

    var controlHtml = function(data, col, title) {
        let html = ``;
        switch (data.type) {
            case "input":
                html = `&nbsp;<span class='fileKey addFiled input' tabindex="0" style="min-width:${data.width}px" id='${col}' data-values='${col}' title="${title}" value="">&nbsp;&nbsp;</span>`;
                return html;
            case "textarea":
                html = `<textarea class='fileKey textarea' cols="40" rows="6" style="min-width:${data.width}px" id='${col}' data-values='${col}' title="${title}" value=""> </textarea>`;
                return html;
            case "checkbox":
                html = `<input class='fileKey checkFiled' type='checkbox' id='${col}' data-values='${col}' title="${title}" value=""/>`;
                if (title != "") {
                    html += `<label for='${col}'>${title}</label>`;
                }
                return html;
            case "selectbox":
                html = `<select style="min-width:${data.width || width}px" id='${col}' data-values='${col}' title="${
                    data.title
                }">
                        <option>18岁以下</option>
                        <option>18-28岁</option>
                        <option>28-38岁</option>
                        <option>38岁以上</option>
                    </select>`;
                return html;
        }
    };

    var open = function(editor) {
        //let editorContent = getContent(editor);
        let fieldItem = [];
        if (editor.settings.initSelectItem) {
            bodyItemsConfig[0].items = [];
            fieldItem = [];
            //  let length = editor.settings.selectObj.length;
            for (let index = 0; index < editor.settings.selectObj.length; index++) {
                const element = editor.settings.selectObj[index];
                bodyItemsConfig[0].items.push({ text: element.name, value: element.id + "" });
                if (index == 0) {
                    element.columns.forEach(e => {
                        fieldItem.push({
                            text: e.displayName,
                            value: e.columnName + ""
                        });
                    });
                }
            }
            bodyItemsConfig.forEach(s => {
                if (s.name == "columnName") {
                    s.items = fieldItem;
                }
            });
        }
        editor.windowManager.open({
            title: "动态选择属性字段",
            body: {
                type: "panel",
                items: bodyItemsConfig
            },
            buttons: [{
                    type: "cancel",
                    text: "Close"
                },
                {
                    type: "submit",
                    text: "Save",
                    primary: true
                }
            ],
            //弹窗文本默认数据赋值
            initialData: {
                width: "150",
                columnName: fieldItem + ""
            },
            onChange: function(api, details) {
                let data = api.getData();
                let fieldItem = [];
                if (details.name == "dataObjects") {
                    let selItem = bodyItemsConfig.findIndex(item => item.name == details.name);
                    let instaItem = null;
                    if (selItem > -1) {
                        let indexOf = bodyItemsConfig[selItem].items.findIndex(s => s.value == data.dataObjects);
                        /// 删除当前选中的
                        if (indexOf > -1) {
                            bodyItemsConfig[selItem].items.splice(indexOf, 1);
                        }
                    }
                    editor.settings.selectObj.forEach(element => {
                        if (element.id == data.dataObjects) {
                            // 把选择的数组移到第一位，重新打开
                            bodyItemsConfig[selItem].items.splice(selItem, 0, {
                                text: element.name,
                                value: element.id + ""
                            });

                            element.columns.forEach(e => {
                                fieldItem.push({ text: e.displayName, value: e.columnName + "" });
                            });
                        }
                    });

                    bodyItemsConfig.forEach(s => {
                        if (s.name == "columnName") {
                            s.items = fieldItem;
                        }
                    });

                    editor.settings.initSelectItem = false;
                    console.log("bodyItemsConfig", bodyItemsConfig);
                    api.close();
                    open(editor);
                }
            },
            onSubmit: function(api) {
                var data = api.getData();
                // 将输入框内容插入到内容区光标位置
                editor.settings.selectObj.forEach(element => {
                    element.columns.forEach(e => {
                        if (e.columnName == data.columnName) {
                            let control = controlHtml(data, data.columnName, e.displayName);
                            //插入
                            editor.insertContent(control);
                            // editor.insertContent(`<input class='fileKey' style="border:none;border-bottom:1px solid #ccc;height:26px;margin:0 2px;" id='${e.columnName}' data-values='${e.columnName}' title="${e.displayName}" value="" ></input>`);
                        }
                    });
                });
                api.close();
            }
        });
    };

    var register = function(editor) {
        editor.addCommand("mceCodeEditor", function() {
            open(editor);
        });
    };

    //

    var register$1 = function(editor) {
        editor.ui.registry.addIcon(
            "addFieldSelect",
            '<svg t="1659518263888" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3169" width="20" height="20"><path d="M163.53 163.48h696.48v298.49h99.5V63.99H64.04v895.48h397.99v-99.5h-298.5V163.48z m845.73 447.74L412.27 412.14l199 547.33 99.5-199 199 199 49.75-49.75-199-199 248.74-99.5z" p-id="3170"></path></svg>'
        );

        editor.ui.registry.addButton("addFieldSelect", {
            tooltip: "addFieldSelect",
            icon: "addFieldSelect",
            onAction: function() {
                return open(editor);
            }
        });
        editor.ui.registry.addMenuItem("addFieldSelect", {
            text: "选择模型字段",
            icon: "addFieldSelect",
            onAction: function() {
                return open(editor);
            }
        });
    };

    function Plugin() {
        global.add("addFieldSelect", function(editor) {
            register(editor);
            register$1(editor);
            return {};
        });
    }

    Plugin();
})(window);