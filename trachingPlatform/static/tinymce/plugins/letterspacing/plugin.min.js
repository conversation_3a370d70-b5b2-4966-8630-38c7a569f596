/**
 * letterspacing 1.5v 2021-1-14
 * The tinymce-plugins is used to set the word spacing
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("letterspacing",function(a,l){function g(b){var e=a.dom,f=a.selection.getSelectedBlocks();h.each(f,function(c){if(e.getStyle(c,"text-indent")){var d="";c&&c.children["0"]&&c.children["0"].attributes&&c.children["0"].attributes.style&&(d=(d=(d=c.children["0"].attributes.style.textContent.match(/font-size:?(.+?)"?[;}]/))?d[1]:!1)?2*(parseInt(d)+parseInt(b?b:0))+"px":2*(parseInt(b?b:0)+16)+"px");e.setStyle(c,"text-indent",d?d:"2em")}});a.undoManager.transact(function(){a.focus();
    a.formatter.apply("letterspacing",{value:b})})}var h=tinymce.util.Tools.resolve("tinymce.util.Tools"),k=a.getParam("letterspacing","0px 1px 2px 4px 6px 8px 10px 20px 40px");a.on("init",function(){a.formatter.register({letterspacing:{inline:"span",toggle:!1,styles:{"letter-spacing":"%value"},clear_child_styles:!0}})});a.ui.registry.getAll().icons.letterspacing||a.ui.registry.addIcon("letterspacing",'<svg t="1610616201691" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="969" width="24" height="24"><path d="M682.666667 704l128 106.666667-128 106.666666v-85.333333H341.333333v85.333333L213.333333 810.666667l128-106.666667v85.333333h341.333334v-85.333333zM170.666667 170.666667v682.666666H85.333333V170.666667h85.333334z m768 0v682.666666h-85.333334V170.666667h85.333334z m-394.666667 0l202.666667 469.333333h-89.6l-38.4-93.866667h-213.333334L366.933333 640H277.333333l202.666667-469.333333h64zM512 255.146667L432.213333 469.333333h159.573334L512 255.146667z" p-id="970" fill="#222f3e"></path></svg>');
    a.ui.registry.addMenuButton("letterspacing",{icon:"letterspacing",tooltip:"\u8bbe\u7f6e\u95f4\u8ddd",fetch:function(b){var e=k.split(" ").map(function(f){return{type:"togglemenuitem",text:f,onAction:function(){g(f)}}});b(e)}});return{getMetadata:function(){return{name:"\u8bbe\u7f6e\u95f4\u8ddd",url:"https://github.com/Five-great/tinymce-plugins"}}}});