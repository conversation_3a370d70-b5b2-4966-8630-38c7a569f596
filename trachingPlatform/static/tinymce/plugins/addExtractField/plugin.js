/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.3.1 (2022-05-26)
 */
(function(domGlobals) {
    "use strict";

    var global = tinymce.util.Tools.resolve("tinymce.PluginManager");

    var setContent = function(editor, html) {
        editor.focus();
        console.log("html", html);
        editor.undoManager.transact(function() {
            editor.setContent(html);
        });
    };

    var open = function(editor) {
        // let editorContent = getContent(editor);
        let objectItem = []; // editor.settings.selectObj
        editor.settings.selectObj.forEach(element => {
            objectItem.push({ text: element.name, value: element.id + "" });
        });

        editor.windowManager.open({
            title: "动态提取属性字段",
            body: {
                type: "panel",
                items: [{ type: "selectbox", name: "dataObjectId", label: "所属对象模型", items: objectItem }]
            },
            buttons: [
                { type: "cancel", text: "Close" },
                { type: "submit", text: "Save", primary: true }
            ],
            initialData: {
                width: "150"
            },
            onSubmit: function(api) {

                let data = api.getData();

                let extractList = document.querySelector("iframe").contentWindow.document.querySelectorAll("td");

                let targetList = [];

                editor.settings.selectObj.forEach(element => {
                    extractList.forEach((val, index) => {
                        if (val.innerText.trim().replace("&nbsp;", "")) {
                            var snowflakeId = editor.settings.snowflake.generate();
                            if (element.id == data.dataObjectId) {
                                element.columns.push({
                                    columnsId: snowflakeId,
                                    columnsName: "col_" + snowflakeId,
                                    sort: index + 1,
                                    displayName: val.innerText.trim().replace("&nbsp;", ""),
                                    dataObjectId: data.dataObjectId,
                                    displayName: val.innerText.trim().replace("&nbsp;", "")
                                });
                            } else {
                                element.columns.push({
                                    columnsId: snowflakeId,
                                    columnsName: "col_" + snowflakeId,
                                    sort: index + 1,
                                    displayName: val.innerText.trim().replace("&nbsp;", ""),
                                    dataObjectId: 0,
                                    displayName: val.innerText.trim().replace("&nbsp;", "")
                                });
                            }
                        }
                    });
                });
                api.close();
            }
        });
    };

    var register = function(editor) {
        editor.addCommand("mceCodeEditor", function() {
            open(editor);
        });
    };

    var register$1 = function(editor) {
        editor.ui.registry.addIcon(
            "addExtractField",
            '<svg t="1659518338851" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4163" width="20" height="20"><path d="M947.2 1024H25.6V0h972.8v1024h-51.2z m0-972.8H76.8v921.6h870.4V51.2zM230.4 768h563.2v102.4H230.4v-102.4z m358.4-51.2H435.2V256H230.4V153.6h563.2v102.4h-204.8v460.8z" fill="#484D55" p-id="4164"></path></svg>'
        );

        editor.ui.registry.addButton("addExtractField", {
            tooltip: "addExtractField",
            icon: "addExtractField",
            onAction: function() {
                return open(editor);
            }
        });
        editor.ui.registry.addMenuItem("addExtractField", {
            text: "提取模型字段",
            icon: "addExtractField",
            onAction: function() {
                return open(editor);
            }
        });
    };

    function Plugin() {
        global.add("addExtractField", function(editor) {
            register(editor);
            register$1(editor);
            return {};
        });
    }

    Plugin();
})(window);