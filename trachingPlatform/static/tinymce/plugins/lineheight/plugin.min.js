/**
 * lineheight 1.1v
 * The tinymce-plugins is used to set the line spacing
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("lineheight",function(a,l){var g=tinymce.util.Tools.resolve("tinymce.util.Tools"),h=a.getParam("lineheight_val","1 1.5 1.6 1.75 1.8 2 3 4 5");a.on("init",function(){a.formatter.register({lineheight:{selector:"p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table",styles:{"line-height":"%value"}}})});var k=function(c){a.undoManager.transact(function(){a.focus();a.formatter.apply("lineheight",{value:c})})};a.ui.registry.getAll().icons.lineheight||a.ui.registry.addIcon("lineheight",
'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M9.984 12.984v-1.969h12v1.969h-12zM9.984 18.984v-1.969h12v1.969h-12zM9.984 5.016h12v1.969h-12v-1.969zM6 6.984v10.031h2.484l-3.469 3.469-3.516-3.469h2.484v-10.031h-2.484l3.516-3.469 3.469 3.469h-2.484z"></path></svg>');a.ui.registry.addMenuButton("lineheight",{icon:"lineheight",tooltip:"\u8bbe\u7f6e\u884c\u9ad8",fetch:function(c){var f=a.dom,d=a.selection.getSelectedBlocks(),e=0;g.each(d,function(b){0==e&&
(e=f.getStyle(b,"line-height")?f.getStyle(b,"line-height"):0)});d=h.split(" ").map(function(b){return{type:"togglemenuitem",text:b,active:e==b?!0:!1,onAction:function(){k(b)}}});c(d)}});return{getMetadata:function(){return{name:"\u8bbe\u7f6e\u884c\u9ad8",url:"https://github.com/Five-great/tinymce-plugins"}}}});