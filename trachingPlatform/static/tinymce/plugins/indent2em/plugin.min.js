/**
 * indent2em (Enhancement 1.5v) 2021-01-14
 * The tinymce-plugins is used to set the first line indent (Enhancement)
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("indent2em",function(a,n){function l(c,e){return(c=e.match(new RegExp(c+':?(.+?)"?[;}]')))?c[1]:!1}var m=tinymce.util.Tools.resolve("tinymce.util.Tools"),g=a.getParam("indent2em_val","2em");a.on("init",function(){a.formatter.register({indent2em:{selector:"p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table",styles:{"text-indent":"%value"}}})});var k=function(){a.undoManager.transact(function(){a.focus();var c=a.dom,e=a.selection.getSelectedBlocks(),f="";m.each(e,function(d){var b=
    "";if(d&&d.children["0"]&&d.children["0"].attributes&&d.children["0"].attributes.style){b=l("font-size",d.children["0"].attributes.style.textContent);var h=l("letter-spacing",d.children["0"].attributes.style.textContent);b=b?2*(parseInt(b)+parseInt(h?h:0))+"px":2*(parseInt(h?h:0)+16)+"px"}""==f&&(f=c.getStyle(d,"text-indent")==("2em"!=g?g:b?b:"2em")?"remove":"add");"add"==f?c.setStyle(d,"text-indent","2em"!=g?g:b?b:"2em"):(b=c.getAttrib(d,"style"),b=b.replace(/text-indent?(.+?)"?[;}]/ig,""),c.setAttrib(d,
    "style",b))})})};a.ui.registry.getAll().icons.indent2em||a.ui.registry.addIcon("indent2em",'<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M170.666667 563.2v-102.4H887.466667v102.4zM170.666667 836.266667v-102.4H887.466667v102.4zM512 290.133333v-102.4H887.466667v102.4zM238.933333 341.333333V136.533333l204.8 102.4z" fill="#2c2c2c" p-id="5210"></path></svg>');a.ui.registry.addToggleButton("indent2em",{icon:"indent2em",tooltip:"\u9996\u884c\u7f29\u8fdb",
    onAction:function(){k()},onSetup:function(c,e){return function(f){return c.selection.selectorChangedWithUnbind(e.join(","),f.setActive).unbind}}(a,['*[style*="text-indent"]','*[data-mce-style*="text-indent"]'])});a.ui.registry.addMenuItem("indent2em",{text:"\u9996\u884c\u7f29\u8fdb",onAction:function(){k()}});a.addCommand("indent2em",k);return{getMetadata:function(){return{name:"\u9996\u884c\u7f29\u8fdb",url:"https://github.com/Five-great/tinymce-plugins"}}}});